# Prometheus监控配置 - 微服务架构

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'visthink-erp'
    environment: 'development'

# 规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # 用户与租户服务
  - job_name: 'member-center'
    static_configs:
      - targets: ['member-center:8081']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # 商品服务
  - job_name: 'product-service'
    static_configs:
      - targets: ['product-service:8082']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # 库存服务
  - job_name: 'inventory-service'
    static_configs:
      - targets: ['inventory-service:8083']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # 订单服务
  - job_name: 'order-service'
    static_configs:
      - targets: ['order-service:8084']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # 平台集成服务
  - job_name: 'platform-integration'
    static_configs:
      - targets: ['platform-integration:8085']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # 文件服务
  - job_name: 'file-service'
    static_configs:
      - targets: ['file-service:8086']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # API日志服务
  - job_name: 'api-log-service'
    static_configs:
      - targets: ['api-log-service:8087']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # 工作流服务
  - job_name: 'workflow-service'
    static_configs:
      - targets: ['workflow-service:8088']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # 配置中心
  - job_name: 'config-center'
    static_configs:
      - targets: ['config-center:8888']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # API网关
  - job_name: 'gateway'
    static_configs:
      - targets: ['gateway:8080']
    metrics_path: /q/metrics
    scrape_interval: 15s
    scrape_timeout: 10s

  # PostgreSQL数据库监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Consul监控
  - job_name: 'consul'
    static_configs:
      - targets: ['consul:8500']
    metrics_path: /v1/agent/metrics
    params:
      format: ['prometheus']
    scrape_interval: 30s

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s
    scrape_timeout: 10s

  # MinIO监控
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: /minio/v2/metrics/cluster
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter (系统监控)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # cAdvisor (容器监控)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Jaeger监控
  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    metrics_path: /metrics
    scrape_interval: 30s

# 服务发现配置 (Consul)
  - job_name: 'consul-services'
    consul_sd_configs:
      - server: 'consul:8500'
        services: []
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_service_id]
        target_label: instance
      - source_labels: [__meta_consul_tags]
        target_label: tags
        regex: ,(.+),
        replacement: ${1}
      - source_labels: [__meta_consul_service_address]
        target_label: __address__
        regex: (.+)
        replacement: ${1}:${__meta_consul_service_port}

# 远程写入配置 (可选)
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置 (可选)
# remote_read:
#   - url: "http://remote-storage:9201/read"
