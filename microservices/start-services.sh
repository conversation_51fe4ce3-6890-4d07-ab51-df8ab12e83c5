#!/bin/bash

# Visthink ERP 微服务启动脚本
# 用于按顺序启动微服务，确保依赖关系正确

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker和Docker Compose
check_prerequisites() {
    log_step "检查前置条件..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "前置条件检查通过"
}

# 清理旧容器
cleanup_old_containers() {
    log_step "清理旧容器..."
    
    # 停止并删除旧容器
    docker-compose down --remove-orphans
    
    # 清理未使用的镜像
    docker image prune -f
    
    log_info "旧容器清理完成"
}

# 构建共享模块
build_shared_module() {
    log_step "构建共享模块..."
    
    cd shared-common
    mvn clean install -DskipTests
    cd ..
    
    log_info "共享模块构建完成"
}

# 启动基础设施服务
start_infrastructure() {
    log_step "启动基础设施服务..."
    
    # 启动数据库、Redis、Consul等基础服务
    docker-compose up -d postgres redis consul jaeger prometheus grafana minio
    
    # 等待服务启动
    log_info "等待基础设施服务启动..."
    sleep 30
    
    # 检查服务健康状态
    check_service_health "postgres" "5432"
    check_service_health "redis" "6379"
    check_service_health "consul" "8500"
    
    log_info "基础设施服务启动完成"
}

# 检查服务健康状态
check_service_health() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1
    
    log_info "检查 $service_name 服务健康状态..."
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T $service_name sh -c "exit 0" 2>/dev/null; then
            log_info "$service_name 服务健康检查通过"
            return 0
        fi
        
        log_warn "$service_name 服务未就绪，等待中... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name 服务健康检查失败"
    return 1
}

# 构建微服务
build_microservices() {
    log_step "构建微服务..."
    
    # 构建用户与租户服务
    if [ -d "member-center" ]; then
        log_info "构建 member-center 服务..."
        cd member-center
        mvn clean package -DskipTests
        cd ..
    fi
    
    # 构建其他服务（当它们存在时）
    for service in product-service inventory-service order-service platform-integration file-service api-log-service workflow-service config-center gateway; do
        if [ -d "$service" ]; then
            log_info "构建 $service 服务..."
            cd $service
            mvn clean package -DskipTests
            cd ..
        else
            log_warn "$service 服务目录不存在，跳过构建"
        fi
    done
    
    log_info "微服务构建完成"
}

# 启动核心服务
start_core_services() {
    log_step "启动核心微服务..."
    
    # 按依赖顺序启动服务
    services=("member-center" "product-service" "inventory-service" "order-service")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            log_info "启动 $service 服务..."
            docker-compose up -d $service
            
            # 等待服务启动
            sleep 15
            
            # 检查服务状态
            if docker-compose ps $service | grep -q "Up"; then
                log_info "$service 服务启动成功"
            else
                log_error "$service 服务启动失败"
                docker-compose logs $service
                exit 1
            fi
        else
            log_warn "$service 服务目录不存在，跳过启动"
        fi
    done
    
    log_info "核心微服务启动完成"
}

# 启动支撑服务
start_support_services() {
    log_step "启动支撑服务..."
    
    # 启动支撑服务
    services=("platform-integration" "file-service" "api-log-service" "workflow-service")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            log_info "启动 $service 服务..."
            docker-compose up -d $service
            sleep 10
        else
            log_warn "$service 服务目录不存在，跳过启动"
        fi
    done
    
    log_info "支撑服务启动完成"
}

# 启动网关服务
start_gateway() {
    log_step "启动API网关..."
    
    # 启动Nginx网关
    docker-compose up -d nginx
    
    # 等待网关启动
    sleep 10
    
    # 检查网关状态
    if curl -f http://localhost/health &> /dev/null; then
        log_info "API网关启动成功"
    else
        log_warn "API网关可能未完全就绪"
    fi
    
    log_info "API网关启动完成"
}

# 显示服务状态
show_service_status() {
    log_step "显示服务状态..."
    
    echo ""
    echo "=== 服务状态 ==="
    docker-compose ps
    
    echo ""
    echo "=== 服务访问地址 ==="
    echo "API网关: http://localhost"
    echo "用户服务: http://localhost:8081"
    echo "Consul UI: http://localhost:8500"
    echo "Prometheus: http://localhost:9090"
    echo "Grafana: http://localhost:3000 (admin/admin123)"
    echo "Jaeger UI: http://localhost:16686"
    echo "MinIO Console: http://localhost:9001 (minioadmin/minioadmin123)"
    echo ""
}

# 主函数
main() {
    log_info "开始启动 Visthink ERP 微服务架构..."
    
    # 检查前置条件
    check_prerequisites
    
    # 清理旧容器
    cleanup_old_containers
    
    # 构建共享模块
    build_shared_module
    
    # 启动基础设施
    start_infrastructure
    
    # 构建微服务
    build_microservices
    
    # 启动核心服务
    start_core_services
    
    # 启动支撑服务
    start_support_services
    
    # 启动网关
    start_gateway
    
    # 显示服务状态
    show_service_status
    
    log_info "Visthink ERP 微服务架构启动完成！"
    log_info "请访问 http://localhost 开始使用系统"
}

# 脚本参数处理
case "${1:-}" in
    "infrastructure")
        check_prerequisites
        cleanup_old_containers
        start_infrastructure
        ;;
    "build")
        build_shared_module
        build_microservices
        ;;
    "core")
        start_core_services
        ;;
    "support")
        start_support_services
        ;;
    "gateway")
        start_gateway
        ;;
    "status")
        show_service_status
        ;;
    "stop")
        log_info "停止所有服务..."
        docker-compose down
        ;;
    "restart")
        log_info "重启所有服务..."
        docker-compose restart
        ;;
    "logs")
        service_name=${2:-}
        if [ -n "$service_name" ]; then
            docker-compose logs -f $service_name
        else
            docker-compose logs -f
        fi
        ;;
    *)
        main
        ;;
esac
