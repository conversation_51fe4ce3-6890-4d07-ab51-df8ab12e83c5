#!/usr/bin/env python3
"""
Controller重构自动化脚本

功能：
1. 扫描现有的BaseResource子类
2. 生成重构后的Controller代码
3. 创建重构报告

使用方法：
python refactor-controller.py --service inventory-service --dry-run
python refactor-controller.py --service inventory-service --apply

作者：visthink
"""

import os
import re
import argparse
import json
from pathlib import Path
from typing import List, Dict, Optional

class ControllerRefactor:
    def __init__(self, service_path: str):
        self.service_path = Path(service_path)
        self.resource_path = self.service_path / "src/main/java"
        self.refactor_report = {
            "service": service_path,
            "controllers": [],
            "issues": [],
            "summary": {}
        }
    
    def scan_controllers(self) -> List[Dict]:
        """扫描现有的Controller文件"""
        controllers = []
        
        # 查找所有Java文件
        for java_file in self.resource_path.rglob("*Resource.java"):
            if self.is_base_resource_controller(java_file):
                controller_info = self.analyze_controller(java_file)
                controllers.append(controller_info)
        
        return controllers
    
    def is_base_resource_controller(self, file_path: Path) -> bool:
        """检查是否是BaseResource的子类"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                return 'extends BaseResource' in content
        except Exception as e:
            print(f"读取文件失败: {file_path}, 错误: {e}")
            return False
    
    def analyze_controller(self, file_path: Path) -> Dict:
        """分析Controller文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取类名
        class_match = re.search(r'public class (\w+)', content)
        class_name = class_match.group(1) if class_match else "Unknown"
        
        # 提取泛型参数
        generic_match = re.search(r'extends BaseResource<([^>]+)>', content)
        generic_params = generic_match.group(1) if generic_match else ""
        
        # 提取Service注入
        service_match = re.search(r'@Inject\s+(\w+Service)\s+(\w+);', content)
        service_type = service_match.group(1) if service_match else ""
        service_field = service_match.group(2) if service_match else ""
        
        # 提取实体名称
        entity_name_match = re.search(r'return "([^"]+)";', content)
        entity_name = entity_name_match.group(1) if entity_name_match else ""
        
        # 提取自定义方法
        custom_methods = self.extract_custom_methods(content)
        
        return {
            "file_path": str(file_path),
            "class_name": class_name,
            "generic_params": generic_params,
            "service_type": service_type,
            "service_field": service_field,
            "entity_name": entity_name,
            "custom_methods": custom_methods,
            "line_count": len(content.split('\n'))
        }
    
    def extract_custom_methods(self, content: str) -> List[Dict]:
        """提取自定义方法"""
        methods = []
        
        # 查找所有public方法（排除getService和getEntityName）
        method_pattern = r'@\w+[^}]*?public\s+Uni<[^>]+>\s+(\w+)\([^)]*\)\s*\{[^}]*?\}'
        matches = re.finditer(method_pattern, content, re.DOTALL)
        
        for match in matches:
            method_name = match.group(1)
            if method_name not in ['getService', 'getEntityName']:
                methods.append({
                    "name": method_name,
                    "signature": match.group(0)[:100] + "..."
                })
        
        return methods
    
    def generate_refactored_controller(self, controller_info: Dict) -> str:
        """生成重构后的Controller代码"""
        template = f'''package {self.get_package_name(controller_info["file_path"])};

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

/**
 * {controller_info["entity_name"]}管理REST接口 - 重构版本
 * 
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给{controller_info["service_type"]}处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 */
@Path("/api/{controller_info["entity_name"].lower()}")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "{controller_info["entity_name"]}管理", description = "{controller_info["entity_name"]}管理相关接口")
public class {controller_info["class_name"]}Refactored extends SimpleController {{

    @Inject
    {controller_info["service_type"]} {controller_info["service_field"]};

    /**
     * 创建{controller_info["entity_name"]}
     */
    @POST
    @Operation(summary = "创建{controller_info["entity_name"]}", description = "创建新的{controller_info["entity_name"]}记录")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<Object>> create(@Valid Object request) {{
        logOperation("创建{controller_info["entity_name"]}", "开始处理创建请求");
        
        return getCurrentTenantId()
                .flatMap(tenantId -> {{
                    logOperation("创建{controller_info["entity_name"]}", tenantId, "租户验证通过");
                    return {controller_info["service_field"]}.create(tenantId, request);
                }})
                .map(entity -> {{
                    logOperation("创建{controller_info["entity_name"]}", "创建成功");
                    return success("创建{controller_info["entity_name"]}成功", entity);
                }})
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "创建{controller_info["entity_name"]}"));
    }}

    /**
     * 根据ID查询{controller_info["entity_name"]}
     */
    @GET
    @Path("/{{id}}")
    @Operation(summary = "查询{controller_info["entity_name"]}", description = "根据ID查询{controller_info["entity_name"]}详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<Object>> findById(@PathParam("id") Long id) {{
        logOperation("查询{controller_info["entity_name"]}", "ID: " + id);
        
        return getCurrentTenantId()
                .flatMap(tenantId -> {controller_info["service_field"]}.findById(tenantId, id))
                .map(entity -> {{
                    if (entity != null) {{
                        logOperation("查询{controller_info["entity_name"]}", "查询成功");
                        return success(entity);
                    }} else {{
                        return ApiResponse.notFound("{controller_info["entity_name"]}不存在");
                    }}
                }})
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "查询{controller_info["entity_name"]}"));
    }}

    // TODO: 添加其他CRUD方法
    // TODO: 迁移自定义方法: {[method["name"] for method in controller_info["custom_methods"]]}
}}'''
        
        return template
    
    def get_package_name(self, file_path: str) -> str:
        """从文件路径提取包名"""
        # 简化实现，实际应该解析Java文件
        parts = file_path.split('/')
        java_index = parts.index('java') if 'java' in parts else -1
        if java_index >= 0:
            package_parts = parts[java_index + 1:-1]
            return '.'.join(package_parts)
        return "com.visthink.unknown"
    
    def generate_refactor_report(self, controllers: List[Dict]) -> Dict:
        """生成重构报告"""
        total_lines = sum(c["line_count"] for c in controllers)
        total_custom_methods = sum(len(c["custom_methods"]) for c in controllers)
        
        self.refactor_report.update({
            "controllers": controllers,
            "summary": {
                "total_controllers": len(controllers),
                "total_lines": total_lines,
                "total_custom_methods": total_custom_methods,
                "estimated_refactor_time": f"{len(controllers) * 2}小时"
            }
        })
        
        return self.refactor_report
    
    def run_refactor(self, dry_run: bool = True) -> Dict:
        """执行重构"""
        print(f"开始扫描服务: {self.service_path}")
        
        # 扫描Controller
        controllers = self.scan_controllers()
        print(f"发现 {len(controllers)} 个需要重构的Controller")
        
        # 生成重构代码
        for controller in controllers:
            print(f"分析Controller: {controller['class_name']}")
            
            if not dry_run:
                # 生成重构后的代码
                refactored_code = self.generate_refactored_controller(controller)
                
                # 保存重构后的文件
                output_path = Path(controller["file_path"]).with_name(
                    controller["class_name"] + "Refactored.java"
                )
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(refactored_code)
                
                print(f"生成重构文件: {output_path}")
        
        # 生成报告
        report = self.generate_refactor_report(controllers)
        
        return report

def main():
    parser = argparse.ArgumentParser(description='Controller重构工具')
    parser.add_argument('--service', required=True, help='微服务路径')
    parser.add_argument('--dry-run', action='store_true', help='只分析不生成文件')
    parser.add_argument('--output', default='refactor-report.json', help='报告输出文件')
    
    args = parser.parse_args()
    
    # 执行重构
    refactor = ControllerRefactor(args.service)
    report = refactor.run_refactor(dry_run=args.dry_run)
    
    # 保存报告
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\\n重构报告已保存到: {args.output}")
    print(f"总计需要重构 {report['summary']['total_controllers']} 个Controller")
    print(f"预计耗时: {report['summary']['estimated_refactor_time']}")

if __name__ == "__main__":
    main()
