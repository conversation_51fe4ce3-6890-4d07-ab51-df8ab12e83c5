#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构验证脚本

功能：
1. 验证重构后的代码质量
2. 检查架构合规性
3. 生成质量报告

使用方法：
python validate_refactor.py --service inventory-service
python validate_refactor.py --all-services

作者：visthink
"""

import os
import re
import argparse
import json
from pathlib import Path
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class ValidationRule:
    """验证规则"""
    name: str
    description: str
    pattern: str
    severity: str  # ERROR, WARNING, INFO
    category: str  # ARCHITECTURE, PERFORMANCE, SECURITY, STYLE

class RefactorValidator:
    """重构验证器"""
    
    def __init__(self):
        self.validation_rules = self._init_validation_rules()
        self.validation_report = {
            "summary": {},
            "services": [],
            "issues": [],
            "recommendations": []
        }
    
    def _init_validation_rules(self) -> List[ValidationRule]:
        """初始化验证规则"""
        return [
            # 架构规则
            ValidationRule(
                name="controller_extends_simple_controller",
                description="Controller应该继承SimpleController",
                pattern=r"extends SimpleController",
                severity="ERROR",
                category="ARCHITECTURE"
            ),
            ValidationRule(
                name="no_business_logic_in_controller",
                description="Controller不应包含业务逻辑",
                pattern=r"(if\s*\([^)]*\.equals|for\s*\([^)]*:|while\s*\()",
                severity="WARNING",
                category="ARCHITECTURE"
            ),
            ValidationRule(
                name="service_injection",
                description="Service应该通过@Inject注入",
                pattern=r"@Inject\s+\w+Service\s+\w+;",
                severity="ERROR",
                category="ARCHITECTURE"
            ),
            ValidationRule(
                name="tenant_context_usage",
                description="应该使用getCurrentTenantId()获取租户ID",
                pattern=r"getCurrentTenantId\(\)",
                severity="ERROR",
                category="ARCHITECTURE"
            ),
            
            # 性能规则
            ValidationRule(
                name="reactive_programming",
                description="应该使用Uni<T>返回类型",
                pattern=r"Uni<[^>]+>",
                severity="ERROR",
                category="PERFORMANCE"
            ),
            ValidationRule(
                name="proper_error_handling",
                description="应该使用onFailure().recoverWithItem()处理异常",
                pattern=r"onFailure\(\)\.recoverWithItem",
                severity="WARNING",
                category="PERFORMANCE"
            ),
            
            # 安全规则
            ValidationRule(
                name="input_validation",
                description="应该使用@Valid验证输入参数",
                pattern=r"@Valid\s+\w+",
                severity="WARNING",
                category="SECURITY"
            ),
            ValidationRule(
                name="path_param_validation",
                description="路径参数应该进行验证",
                pattern=r"@PathParam\([^)]+\)\s+Long\s+\w+",
                severity="INFO",
                category="SECURITY"
            ),
            
            # 代码风格规则
            ValidationRule(
                name="proper_logging",
                description="应该使用logOperation记录操作日志",
                pattern=r"logOperation\(",
                severity="INFO",
                category="STYLE"
            ),
            ValidationRule(
                name="api_documentation",
                description="应该使用@Operation注解API文档",
                pattern=r"@Operation\(",
                severity="INFO",
                category="STYLE"
            ),
            ValidationRule(
                name="chinese_comments",
                description="应该包含中文注释",
                pattern=r"[\u4e00-\u9fff]",
                severity="INFO",
                category="STYLE"
            )
        ]
    
    def validate_service(self, service_path: str) -> Dict:
        """验证单个服务"""
        service_path = Path(service_path)
        service_name = service_path.name
        
        print(f"验证服务: {service_name}")
        
        service_report = {
            "service": service_name,
            "path": str(service_path),
            "controllers": [],
            "issues": [],
            "metrics": {
                "total_files": 0,
                "refactored_files": 0,
                "issues_count": 0,
                "score": 0
            }
        }
        
        # 查找所有Controller文件
        resource_path = service_path / "src/main/java"
        if not resource_path.exists():
            service_report["issues"].append({
                "type": "ERROR",
                "message": f"资源路径不存在: {resource_path}"
            })
            return service_report
        
        controller_files = list(resource_path.rglob("*Resource*.java"))
        service_report["metrics"]["total_files"] = len(controller_files)
        
        for controller_file in controller_files:
            controller_report = self._validate_controller(controller_file)
            service_report["controllers"].append(controller_report)
            
            if controller_report["is_refactored"]:
                service_report["metrics"]["refactored_files"] += 1
            
            service_report["metrics"]["issues_count"] += len(controller_report["issues"])
        
        # 计算质量分数
        service_report["metrics"]["score"] = self._calculate_score(service_report)
        
        return service_report
    
    def _validate_controller(self, controller_file: Path) -> Dict:
        """验证单个Controller文件"""
        try:
            content = controller_file.read_text(encoding='utf-8')
        except Exception as e:
            return {
                "file": str(controller_file),
                "is_refactored": False,
                "issues": [{"type": "ERROR", "message": f"读取文件失败: {e}"}],
                "metrics": {}
            }
        
        controller_report = {
            "file": str(controller_file),
            "is_refactored": self._is_refactored(content),
            "issues": [],
            "metrics": {
                "lines_of_code": len(content.splitlines()),
                "methods_count": len(re.findall(r'public\s+Uni<[^>]+>\s+\w+\(', content)),
                "complexity_score": self._calculate_complexity(content)
            }
        }
        
        # 应用验证规则
        for rule in self.validation_rules:
            issues = self._apply_rule(content, rule, controller_file)
            controller_report["issues"].extend(issues)
        
        return controller_report
    
    def _is_refactored(self, content: str) -> bool:
        """检查是否已重构"""
        return "extends SimpleController" in content
    
    def _apply_rule(self, content: str, rule: ValidationRule, file_path: Path) -> List[Dict]:
        """应用验证规则"""
        issues = []
        
        if rule.name == "no_business_logic_in_controller":
            # 特殊处理：检查是否包含业务逻辑
            matches = re.findall(rule.pattern, content)
            if matches and "extends SimpleController" in content:
                issues.append({
                    "type": rule.severity,
                    "rule": rule.name,
                    "message": f"{rule.description}: 发现 {len(matches)} 处可能的业务逻辑",
                    "file": str(file_path),
                    "category": rule.category
                })
        else:
            # 常规规则检查
            matches = re.findall(rule.pattern, content)
            expected_count = self._get_expected_count(rule.name, content)
            
            if rule.severity == "ERROR" and len(matches) < expected_count:
                issues.append({
                    "type": rule.severity,
                    "rule": rule.name,
                    "message": f"{rule.description}: 期望 {expected_count} 处，实际 {len(matches)} 处",
                    "file": str(file_path),
                    "category": rule.category
                })
            elif rule.severity in ["WARNING", "INFO"] and len(matches) == 0:
                issues.append({
                    "type": rule.severity,
                    "rule": rule.name,
                    "message": rule.description,
                    "file": str(file_path),
                    "category": rule.category
                })
        
        return issues
    
    def _get_expected_count(self, rule_name: str, content: str) -> int:
        """获取规则期望的匹配次数"""
        if rule_name == "controller_extends_simple_controller":
            return 1 if "public class" in content else 0
        elif rule_name == "service_injection":
            return content.count("Service ")
        elif rule_name == "tenant_context_usage":
            return content.count("@GET") + content.count("@POST") + content.count("@PUT") + content.count("@DELETE")
        elif rule_name == "reactive_programming":
            return content.count("public Uni")
        else:
            return 1
    
    def _calculate_complexity(self, content: str) -> int:
        """计算代码复杂度"""
        complexity = 1  # 基础复杂度
        
        # 条件语句
        complexity += len(re.findall(r'\bif\b', content))
        complexity += len(re.findall(r'\belse\b', content))
        complexity += len(re.findall(r'\bswitch\b', content))
        complexity += len(re.findall(r'\bcase\b', content))
        
        # 循环语句
        complexity += len(re.findall(r'\bfor\b', content))
        complexity += len(re.findall(r'\bwhile\b', content))
        complexity += len(re.findall(r'\bdo\b', content))
        
        # 异常处理
        complexity += len(re.findall(r'\btry\b', content))
        complexity += len(re.findall(r'\bcatch\b', content))
        
        return complexity
    
    def _calculate_score(self, service_report: Dict) -> int:
        """计算质量分数"""
        metrics = service_report["metrics"]
        
        if metrics["total_files"] == 0:
            return 0
        
        # 基础分数
        score = 100
        
        # 重构完成度
        refactor_ratio = metrics["refactored_files"] / metrics["total_files"]
        score = score * refactor_ratio
        
        # 问题扣分
        error_count = sum(1 for controller in service_report["controllers"] 
                         for issue in controller["issues"] 
                         if issue["type"] == "ERROR")
        warning_count = sum(1 for controller in service_report["controllers"] 
                           for issue in controller["issues"] 
                           if issue["type"] == "WARNING")
        
        score -= error_count * 10  # 错误扣10分
        score -= warning_count * 5  # 警告扣5分
        
        return max(0, int(score))
    
    def validate_all_services(self, base_path: str = "microservices") -> Dict:
        """验证所有服务"""
        base_path = Path(base_path)
        
        print(f"验证所有服务，基础路径: {base_path}")
        
        # 查找所有微服务目录
        service_dirs = [d for d in base_path.iterdir() 
                       if d.is_dir() and d.name.endswith('-service')]
        
        print(f"发现 {len(service_dirs)} 个微服务")
        
        for service_dir in service_dirs:
            service_report = self.validate_service(str(service_dir))
            self.validation_report["services"].append(service_report)
        
        # 生成总体报告
        self._generate_summary()
        
        return self.validation_report
    
    def _generate_summary(self):
        """生成总体摘要"""
        total_services = len(self.validation_report["services"])
        total_files = sum(s["metrics"]["total_files"] for s in self.validation_report["services"])
        refactored_files = sum(s["metrics"]["refactored_files"] for s in self.validation_report["services"])
        total_issues = sum(s["metrics"]["issues_count"] for s in self.validation_report["services"])
        avg_score = sum(s["metrics"]["score"] for s in self.validation_report["services"]) / total_services if total_services > 0 else 0
        
        self.validation_report["summary"] = {
            "total_services": total_services,
            "total_files": total_files,
            "refactored_files": refactored_files,
            "refactor_progress": f"{refactored_files}/{total_files} ({refactored_files/total_files*100:.1f}%)" if total_files > 0 else "0/0 (0%)",
            "total_issues": total_issues,
            "average_score": f"{avg_score:.1f}",
            "status": "完成" if refactored_files == total_files else "进行中"
        }
        
        # 生成建议
        self._generate_recommendations()
    
    def _generate_recommendations(self):
        """生成改进建议"""
        recommendations = []
        
        # 基于问题统计生成建议
        issue_categories = {}
        for service in self.validation_report["services"]:
            for controller in service["controllers"]:
                for issue in controller["issues"]:
                    category = issue["category"]
                    issue_categories[category] = issue_categories.get(category, 0) + 1
        
        if issue_categories.get("ARCHITECTURE", 0) > 0:
            recommendations.append({
                "priority": "HIGH",
                "category": "ARCHITECTURE",
                "title": "架构合规性改进",
                "description": "发现架构相关问题，建议优先修复Controller继承关系和业务逻辑分离"
            })
        
        if issue_categories.get("PERFORMANCE", 0) > 0:
            recommendations.append({
                "priority": "MEDIUM",
                "category": "PERFORMANCE",
                "title": "性能优化",
                "description": "建议完善响应式编程模式和异常处理机制"
            })
        
        if issue_categories.get("SECURITY", 0) > 0:
            recommendations.append({
                "priority": "MEDIUM",
                "category": "SECURITY",
                "title": "安全性增强",
                "description": "建议加强输入验证和参数校验"
            })
        
        self.validation_report["recommendations"] = recommendations

def main():
    parser = argparse.ArgumentParser(description='重构验证工具')
    parser.add_argument('--service', help='验证指定服务')
    parser.add_argument('--all-services', action='store_true', help='验证所有服务')
    parser.add_argument('--output', help='报告输出文件')
    
    args = parser.parse_args()
    
    validator = RefactorValidator()
    
    if args.service:
        report = validator.validate_service(args.service)
    elif args.all_services:
        report = validator.validate_all_services()
    else:
        print("请指定 --service 或 --all-services 参数")
        return
    
    # 输出报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"验证报告已保存到: {args.output}")
    else:
        print("\n=== 验证报告 ===")
        if "summary" in report:
            print(f"总体进度: {report['summary']['refactor_progress']}")
            print(f"平均分数: {report['summary']['average_score']}")
            print(f"总问题数: {report['summary']['total_issues']}")
        print(json.dumps(report, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
