#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Controller重构自动化脚本

功能：
1. 扫描现有的BaseResource子类
2. 生成重构后的Controller代码
3. 创建重构报告

使用方法：
python refactor_controller.py --service inventory-service --dry-run
python refactor_controller.py --service inventory-service --apply

作者：visthink
"""

import os
import re
import argparse
import json
from pathlib import Path
from typing import List, Dict, Optional

class ControllerRefactor:
    """Controller重构工具类"""
    
    def __init__(self, service_path: str):
        self.service_path = Path(service_path)
        self.resource_path = self.service_path / "src/main/java"
        self.refactor_report = {
            "service": service_path,
            "controllers": [],
            "issues": [],
            "summary": {}
        }
    
    def scan_controllers(self) -> List[Dict]:
        """扫描现有的Controller类"""
        controllers = []
        
        # 查找所有Java文件
        for java_file in self.resource_path.rglob("*Resource.java"):
            if self._is_base_resource_controller(java_file):
                controller_info = self._analyze_controller(java_file)
                controllers.append(controller_info)
        
        return controllers
    
    def _is_base_resource_controller(self, java_file: Path) -> bool:
        """检查是否是BaseResource的子类"""
        try:
            content = java_file.read_text(encoding='utf-8')
            return "extends BaseResource" in content
        except Exception as e:
            print(f"读取文件失败: {java_file}, 错误: {e}")
            return False
    
    def _analyze_controller(self, java_file: Path) -> Dict:
        """分析Controller类的结构"""
        content = java_file.read_text(encoding='utf-8')
        
        # 提取类名
        class_match = re.search(r'public class (\w+)', content)
        class_name = class_match.group(1) if class_match else "Unknown"
        
        # 提取泛型参数
        generic_match = re.search(r'extends BaseResource<([^>]+)>', content)
        generic_params = generic_match.group(1) if generic_match else ""
        
        # 提取Service注入
        service_match = re.search(r'@Inject\s+(\w+Service)\s+(\w+);', content)
        service_type = service_match.group(1) if service_match else ""
        service_field = service_match.group(2) if service_match else ""
        
        # 提取Path注解
        path_match = re.search(r'@Path\("([^"]+)"\)', content)
        api_path = path_match.group(1) if path_match else ""
        
        # 提取自定义方法
        custom_methods = self._extract_custom_methods(content)
        
        return {
            "file_path": str(java_file),
            "class_name": class_name,
            "generic_params": generic_params,
            "service_type": service_type,
            "service_field": service_field,
            "api_path": api_path,
            "custom_methods": custom_methods,
            "needs_refactor": True
        }
    
    def _extract_custom_methods(self, content: str) -> List[Dict]:
        """提取自定义方法"""
        methods = []
        
        # 查找所有公共方法
        method_pattern = r'@(GET|POST|PUT|DELETE|PATCH)\s*(?:@Path\("([^"]+)"\))?\s*[^{]*public\s+Uni<[^>]+>\s+(\w+)\([^)]*\)\s*{'
        
        for match in re.finditer(method_pattern, content, re.MULTILINE | re.DOTALL):
            http_method = match.group(1)
            path = match.group(2) or ""
            method_name = match.group(3)
            
            # 跳过标准CRUD方法
            if method_name not in ['create', 'findById', 'findAll', 'update', 'delete', 'count']:
                methods.append({
                    "name": method_name,
                    "http_method": http_method,
                    "path": path
                })
        
        return methods
    
    def generate_refactored_controller(self, controller_info: Dict) -> str:
        """生成重构后的Controller代码"""
        template = self._get_controller_template()
        
        # 替换模板变量
        code = template.format(
            package=self._extract_package(controller_info["file_path"]),
            class_name=controller_info["class_name"] + "Refactored",
            original_class_name=controller_info["class_name"],
            service_type=controller_info["service_type"],
            service_field=controller_info["service_field"],
            api_path=controller_info["api_path"],
            entity_name=self._extract_entity_name(controller_info["generic_params"]),
            custom_methods=self._generate_custom_methods(controller_info["custom_methods"])
        )
        
        return code
    
    def _get_controller_template(self) -> str:
        """获取Controller模板"""
        return '''package {package};

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

/**
 * {original_class_name} - 重构版本
 * 
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给Service处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 */
@Path("{api_path}")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "{entity_name}管理", description = "{entity_name}管理相关接口")
public class {class_name} extends SimpleController {{

    @Inject
    {service_type} {service_field};

    /**
     * 创建{entity_name}
     */
    @POST
    @Operation(summary = "创建{entity_name}", description = "创建新的{entity_name}")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<Object>> create(@Valid Object request) {{
        logOperation("创建{entity_name}", "开始处理创建请求");
        
        return getCurrentTenantId()
                .flatMap(tenantId -> {{
                    logOperation("创建{entity_name}", tenantId, "租户验证通过");
                    return {service_field}.create(tenantId, request);
                }})
                .map(entity -> {{
                    logOperation("创建{entity_name}", "创建成功");
                    return success("创建{entity_name}成功", entity);
                }})
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "创建{entity_name}"));
    }}

    /**
     * 根据ID查询{entity_name}
     */
    @GET
    @Path("/{{id}}")
    @Operation(summary = "查询{entity_name}", description = "根据ID查询{entity_name}详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<Object>> findById(@PathParam("id") Long id) {{
        logOperation("查询{entity_name}", "ID: " + id);
        
        return getCurrentTenantId()
                .flatMap(tenantId -> {service_field}.findById(tenantId, id))
                .map(entity -> {{
                    if (entity != null) {{
                        logOperation("查询{entity_name}", "查询成功");
                        return success(entity);
                    }} else {{
                        return ApiResponse.notFound("{entity_name}不存在");
                    }}
                }})
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "查询{entity_name}"));
    }}

{custom_methods}
}}'''
    
    def _generate_custom_methods(self, methods: List[Dict]) -> str:
        """生成自定义方法代码"""
        if not methods:
            return ""
        
        method_codes = []
        for method in methods:
            method_code = f'''
    /**
     * {method["name"]}
     */
    @{method["http_method"]}
    @Path("{method["path"]}")
    @Operation(summary = "{method["name"]}", description = "{method["name"]}操作")
    @APIResponse(responseCode = "200", description = "操作成功")
    public Uni<ApiResponse<Object>> {method["name"]}() {{
        logOperation("{method["name"]}", "开始处理请求");
        
        return getCurrentTenantId()
                .flatMap(tenantId -> {service_field}.{method["name"]}(tenantId))
                .map(result -> {{
                    logOperation("{method["name"]}", "操作成功");
                    return success(result);
                }})
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "{method["name"]}"));
    }}'''
            method_codes.append(method_code)
        
        return "\n".join(method_codes)
    
    def _extract_package(self, file_path: str) -> str:
        """提取包名"""
        try:
            content = Path(file_path).read_text(encoding='utf-8')
            match = re.search(r'package ([^;]+);', content)
            return match.group(1) if match else "com.visthink.unknown"
        except:
            return "com.visthink.unknown"
    
    def _extract_entity_name(self, generic_params: str) -> str:
        """提取实体名称"""
        if not generic_params:
            return "实体"
        
        # 提取第一个泛型参数作为实体类型
        parts = generic_params.split(',')
        entity_type = parts[0].strip()
        
        # 简单的实体名称映射
        name_mapping = {
            "Inventory": "库存",
            "User": "用户",
            "Order": "订单",
            "Product": "商品",
            "Menu": "菜单",
            "Permission": "权限",
            "Role": "角色",
            "Dictionary": "字典"
        }
        
        return name_mapping.get(entity_type, entity_type)
    
    def run_refactor(self, dry_run: bool = True) -> Dict:
        """执行重构"""
        print(f"开始扫描服务: {self.service_path}")
        
        controllers = self.scan_controllers()
        self.refactor_report["controllers"] = controllers
        
        print(f"发现 {len(controllers)} 个需要重构的Controller")
        
        for controller in controllers:
            print(f"  - {controller['class_name']}")
            
            if not dry_run:
                # 生成重构后的代码
                refactored_code = self.generate_refactored_controller(controller)
                
                # 保存重构后的文件
                original_file = Path(controller["file_path"])
                refactored_file = original_file.parent / f"{controller['class_name']}Refactored.java"
                
                refactored_file.write_text(refactored_code, encoding='utf-8')
                print(f"    生成重构文件: {refactored_file}")
        
        # 生成报告
        self.refactor_report["summary"] = {
            "total_controllers": len(controllers),
            "refactored": 0 if dry_run else len(controllers),
            "status": "dry_run" if dry_run else "completed"
        }
        
        return self.refactor_report

def main():
    parser = argparse.ArgumentParser(description='Controller重构工具')
    parser.add_argument('--service', required=True, help='微服务路径')
    parser.add_argument('--dry-run', action='store_true', help='只分析不执行')
    parser.add_argument('--output', help='报告输出文件')
    
    args = parser.parse_args()
    
    # 执行重构
    refactor = ControllerRefactor(args.service)
    report = refactor.run_refactor(dry_run=args.dry_run)
    
    # 输出报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"报告已保存到: {args.output}")
    else:
        print("\n=== 重构报告 ===")
        print(json.dumps(report, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
