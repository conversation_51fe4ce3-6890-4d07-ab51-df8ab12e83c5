# 🚀 Visthink ERP 微服务架构

基于Quarkus的云原生多租户ERP微服务架构，提供高性能、高可用、易扩展的企业级解决方案。

## 📋 **架构概览**

### **技术栈**
- **框架**: Quarkus 3.22.2 + Hibernate Reactive
- **数据库**: PostgreSQL 15 (每个微服务独立数据库)
- **缓存**: Redis 7
- **消息队列**: Apache Kafka (可选)
- **服务注册**: Consul
- **API网关**: Nginx
- **监控**: Prometheus + Grafana
- **链路追踪**: <PERSON><PERSON>ger
- **对象存储**: MinIO
- **容器化**: Docker + Docker Compose

### **微服务列表**

| 服务名称 | 端口 | 描述 | 状态 |
|---------|------|------|------|
| **gateway** | 8080 | API网关 | 🚧 规划中 |
| **member-center** | 8081 | 用户与租户管理 | ✅ 已完成 |
| **product-service** | 8082 | 商品管理 | 🚧 开发中 |
| **inventory-service** | 8083 | 库存管理 | 🚧 开发中 |
| **order-service** | 8084 | 订单管理 | 🚧 开发中 |
| **platform-integration** | 8085 | 平台集成 | 🚧 规划中 |
| **file-service** | 8086 | 文件管理 | 🚧 规划中 |
| **api-log-service** | 8087 | API日志 | 🚧 规划中 |
| **workflow-service** | 8088 | 工作流 | 🚧 规划中 |
| **config-center** | 8888 | 配置中心 | 🚧 规划中 |

### **基础设施服务**

| 服务名称 | 端口 | 描述 |
|---------|------|------|
| **PostgreSQL** | 35432 | 主数据库 |
| **Redis** | 6379 | 缓存服务 |
| **Consul** | 8500 | 服务注册中心 |
| **Prometheus** | 9090 | 监控数据收集 |
| **Grafana** | 3000 | 监控可视化 |
| **Jaeger** | 16686 | 链路追踪 |
| **MinIO** | 9000/9001 | 对象存储 |

## 🚀 **快速开始**

### **前置要求**
- Docker 20.10+
- Docker Compose 2.0+
- Maven 3.8+
- JDK 17+

### **启动服务**

1. **克隆项目**
```bash
git clone <repository-url>
cd microservices
```

2. **一键启动所有服务**
```bash
chmod +x start-services.sh
./start-services.sh
```

3. **分步启动（推荐）**
```bash
# 启动基础设施
./start-services.sh infrastructure

# 构建微服务
./start-services.sh build

# 启动核心服务
./start-services.sh core

# 启动支撑服务
./start-services.sh support

# 启动网关
./start-services.sh gateway
```

### **验证服务**
```bash
# 检查服务状态
./start-services.sh status

# 查看特定服务日志
./start-services.sh logs member-center

# 健康检查
curl http://localhost/health
```

## 📊 **服务访问地址**

### **业务服务**
- **API网关**: http://localhost
- **用户服务**: http://localhost:8081
- **API文档**: http://localhost:8081/q/swagger-ui

### **管理界面**
- **Consul**: http://localhost:8500
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)
- **Jaeger**: http://localhost:16686
- **MinIO**: http://localhost:9001 (minioadmin/minioadmin123)

## 🏗️ **项目结构**

```
microservices/
├── shared-common/              # 共享模块
│   ├── src/main/java/com/visthink/shared/
│   │   ├── entity/            # 基础实体类
│   │   ├── dto/               # 通用DTO
│   │   ├── service/           # 基础服务接口
│   │   ├── repository/        # 基础Repository
│   │   ├── resource/          # 基础Resource
│   │   ├── context/           # 租户上下文
│   │   └── exception/         # 异常处理
│   └── pom.xml
├── member-center/              # 用户与租户服务
│   ├── src/main/java/com/visthink/member/
│   │   ├── entity/            # 用户、租户实体
│   │   ├── repository/        # 数据访问层
│   │   ├── service/           # 业务逻辑层
│   │   ├── resource/          # REST API层
│   │   └── dto/               # 数据传输对象
│   ├── src/main/resources/
│   │   ├── application.yml    # 配置文件
│   │   └── db/migration/      # 数据库迁移脚本
│   ├── Dockerfile
│   └── pom.xml
├── product-service/            # 商品服务 (待开发)
├── inventory-service/          # 库存服务 (待开发)
├── order-service/              # 订单服务 (待开发)
├── platform-integration/      # 平台集成服务 (待开发)
├── file-service/               # 文件服务 (待开发)
├── api-log-service/            # API日志服务 (待开发)
├── workflow-service/           # 工作流服务 (待开发)
├── config-center/              # 配置中心 (待开发)
├── gateway/                    # API网关 (待开发)
├── monitoring/                 # 监控配置
│   ├── prometheus.yml
│   └── grafana/
├── nginx/                      # 网关配置
│   ├── nginx.conf
│   └── conf.d/
├── init-scripts/               # 数据库初始化脚本
├── docker-compose.yml          # Docker编排文件
├── start-services.sh           # 启动脚本
├── pom.xml                     # 父POM文件
└── README.md
```

## 🔧 **开发指南**

### **创建新微服务**

1. **复制模板**
```bash
cp -r member-center new-service
cd new-service
```

2. **修改配置**
- 更新 `pom.xml` 中的 `artifactId`
- 修改 `application.yml` 中的端口和数据库配置
- 更新包名和类名

3. **添加到父POM**
```xml
<modules>
    <module>new-service</module>
</modules>
```

4. **更新Docker Compose**
```yaml
new-service:
  build: ./new-service
  ports:
    - "8089:8089"
```

### **数据库设计原则**

1. **每个微服务独立数据库**
2. **使用Flyway进行版本管理**
3. **保持多租户数据隔离**
4. **遵循命名规范**

### **API设计规范**

1. **RESTful风格**
2. **统一响应格式**
3. **版本控制 (/api/v1/)**
4. **OpenAPI文档**

### **监控和日志**

1. **集成Micrometer指标**
2. **结构化日志输出**
3. **分布式链路追踪**
4. **健康检查端点**

## 🔒 **安全配置**

### **JWT认证**
- 统一的JWT令牌验证
- 租户信息自动注入
- 权限控制集成

### **API限流**
- Nginx层面限流
- 应用层面限流
- 租户级别限流

### **数据安全**
- 多租户数据隔离
- 敏感数据加密
- 审计日志记录

## 📈 **性能优化**

### **缓存策略**
- Redis分布式缓存
- 应用级缓存
- 数据库查询优化

### **连接池配置**
- 数据库连接池
- Redis连接池
- HTTP客户端连接池

### **响应式编程**
- 非阻塞I/O
- 背压处理
- 资源高效利用

## 🚀 **部署指南**

### **本地开发环境**
```bash
./start-services.sh
```

### **测试环境**
```bash
docker-compose -f docker-compose.test.yml up -d
```

### **生产环境**
```bash
# 使用Kubernetes部署
kubectl apply -f k8s/
```

## 📝 **API文档**

每个微服务都提供OpenAPI文档：
- **用户服务**: http://localhost:8081/q/swagger-ui
- **商品服务**: http://localhost:8082/q/swagger-ui
- **库存服务**: http://localhost:8083/q/swagger-ui

## 🐛 **故障排查**

### **常见问题**

1. **服务启动失败**
```bash
# 查看服务日志
docker-compose logs service-name

# 检查端口占用
netstat -tulpn | grep :8081
```

2. **数据库连接失败**
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 查看数据库日志
docker-compose logs postgres
```

3. **服务间通信失败**
```bash
# 检查网络连接
docker-compose exec member-center ping product-service

# 查看Consul服务注册
curl http://localhost:8500/v1/catalog/services
```

## 🤝 **贡献指南**

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 📄 **许可证**

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 **联系我们**

- **邮箱**: <EMAIL>
- **官网**: https://www.visthink.com
- **文档**: https://docs.visthink.com

---

**注意**: 这是一个正在开发中的项目，部分功能可能还未完全实现。请关注项目更新获取最新进展。
