package com.visthink.order.service;

import com.visthink.order.entity.Order;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.PageRequest;
import io.smallrye.mutiny.Uni;

import java.util.List;

/**
 * 订单服务接口 - 最小化版本
 * 
 * 提供订单管理的核心功能，简化版本用于快速编译通过
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface OrderServiceMinimal {

    /**
     * 根据ID获取订单
     * 
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 订单信息
     */
    Uni<Order> findById(Long tenantId, Long orderId);

    /**
     * 获取所有订单
     * 
     * @param tenantId 租户ID
     * @return 订单列表
     */
    Uni<List<Order>> findAll(Long tenantId);

    /**
     * 分页查询订单
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    Uni<PageResult<Order>> findPage(Long tenantId, PageRequest pageRequest);

    /**
     * 保存订单
     * 
     * @param tenantId 租户ID
     * @param order 订单信息
     * @return 保存后的订单
     */
    Uni<Order> save(Long tenantId, Order order);

    /**
     * 删除订单
     * 
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 删除结果
     */
    Uni<Boolean> deleteById(Long tenantId, Long orderId);

    /**
     * 检查订单是否存在
     * 
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 是否存在
     */
    Uni<Boolean> exists(Long tenantId, Long orderId);
}
