package com.visthink.order.dto;

import java.util.List;

/**
 * 库存确认请求
 * 
 * 用于确认出库，将预占库存转为实际扣减
 * 
 * <AUTHOR>
 */
public class InventoryConfirmRequest {
    
    private String businessType;
    private Long businessId;
    private String businessNo;
    private String reason;
    private List<ConfirmItem> items;
    
    public String getBusinessType() {
        return businessType;
    }
    
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    
    public Long getBusinessId() {
        return businessId;
    }
    
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }
    
    public String getBusinessNo() {
        return businessNo;
    }
    
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public List<ConfirmItem> getItems() {
        return items;
    }
    
    public void setItems(List<ConfirmItem> items) {
        this.items = items;
    }
    
    /**
     * 确认项
     */
    public static class ConfirmItem {
        private Long skuId;
        private Integer quantity;
        private Long warehouseId; // 可选，指定仓库
        
        public Long getSkuId() {
            return skuId;
        }
        
        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }
        
        public Integer getQuantity() {
            return quantity;
        }
        
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        
        public Long getWarehouseId() {
            return warehouseId;
        }
        
        public void setWarehouseId(Long warehouseId) {
            this.warehouseId = warehouseId;
        }
    }
}
