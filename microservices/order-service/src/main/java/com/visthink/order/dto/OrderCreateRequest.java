package com.visthink.order.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单创建请求DTO
 *
 * 用于接收创建订单的请求参数，包含订单基本信息、收货地址、订单明细等
 * 提供完整的参数验证和业务规则校验
 *
 * <AUTHOR>
 */
@Data
public class OrderCreateRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 用户名称
     */
    @NotBlank(message = "用户名称不能为空")
    @Size(max = 100, message = "用户名称长度不能超过100个字符")
    private String userName;

    /**
     * 客户ID（兼容字段）
     */
    public Long getCustomerId() {
        return this.userId;
    }

    public void setCustomerId(Long customerId) {
        this.userId = customerId;
    }

    /**
     * 客户名称（兼容字段）
     */
    public String getCustomerName() {
        return this.userName;
    }

    public void setCustomerName(String customerName) {
        this.userName = customerName;
    }

    /**
     * 订单明细列表（兼容字段）
     */
    public List<OrderItemCreateRequest> getItems() {
        return this.orderItems;
    }

    public void setItems(List<OrderItemCreateRequest> items) {
        this.orderItems = items;
    }

    /**
     * 订单类型：1-普通订单，2-预售订单，3-团购订单，4-秒杀订单
     */
    @NotNull(message = "订单类型不能为空")
    @Min(value = 1, message = "订单类型值无效")
    @Max(value = 4, message = "订单类型值无效")
    private Integer orderType = 1;

    /**
     * 订单来源：1-PC端，2-移动端，3-小程序，4-APP，5-API
     */
    @NotNull(message = "订单来源不能为空")
    @Min(value = 1, message = "订单来源值无效")
    @Max(value = 5, message = "订单来源值无效")
    private Integer orderSource = 1;

    /**
     * 订单渠道：1-直销，2-分销，3-平台（淘宝、京东等）
     */
    private Integer orderChannel = 1;

    /**
     * 外部订单号（第三方平台订单号）
     */
    @Size(max = 100, message = "外部订单号长度不能超过100个字符")
    private String externalOrderNumber;

    /**
     * 收货人姓名
     */
    @NotBlank(message = "收货人姓名不能为空")
    @Size(max = 50, message = "收货人姓名长度不能超过50个字符")
    private String receiverName;

    /**
     * 收货人电话
     */
    @NotBlank(message = "收货人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "收货人电话格式不正确")
    private String receiverPhone;

    /**
     * 收货人地址
     */
    @NotBlank(message = "收货人地址不能为空")
    @Size(max = 500, message = "收货人地址长度不能超过500个字符")
    private String receiverAddress;

    /**
     * 省份
     */
    @NotBlank(message = "省份不能为空")
    @Size(max = 50, message = "省份长度不能超过50个字符")
    private String receiverProvince;

    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空")
    @Size(max = 50, message = "城市长度不能超过50个字符")
    private String receiverCity;

    /**
     * 区县
     */
    @NotBlank(message = "区县不能为空")
    @Size(max = 50, message = "区县长度不能超过50个字符")
    private String receiverDistrict;

    /**
     * 邮政编码
     */
    @Pattern(regexp = "^\\d{6}$", message = "邮政编码格式不正确")
    private String receiverZipCode;

    /**
     * 买家留言
     */
    @Size(max = 500, message = "买家留言长度不能超过500个字符")
    private String buyerMessage;

    /**
     * 运费金额
     */
    @DecimalMin(value = "0.00", message = "运费金额不能为负数")
    @Digits(integer = 8, fraction = 2, message = "运费金额格式不正确")
    private BigDecimal shippingAmount = BigDecimal.ZERO;

    /**
     * 优惠金额
     */
    @DecimalMin(value = "0.00", message = "优惠金额不能为负数")
    @Digits(integer = 8, fraction = 2, message = "优惠金额格式不正确")
    private BigDecimal discountAmount = BigDecimal.ZERO;

    /**
     * 税费金额
     */
    @DecimalMin(value = "0.00", message = "税费金额不能为负数")
    @Digits(integer = 8, fraction = 2, message = "税费金额格式不正确")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券编码
     */
    @Size(max = 50, message = "优惠券编码长度不能超过50个字符")
    private String couponCode;

    /**
     * 促销活动ID
     */
    private Long promotionId;

    /**
     * 是否需要发票
     */
    private Boolean needInvoice = false;

    /**
     * 发票类型：1-普通发票，2-增值税发票
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    @Size(max = 200, message = "发票抬头长度不能超过200个字符")
    private String invoiceTitle;

    /**
     * 发票内容
     */
    @Size(max = 200, message = "发票内容长度不能超过200个字符")
    private String invoiceContent;

    /**
     * 订单明细列表
     */
    @NotEmpty(message = "订单明细不能为空")
    @Valid
    private List<OrderItemCreateRequest> orderItems;

    /**
     * 订单明细创建请求DTO
     */
    @Data
    public static class OrderItemCreateRequest {

        /**
         * 商品ID
         */
        @NotNull(message = "商品ID不能为空")
        private Long productId;

        /**
         * 商品编码
         */
        @Size(max = 100, message = "商品编码长度不能超过100个字符")
        private String productCode;

        /**
         * 商品名称
         */
        @NotBlank(message = "商品名称不能为空")
        @Size(max = 500, message = "商品名称长度不能超过500个字符")
        private String productName;

        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * SKU编码
         */
        @Size(max = 100, message = "SKU编码长度不能超过100个字符")
        private String skuCode;

        /**
         * SKU名称
         */
        @Size(max = 500, message = "SKU名称长度不能超过500个字符")
        private String skuName;

        /**
         * 商品规格信息（JSON格式）
         */
        private String specInfo;

        /**
         * 商品主图URL
         */
        @Size(max = 500, message = "商品主图URL长度不能超过500个字符")
        private String productImage;

        /**
         * 商品单价
         */
        @NotNull(message = "商品单价不能为空")
        @DecimalMin(value = "0.01", message = "商品单价必须大于0")
        @Digits(integer = 8, fraction = 2, message = "商品单价格式不正确")
        private BigDecimal unitPrice;

        /**
         * 商品原价（市场价）
         */
        @DecimalMin(value = "0.00", message = "商品原价不能为负数")
        @Digits(integer = 8, fraction = 2, message = "商品原价格式不正确")
        private BigDecimal originalPrice;

        /**
         * 商品数量
         */
        @NotNull(message = "商品数量不能为空")
        @Min(value = 1, message = "商品数量必须大于0")
        private Integer quantity;

        /**
         * 优惠金额
         */
        @DecimalMin(value = "0.00", message = "优惠金额不能为负数")
        @Digits(integer = 8, fraction = 2, message = "优惠金额格式不正确")
        private BigDecimal discountAmount = BigDecimal.ZERO;

        /**
         * 商品重量（克）
         */
        @Min(value = 0, message = "商品重量不能为负数")
        private Integer weight;

        /**
         * 商品体积（立方厘米）
         */
        @Min(value = 0, message = "商品体积不能为负数")
        private Integer volume;

        /**
         * 分类ID
         */
        private Long categoryId;

        /**
         * 分类名称
         */
        @Size(max = 200, message = "分类名称长度不能超过200个字符")
        private String categoryName;

        /**
         * 品牌ID
         */
        private Long brandId;

        /**
         * 品牌名称
         */
        @Size(max = 200, message = "品牌名称长度不能超过200个字符")
        private String brandName;

        /**
         * 供应商ID
         */
        private Long supplierId;

        /**
         * 供应商名称
         */
        @Size(max = 200, message = "供应商名称长度不能超过200个字符")
        private String supplierName;

        /**
         * 仓库ID
         */
        private Long warehouseId;

        /**
         * 仓库名称
         */
        @Size(max = 200, message = "仓库名称长度不能超过200个字符")
        private String warehouseName;

        /**
         * 是否是赠品
         */
        private Boolean isGift = false;

        /**
         * 促销活动ID
         */
        private Long promotionId;

        /**
         * 促销活动名称
         */
        @Size(max = 200, message = "促销活动名称长度不能超过200个字符")
        private String promotionName;

        /**
         * 促销类型：1-满减，2-折扣，3-赠品，4-优惠券
         */
        private Integer promotionType;

        /**
         * 商品属性（JSON格式）
         */
        private String productAttributes;
    }

    /**
     * 验证订单数据
     */
    public void validate() {
        // 验证发票信息
        if (Boolean.TRUE.equals(needInvoice)) {
            if (invoiceType == null) {
                throw new IllegalArgumentException("需要发票时，发票类型不能为空");
            }
            if (invoiceTitle == null || invoiceTitle.trim().isEmpty()) {
                throw new IllegalArgumentException("需要发票时，发票抬头不能为空");
            }
        }

        // 验证订单明细
        if (orderItems == null || orderItems.isEmpty()) {
            throw new IllegalArgumentException("订单明细不能为空");
        }

        // 验证每个订单明细
        for (OrderItemCreateRequest item : orderItems) {
            if (item.getProductId() == null) {
                throw new IllegalArgumentException("商品ID不能为空");
            }
            if (item.getUnitPrice() == null || item.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new IllegalArgumentException("商品单价必须大于0");
            }
            if (item.getQuantity() == null || item.getQuantity() <= 0) {
                throw new IllegalArgumentException("商品数量必须大于0");
            }
        }
    }

    /**
     * 计算商品总金额
     */
    public BigDecimal calculateGoodsAmount() {
        if (orderItems == null || orderItems.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return orderItems.stream()
                .map(item -> item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity()))
                        .subtract(item.getDiscountAmount() != null ? item.getDiscountAmount() : BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算订单总金额
     */
    public BigDecimal calculateTotalAmount() {
        BigDecimal goodsAmount = calculateGoodsAmount();
        return goodsAmount
                .add(shippingAmount != null ? shippingAmount : BigDecimal.ZERO)
                .add(taxAmount != null ? taxAmount : BigDecimal.ZERO)
                .subtract(discountAmount != null ? discountAmount : BigDecimal.ZERO);
    }

    /**
     * 计算订单总重量
     */
    public Integer calculateTotalWeight() {
        if (orderItems == null || orderItems.isEmpty()) {
            return 0;
        }

        return orderItems.stream()
                .mapToInt(item -> {
                    Integer weight = item.getWeight() != null ? item.getWeight() : 0;
                    Integer quantity = item.getQuantity() != null ? item.getQuantity() : 0;
                    return weight * quantity;
                })
                .sum();
    }

    /**
     * 计算订单总体积
     */
    public Integer calculateTotalVolume() {
        if (orderItems == null || orderItems.isEmpty()) {
            return 0;
        }

        return orderItems.stream()
                .mapToInt(item -> {
                    Integer volume = item.getVolume() != null ? item.getVolume() : 0;
                    Integer quantity = item.getQuantity() != null ? item.getQuantity() : 0;
                    return volume * quantity;
                })
                .sum();
    }
}
