package com.visthink.order.resource;

import com.visthink.common.base.SimpleController;
import com.visthink.order.dto.OrderCreateRequest;
import com.visthink.order.dto.OrderUpdateRequest;
import com.visthink.order.entity.Order;
import com.visthink.order.service.OrderService;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

/**
 * 订单资源接口 - 简化版本 (重构版本)
 *
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给OrderService处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/orders-simple")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "订单管理-简化版", description = "订单管理简化版相关接口")
public class OrderResourceSimple extends SimpleController {

    @Inject
    @Named("orderServiceSimple")
    OrderService orderService;

    /**
     * 创建订单
     *
     * @param request 订单创建请求
     * @return 创建的订单信息
     */
    @POST
    @Operation(summary = "创建订单", description = "创建新的订单")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<Order>> createOrder(@Valid OrderCreateRequest request) {
        logOperation("创建订单", "开始处理创建请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("创建订单", tenantId, "租户验证通过");
                    return orderService.createOrder(tenantId, request);
                })
                .map(order -> {
                    logOperation("创建订单", "创建成功，订单ID: " + order.id);
                    return success("创建订单成功", order);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "创建订单"));
    }

    /**
     * 根据ID获取订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "获取订单详情", description = "根据ID获取订单详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<Order>> getOrderById(@PathParam("id") Long id) {
        logOperation("获取订单详情", "订单ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> orderService.getOrderById(tenantId, id))
                .map(order -> {
                    if (order != null) {
                        logOperation("获取订单详情", "查询成功");
                        return success(order);
                    } else {
                        return ApiResponse.<Order>notFound("订单不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "获取订单详情"));
    }

    /**
     * 更新订单信息
     *
     * @param id 订单ID
     * @param request 更新请求
     * @return 更新后的订单信息
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新订单", description = "根据ID更新订单信息")
    @APIResponse(responseCode = "200", description = "更新成功")
    public Uni<ApiResponse<Order>> updateOrder(@PathParam("id") Long id, @Valid OrderUpdateRequest request) {
        logOperation("更新订单", "订单ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> orderService.updateOrder(tenantId, id, request))
                .map(order -> {
                    logOperation("更新订单", "更新成功");
                    return success("更新订单成功", order);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "更新订单"));
    }

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除订单", description = "根据ID删除订单")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Boolean>> deleteOrder(@PathParam("id") Long id) {
        logOperation("删除订单", "订单ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> orderService.deleteOrder(tenantId, id))
                .map(deleted -> {
                    if (deleted) {
                        logOperation("删除订单", "删除成功");
                        return success("删除订单成功", true);
                    } else {
                        return ApiResponse.<Boolean>notFound("订单不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "删除订单"));
    }

    /**
     * 分页查询订单列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param status 订单状态
     * @param customerName 客户名称
     * @return 订单列表
     */
    @GET
    @Operation(summary = "分页查询订单", description = "分页查询订单列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<PageResult<Order>>> getOrderList(
            @QueryParam("page") @DefaultValue("1") Integer page,
            @QueryParam("size") @DefaultValue("10") Integer size,
            @QueryParam("status") String status,
            @QueryParam("customerName") String customerName) {

        logOperation("分页查询订单", String.format("页码: %d, 页大小: %d", page, size));

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    PageRequest pageRequest = new PageRequest(page, size);
                    return orderService.getOrderList(tenantId, pageRequest, status, customerName);
                })
                .map(pageResult -> {
                    logOperation("分页查询订单", "查询成功，共" + pageResult.getTotal() + "条记录");
                    return success(pageResult);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "分页查询订单"));
    }
}
