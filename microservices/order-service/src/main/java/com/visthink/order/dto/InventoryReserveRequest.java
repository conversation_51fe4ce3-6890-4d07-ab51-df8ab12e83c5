package com.visthink.order.dto;

import java.util.List;

/**
 * 库存预占请求
 * 
 * 用于预占商品库存
 * 
 * <AUTHOR>
 */
public class InventoryReserveRequest {
    
    private String businessType;
    private Long businessId;
    private String businessNo;
    private String reason;
    private List<ReserveItem> items;
    
    public String getBusinessType() {
        return businessType;
    }
    
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    
    public Long getBusinessId() {
        return businessId;
    }
    
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }
    
    public String getBusinessNo() {
        return businessNo;
    }
    
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public List<ReserveItem> getItems() {
        return items;
    }
    
    public void setItems(List<ReserveItem> items) {
        this.items = items;
    }
    
    /**
     * 预占项
     */
    public static class ReserveItem {
        private Long skuId;
        private Integer quantity;
        private Long warehouseId; // 可选，指定仓库
        
        public Long getSkuId() {
            return skuId;
        }
        
        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }
        
        public Integer getQuantity() {
            return quantity;
        }
        
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        
        public Long getWarehouseId() {
            return warehouseId;
        }
        
        public void setWarehouseId(Long warehouseId) {
            this.warehouseId = warehouseId;
        }
    }
}
