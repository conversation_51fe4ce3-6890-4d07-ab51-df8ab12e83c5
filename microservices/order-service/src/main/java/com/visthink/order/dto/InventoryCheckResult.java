package com.visthink.order.dto;

import java.util.List;

/**
 * 库存检查结果
 * 
 * 返回库存检查的详细结果
 * 
 * <AUTHOR>
 */
public class InventoryCheckResult {
    
    private boolean allAvailable;
    private List<CheckResultItem> items;
    private String message;
    
    public boolean isAllAvailable() {
        return allAvailable;
    }
    
    public void setAllAvailable(boolean allAvailable) {
        this.allAvailable = allAvailable;
    }
    
    public List<CheckResultItem> getItems() {
        return items;
    }
    
    public void setItems(List<CheckResultItem> items) {
        this.items = items;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    /**
     * 检查结果项
     */
    public static class CheckResultItem {
        private Long skuId;
        private String skuCode;
        private String skuName;
        private Integer requiredQuantity;
        private Integer availableQuantity;
        private boolean available;
        private String message;
        
        public Long getSkuId() {
            return skuId;
        }
        
        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }
        
        public String getSkuCode() {
            return skuCode;
        }
        
        public void setSkuCode(String skuCode) {
            this.skuCode = skuCode;
        }
        
        public String getSkuName() {
            return skuName;
        }
        
        public void setSkuName(String skuName) {
            this.skuName = skuName;
        }
        
        public Integer getRequiredQuantity() {
            return requiredQuantity;
        }
        
        public void setRequiredQuantity(Integer requiredQuantity) {
            this.requiredQuantity = requiredQuantity;
        }
        
        public Integer getAvailableQuantity() {
            return availableQuantity;
        }
        
        public void setAvailableQuantity(Integer availableQuantity) {
            this.availableQuantity = availableQuantity;
        }
        
        public boolean isAvailable() {
            return available;
        }
        
        public void setAvailable(boolean available) {
            this.available = available;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
    }
}
