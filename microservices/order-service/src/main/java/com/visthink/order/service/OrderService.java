package com.visthink.order.service;

import com.visthink.order.dto.OrderCreateRequest;
import com.visthink.order.dto.OrderUpdateRequest;
import com.visthink.order.entity.Order;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;

import java.util.List;

/**
 * 订单业务服务接口
 *
 * 定义订单相关的业务逻辑操作，包括订单生命周期管理、状态流转、支付处理等
 * 专门为订单业务设计，不继承通用BaseService以保持业务灵活性
 *
 * <AUTHOR>
 */
public interface OrderService {

    /**
     * 创建订单
     *
     * @param tenantId 租户ID
     * @param request 订单创建请求
     * @return 创建的订单
     */
    Uni<Order> createOrder(Long tenantId, OrderCreateRequest request);

    /**
     * 根据ID获取订单详情
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 订单详情
     */
    Uni<Order> getOrderById(Long tenantId, Long orderId);

    /**
     * 更新订单信息
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @param request 更新请求
     * @return 更新后的订单
     */
    Uni<Order> updateOrder(Long tenantId, Long orderId, OrderUpdateRequest request);

    /**
     * 删除订单
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 删除结果
     */
    Uni<Boolean> deleteOrder(Long tenantId, Long orderId);

    /**
     * 分页查询订单列表
     *
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param status 订单状态（可选）
     * @param customerName 客户名称（可选）
     * @return 分页结果
     */
    Uni<PageResult<Order>> getOrderList(Long tenantId, PageRequest pageRequest, String status, String customerName);

    /**
     * 取消订单
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @param reason 取消原因
     * @return 取消结果
     */
    Uni<Boolean> cancelOrder(Long tenantId, Long orderId, String reason);

    /**
     * 确认订单
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 确认结果
     */
    Uni<Boolean> confirmOrder(Long tenantId, Long orderId);

    /**
     * 订单发货
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 发货结果
     */
    Uni<Boolean> shipOrder(Long tenantId, Long orderId);

    /**
     * 完成订单
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 完成结果
     */
    Uni<Boolean> completeOrder(Long tenantId, Long orderId);

}
