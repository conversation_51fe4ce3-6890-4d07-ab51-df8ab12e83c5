package com.visthink.order.dto;

import java.math.BigDecimal;

/**
 * 订单项请求DTO
 * 
 * 用于创建订单时的订单项信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderItemRequest {
    
    /**
     * 商品ID
     */
    private Long productId;
    
    /**
     * SKU ID
     */
    private Long skuId;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * SKU 编码
     */
    private String skuCode;
    
    /**
     * 商品规格
     */
    private String specification;
    
    /**
     * 购买数量
     */
    private Integer quantity;
    
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 小计金额
     */
    private BigDecimal subtotal;
    
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 备注
     */
    private String remark;

    // ==================== Constructors ====================

    public OrderItemRequest() {
    }

    public OrderItemRequest(Long productId, Long skuId, String productName, 
                           Integer quantity, BigDecimal unitPrice) {
        this.productId = productId;
        this.skuId = skuId;
        this.productName = productName;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
    }

    // ==================== Getters and Setters ====================

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public String getSpecification() {
        return specification;
    }

    public void setSpecification(String specification) {
        this.specification = specification;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
        // 自动计算小计
        if (this.unitPrice != null && quantity != null) {
            this.subtotal = this.unitPrice.multiply(BigDecimal.valueOf(quantity));
        }
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        // 自动计算小计
        if (unitPrice != null && this.quantity != null) {
            this.subtotal = unitPrice.multiply(BigDecimal.valueOf(this.quantity));
        }
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // ==================== 业务方法 ====================

    /**
     * 计算实际支付金额（小计 - 折扣）
     */
    public BigDecimal getActualAmount() {
        BigDecimal actual = subtotal != null ? subtotal : BigDecimal.ZERO;
        if (discountAmount != null) {
            actual = actual.subtract(discountAmount);
        }
        return actual.max(BigDecimal.ZERO); // 确保不为负数
    }

    /**
     * 验证订单项数据是否有效
     */
    public boolean isValid() {
        return productId != null && 
               quantity != null && quantity > 0 &&
               unitPrice != null && unitPrice.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 重新计算小计
     */
    public void recalculateSubtotal() {
        if (unitPrice != null && quantity != null) {
            this.subtotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
        }
    }

    @Override
    public String toString() {
        return "OrderItemRequest{" +
                "productId=" + productId +
                ", skuId=" + skuId +
                ", productName='" + productName + '\'' +
                ", skuCode='" + skuCode + '\'' +
                ", quantity=" + quantity +
                ", unitPrice=" + unitPrice +
                ", subtotal=" + subtotal +
                ", discountAmount=" + discountAmount +
                '}';
    }
}
