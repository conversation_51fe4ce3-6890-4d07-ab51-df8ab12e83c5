package com.visthink.order.dto;

import java.util.List;

/**
 * 库存释放请求
 * 
 * 用于释放预占的库存
 * 
 * <AUTHOR>
 */
public class InventoryReleaseRequest {
    
    private String businessType;
    private Long businessId;
    private String businessNo;
    private String reason;
    private List<ReleaseItem> items;
    
    public String getBusinessType() {
        return businessType;
    }
    
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    
    public Long getBusinessId() {
        return businessId;
    }
    
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }
    
    public String getBusinessNo() {
        return businessNo;
    }
    
    public void setBusinessNo(String businessNo) {
        this.businessNo = businessNo;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public List<ReleaseItem> getItems() {
        return items;
    }
    
    public void setItems(List<ReleaseItem> items) {
        this.items = items;
    }
    
    /**
     * 释放项
     */
    public static class ReleaseItem {
        private Long skuId;
        private Integer quantity;
        private Long warehouseId; // 可选，指定仓库
        
        public Long getSkuId() {
            return skuId;
        }
        
        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }
        
        public Integer getQuantity() {
            return quantity;
        }
        
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        
        public Long getWarehouseId() {
            return warehouseId;
        }
        
        public void setWarehouseId(Long warehouseId) {
            this.warehouseId = warehouseId;
        }
    }
}
