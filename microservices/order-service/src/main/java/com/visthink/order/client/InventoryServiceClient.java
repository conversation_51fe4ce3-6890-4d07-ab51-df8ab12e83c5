package com.visthink.order.client;

import com.visthink.common.dto.ApiResponse;
import com.visthink.order.dto.InventoryConfirmRequest;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存服务客户端
 * 
 * 用于与库存服务进行通信，进行库存查询、预占、扣减等操作
 * 支持响应式编程和服务间调用
 * 
 * <AUTHOR>
 */
@RegisterRestClient(configKey = "inventory-service")
@Path("/api/inventory")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface InventoryServiceClient {

    /**
     * 查询商品库存信息
     * 
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @param warehouseId 仓库ID（可选）
     * @return 库存信息
     */
    @GET
    @Path("/product/{productId}")
    Uni<ApiResponse<InventoryInfo>> getInventoryByProductId(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                          @PathParam("productId") Long productId,
                                                          @QueryParam("warehouseId") Long warehouseId);

    /**
     * 查询SKU库存信息
     * 
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @param warehouseId 仓库ID（可选）
     * @return 库存信息
     */
    @GET
    @Path("/sku/{skuId}")
    Uni<ApiResponse<InventoryInfo>> getInventoryBySkuId(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                      @PathParam("skuId") Long skuId,
                                                      @QueryParam("warehouseId") Long warehouseId);

    /**
     * 批量查询库存信息
     *
     * @param tenantId 租户ID
     * @param request 批量查询请求
     * @return 库存信息列表
     */
    @POST
    @Path("/batch")
    Uni<ApiResponse<List<InventoryInfo>>> getBatchInventory(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                          BatchInventoryRequest request);

    /**
     * 检查库存是否充足
     *
     * @param tenantId 租户ID
     * @param request 库存检查请求
     * @return 检查结果
     */
    @POST
    @Path("/check")
    Uni<ApiResponse<InventoryCheckResult>> checkInventory(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                        InventoryCheckRequest request);

    /**
     * 预占库存
     *
     * @param tenantId 租户ID
     * @param request 库存预占请求
     * @return 预占结果
     */
    @POST
    @Path("/reserve")
    Uni<ApiResponse<InventoryReserveResult>> reserveInventory(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                            InventoryReserveRequest request);

    /**
     * 释放预占库存
     *
     * @param tenantId 租户ID
     * @param request 库存释放请求
     * @return 释放结果
     */
    @POST
    @Path("/release")
    Uni<ApiResponse<Boolean>> releaseInventory(@HeaderParam("X-Tenant-Id") Long tenantId,
                                             InventoryReleaseRequest request);

    /**
     * 确认出库（将预占库存转为实际扣减）
     *
     * @param tenantId 租户ID
     * @param request 确认出库请求
     * @return 确认结果
     */
    @POST
    @Path("/confirm")
    Uni<ApiResponse<Boolean>> confirmOutbound(@HeaderParam("X-Tenant-Id") Long tenantId,
                                            InventoryConfirmRequest request);

    /**
     * 扣减库存（确认出库）
     *
     * @param tenantId 租户ID
     * @param request 库存扣减请求
     * @return 扣减结果
     */
    @POST
    @Path("/deduct")
    Uni<ApiResponse<InventoryDeductResult>> deductInventory(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                          InventoryDeductRequest request);

    /**
     * 库存信息DTO
     */
    class InventoryInfo {
        public Long id;
        public Long productId;
        public String productCode;
        public String productName;
        public Long skuId;
        public String skuCode;
        public String skuName;
        public Long warehouseId;
        public String warehouseName;
        public Integer quantity;
        public Integer availableQuantity;
        public Integer reservedQuantity;
        public Integer inTransitQuantity;
        public Integer safetyStock;
        public Integer warningStock;
        public Integer maxStock;
        public Integer minStock;
        public Integer reorderPoint;
        public Integer reorderQuantity;
        public BigDecimal averageCost;
        public BigDecimal totalValue;
        public Integer status;
    }

    /**
     * 批量库存查询请求
     */
    class BatchInventoryRequest {
        public List<InventoryQueryItem> items;
        public Long warehouseId;

        public static class InventoryQueryItem {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
        }
    }

    /**
     * 库存检查请求
     */
    class InventoryCheckRequest {
        public String businessType;
        public String businessId;
        public List<InventoryCheckItem> items;

        public static class InventoryCheckItem {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
            public Integer requiredQuantity;
            public Long warehouseId;
        }
    }

    /**
     * 库存检查结果
     */
    class InventoryCheckResult {
        public Boolean sufficient;
        public String message;
        public List<InventoryCheckItemResult> items;

        public static class InventoryCheckItemResult {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
            public Integer requiredQuantity;
            public Integer availableQuantity;
            public Boolean sufficient;
            public String message;
        }
    }

    /**
     * 库存预占请求
     */
    class InventoryReserveRequest {
        public String businessType;
        public String businessId;
        public String remark;
        public List<InventoryReserveItem> items;

        public static class InventoryReserveItem {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
            public Integer reserveQuantity;
            public Long warehouseId;
            public String remark;
        }
    }

    /**
     * 库存预占结果
     */
    class InventoryReserveResult {
        public Boolean success;
        public String message;
        public String reserveId;
        public List<InventoryReserveItemResult> items;

        public static class InventoryReserveItemResult {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
            public Integer reserveQuantity;
            public Integer actualReserveQuantity;
            public Boolean success;
            public String message;
        }
    }

    /**
     * 库存释放请求
     */
    class InventoryReleaseRequest {
        public String businessType;
        public String businessId;
        public String reserveId;
        public String remark;
        public List<InventoryReleaseItem> items;

        public static class InventoryReleaseItem {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
            public Integer releaseQuantity;
            public Long warehouseId;
            public String remark;
        }
    }

    /**
     * 库存扣减请求
     */
    class InventoryDeductRequest {
        public String businessType;
        public String businessId;
        public String reserveId;
        public String remark;
        public List<InventoryDeductItem> items;

        public static class InventoryDeductItem {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
            public Integer deductQuantity;
            public Long warehouseId;
            public String remark;
        }
    }

    /**
     * 库存扣减结果
     */
    class InventoryDeductResult {
        public Boolean success;
        public String message;
        public List<InventoryDeductItemResult> items;

        public static class InventoryDeductItemResult {
            public Long productId;
            public Long skuId;
            public String productCode;
            public String skuCode;
            public Integer deductQuantity;
            public Integer actualDeductQuantity;
            public Boolean success;
            public String message;
        }
    }
}
