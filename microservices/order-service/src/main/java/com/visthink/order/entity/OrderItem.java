package com.visthink.order.entity;

import com.visthink.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 订单明细实体
 *
 * 管理订单中的商品明细信息，包括商品信息、数量、价格等
 * 支持多规格商品和价格快照功能
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "order_item", indexes = {
    @Index(name = "idx_order_item_tenant_id", columnList = "tenant_id"),
    @Index(name = "idx_order_item_order_id", columnList = "order_id"),
    @Index(name = "idx_order_item_product_id", columnList = "product_id"),
    @Index(name = "idx_order_item_sku_id", columnList = "sku_id")
})
@EqualsAndHashCode(callSuper = true)
public class OrderItem extends BaseEntity {

    /**
     * 订单ID
     */
    @Column(name = "order_id", nullable = false)
    private Long orderId;

    /**
     * 订单对象（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", insertable = false, updatable = false)
    private Order order;

    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;

    /**
     * 商品编码
     */
    @Column(name = "product_code", length = 100)
    private String productCode;

    /**
     * 商品名称（快照）
     */
    @Column(name = "product_name", nullable = false, length = 500)
    private String productName;

    /**
     * SKU ID
     */
    @Column(name = "sku_id")
    private Long skuId;

    /**
     * SKU编码
     */
    @Column(name = "sku_code", length = 100)
    private String skuCode;

    /**
     * SKU名称（快照）
     */
    @Column(name = "sku_name", length = 500)
    private String skuName;

    /**
     * 商品规格信息（JSON格式快照）
     */
    @Column(name = "spec_info", columnDefinition = "TEXT")
    private String specInfo;

    /**
     * 商品主图URL（快照）
     */
    @Column(name = "product_image", length = 500)
    private String productImage;

    /**
     * 商品单价（下单时价格快照）
     */
    @Column(name = "unit_price", precision = 10, scale = 2, nullable = false)
    private BigDecimal unitPrice;

    /**
     * 商品原价（市场价快照）
     */
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    /**
     * 商品数量
     */
    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    /**
     * 小计金额（单价 × 数量）
     */
    @Column(name = "subtotal", precision = 12, scale = 2, nullable = false)
    private BigDecimal subtotal;

    /**
     * 优惠金额
     */
    @Column(name = "discount_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal discountAmount = BigDecimal.ZERO;

    /**
     * 实际支付金额（小计 - 优惠）
     */
    @Column(name = "actual_amount", precision = 12, scale = 2, nullable = false)
    private BigDecimal actualAmount;

    /**
     * 商品重量（克）
     */
    @Column(name = "weight")
    private Integer weight;

    /**
     * 商品体积（立方厘米）
     */
    @Column(name = "volume")
    private Integer volume;

    /**
     * 分类ID（快照）
     */
    @Column(name = "category_id")
    private Long categoryId;

    /**
     * 分类名称（快照）
     */
    @Column(name = "category_name", length = 200)
    private String categoryName;

    /**
     * 品牌ID（快照）
     */
    @Column(name = "brand_id")
    private Long brandId;

    /**
     * 品牌名称（快照）
     */
    @Column(name = "brand_name", length = 200)
    private String brandName;

    /**
     * 供应商ID
     */
    @Column(name = "supplier_id")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Column(name = "supplier_name", length = 200)
    private String supplierName;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private Long warehouseId;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name", length = 200)
    private String warehouseName;

    /**
     * 发货状态：1-未发货，2-部分发货，3-已发货，4-已收货
     */
    @Column(name = "shipping_status", nullable = false)
    private Integer shippingStatus = 1;

    /**
     * 已发货数量
     */
    @Column(name = "shipped_quantity", nullable = false)
    private Integer shippedQuantity = 0;

    /**
     * 退货数量
     */
    @Column(name = "return_quantity", nullable = false)
    private Integer returnQuantity = 0;

    /**
     * 退款数量
     */
    @Column(name = "refund_quantity", nullable = false)
    private Integer refundQuantity = 0;

    /**
     * 是否是赠品
     */
    @Column(name = "is_gift", nullable = false)
    private Boolean isGift = false;

    /**
     * 促销活动ID
     */
    @Column(name = "promotion_id")
    private Long promotionId;

    /**
     * 促销活动名称
     */
    @Column(name = "promotion_name", length = 200)
    private String promotionName;

    /**
     * 促销类型：1-满减，2-折扣，3-赠品，4-优惠券
     */
    @Column(name = "promotion_type")
    private Integer promotionType;

    /**
     * 商品属性（JSON格式快照）
     */
    @Column(name = "product_attributes", columnDefinition = "TEXT")
    private String productAttributes;

    /**
     * 发货状态常量
     */
    public static class ShippingStatus {
        public static final int UNSHIPPED = 1;      // 未发货
        public static final int PARTIAL_SHIPPED = 2; // 部分发货
        public static final int SHIPPED = 3;        // 已发货
        public static final int RECEIVED = 4;       // 已收货
    }

    /**
     * 促销类型常量
     */
    public static class PromotionType {
        public static final int FULL_REDUCTION = 1;  // 满减
        public static final int DISCOUNT = 2;        // 折扣
        public static final int GIFT = 3;            // 赠品
        public static final int COUPON = 4;          // 优惠券
    }

    /**
     * 计算小计金额
     */
    public void calculateSubtotal() {
        if (this.unitPrice != null && this.quantity != null) {
            this.subtotal = this.unitPrice.multiply(BigDecimal.valueOf(this.quantity));
        } else {
            this.subtotal = BigDecimal.ZERO;
        }
    }

    /**
     * 计算实际支付金额
     */
    public void calculateActualAmount() {
        if (this.subtotal != null && this.discountAmount != null) {
            this.actualAmount = this.subtotal.subtract(this.discountAmount);
            // 确保实际金额不为负数
            if (this.actualAmount.compareTo(BigDecimal.ZERO) < 0) {
                this.actualAmount = BigDecimal.ZERO;
            }
        } else {
            this.actualAmount = this.subtotal != null ? this.subtotal : BigDecimal.ZERO;
        }
    }

    /**
     * 判断是否可以发货
     */
    public boolean canShip() {
        return this.shippingStatus == ShippingStatus.UNSHIPPED
               && this.quantity > this.shippedQuantity;
    }

    /**
     * 判断是否已完全发货
     */
    public boolean isFullyShipped() {
        return this.shippedQuantity >= this.quantity;
    }

    /**
     * 判断是否可以退货
     */
    public boolean canReturn() {
        return this.shippingStatus == ShippingStatus.SHIPPED
               && this.returnQuantity < this.shippedQuantity;
    }

    /**
     * 获取可发货数量
     */
    public int getAvailableShipQuantity() {
        return Math.max(0, this.quantity - this.shippedQuantity);
    }

    /**
     * 获取可退货数量
     */
    public int getAvailableReturnQuantity() {
        return Math.max(0, this.shippedQuantity - this.returnQuantity);
    }

    /**
     * 更新发货数量
     */
    public void updateShippedQuantity(int shippedQty) {
        this.shippedQuantity = Math.min(this.quantity, this.shippedQuantity + shippedQty);

        // 更新发货状态
        if (this.shippedQuantity >= this.quantity) {
            this.shippingStatus = ShippingStatus.SHIPPED;
        } else if (this.shippedQuantity > 0) {
            this.shippingStatus = ShippingStatus.PARTIAL_SHIPPED;
        }
    }

    /**
     * 更新退货数量
     */
    public void updateReturnQuantity(int returnQty) {
        this.returnQuantity = Math.min(this.shippedQuantity, this.returnQuantity + returnQty);
    }
}
