package com.visthink.order.dto;

import jakarta.ws.rs.QueryParam;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单查询请求DTO
 * 
 * 用于接收订单查询的请求参数，支持多条件组合查询
 * 提供灵活的查询条件和排序选项
 * 
 * <AUTHOR>
 */
@Data
public class OrderQueryRequest {

    /**
     * 订单号
     */
    @QueryParam("orderNumber")
    private String orderNumber;

    /**
     * 用户ID
     */
    @QueryParam("userId")
    private Long userId;

    /**
     * 用户名称
     */
    @QueryParam("userName")
    private String userName;

    /**
     * 订单类型：1-普通订单，2-预售订单，3-团购订单，4-秒杀订单
     */
    @QueryParam("orderType")
    private Integer orderType;

    /**
     * 订单状态：1-待支付，2-已支付，3-已发货，4-已完成，5-已取消，6-已退款
     */
    @QueryParam("orderStatus")
    private Integer orderStatus;

    /**
     * 支付状态：1-未支付，2-支付中，3-已支付，4-支付失败，5-已退款
     */
    @QueryParam("paymentStatus")
    private Integer paymentStatus;

    /**
     * 发货状态：1-未发货，2-部分发货，3-已发货，4-已收货
     */
    @QueryParam("shippingStatus")
    private Integer shippingStatus;

    /**
     * 订单来源：1-PC端，2-移动端，3-小程序，4-APP，5-API
     */
    @QueryParam("orderSource")
    private Integer orderSource;

    /**
     * 订单渠道：1-直销，2-分销，3-平台（淘宝、京东等）
     */
    @QueryParam("orderChannel")
    private Integer orderChannel;

    /**
     * 外部订单号（第三方平台订单号）
     */
    @QueryParam("externalOrderNumber")
    private String externalOrderNumber;

    /**
     * 收货人姓名
     */
    @QueryParam("receiverName")
    private String receiverName;

    /**
     * 收货人电话
     */
    @QueryParam("receiverPhone")
    private String receiverPhone;

    /**
     * 收货人地址（模糊查询）
     */
    @QueryParam("receiverAddress")
    private String receiverAddress;

    /**
     * 省份
     */
    @QueryParam("receiverProvince")
    private String receiverProvince;

    /**
     * 城市
     */
    @QueryParam("receiverCity")
    private String receiverCity;

    /**
     * 区县
     */
    @QueryParam("receiverDistrict")
    private String receiverDistrict;

    /**
     * 最小订单金额
     */
    @QueryParam("minTotalAmount")
    private java.math.BigDecimal minTotalAmount;

    /**
     * 最大订单金额
     */
    @QueryParam("maxTotalAmount")
    private java.math.BigDecimal maxTotalAmount;

    /**
     * 优惠券ID
     */
    @QueryParam("couponId")
    private Long couponId;

    /**
     * 优惠券编码
     */
    @QueryParam("couponCode")
    private String couponCode;

    /**
     * 促销活动ID
     */
    @QueryParam("promotionId")
    private Long promotionId;

    /**
     * 是否需要发票
     */
    @QueryParam("needInvoice")
    private Boolean needInvoice;

    /**
     * 发票类型：1-普通发票，2-增值税发票
     */
    @QueryParam("invoiceType")
    private Integer invoiceType;

    /**
     * 创建时间开始
     */
    @QueryParam("createTimeStart")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @QueryParam("createTimeEnd")
    private LocalDateTime createTimeEnd;

    /**
     * 支付时间开始
     */
    @QueryParam("paidTimeStart")
    private LocalDateTime paidTimeStart;

    /**
     * 支付时间结束
     */
    @QueryParam("paidTimeEnd")
    private LocalDateTime paidTimeEnd;

    /**
     * 发货时间开始
     */
    @QueryParam("shippedTimeStart")
    private LocalDateTime shippedTimeStart;

    /**
     * 发货时间结束
     */
    @QueryParam("shippedTimeEnd")
    private LocalDateTime shippedTimeEnd;

    /**
     * 完成时间开始
     */
    @QueryParam("completedTimeStart")
    private LocalDateTime completedTimeStart;

    /**
     * 完成时间结束
     */
    @QueryParam("completedTimeEnd")
    private LocalDateTime completedTimeEnd;

    /**
     * 关键词搜索（订单号、用户名、收货人等）
     */
    @QueryParam("keyword")
    private String keyword;

    /**
     * 订单标签
     */
    @QueryParam("orderTags")
    private String orderTags;

    /**
     * 是否包含订单明细
     */
    @QueryParam("includeItems")
    private Boolean includeItems = false;

    /**
     * 是否包含支付记录
     */
    @QueryParam("includePayments")
    private Boolean includePayments = false;

    /**
     * 商品ID（查询包含指定商品的订单）
     */
    @QueryParam("productId")
    private Long productId;

    /**
     * SKU ID（查询包含指定SKU的订单）
     */
    @QueryParam("skuId")
    private Long skuId;

    /**
     * 商品名称（模糊查询）
     */
    @QueryParam("productName")
    private String productName;

    /**
     * 品牌ID
     */
    @QueryParam("brandId")
    private Long brandId;

    /**
     * 分类ID
     */
    @QueryParam("categoryId")
    private Long categoryId;

    /**
     * 供应商ID
     */
    @QueryParam("supplierId")
    private Long supplierId;

    /**
     * 仓库ID
     */
    @QueryParam("warehouseId")
    private Long warehouseId;

    /**
     * 判断是否有时间范围查询
     */
    public boolean hasTimeRangeQuery() {
        return createTimeStart != null || createTimeEnd != null ||
               paidTimeStart != null || paidTimeEnd != null ||
               shippedTimeStart != null || shippedTimeEnd != null ||
               completedTimeStart != null || completedTimeEnd != null;
    }

    /**
     * 判断是否有金额范围查询
     */
    public boolean hasAmountRangeQuery() {
        return minTotalAmount != null || maxTotalAmount != null;
    }

    /**
     * 判断是否有收货地址查询
     */
    public boolean hasAddressQuery() {
        return receiverName != null || receiverPhone != null || receiverAddress != null ||
               receiverProvince != null || receiverCity != null || receiverDistrict != null;
    }

    /**
     * 判断是否有商品相关查询
     */
    public boolean hasProductQuery() {
        return productId != null || skuId != null || productName != null ||
               brandId != null || categoryId != null || supplierId != null || warehouseId != null;
    }

    /**
     * 判断是否有状态查询
     */
    public boolean hasStatusQuery() {
        return orderStatus != null || paymentStatus != null || shippingStatus != null;
    }

    /**
     * 判断是否有优惠相关查询
     */
    public boolean hasPromotionQuery() {
        return couponId != null || couponCode != null || promotionId != null;
    }

    /**
     * 判断是否有发票相关查询
     */
    public boolean hasInvoiceQuery() {
        return needInvoice != null || invoiceType != null;
    }

    /**
     * 判断是否为空查询（没有任何查询条件）
     */
    public boolean isEmpty() {
        return orderNumber == null && userId == null && userName == null &&
               orderType == null && orderStatus == null && paymentStatus == null &&
               shippingStatus == null && orderSource == null && orderChannel == null &&
               externalOrderNumber == null && receiverName == null && receiverPhone == null &&
               receiverAddress == null && receiverProvince == null && receiverCity == null &&
               receiverDistrict == null && minTotalAmount == null && maxTotalAmount == null &&
               couponId == null && couponCode == null && promotionId == null &&
               needInvoice == null && invoiceType == null && createTimeStart == null &&
               createTimeEnd == null && paidTimeStart == null && paidTimeEnd == null &&
               shippedTimeStart == null && shippedTimeEnd == null && completedTimeStart == null &&
               completedTimeEnd == null && keyword == null && orderTags == null &&
               productId == null && skuId == null && productName == null &&
               brandId == null && categoryId == null && supplierId == null && warehouseId == null;
    }

    /**
     * 验证查询参数
     */
    public void validate() {
        // 验证时间范围
        if (createTimeStart != null && createTimeEnd != null && createTimeStart.isAfter(createTimeEnd)) {
            throw new IllegalArgumentException("创建时间开始不能晚于结束时间");
        }
        if (paidTimeStart != null && paidTimeEnd != null && paidTimeStart.isAfter(paidTimeEnd)) {
            throw new IllegalArgumentException("支付时间开始不能晚于结束时间");
        }
        if (shippedTimeStart != null && shippedTimeEnd != null && shippedTimeStart.isAfter(shippedTimeEnd)) {
            throw new IllegalArgumentException("发货时间开始不能晚于结束时间");
        }
        if (completedTimeStart != null && completedTimeEnd != null && completedTimeStart.isAfter(completedTimeEnd)) {
            throw new IllegalArgumentException("完成时间开始不能晚于结束时间");
        }

        // 验证金额范围
        if (minTotalAmount != null && maxTotalAmount != null && minTotalAmount.compareTo(maxTotalAmount) > 0) {
            throw new IllegalArgumentException("最小订单金额不能大于最大订单金额");
        }

        // 验证状态值
        if (orderType != null && (orderType < 1 || orderType > 4)) {
            throw new IllegalArgumentException("订单类型值无效");
        }
        if (orderStatus != null && (orderStatus < 1 || orderStatus > 6)) {
            throw new IllegalArgumentException("订单状态值无效");
        }
        if (paymentStatus != null && (paymentStatus < 1 || paymentStatus > 5)) {
            throw new IllegalArgumentException("支付状态值无效");
        }
        if (shippingStatus != null && (shippingStatus < 1 || shippingStatus > 4)) {
            throw new IllegalArgumentException("发货状态值无效");
        }
        if (orderSource != null && (orderSource < 1 || orderSource > 5)) {
            throw new IllegalArgumentException("订单来源值无效");
        }
        if (orderChannel != null && (orderChannel < 1 || orderChannel > 3)) {
            throw new IllegalArgumentException("订单渠道值无效");
        }
        if (invoiceType != null && (invoiceType < 1 || invoiceType > 2)) {
            throw new IllegalArgumentException("发票类型值无效");
        }
    }
}
