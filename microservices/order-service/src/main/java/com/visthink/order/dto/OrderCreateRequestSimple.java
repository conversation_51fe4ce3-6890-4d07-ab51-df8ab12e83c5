package com.visthink.order.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单创建请求DTO - 简化版本
 * 
 * 用于创建订单的基础信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderCreateRequestSimple {
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 订单项列表
     */
    private List<OrderItemRequest> items;
    
    /**
     * 收货地址
     */
    private String shippingAddress;
    
    /**
     * 收货人姓名
     */
    private String receiverName;
    
    /**
     * 收货人电话
     */
    private String receiverPhone;
    
    /**
     * 买家留言
     */
    private String buyerMessage;
    
    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    // ==================== Constructors ====================

    public OrderCreateRequestSimple() {
    }

    public OrderCreateRequestSimple(Long customerId, String customerName, List<OrderItemRequest> items) {
        this.customerId = customerId;
        this.customerName = customerName;
        this.items = items;
    }

    // ==================== Getters and Setters ====================

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public List<OrderItemRequest> getItems() {
        return items;
    }

    public void setItems(List<OrderItemRequest> items) {
        this.items = items;
    }

    public String getShippingAddress() {
        return shippingAddress;
    }

    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    // ==================== 业务方法 ====================

    /**
     * 验证请求数据是否有效
     */
    public boolean isValid() {
        return customerId != null && 
               customerName != null && !customerName.trim().isEmpty() &&
               items != null && !items.isEmpty() &&
               items.stream().allMatch(OrderItemRequest::isValid);
    }

    /**
     * 计算订单总金额
     */
    public BigDecimal calculateTotalAmount() {
        if (items == null || items.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return items.stream()
                .map(OrderItemRequest::getActualAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取订单项数量
     */
    public int getItemCount() {
        return items != null ? items.size() : 0;
    }

    /**
     * 获取商品总数量
     */
    public int getTotalQuantity() {
        if (items == null || items.isEmpty()) {
            return 0;
        }
        
        return items.stream()
                .mapToInt(item -> item.getQuantity() != null ? item.getQuantity() : 0)
                .sum();
    }

    @Override
    public String toString() {
        return "OrderCreateRequestSimple{" +
                "customerId=" + customerId +
                ", customerName='" + customerName + '\'' +
                ", itemCount=" + getItemCount() +
                ", totalQuantity=" + getTotalQuantity() +
                ", totalAmount=" + totalAmount +
                ", shippingAddress='" + shippingAddress + '\'' +
                '}';
    }
}
