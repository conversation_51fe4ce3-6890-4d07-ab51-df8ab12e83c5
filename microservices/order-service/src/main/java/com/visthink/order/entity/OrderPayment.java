package com.visthink.order.entity;

import com.visthink.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单支付记录实体
 *
 * 管理订单的支付信息，包括支付方式、支付状态、支付流水等
 * 支持多种支付方式和分期支付功能
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "order_payment", indexes = {
    @Index(name = "idx_order_payment_tenant_id", columnList = "tenant_id"),
    @Index(name = "idx_order_payment_order_id", columnList = "order_id"),
    @Index(name = "idx_order_payment_number", columnList = "payment_number"),
    @Index(name = "idx_order_payment_status", columnList = "payment_status"),
    @Index(name = "idx_order_payment_method", columnList = "payment_method"),
    @Index(name = "idx_order_payment_trade_no", columnList = "trade_no")
})
@EqualsAndHashCode(callSuper = true)
public class OrderPayment extends BaseEntity {

    /**
     * 订单ID
     */
    @Column(name = "order_id", nullable = false)
    private Long orderId;

    /**
     * 订单对象（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", insertable = false, updatable = false)
    private Order order;

    /**
     * 支付单号（系统内唯一）
     */
    @Column(name = "payment_number", unique = true, nullable = false, length = 50)
    private String paymentNumber;

    /**
     * 支付方式：1-支付宝，2-微信支付，3-银行卡，4-余额支付，5-货到付款
     */
    @Column(name = "payment_method", nullable = false)
    private Integer paymentMethod;

    /**
     * 支付方式名称
     */
    @Column(name = "payment_method_name", length = 50)
    private String paymentMethodName;

    /**
     * 支付状态：1-待支付，2-支付中，3-支付成功，4-支付失败，5-已退款，6-部分退款
     */
    @Column(name = "payment_status", nullable = false)
    private Integer paymentStatus = 1;

    /**
     * 支付金额
     */
    @Column(name = "payment_amount", precision = 12, scale = 2, nullable = false)
    private BigDecimal paymentAmount;

    /**
     * 实际支付金额
     */
    @Column(name = "actual_amount", precision = 12, scale = 2)
    private BigDecimal actualAmount;

    /**
     * 退款金额
     */
    @Column(name = "refund_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal refundAmount = BigDecimal.ZERO;

    /**
     * 手续费
     */
    @Column(name = "fee_amount", precision = 8, scale = 2, nullable = false)
    private BigDecimal feeAmount = BigDecimal.ZERO;

    /**
     * 第三方交易号
     */
    @Column(name = "trade_no", length = 100)
    private String tradeNo;

    /**
     * 第三方支付平台
     */
    @Column(name = "payment_platform", length = 50)
    private String paymentPlatform;

    /**
     * 支付渠道
     */
    @Column(name = "payment_channel", length = 50)
    private String paymentChannel;

    /**
     * 支付账户
     */
    @Column(name = "payment_account", length = 100)
    private String paymentAccount;

    /**
     * 银行编码
     */
    @Column(name = "bank_code", length = 20)
    private String bankCode;

    /**
     * 银行名称
     */
    @Column(name = "bank_name", length = 100)
    private String bankName;

    /**
     * 支付时间
     */
    @Column(name = "paid_at")
    private LocalDateTime paidAt;

    /**
     * 支付确认时间
     */
    @Column(name = "confirmed_at")
    private LocalDateTime confirmedAt;

    /**
     * 支付超时时间
     */
    @Column(name = "timeout_at")
    private LocalDateTime timeoutAt;

    /**
     * 退款时间
     */
    @Column(name = "refunded_at")
    private LocalDateTime refundedAt;

    /**
     * 退款原因
     */
    @Column(name = "refund_reason", length = 200)
    private String refundReason;

    /**
     * 退款单号
     */
    @Column(name = "refund_number", length = 50)
    private String refundNumber;

    /**
     * 支付回调数据（JSON格式）
     */
    @Column(name = "callback_data", columnDefinition = "TEXT")
    private String callbackData;

    /**
     * 支付请求数据（JSON格式）
     */
    @Column(name = "request_data", columnDefinition = "TEXT")
    private String requestData;

    /**
     * 支付响应数据（JSON格式）
     */
    @Column(name = "response_data", columnDefinition = "TEXT")
    private String responseData;

    /**
     * 错误码
     */
    @Column(name = "error_code", length = 50)
    private String errorCode;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    /**
     * 重试次数
     */
    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Column(name = "max_retry_count", nullable = false)
    private Integer maxRetryCount = 3;

    /**
     * 下次重试时间
     */
    @Column(name = "next_retry_at")
    private LocalDateTime nextRetryAt;

    /**
     * 是否自动重试
     */
    @Column(name = "auto_retry", nullable = false)
    private Boolean autoRetry = true;

    /**
     * 支付备注
     */
    @Column(name = "payment_remark", length = 500)
    private String paymentRemark;

    /**
     * 支付方式常量
     */
    public static class Method {
        public static final int ALIPAY = 1;        // 支付宝
        public static final int WECHAT = 2;        // 微信支付
        public static final int BANK_CARD = 3;     // 银行卡
        public static final int BALANCE = 4;       // 余额支付
        public static final int COD = 5;           // 货到付款
    }

    /**
     * 支付状态常量
     */
    public static class Status {
        public static final int PENDING = 1;        // 待支付
        public static final int PROCESSING = 2;     // 支付中
        public static final int SUCCESS = 3;        // 支付成功
        public static final int FAILED = 4;         // 支付失败
        public static final int REFUNDED = 5;       // 已退款
        public static final int PARTIAL_REFUNDED = 6; // 部分退款
    }

    /**
     * 判断是否支付成功
     */
    public boolean isPaymentSuccess() {
        return this.paymentStatus == Status.SUCCESS;
    }

    /**
     * 判断是否支付失败
     */
    public boolean isPaymentFailed() {
        return this.paymentStatus == Status.FAILED;
    }

    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return this.autoRetry
               && this.retryCount < this.maxRetryCount
               && (this.paymentStatus == Status.FAILED || this.paymentStatus == Status.PROCESSING);
    }

    /**
     * 判断是否已退款
     */
    public boolean isRefunded() {
        return this.paymentStatus == Status.REFUNDED
               || this.paymentStatus == Status.PARTIAL_REFUNDED;
    }

    /**
     * 判断是否可以退款
     */
    public boolean canRefund() {
        return this.paymentStatus == Status.SUCCESS
               && this.refundAmount.compareTo(this.actualAmount) < 0;
    }

    /**
     * 获取可退款金额
     */
    public BigDecimal getAvailableRefundAmount() {
        if (this.actualAmount == null || this.refundAmount == null) {
            return BigDecimal.ZERO;
        }
        return this.actualAmount.subtract(this.refundAmount);
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;

        // 计算下次重试时间（指数退避）
        if (this.canRetry()) {
            long delayMinutes = (long) Math.pow(2, this.retryCount) * 5; // 5, 10, 20, 40分钟
            this.nextRetryAt = LocalDateTime.now().plusMinutes(delayMinutes);
        }
    }

    /**
     * 设置支付成功
     */
    public void setPaymentSuccess(String tradeNo, BigDecimal actualAmount) {
        this.paymentStatus = Status.SUCCESS;
        this.tradeNo = tradeNo;
        this.actualAmount = actualAmount;
        this.paidAt = LocalDateTime.now();
        this.confirmedAt = LocalDateTime.now();
    }

    /**
     * 设置支付失败
     */
    public void setPaymentFailed(String errorCode, String errorMessage) {
        this.paymentStatus = Status.FAILED;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.incrementRetryCount();
    }

    /**
     * 设置退款
     */
    public void setRefund(BigDecimal refundAmount, String refundReason, String refundNumber) {
        this.refundAmount = this.refundAmount.add(refundAmount);
        this.refundReason = refundReason;
        this.refundNumber = refundNumber;
        this.refundedAt = LocalDateTime.now();

        // 判断是全额退款还是部分退款
        if (this.refundAmount.compareTo(this.actualAmount) >= 0) {
            this.paymentStatus = Status.REFUNDED;
        } else {
            this.paymentStatus = Status.PARTIAL_REFUNDED;
        }
    }
}
