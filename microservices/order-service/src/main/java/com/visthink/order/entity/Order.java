package com.visthink.order.entity;

import com.visthink.common.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单主表实体
 *
 * 管理订单的基本信息、状态流转、金额计算等核心功能
 * 支持多租户数据隔离和完整的订单生命周期管理
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "order_main", indexes = {
    @Index(name = "idx_order_tenant_id", columnList = "tenant_id"),
    @Index(name = "idx_order_number", columnList = "order_number"),
    @Index(name = "idx_order_status", columnList = "order_status"),
    @Index(name = "idx_order_user_id", columnList = "user_id"),
    @Index(name = "idx_order_create_time", columnList = "create_time"),
    @Index(name = "idx_order_payment_status", columnList = "payment_status")
})
@EqualsAndHashCode(callSuper = true)
public class Order extends BaseEntity {

    /**
     * 订单编号（系统内唯一）
     */
    @Column(name = "order_number", unique = true, nullable = false, length = 50)
    public String orderNumber;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name", length = 100)
    private String userName;

    /**
     * 订单类型：1-普通订单，2-预售订单，3-团购订单，4-秒杀订单
     */
    @Column(name = "order_type", nullable = false)
    private Integer orderType = 1;

    /**
     * 订单状态：1-待支付，2-已支付，3-已发货，4-已完成，5-已取消，6-已退款
     */
    @Column(name = "order_status", nullable = false)
    private Integer orderStatus = 1;

    /**
     * 支付状态：1-未支付，2-支付中，3-已支付，4-支付失败，5-已退款
     */
    @Column(name = "payment_status", nullable = false)
    private Integer paymentStatus = 1;

    /**
     * 发货状态：1-未发货，2-部分发货，3-已发货，4-已收货
     */
    @Column(name = "shipping_status", nullable = false)
    private Integer shippingStatus = 1;

    /**
     * 订单来源：1-PC端，2-移动端，3-小程序，4-APP，5-API
     */
    @Column(name = "order_source", nullable = false)
    private Integer orderSource = 1;

    /**
     * 订单渠道：1-直销，2-分销，3-平台（淘宝、京东等）
     */
    @Column(name = "order_channel")
    private Integer orderChannel = 1;

    /**
     * 外部订单号（第三方平台订单号）
     */
    @Column(name = "external_order_number", length = 100)
    private String externalOrderNumber;

    /**
     * 商品总金额（不含优惠）
     */
    @Column(name = "goods_amount", precision = 12, scale = 2, nullable = false)
    private BigDecimal goodsAmount = BigDecimal.ZERO;

    /**
     * 运费金额
     */
    @Column(name = "shipping_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal shippingAmount = BigDecimal.ZERO;

    /**
     * 优惠金额
     */
    @Column(name = "discount_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal discountAmount = BigDecimal.ZERO;

    /**
     * 税费金额
     */
    @Column(name = "tax_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * 订单总金额（实际支付金额）
     */
    @Column(name = "total_amount", precision = 12, scale = 2, nullable = false)
    private BigDecimal totalAmount = BigDecimal.ZERO;

    /**
     * 已支付金额
     */
    @Column(name = "paid_amount", precision = 12, scale = 2, nullable = false)
    private BigDecimal paidAmount = BigDecimal.ZERO;

    /**
     * 退款金额
     */
    @Column(name = "refund_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal refundAmount = BigDecimal.ZERO;

    /**
     * 收货人姓名
     */
    @Column(name = "receiver_name", length = 50)
    private String receiverName;

    /**
     * 收货人电话
     */
    @Column(name = "receiver_phone", length = 20)
    private String receiverPhone;

    /**
     * 收货人地址
     */
    @Column(name = "receiver_address", length = 500)
    private String receiverAddress;

    /**
     * 省份
     */
    @Column(name = "receiver_province", length = 50)
    private String receiverProvince;

    /**
     * 城市
     */
    @Column(name = "receiver_city", length = 50)
    private String receiverCity;

    /**
     * 区县
     */
    @Column(name = "receiver_district", length = 50)
    private String receiverDistrict;

    /**
     * 邮政编码
     */
    @Column(name = "receiver_zip_code", length = 10)
    private String receiverZipCode;

    /**
     * 买家留言
     */
    @Column(name = "buyer_message", length = 500)
    private String buyerMessage;

    /**
     * 卖家备注
     */
    @Column(name = "seller_remark", length = 500)
    private String sellerRemark;

    /**
     * 订单标签（JSON格式）
     */
    @Column(name = "order_tags", columnDefinition = "TEXT")
    private String orderTags;

    /**
     * 优惠券ID
     */
    @Column(name = "coupon_id")
    private Long couponId;

    /**
     * 优惠券编码
     */
    @Column(name = "coupon_code", length = 50)
    private String couponCode;

    /**
     * 促销活动ID
     */
    @Column(name = "promotion_id")
    private Long promotionId;

    /**
     * 支付时间
     */
    @Column(name = "paid_at")
    private LocalDateTime paidAt;

    /**
     * 发货时间
     */
    @Column(name = "shipped_at")
    private LocalDateTime shippedAt;

    /**
     * 完成时间
     */
    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    /**
     * 取消时间
     */
    @Column(name = "cancelled_at")
    private LocalDateTime cancelledAt;

    /**
     * 取消原因
     */
    @Column(name = "cancel_reason", length = 200)
    private String cancelReason;

    /**
     * 超时时间
     */
    @Column(name = "timeout_at")
    private LocalDateTime timeoutAt;

    /**
     * 是否需要发票
     */
    @Column(name = "need_invoice", nullable = false)
    private Boolean needInvoice = false;

    /**
     * 发票类型：1-普通发票，2-增值税发票
     */
    @Column(name = "invoice_type")
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    @Column(name = "invoice_title", length = 200)
    private String invoiceTitle;

    /**
     * 发票内容
     */
    @Column(name = "invoice_content", length = 200)
    private String invoiceContent;

    /**
     * 订单明细列表（一对多关系）
     */
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderItem> orderItems;

    /**
     * 支付记录列表（一对多关系）
     */
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderPayment> orderPayments;

    /**
     * 订单状态常量
     */
    public static class Status {
        public static final int PENDING_PAYMENT = 1;  // 待支付
        public static final int PAID = 2;             // 已支付
        public static final int SHIPPED = 3;          // 已发货
        public static final int COMPLETED = 4;        // 已完成
        public static final int CANCELLED = 5;        // 已取消
        public static final int REFUNDED = 6;         // 已退款
    }

    /**
     * 支付状态常量
     */
    public static class PaymentStatus {
        public static final int UNPAID = 1;      // 未支付
        public static final int PAYING = 2;      // 支付中
        public static final int PAID = 3;        // 已支付
        public static final int FAILED = 4;      // 支付失败
        public static final int REFUNDED = 5;    // 已退款
    }

    /**
     * 发货状态常量
     */
    public static class ShippingStatus {
        public static final int UNSHIPPED = 1;      // 未发货
        public static final int PARTIAL_SHIPPED = 2; // 部分发货
        public static final int SHIPPED = 3;        // 已发货
        public static final int RECEIVED = 4;       // 已收货
    }

    /**
     * 订单类型常量
     */
    public static class Type {
        public static final int NORMAL = 1;     // 普通订单
        public static final int PRESALE = 2;    // 预售订单
        public static final int GROUP = 3;      // 团购订单
        public static final int SECKILL = 4;    // 秒杀订单
    }

    /**
     * 计算订单总金额
     */
    public void calculateTotalAmount() {
        this.totalAmount = this.goodsAmount
                .add(this.shippingAmount)
                .add(this.taxAmount)
                .subtract(this.discountAmount);

        // 确保总金额不为负数
        if (this.totalAmount.compareTo(BigDecimal.ZERO) < 0) {
            this.totalAmount = BigDecimal.ZERO;
        }
    }

    /**
     * 判断订单是否可以支付
     */
    public boolean canPay() {
        return this.orderStatus == Status.PENDING_PAYMENT
               && this.paymentStatus == PaymentStatus.UNPAID;
    }

    /**
     * 判断订单是否可以发货
     */
    public boolean canShip() {
        return this.orderStatus == Status.PAID
               && this.paymentStatus == PaymentStatus.PAID
               && this.shippingStatus == ShippingStatus.UNSHIPPED;
    }

    /**
     * 判断订单是否可以取消
     */
    public boolean canCancel() {
        return this.orderStatus == Status.PENDING_PAYMENT
               || (this.orderStatus == Status.PAID && this.shippingStatus == ShippingStatus.UNSHIPPED);
    }

    /**
     * 判断订单是否已完成
     */
    public boolean isCompleted() {
        return this.orderStatus == Status.COMPLETED;
    }

    /**
     * 判断订单是否已取消
     */
    public boolean isCancelled() {
        return this.orderStatus == Status.CANCELLED;
    }

    // ==================== 兼容性方法 ====================

    /**
     * 设置订单号（兼容性方法）
     */
    public void setOrderNo(String orderNo) {
        this.orderNumber = orderNo;
    }

    /**
     * 获取订单号（兼容性方法）
     */
    public String getOrderNo() {
        return this.orderNumber;
    }

    /**
     * 设置应付金额（兼容性方法）
     */
    public void setPayableAmount(BigDecimal payableAmount) {
        this.totalAmount = payableAmount;
    }

    /**
     * 获取应付金额（兼容性方法）
     */
    public BigDecimal getPayableAmount() {
        return this.totalAmount;
    }

    /**
     * 设置客户ID（兼容性方法）
     */
    public void setCustomerId(Long customerId) {
        this.userId = customerId;
    }

    /**
     * 获取客户ID（兼容性方法）
     */
    public Long getCustomerId() {
        return this.userId;
    }

    /**
     * 设置客户名称（兼容性方法）
     */
    public void setCustomerName(String customerName) {
        this.userName = customerName;
    }

    /**
     * 获取客户名称（兼容性方法）
     */
    public String getCustomerName() {
        return this.userName;
    }

    /**
     * 设置订单状态（字符串格式）
     */
    public void setOrderStatus(String status) {
        switch (status) {
            case "PENDING":
                this.orderStatus = Status.PENDING_PAYMENT;
                break;
            case "PAID":
                this.orderStatus = Status.PAID;
                break;
            case "SHIPPED":
                this.orderStatus = Status.SHIPPED;
                break;
            case "COMPLETED":
                this.orderStatus = Status.COMPLETED;
                break;
            case "CANCELLED":
                this.orderStatus = Status.CANCELLED;
                break;
            default:
                this.orderStatus = Status.PENDING_PAYMENT;
        }
    }

    /**
     * 设置支付状态（字符串格式）
     */
    public void setPaymentStatus(String status) {
        switch (status) {
            case "UNPAID":
                this.paymentStatus = PaymentStatus.UNPAID;
                break;
            case "PAID":
                this.paymentStatus = PaymentStatus.PAID;
                break;
            case "FAILED":
                this.paymentStatus = PaymentStatus.FAILED;
                break;
            default:
                this.paymentStatus = PaymentStatus.UNPAID;
        }
    }

    /**
     * 设置发货状态（字符串格式）
     */
    public void setShippingStatus(String status) {
        switch (status) {
            case "PENDING":
                this.shippingStatus = ShippingStatus.UNSHIPPED;
                break;
            case "SHIPPED":
                this.shippingStatus = ShippingStatus.SHIPPED;
                break;
            case "RECEIVED":
                this.shippingStatus = ShippingStatus.RECEIVED;
                break;
            default:
                this.shippingStatus = ShippingStatus.UNSHIPPED;
        }
    }

    /**
     * 设置取消时间（兼容性方法）
     */
    public void setCancelTime(LocalDateTime cancelTime) {
        this.cancelledAt = cancelTime;
    }

    /**
     * 获取取消时间（兼容性方法）
     */
    public LocalDateTime getCancelTime() {
        return this.cancelledAt;
    }

    /**
     * 设置确认时间（兼容性方法）
     */
    public void setConfirmTime(LocalDateTime confirmTime) {
        // 可以使用 paidAt 作为确认时间
        this.paidAt = confirmTime;
    }

    /**
     * 获取确认时间（兼容性方法）
     */
    public LocalDateTime getConfirmTime() {
        return this.paidAt;
    }

    /**
     * 设置发货时间（兼容性方法）
     */
    public void setShipTime(LocalDateTime shipTime) {
        this.shippedAt = shipTime;
    }

    /**
     * 获取发货时间（兼容性方法）
     */
    public LocalDateTime getShipTime() {
        return this.shippedAt;
    }

    /**
     * 设置完成时间（兼容性方法）
     */
    public void setCompleteTime(LocalDateTime completeTime) {
        this.completedAt = completeTime;
    }

    /**
     * 获取完成时间（兼容性方法）
     */
    public LocalDateTime getCompleteTime() {
        return this.completedAt;
    }

    /**
     * 设置收货地址（兼容性方法）
     */
    public void setShippingAddress(String shippingAddress) {
        this.receiverAddress = shippingAddress;
    }

    /**
     * 获取收货地址（兼容性方法）
     */
    public String getShippingAddress() {
        return this.receiverAddress;
    }

    /**
     * 设置备注（兼容性方法）
     */
    public void setRemark(String remark) {
        this.sellerRemark = remark;
    }

    /**
     * 获取备注（兼容性方法）
     */
    public String getRemark() {
        return this.sellerRemark;
    }
}
