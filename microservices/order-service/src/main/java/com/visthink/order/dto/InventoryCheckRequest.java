package com.visthink.order.dto;

import java.util.List;

/**
 * 库存检查请求
 * 
 * 用于批量检查商品库存是否充足
 * 
 * <AUTHOR>
 */
public class InventoryCheckRequest {
    
    private List<CheckItem> items;
    
    public List<CheckItem> getItems() {
        return items;
    }
    
    public void setItems(List<CheckItem> items) {
        this.items = items;
    }
    
    /**
     * 检查项
     */
    public static class CheckItem {
        private Long skuId;
        private Integer quantity;
        private Long warehouseId; // 可选，指定仓库
        
        public Long getSkuId() {
            return skuId;
        }
        
        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }
        
        public Integer getQuantity() {
            return quantity;
        }
        
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        
        public Long getWarehouseId() {
            return warehouseId;
        }
        
        public void setWarehouseId(Long warehouseId) {
            this.warehouseId = warehouseId;
        }
    }
}
