package com.visthink.order.client;

import com.visthink.common.dto.ApiResponse;
import com.visthink.order.dto.ProductInfo;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

/**
 * 商品服务客户端
 * 
 * 用于订单服务调用商品服务的商品信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RegisterRestClient(configKey = "product-service")
@Path("/api/products")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface ProductServiceClient {

    /**
     * 根据商品ID获取商品信息
     * 
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 商品信息
     */
    @GET
    @Path("/{productId}")
    Uni<ApiResponse<ProductInfo>> getProductById(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                @PathParam("productId") Long productId);

    /**
     * 根据SKU ID获取商品信息
     * 
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @return 商品信息
     */
    @GET
    @Path("/sku/{skuId}")
    Uni<ApiResponse<ProductInfo>> getProductBySkuId(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                   @PathParam("skuId") Long skuId);

    /**
     * 批量获取商品信息
     * 
     * @param tenantId 租户ID
     * @param productIds 商品ID列表
     * @return 商品信息列表
     */
    @POST
    @Path("/batch")
    Uni<ApiResponse<List<ProductInfo>>> getBatchProducts(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                        List<Long> productIds);

    /**
     * 验证商品是否存在且可售
     * 
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 验证结果
     */
    @GET
    @Path("/{productId}/validate")
    Uni<ApiResponse<Boolean>> validateProduct(@HeaderParam("X-Tenant-Id") Long tenantId,
                                             @PathParam("productId") Long productId);

    /**
     * 获取商品价格信息
     * 
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @param quantity 数量
     * @return 价格信息
     */
    @GET
    @Path("/{productId}/price")
    Uni<ApiResponse<ProductPriceInfo>> getProductPrice(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                      @PathParam("productId") Long productId,
                                                      @QueryParam("quantity") Integer quantity);

    /**
     * 商品价格信息DTO
     */
    class ProductPriceInfo {
        private Long productId;
        private java.math.BigDecimal unitPrice;
        private java.math.BigDecimal totalPrice;
        private java.math.BigDecimal discountAmount;
        private String priceType;

        // Getters and Setters
        public Long getProductId() {
            return productId;
        }

        public void setProductId(Long productId) {
            this.productId = productId;
        }

        public java.math.BigDecimal getUnitPrice() {
            return unitPrice;
        }

        public void setUnitPrice(java.math.BigDecimal unitPrice) {
            this.unitPrice = unitPrice;
        }

        public java.math.BigDecimal getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(java.math.BigDecimal totalPrice) {
            this.totalPrice = totalPrice;
        }

        public java.math.BigDecimal getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(java.math.BigDecimal discountAmount) {
            this.discountAmount = discountAmount;
        }

        public String getPriceType() {
            return priceType;
        }

        public void setPriceType(String priceType) {
            this.priceType = priceType;
        }
    }
}
