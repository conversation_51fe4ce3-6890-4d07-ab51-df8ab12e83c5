package com.visthink.order.dto;

import java.util.List;

/**
 * 库存预占结果
 * 
 * 返回库存预占的详细结果
 * 
 * <AUTHOR>
 */
public class InventoryReserveResult {
    
    private boolean success;
    private String reserveNo; // 预占单号
    private List<ReserveResultItem> items;
    private String message;
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getReserveNo() {
        return reserveNo;
    }
    
    public void setReserveNo(String reserveNo) {
        this.reserveNo = reserveNo;
    }
    
    public List<ReserveResultItem> getItems() {
        return items;
    }
    
    public void setItems(List<ReserveResultItem> items) {
        this.items = items;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    /**
     * 预占结果项
     */
    public static class ReserveResultItem {
        private Long skuId;
        private String skuCode;
        private Integer requestedQuantity;
        private Integer reservedQuantity;
        private boolean success;
        private String message;
        
        public Long getSkuId() {
            return skuId;
        }
        
        public void setSkuId(Long skuId) {
            this.skuId = skuId;
        }
        
        public String getSkuCode() {
            return skuCode;
        }
        
        public void setSkuCode(String skuCode) {
            this.skuCode = skuCode;
        }
        
        public Integer getRequestedQuantity() {
            return requestedQuantity;
        }
        
        public void setRequestedQuantity(Integer requestedQuantity) {
            this.requestedQuantity = requestedQuantity;
        }
        
        public Integer getReservedQuantity() {
            return reservedQuantity;
        }
        
        public void setReservedQuantity(Integer reservedQuantity) {
            this.reservedQuantity = reservedQuantity;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
    }
}
