package com.visthink.order.client;

import com.visthink.common.dto.ApiResponse;
import com.visthink.order.dto.UserInfo;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;

/**
 * 用户服务客户端
 * 
 * 用于订单服务调用用户中心的用户信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RegisterRestClient(configKey = "member-service")
@Path("/api/users")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface MemberServiceClient {

    /**
     * 根据用户ID获取用户信息
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户信息
     */
    @GET
    @Path("/{userId}")
    Uni<ApiResponse<UserInfo>> getUserById(@HeaderParam("X-Tenant-Id") Long tenantId,
                                          @PathParam("userId") Long userId);

    /**
     * 根据用户名获取用户信息
     * 
     * @param tenantId 租户ID
     * @param username 用户名
     * @return 用户信息
     */
    @GET
    @Path("/username/{username}")
    Uni<ApiResponse<UserInfo>> getUserByUsername(@HeaderParam("X-Tenant-Id") Long tenantId,
                                                @PathParam("username") String username);

    /**
     * 验证用户是否存在且状态正常
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 验证结果
     */
    @GET
    @Path("/{userId}/validate")
    Uni<ApiResponse<Boolean>> validateUser(@HeaderParam("X-Tenant-Id") Long tenantId,
                                          @PathParam("userId") Long userId);
}
