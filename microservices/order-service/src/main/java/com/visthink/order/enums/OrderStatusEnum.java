package com.visthink.order.enums;

/**
 * 订单状态枚举
 */
public enum OrderStatusEnum {

    PENDING_PAYMENT(1, "待付款"),
    PENDING_SHIPMENT(2, "待发货"),
    SHIPPED(3, "已发货"),
    COMPLETED(4, "已完成"),
    CANCELLED(5, "已取消"),
    REFUNDING(6, "退款中"),
    REFUNDED(7, "已退款");

    private final Integer code;
    private final String description;

    OrderStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static OrderStatusEnum getByCode(Integer code) {
        for (OrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效状态
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 检查是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING_PAYMENT || this == PENDING_SHIPMENT;
    }

    /**
     * 检查是否可以发货
     */
    public boolean canShip() {
        return this == PENDING_SHIPMENT;
    }

    /**
     * 检查是否可以确认收货
     */
    public boolean canConfirmDelivery() {
        return this == SHIPPED;
    }
}
