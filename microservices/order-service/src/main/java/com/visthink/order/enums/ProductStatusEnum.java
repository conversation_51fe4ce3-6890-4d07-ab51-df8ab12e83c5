package com.visthink.erp.core.common.enums;

/**
 * 商品状态枚举
 */
public enum ProductStatusEnum {
    
    NORMAL(1, "正常"),
    DISABLED(2, "停售"),
    DELETED(3, "删除");

    private final Integer code;
    private final String description;

    ProductStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     */
    public static ProductStatusEnum getByCode(Integer code) {
        for (ProductStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效状态
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
