package com.visthink.order.dto;

import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单更新请求DTO
 *
 * 用于接收更新订单的请求参数，支持部分字段更新
 * 提供完整的参数验证和业务规则校验
 *
 * <AUTHOR>
 */
@Data
public class OrderUpdateRequest {

    /**
     * 客户名称（兼容字段）
     */
    public String getCustomerName() {
        return this.receiverName;
    }

    public void setCustomerName(String customerName) {
        this.receiverName = customerName;
    }

    /**
     * 收货地址（兼容字段）
     */
    public String getShippingAddress() {
        return this.receiverAddress;
    }

    public void setShippingAddress(String shippingAddress) {
        this.receiverAddress = shippingAddress;
    }

    /**
     * 收货人姓名
     */
    @Size(max = 50, message = "收货人姓名长度不能超过50个字符")
    private String receiverName;

    /**
     * 收货人电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "收货人电话格式不正确")
    private String receiverPhone;

    /**
     * 收货人地址
     */
    @Size(max = 500, message = "收货人地址长度不能超过500个字符")
    private String receiverAddress;

    /**
     * 省份
     */
    @Size(max = 50, message = "省份长度不能超过50个字符")
    private String receiverProvince;

    /**
     * 城市
     */
    @Size(max = 50, message = "城市长度不能超过50个字符")
    private String receiverCity;

    /**
     * 区县
     */
    @Size(max = 50, message = "区县长度不能超过50个字符")
    private String receiverDistrict;

    /**
     * 邮政编码
     */
    @Pattern(regexp = "^\\d{6}$", message = "邮政编码格式不正确")
    private String receiverZipCode;

    /**
     * 买家留言
     */
    @Size(max = 500, message = "买家留言长度不能超过500个字符")
    private String buyerMessage;

    /**
     * 卖家备注
     */
    @Size(max = 500, message = "卖家备注长度不能超过500个字符")
    private String sellerRemark;

    /**
     * 运费金额
     */
    @DecimalMin(value = "0.00", message = "运费金额不能为负数")
    @Digits(integer = 8, fraction = 2, message = "运费金额格式不正确")
    private BigDecimal shippingAmount;

    /**
     * 优惠金额
     */
    @DecimalMin(value = "0.00", message = "优惠金额不能为负数")
    @Digits(integer = 8, fraction = 2, message = "优惠金额格式不正确")
    private BigDecimal discountAmount;

    /**
     * 税费金额
     */
    @DecimalMin(value = "0.00", message = "税费金额不能为负数")
    @Digits(integer = 8, fraction = 2, message = "税费金额格式不正确")
    private BigDecimal taxAmount;

    /**
     * 订单标签（JSON格式）
     */
    private String orderTags;

    /**
     * 优惠券ID
     */
    private Long couponId;

    /**
     * 优惠券编码
     */
    @Size(max = 50, message = "优惠券编码长度不能超过50个字符")
    private String couponCode;

    /**
     * 促销活动ID
     */
    private Long promotionId;

    /**
     * 是否需要发票
     */
    private Boolean needInvoice;

    /**
     * 发票类型：1-普通发票，2-增值税发票
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    @Size(max = 200, message = "发票抬头长度不能超过200个字符")
    private String invoiceTitle;

    /**
     * 发票内容
     */
    @Size(max = 200, message = "发票内容长度不能超过200个字符")
    private String invoiceContent;

    /**
     * 备注
     */
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    /**
     * 验证更新数据
     */
    public void validate() {
        // 验证发票信息
        if (Boolean.TRUE.equals(needInvoice)) {
            if (invoiceType == null) {
                throw new IllegalArgumentException("需要发票时，发票类型不能为空");
            }
            if (invoiceTitle == null || invoiceTitle.trim().isEmpty()) {
                throw new IllegalArgumentException("需要发票时，发票抬头不能为空");
            }
        }

        // 验证发票类型
        if (invoiceType != null && (invoiceType < 1 || invoiceType > 2)) {
            throw new IllegalArgumentException("发票类型值无效");
        }
    }

    /**
     * 判断是否有收货地址更新
     */
    public boolean hasAddressUpdate() {
        return receiverName != null || receiverPhone != null || receiverAddress != null ||
               receiverProvince != null || receiverCity != null || receiverDistrict != null ||
               receiverZipCode != null;
    }

    /**
     * 判断是否有金额更新
     */
    public boolean hasAmountUpdate() {
        return shippingAmount != null || discountAmount != null || taxAmount != null;
    }

    /**
     * 判断是否有发票信息更新
     */
    public boolean hasInvoiceUpdate() {
        return needInvoice != null || invoiceType != null || invoiceTitle != null || invoiceContent != null;
    }

    /**
     * 判断是否有优惠信息更新
     */
    public boolean hasPromotionUpdate() {
        return couponId != null || couponCode != null || promotionId != null;
    }

    /**
     * 判断是否为空更新（没有任何字段需要更新）
     */
    public boolean isEmpty() {
        return receiverName == null && receiverPhone == null && receiverAddress == null &&
               receiverProvince == null && receiverCity == null && receiverDistrict == null &&
               receiverZipCode == null && buyerMessage == null && sellerRemark == null &&
               shippingAmount == null && discountAmount == null && taxAmount == null &&
               orderTags == null && couponId == null && couponCode == null && promotionId == null &&
               needInvoice == null && invoiceType == null && invoiceTitle == null &&
               invoiceContent == null && remark == null;
    }
}
