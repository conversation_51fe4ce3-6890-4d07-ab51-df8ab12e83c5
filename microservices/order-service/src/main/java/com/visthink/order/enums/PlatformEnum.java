package com.visthink.erp.core.common.enums;

/**
 * 电商平台枚举
 */
public enum PlatformEnum {
    
    TAOBAO("taobao", "淘宝", "https://eco.taobao.com/router/rest"),
    TMALL("tmall", "天猫", "https://eco.taobao.com/router/rest"),
    J<PERSON>("jd", "京东", "https://api.jd.com/routerjson"),
    PDD("pdd", "拼多多", "https://gw-api.pinduoduo.com/api/router"),
    DOUYIN("douyin", "抖音", "https://openapi-fxg.jinritemai.com"),
    KUAISHOU("kuaishou", "快手", "https://open.kwaixiaodian.com");

    private final String code;
    private final String name;
    private final String apiBaseUrl;

    PlatformEnum(String code, String name, String apiBaseUrl) {
        this.code = code;
        this.name = name;
        this.apiBaseUrl = apiBaseUrl;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    /**
     * 根据代码获取枚举
     */
    public static PlatformEnum getByCode(String code) {
        for (PlatformEnum platform : values()) {
            if (platform.getCode().equals(code)) {
                return platform;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效平台
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }

    /**
     * 获取所有平台代码
     */
    public static String[] getAllCodes() {
        PlatformEnum[] platforms = values();
        String[] codes = new String[platforms.length];
        for (int i = 0; i < platforms.length; i++) {
            codes[i] = platforms[i].getCode();
        }
        return codes;
    }
}
