package com.visthink.order.dto;

import java.time.LocalDateTime;

/**
 * 用户信息DTO
 * 
 * 用于订单服务获取用户基本信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class UserInfo {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 账户状态
     */
    private Integer accountStatus;
    
    /**
     * 用户类型
     */
    private Integer userType;
    
    /**
     * 是否激活
     */
    private Boolean isActivated;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // ==================== Getters and Setters ====================

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Boolean getIsActivated() {
        return isActivated;
    }

    public void setIsActivated(Boolean isActivated) {
        this.isActivated = isActivated;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    // ==================== 业务方法 ====================

    /**
     * 判断用户是否正常状态
     */
    public boolean isNormal() {
        return accountStatus != null && accountStatus == 1 && 
               isActivated != null && isActivated;
    }

    /**
     * 判断用户是否被锁定
     */
    public boolean isLocked() {
        return accountStatus != null && accountStatus == 2;
    }

    /**
     * 判断用户是否被禁用
     */
    public boolean isDisabled() {
        return accountStatus != null && accountStatus == 3;
    }

    @Override
    public String toString() {
        return "UserInfo{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", username='" + username + '\'' +
                ", nickname='" + nickname + '\'' +
                ", email='" + email + '\'' +
                ", phone='" + phone + '\'' +
                ", accountStatus=" + accountStatus +
                ", userType=" + userType +
                ", isActivated=" + isActivated +
                '}';
    }
}
