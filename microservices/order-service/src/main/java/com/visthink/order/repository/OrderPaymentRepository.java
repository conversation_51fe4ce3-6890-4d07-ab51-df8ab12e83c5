package com.visthink.order.repository;

import com.visthink.common.repository.BaseRepository;
import com.visthink.order.entity.OrderPayment;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单支付记录数据访问接口
 *
 * 提供订单支付相关的数据库操作，包括基础CRUD、状态查询、统计分析等功能
 * 继承BaseRepository获得通用的数据访问能力
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class OrderPaymentRepository implements BaseRepository<OrderPayment> {

    /**
     * 根据订单ID查询支付记录列表
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findByOrderId(Long tenantId, Long orderId) {
        return find("tenantId = ?1 and orderId = ?2 and deleted = 0 order by createTime desc",
                   tenantId, orderId).list();
    }

    /**
     * 根据支付单号查询支付记录
     *
     * @param tenantId 租户ID
     * @param paymentNumber 支付单号
     * @return 支付记录
     */
    public Uni<OrderPayment> findByPaymentNumber(Long tenantId, String paymentNumber) {
        return find("tenantId = ?1 and paymentNumber = ?2 and deleted = 0",
                   tenantId, paymentNumber).firstResult();
    }

    /**
     * 根据第三方交易号查询支付记录
     *
     * @param tenantId 租户ID
     * @param tradeNo 第三方交易号
     * @return 支付记录
     */
    public Uni<OrderPayment> findByTradeNo(Long tenantId, String tradeNo) {
        return find("tenantId = ?1 and tradeNo = ?2 and deleted = 0",
                   tenantId, tradeNo).firstResult();
    }

    /**
     * 根据支付状态查询支付记录列表
     *
     * @param tenantId 租户ID
     * @param paymentStatus 支付状态
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findByPaymentStatus(Long tenantId, Integer paymentStatus) {
        return find("tenantId = ?1 and paymentStatus = ?2 and deleted = 0 order by createTime desc",
                   tenantId, paymentStatus).list();
    }

    /**
     * 根据支付方式查询支付记录列表
     *
     * @param tenantId 租户ID
     * @param paymentMethod 支付方式
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findByPaymentMethod(Long tenantId, Integer paymentMethod) {
        return find("tenantId = ?1 and paymentMethod = ?2 and deleted = 0 order by createTime desc",
                   tenantId, paymentMethod).list();
    }

    /**
     * 查询待支付的支付记录
     *
     * @param tenantId 租户ID
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findPendingPayments(Long tenantId) {
        return findByPaymentStatus(tenantId, OrderPayment.Status.PENDING);
    }

    /**
     * 查询支付中的支付记录
     *
     * @param tenantId 租户ID
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findProcessingPayments(Long tenantId) {
        return findByPaymentStatus(tenantId, OrderPayment.Status.PROCESSING);
    }

    /**
     * 查询支付成功的支付记录
     *
     * @param tenantId 租户ID
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findSuccessPayments(Long tenantId) {
        return findByPaymentStatus(tenantId, OrderPayment.Status.SUCCESS);
    }

    /**
     * 查询支付失败的支付记录
     *
     * @param tenantId 租户ID
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findFailedPayments(Long tenantId) {
        return findByPaymentStatus(tenantId, OrderPayment.Status.FAILED);
    }

    /**
     * 查询超时未支付的支付记录
     *
     * @param tenantId 租户ID
     * @param timeoutAt 超时时间
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findTimeoutPayments(Long tenantId, LocalDateTime timeoutAt) {
        return find("tenantId = ?1 and paymentStatus in (?2, ?3) and timeoutAt <= ?4 and deleted = 0",
                   tenantId, OrderPayment.Status.PENDING, OrderPayment.Status.PROCESSING, timeoutAt).list();
    }

    /**
     * 查询需要重试的支付记录
     *
     * @param tenantId 租户ID
     * @param retryTime 重试时间
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findRetryPayments(Long tenantId, LocalDateTime retryTime) {
        return find("tenantId = ?1 and paymentStatus = ?2 and autoRetry = true and retryCount < maxRetryCount and nextRetryAt <= ?3 and deleted = 0",
                   tenantId, OrderPayment.Status.FAILED, retryTime).list();
    }

    /**
     * 根据退款单号查询支付记录
     *
     * @param tenantId 租户ID
     * @param refundNumber 退款单号
     * @return 支付记录
     */
    public Uni<OrderPayment> findByRefundNumber(Long tenantId, String refundNumber) {
        return find("tenantId = ?1 and refundNumber = ?2 and deleted = 0",
                   tenantId, refundNumber).firstResult();
    }

    /**
     * 查询已退款的支付记录
     *
     * @param tenantId 租户ID
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findRefundedPayments(Long tenantId) {
        return find("tenantId = ?1 and paymentStatus in (?2, ?3) and deleted = 0 order by refundedAt desc",
                   tenantId, OrderPayment.Status.REFUNDED, OrderPayment.Status.PARTIAL_REFUNDED).list();
    }

    /**
     * 统计订单的支付记录数量
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 支付记录数量
     */
    public Uni<Long> countByOrderId(Long tenantId, Long orderId) {
        return count("tenantId = ?1 and orderId = ?2 and deleted = 0", tenantId, orderId);
    }

    /**
     * 统计指定状态的支付记录数量
     *
     * @param tenantId 租户ID
     * @param paymentStatus 支付状态
     * @return 支付记录数量
     */
    public Uni<Long> countByPaymentStatus(Long tenantId, Integer paymentStatus) {
        return count("tenantId = ?1 and paymentStatus = ?2 and deleted = 0", tenantId, paymentStatus);
    }

    /**
     * 统计指定支付方式的支付记录数量
     *
     * @param tenantId 租户ID
     * @param paymentMethod 支付方式
     * @return 支付记录数量
     */
    public Uni<Long> countByPaymentMethod(Long tenantId, Integer paymentMethod) {
        return count("tenantId = ?1 and paymentMethod = ?2 and deleted = 0", tenantId, paymentMethod);
    }

    /**
     * 统计指定时间范围内的支付记录数量
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 支付记录数量
     */
    public Uni<Long> countByTimeRange(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return count("tenantId = ?1 and createTime >= ?2 and createTime <= ?3 and deleted = 0",
                    tenantId, startTime, endTime);
    }

    /**
     * 查询指定时间范围内的支付记录
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findByTimeRange(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return find("tenantId = ?1 and createTime >= ?2 and createTime <= ?3 and deleted = 0 order by createTime desc",
                   tenantId, startTime, endTime).list();
    }

    /**
     * 查询今日支付记录
     *
     * @param tenantId 租户ID
     * @return 支付记录列表
     */
    public Uni<List<OrderPayment>> findTodayPayments(Long tenantId) {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        return findByTimeRange(tenantId, startOfDay, endOfDay);
    }

    /**
     * 统计今日支付记录数量
     *
     * @param tenantId 租户ID
     * @return 支付记录数量
     */
    public Uni<Long> countTodayPayments(Long tenantId) {
        LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endOfDay = startOfDay.plusDays(1).minusNanos(1);
        return countByTimeRange(tenantId, startOfDay, endOfDay);
    }

    /**
     * 更新支付状态
     *
     * @param tenantId 租户ID
     * @param paymentId 支付ID
     * @param paymentStatus 支付状态
     * @return 更新结果
     */
    public Uni<Boolean> updatePaymentStatus(Long tenantId, Long paymentId, Integer paymentStatus) {
        return update("paymentStatus = ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                     paymentStatus, tenantId, paymentId)
                .map(count -> count > 0);
    }

    /**
     * 更新第三方交易号
     *
     * @param tenantId 租户ID
     * @param paymentId 支付ID
     * @param tradeNo 第三方交易号
     * @return 更新结果
     */
    public Uni<Boolean> updateTradeNo(Long tenantId, Long paymentId, String tradeNo) {
        return update("tradeNo = ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                     tradeNo, tenantId, paymentId)
                .map(count -> count > 0);
    }

    /**
     * 增加重试次数
     *
     * @param tenantId 租户ID
     * @param paymentId 支付ID
     * @param nextRetryAt 下次重试时间
     * @return 更新结果
     */
    public Uni<Boolean> incrementRetryCount(Long tenantId, Long paymentId, LocalDateTime nextRetryAt) {
        return update("retryCount = retryCount + 1, nextRetryAt = ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                     nextRetryAt, tenantId, paymentId)
                .map(count -> count > 0);
    }

    /**
     * 批量更新支付状态
     *
     * @param tenantId 租户ID
     * @param paymentIds 支付ID列表
     * @param paymentStatus 支付状态
     * @return 更新数量
     */
    public Uni<Long> batchUpdatePaymentStatus(Long tenantId, List<Long> paymentIds, Integer paymentStatus) {
        if (paymentIds == null || paymentIds.isEmpty()) {
            return Uni.createFrom().item(0L);
        }

        StringBuilder query = new StringBuilder("paymentStatus = ?1, updateTime = current_timestamp where tenantId = ?2 and id in (");
        for (int i = 0; i < paymentIds.size(); i++) {
            if (i > 0) query.append(",");
            query.append("?").append(i + 3);
        }
        query.append(") and deleted = 0");

        Object[] params = new Object[paymentIds.size() + 2];
        params[0] = paymentStatus;
        params[1] = tenantId;
        for (int i = 0; i < paymentIds.size(); i++) {
            params[i + 2] = paymentIds.get(i);
        }

        return update(query.toString(), params)
                .onItem().transform(count -> count.longValue());
    }
}
