package com.visthink.order.repository;

import com.visthink.order.entity.OrderItem;
import com.visthink.common.repository.BaseRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 订单明细数据访问接口
 *
 * 提供订单明细相关的数据库操作，包括基础CRUD、关联查询、统计分析等功能
 * 继承BaseRepository获得通用的数据访问能力
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class OrderItemRepository implements BaseRepository<OrderItem> {

    /**
     * 根据订单ID查询订单明细列表
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findByOrderId(Long tenantId, Long orderId) {
        return find("tenantId = ?1 and orderId = ?2 and deleted = 0 order by id",
                   tenantId, orderId).list();
    }

    /**
     * 根据商品ID查询订单明细列表
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findByProductId(Long tenantId, Long productId) {
        return find("tenantId = ?1 and productId = ?2 and deleted = 0 order by createTime desc",
                   tenantId, productId).list();
    }

    /**
     * 根据SKU ID查询订单明细列表
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findBySkuId(Long tenantId, Long skuId) {
        return find("tenantId = ?1 and skuId = ?2 and deleted = 0 order by createTime desc",
                   tenantId, skuId).list();
    }

    /**
     * 根据发货状态查询订单明细列表
     *
     * @param tenantId 租户ID
     * @param shippingStatus 发货状态
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findByShippingStatus(Long tenantId, Integer shippingStatus) {
        return find("tenantId = ?1 and shippingStatus = ?2 and deleted = 0 order by createTime desc",
                   tenantId, shippingStatus).list();
    }

    /**
     * 查询待发货的订单明细
     *
     * @param tenantId 租户ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findPendingShipmentItems(Long tenantId) {
        return findByShippingStatus(tenantId, OrderItem.ShippingStatus.UNSHIPPED);
    }

    /**
     * 查询部分发货的订单明细
     *
     * @param tenantId 租户ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findPartialShippedItems(Long tenantId) {
        return findByShippingStatus(tenantId, OrderItem.ShippingStatus.PARTIAL_SHIPPED);
    }

    /**
     * 根据仓库ID查询订单明细列表
     *
     * @param tenantId 租户ID
     * @param warehouseId 仓库ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findByWarehouseId(Long tenantId, Long warehouseId) {
        return find("tenantId = ?1 and warehouseId = ?2 and deleted = 0 order by createTime desc",
                   tenantId, warehouseId).list();
    }

    /**
     * 根据供应商ID查询订单明细列表
     *
     * @param tenantId 租户ID
     * @param supplierId 供应商ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findBySupplierId(Long tenantId, Long supplierId) {
        return find("tenantId = ?1 and supplierId = ?2 and deleted = 0 order by createTime desc",
                   tenantId, supplierId).list();
    }

    /**
     * 查询赠品订单明细
     *
     * @param tenantId 租户ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findGiftItems(Long tenantId) {
        return find("tenantId = ?1 and isGift = true and deleted = 0 order by createTime desc",
                   tenantId).list();
    }

    /**
     * 根据促销活动ID查询订单明细列表
     *
     * @param tenantId 租户ID
     * @param promotionId 促销活动ID
     * @return 订单明细列表
     */
    public Uni<List<OrderItem>> findByPromotionId(Long tenantId, Long promotionId) {
        return find("tenantId = ?1 and promotionId = ?2 and deleted = 0 order by createTime desc",
                   tenantId, promotionId).list();
    }

    /**
     * 统计商品销售数量
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 销售数量
     */
    public Uni<Long> countSoldQuantityByProductId(Long tenantId, Long productId) {
        return getSession()
                .flatMap(session -> session.createQuery(
                        "select coalesce(sum(oi.quantity), 0) from OrderItem oi where oi.tenantId = :tenantId and oi.productId = :productId and oi.deleted = 0",
                        Long.class)
                        .setParameter("tenantId", tenantId)
                        .setParameter("productId", productId)
                        .getSingleResult());
    }

    /**
     * 统计SKU销售数量
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @return 销售数量
     */
    public Uni<Long> countSoldQuantityBySkuId(Long tenantId, Long skuId) {
        return getSession()
                .flatMap(session -> session.createQuery(
                        "select coalesce(sum(oi.quantity), 0) from OrderItem oi where oi.tenantId = :tenantId and oi.skuId = :skuId and oi.deleted = 0",
                        Long.class)
                        .setParameter("tenantId", tenantId)
                        .setParameter("skuId", skuId)
                        .getSingleResult());
    }

    /**
     * 统计订单明细数量
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 明细数量
     */
    public Uni<Long> countByOrderId(Long tenantId, Long orderId) {
        return count("tenantId = ?1 and orderId = ?2 and deleted = 0", tenantId, orderId);
    }

    /**
     * 统计商品被购买次数
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 购买次数
     */
    public Uni<Long> countOrdersByProductId(Long tenantId, Long productId) {
        return count("tenantId = ?1 and productId = ?2 and deleted = 0", tenantId, productId);
    }

    /**
     * 查询热销商品（按销量排序）
     *
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return 商品ID和销量列表
     */
    public Uni<List<Object[]>> findHotProducts(Long tenantId, int limit) {
        return getSession()
                .flatMap(session -> session.createQuery(
                        "select oi.productId, sum(oi.quantity) as totalQuantity from OrderItem oi " +
                        "where oi.tenantId = :tenantId and oi.deleted = 0 " +
                        "group by oi.productId " +
                        "order by totalQuantity desc", Object[].class)
                        .setParameter("tenantId", tenantId)
                        .setMaxResults(limit)
                        .getResultList());
    }

    /**
     * 查询热销SKU（按销量排序）
     *
     * @param tenantId 租户ID
     * @param limit 限制数量
     * @return SKU ID和销量列表
     */
    public Uni<List<Object[]>> findHotSkus(Long tenantId, int limit) {
        return getSession()
                .flatMap(session -> session.createQuery(
                        "select oi.skuId, sum(oi.quantity) as totalQuantity from OrderItem oi " +
                        "where oi.tenantId = :tenantId and oi.skuId is not null and oi.deleted = 0 " +
                        "group by oi.skuId " +
                        "order by totalQuantity desc", Object[].class)
                        .setParameter("tenantId", tenantId)
                        .setMaxResults(limit)
                        .getResultList());
    }

    /**
     * 根据订单ID批量删除订单明细
     *
     * @param tenantId 租户ID
     * @param orderId 订单ID
     * @return 删除数量
     */
    public Uni<Long> deleteByOrderId(Long tenantId, Long orderId) {
        return update("deleted = 1, updateTime = current_timestamp where tenantId = ?1 and orderId = ?2 and deleted = 0",
                     tenantId, orderId)
                .map(Integer::longValue);
    }

    /**
     * 批量更新发货状态
     *
     * @param tenantId 租户ID
     * @param itemIds 明细ID列表
     * @param shippingStatus 发货状态
     * @return 更新数量
     */
    public Uni<Long> batchUpdateShippingStatus(Long tenantId, List<Long> itemIds, Integer shippingStatus) {
        if (itemIds == null || itemIds.isEmpty()) {
            return Uni.createFrom().item(0L);
        }

        StringBuilder query = new StringBuilder("shippingStatus = ?1, updateTime = current_timestamp where tenantId = ?2 and id in (");
        for (int i = 0; i < itemIds.size(); i++) {
            if (i > 0) query.append(",");
            query.append("?").append(i + 3);
        }
        query.append(") and deleted = 0");

        Object[] params = new Object[itemIds.size() + 2];
        params[0] = shippingStatus;
        params[1] = tenantId;
        for (int i = 0; i < itemIds.size(); i++) {
            params[i + 2] = itemIds.get(i);
        }

        return update(query.toString(), params)
                .map(count -> count.longValue());
    }

    /**
     * 更新已发货数量
     *
     * @param tenantId 租户ID
     * @param itemId 明细ID
     * @param shippedQuantity 已发货数量
     * @return 更新结果
     */
    public Uni<Boolean> updateShippedQuantity(Long tenantId, Long itemId, Integer shippedQuantity) {
        return update("shippedQuantity = ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                     shippedQuantity, tenantId, itemId)
                .map(count -> count > 0);
    }

    /**
     * 更新退货数量
     *
     * @param tenantId 租户ID
     * @param itemId 明细ID
     * @param returnQuantity 退货数量
     * @return 更新结果
     */
    public Uni<Boolean> updateReturnQuantity(Long tenantId, Long itemId, Integer returnQuantity) {
        return update("returnQuantity = ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                     returnQuantity, tenantId, itemId)
                .map(count -> count > 0);
    }

    /**
     * 更新退款数量
     *
     * @param tenantId 租户ID
     * @param itemId 明细ID
     * @param refundQuantity 退款数量
     * @return 更新结果
     */
    public Uni<Boolean> updateRefundQuantity(Long tenantId, Long itemId, Integer refundQuantity) {
        return update("refundQuantity = ?1, updateTime = current_timestamp where tenantId = ?2 and id = ?3 and deleted = 0",
                     refundQuantity, tenantId, itemId)
                .map(count -> count > 0);
    }
}
