package com.visthink.order.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品信息DTO
 * 
 * 用于订单服务获取商品基本信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ProductInfo {
    
    /**
     * 商品ID
     */
    private Long id;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 商品编码
     */
    private String productCode;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 商品分类ID
     */
    private Long categoryId;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * SKU ID
     */
    private Long skuId;
    
    /**
     * SKU 编码
     */
    private String skuCode;
    
    /**
     * 销售价格
     */
    private BigDecimal salePrice;
    
    /**
     * 市场价格
     */
    private BigDecimal marketPrice;
    
    /**
     * 成本价格
     */
    private BigDecimal costPrice;
    
    /**
     * 商品状态 (1:上架 2:下架 3:草稿)
     */
    private Integer status;
    
    /**
     * 是否可售
     */
    private Boolean isSaleable;
    
    /**
     * 重量(克)
     */
    private Integer weight;
    
    /**
     * 商品图片URL
     */
    private String imageUrl;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // ==================== Getters and Setters ====================

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getIsSaleable() {
        return isSaleable;
    }

    public void setIsSaleable(Boolean isSaleable) {
        this.isSaleable = isSaleable;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    // ==================== 业务方法 ====================

    /**
     * 判断商品是否可售
     */
    public boolean isAvailableForSale() {
        return status != null && status == 1 && 
               isSaleable != null && isSaleable;
    }

    /**
     * 判断商品是否上架
     */
    public boolean isOnSale() {
        return status != null && status == 1;
    }

    /**
     * 判断商品是否下架
     */
    public boolean isOffSale() {
        return status != null && status == 2;
    }

    @Override
    public String toString() {
        return "ProductInfo{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", productCode='" + productCode + '\'' +
                ", productName='" + productName + '\'' +
                ", skuId=" + skuId +
                ", skuCode='" + skuCode + '\'' +
                ", salePrice=" + salePrice +
                ", status=" + status +
                ", isSaleable=" + isSaleable +
                '}';
    }
}
