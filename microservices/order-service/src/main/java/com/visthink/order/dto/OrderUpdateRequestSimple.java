package com.visthink.order.dto;

import java.math.BigDecimal;

/**
 * 订单更新请求DTO - 简化版本
 * 
 * 用于更新订单的基础信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class OrderUpdateRequestSimple {
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 收货地址
     */
    private String shippingAddress;
    
    /**
     * 收货人姓名
     */
    private String receiverName;
    
    /**
     * 收货人电话
     */
    private String receiverPhone;
    
    /**
     * 买家留言
     */
    private String buyerMessage;
    
    /**
     * 卖家备注
     */
    private String remark;
    
    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    // ==================== Constructors ====================

    public OrderUpdateRequestSimple() {
    }

    public OrderUpdateRequestSimple(String customerName, String shippingAddress) {
        this.customerName = customerName;
        this.shippingAddress = shippingAddress;
    }

    // ==================== Getters and Setters ====================

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getShippingAddress() {
        return shippingAddress;
    }

    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    // ==================== 业务方法 ====================

    /**
     * 检查是否有任何字段需要更新
     */
    public boolean hasUpdates() {
        return customerName != null || 
               shippingAddress != null || 
               receiverName != null || 
               receiverPhone != null || 
               buyerMessage != null || 
               remark != null || 
               totalAmount != null;
    }

    /**
     * 检查是否只更新基本信息
     */
    public boolean isBasicInfoUpdate() {
        return (customerName != null || shippingAddress != null || 
                receiverName != null || receiverPhone != null) &&
               totalAmount == null;
    }

    /**
     * 检查是否包含金额更新
     */
    public boolean hasAmountUpdate() {
        return totalAmount != null;
    }

    @Override
    public String toString() {
        return "OrderUpdateRequestSimple{" +
                "customerName='" + customerName + '\'' +
                ", shippingAddress='" + shippingAddress + '\'' +
                ", receiverName='" + receiverName + '\'' +
                ", receiverPhone='" + receiverPhone + '\'' +
                ", remark='" + remark + '\'' +
                ", totalAmount=" + totalAmount +
                '}';
    }
}
