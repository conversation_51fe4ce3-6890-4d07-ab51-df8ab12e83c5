package com.visthink.order.resource;

import com.visthink.common.base.SimpleController;
import com.visthink.order.entity.Order;
import com.visthink.order.service.OrderServiceMinimal;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import java.util.List;

/**
 * 订单资源接口 - 最小化版本 (重构版本)
 *
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给OrderServiceMinimal处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/orders")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "订单管理", description = "订单管理相关接口")
public class OrderResourceMinimal extends SimpleController {

    @Inject
    OrderServiceMinimal orderService;

    /**
     * 根据ID获取订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "获取订单详情", description = "根据ID获取订单详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<Order>> getOrderById(@PathParam("id") Long id) {
        logOperation("获取订单详情", "订单ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("获取订单详情", tenantId, "租户验证通过");
                    return orderService.findById(tenantId, id);
                })
                .map(order -> {
                    if (order != null) {
                        logOperation("获取订单详情", "查询成功");
                        return success(order);
                    } else {
                        return ApiResponse.<Order>notFound("订单不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "获取订单详情"));
    }

    /**
     * 获取订单列表
     *
     * @return 订单列表
     */
    @GET
    @Operation(summary = "获取订单列表", description = "获取所有订单列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<Order>>> getOrderList() {
        logOperation("获取订单列表", "开始处理查询请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("获取订单列表", tenantId, "租户验证通过");
                    return orderService.findAll(tenantId);
                })
                .map(orders -> {
                    logOperation("获取订单列表", "查询成功，共" + orders.size() + "条记录");
                    return success(orders);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "获取订单列表"));
    }

    /**
     * 分页查询订单列表
     *
     * @param page 页码
     * @param size 每页大小
     * @return 分页结果
     */
    @GET
    @Path("/page")
    @Operation(summary = "分页查询订单", description = "分页查询订单列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<PageResult<Order>>> getOrderPage(
            @QueryParam("page") @DefaultValue("1") Integer page,
            @QueryParam("size") @DefaultValue("10") Integer size) {

        logOperation("分页查询订单", String.format("页码: %d, 页大小: %d", page, size));

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("分页查询订单", tenantId, "租户验证通过");
                    PageRequest pageRequest = new PageRequest(page, size);
                    return orderService.findPage(tenantId, pageRequest);
                })
                .map(pageResult -> {
                    logOperation("分页查询订单",
                        String.format("查询成功，共%d条记录", pageResult.getTotal()));
                    return success(pageResult);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "分页查询订单"));
    }

    /**
     * 创建订单
     *
     * @param order 订单信息
     * @return 创建的订单
     */
    @POST
    @Operation(summary = "创建订单", description = "创建新的订单")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<Order>> createOrder(@Valid Order order) {
        logOperation("创建订单", "开始处理创建请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("创建订单", tenantId, "租户验证通过");
                    return orderService.save(tenantId, order);
                })
                .map(createdOrder -> {
                    logOperation("创建订单", "创建成功，订单ID: " + createdOrder.id);
                    return success("创建订单成功", createdOrder);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "创建订单"));
    }

    /**
     * 更新订单
     *
     * @param id 订单ID
     * @param order 订单信息
     * @return 更新后的订单
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新订单", description = "根据ID更新订单")
    @APIResponse(responseCode = "200", description = "更新成功")
    public Uni<ApiResponse<Order>> updateOrder(@PathParam("id") Long id, @Valid Order order) {
        logOperation("更新订单", "订单ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("更新订单", tenantId, "租户验证通过");
                    order.id = id;
                    return orderService.save(tenantId, order);
                })
                .map(updatedOrder -> {
                    logOperation("更新订单", "更新成功");
                    return success("更新订单成功", updatedOrder);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "更新订单"));
    }

    /**
     * 删除订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除订单", description = "根据ID删除订单")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Boolean>> deleteOrder(@PathParam("id") Long id) {
        logOperation("删除订单", "订单ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("删除订单", tenantId, "租户验证通过");
                    return orderService.deleteById(tenantId, id);
                })
                .map(deleted -> {
                    if (deleted) {
                        logOperation("删除订单", "删除成功");
                        return success("删除订单成功", true);
                    } else {
                        return ApiResponse.<Boolean>notFound("订单不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "删除订单"));
    }

    /**
     * 检查订单是否存在
     *
     * @param id 订单ID
     * @return 是否存在
     */
    @GET
    @Path("/{id}/exists")
    @Operation(summary = "检查订单是否存在", description = "检查指定ID的订单是否存在")
    @APIResponse(responseCode = "200", description = "检查成功")
    public Uni<ApiResponse<Boolean>> checkOrderExists(@PathParam("id") Long id) {
        logOperation("检查订单是否存在", "订单ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("检查订单是否存在", tenantId, "租户验证通过");
                    return orderService.exists(tenantId, id);
                })
                .map(exists -> {
                    logOperation("检查订单是否存在", "检查完成: " + exists);
                    return success(exists);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "检查订单是否存在"));
    }
}
