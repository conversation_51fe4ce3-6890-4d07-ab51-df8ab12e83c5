# Visthink ERP 微服务环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值
# 注意：.env 文件包含敏感信息，不要提交到版本控制系统

# ===========================================
# 数据库配置 (必需)
# ===========================================

# 数据库用户名
DB_USERNAME=your_db_username

# 数据库密码 (建议使用强密码，至少8位，包含大小写字母、数字和特殊字符)
DB_PASSWORD=your_strong_db_password

# 数据库连接URL
# 格式：vertx-reactive:postgresql://host:port/database_name
DB_URL=vertx-reactive:postgresql://localhost:35432/visthink_erp

# ===========================================
# JWT安全配置 (必需)
# ===========================================

# JWT密钥 (必须至少32个字符，建议使用随机生成的强密钥)
# 可以使用以下命令生成：openssl rand -base64 32
JWT_SECRET=your_super_secret_jwt_key_at_least_256_bits_long_change_this_in_production

# JWT过期时间 (毫秒，默认24小时)
JWT_EXPIRATION=86400000

# JWT签发者
JWT_ISSUER=https://api.visthink.com

# ===========================================
# Redis配置 (可选)
# ===========================================

# Redis连接URL
REDIS_URL=redis://localhost:6379

# Redis密码 (如果Redis设置了密码)
REDIS_PASSWORD=your_redis_password

# ===========================================
# 邮件服务配置 (可选)
# ===========================================

# 是否启用邮件服务
MAIL_ENABLED=false

# SMTP服务器地址
MAIL_SMTP_HOST=smtp.qq.com

# SMTP端口
MAIL_SMTP_PORT=587

# 邮件用户名
MAIL_USERNAME=<EMAIL>

# 邮件密码或授权码
MAIL_PASSWORD=your_email_password

# 发件人邮箱
MAIL_FROM=<EMAIL>

# 发件人名称
MAIL_FROM_NAME=Visthink ERP

# ===========================================
# 短信服务配置 (可选)
# ===========================================

# 是否启用短信服务
SMS_ENABLED=false

# 短信服务提供商 (aliyun/tencent/huawei)
SMS_PROVIDER=aliyun

# 短信服务访问密钥
SMS_ACCESS_KEY=your_sms_access_key

# 短信服务秘密密钥
SMS_SECRET_KEY=your_sms_secret_key

# 短信签名
SMS_SIGN_NAME=Visthink

# 短信模板代码
SMS_TEMPLATE_CODE=SMS_123456789

# ===========================================
# 文件存储配置 (可选)
# ===========================================

# 文件存储类型 (local/s3/minio/oss)
FILE_STORAGE_TYPE=local

# 本地存储路径
FILE_STORAGE_PATH=/opt/visthink/files

# S3/MinIO配置
S3_ENDPOINT=https://s3.amazonaws.com
S3_ACCESS_KEY=your_s3_access_key
S3_SECRET_KEY=your_s3_secret_key
S3_BUCKET_NAME=visthink-files
S3_REGION=us-east-1

# ===========================================
# 监控和日志配置 (可选)
# ===========================================

# 日志级别 (DEBUG/INFO/WARN/ERROR)
LOG_LEVEL=INFO

# 是否启用SQL日志
LOG_SQL=false

# 是否启用性能监控
METRICS_ENABLED=true

# ===========================================
# 安全配置 (可选)
# ===========================================

# 是否启用HTTPS
HTTPS_ENABLED=false

# SSL证书路径
SSL_CERT_PATH=/opt/certs/server.crt
SSL_KEY_PATH=/opt/certs/server.key

# 跨域配置
CORS_ORIGINS=*
CORS_METHODS=GET,PUT,POST,DELETE,OPTIONS
CORS_HEADERS=accept,authorization,content-type,x-requested-with,x-tenant-id

# ===========================================
# 开发环境配置 (仅开发使用)
# ===========================================

# 开发模式
DEV_MODE=false

# 热重载
HOT_RELOAD=false

# 调试端口
DEBUG_PORT=5005

# ===========================================
# 生产环境配置建议
# ===========================================

# 1. 使用强密码和随机密钥
# 2. 定期轮换密钥和密码
# 3. 使用专用的数据库用户，限制权限
# 4. 启用HTTPS和SSL证书
# 5. 配置防火墙和网络安全组
# 6. 定期备份数据库
# 7. 监控系统性能和安全日志
# 8. 使用密钥管理服务 (如AWS KMS, Azure Key Vault)

# ===========================================
# 密钥生成命令参考
# ===========================================

# 生成JWT密钥：
# openssl rand -base64 32

# 生成数据库密码：
# openssl rand -base64 16

# 生成UUID：
# uuidgen

# ===========================================
# 安全检查清单
# ===========================================

# □ 所有密码长度至少8位
# □ JWT密钥长度至少32位
# □ 不使用默认密码
# □ 不在代码中硬编码密钥
# □ 定期更换密钥和密码
# □ 限制数据库用户权限
# □ 启用SSL/TLS加密
# □ 配置防火墙规则
# □ 启用访问日志
# □ 定期安全审计
