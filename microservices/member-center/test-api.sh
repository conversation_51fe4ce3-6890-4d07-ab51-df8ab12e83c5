#!/bin/bash

# =====================================================
# Member Center API接口测试脚本
# 版本: 1.0
# 描述: 测试微服务的各个API接口功能
# 作者: visthink
# 日期: 2024-12-19
# =====================================================

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8082"
TENANT_ID=1
TEST_RESULTS_FILE="/tmp/api_test_results.json"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试API接口
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    local expected_code=${5:-200}
    
    log_info "测试 $description"
    echo "  请求: $method $url"
    
    if [ -n "$data" ]; then
        echo "  数据: $data"
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -H "X-Tenant-Id: $TENANT_ID" \
            -d "$data" \
            "$BASE_URL$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "X-Tenant-Id: $TENANT_ID" \
            "$BASE_URL$url")
    fi
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "  响应码: $http_code"
    echo "  响应体: $response_body"
    
    if [ "$http_code" -eq "$expected_code" ]; then
        log_success "$description 测试通过"
        echo ""
        return 0
    else
        log_error "$description 测试失败 (期望: $expected_code, 实际: $http_code)"
        echo ""
        return 1
    fi
}

# 测试角色管理API
test_role_api() {
    log_info "========== 测试角色管理API =========="
    
    # 1. 查询角色列表
    test_api "GET" "/api/roles" "" "查询角色列表"
    
    # 2. 创建角色
    role_data='{
        "roleCode": "test_role",
        "roleName": "测试角色",
        "roleType": 3,
        "description": "API测试创建的角色",
        "dataScope": 4
    }'
    test_api "POST" "/api/roles" "$role_data" "创建角色"
    
    # 3. 查询角色详情（假设ID为1）
    test_api "GET" "/api/roles/1" "" "查询角色详情" 200
    
    # 4. 更新角色
    update_role_data='{
        "roleCode": "test_role_updated",
        "roleName": "更新的测试角色",
        "roleType": 3,
        "description": "API测试更新的角色",
        "dataScope": 4
    }'
    test_api "PUT" "/api/roles/1" "$update_role_data" "更新角色"
    
    # 5. 检查角色编码
    test_api "GET" "/api/roles/check-code?roleCode=test_role_new" "" "检查角色编码"
}

# 测试权限管理API
test_permission_api() {
    log_info "========== 测试权限管理API =========="
    
    # 1. 查询权限列表
    test_api "GET" "/api/permissions" "" "查询权限列表"
    
    # 2. 创建权限
    permission_data='{
        "permissionCode": "test:read",
        "permissionName": "测试读取权限",
        "permissionType": 1,
        "resourceType": "API",
        "description": "API测试创建的权限"
    }'
    test_api "POST" "/api/permissions" "$permission_data" "创建权限"
    
    # 3. 查询权限详情
    test_api "GET" "/api/permissions/1" "" "查询权限详情" 200
    
    # 4. 根据类型查询权限
    test_api "GET" "/api/permissions/type/1" "" "根据类型查询权限"
}

# 测试菜单管理API
test_menu_api() {
    log_info "========== 测试菜单管理API =========="
    
    # 1. 查询菜单树
    test_api "GET" "/api/menus/tree" "" "查询菜单树"
    
    # 2. 创建菜单
    menu_data='{
        "menuCode": "test_menu",
        "menuName": "测试菜单",
        "menuType": 2,
        "menuPath": "/test",
        "componentPath": "TestComponent",
        "menuIcon": "test-icon",
        "isVisible": true,
        "sortOrder": 1
    }'
    test_api "POST" "/api/menus" "$menu_data" "创建菜单"
    
    # 3. 查询菜单列表
    test_api "GET" "/api/menus" "" "查询菜单列表"
    
    # 4. 查询根菜单
    test_api "GET" "/api/menus/root" "" "查询根菜单"
}

# 测试字典管理API
test_dictionary_api() {
    log_info "========== 测试字典管理API =========="
    
    # 1. 查询字典类型
    test_api "GET" "/api/dictionaries/types" "" "查询字典类型"
    
    # 2. 创建字典类型
    dict_type_data='{
        "dictCode": "test_dict",
        "dictName": "测试字典",
        "dictType": 1,
        "description": "API测试创建的字典类型"
    }'
    test_api "POST" "/api/dictionaries" "$dict_type_data" "创建字典类型"
    
    # 3. 查询字典树
    test_api "GET" "/api/dictionaries/tree" "" "查询字典树"
    
    # 4. 查询字典项
    test_api "GET" "/api/dictionaries/items/user_status" "" "查询用户状态字典项"
    
    # 5. 获取字典键值对
    test_api "GET" "/api/dictionaries/user_status/keyvalue" "" "获取用户状态字典键值对"
}

# 测试健康检查API
test_health_api() {
    log_info "========== 测试健康检查API =========="
    
    # 1. 基本健康检查
    test_api "GET" "/q/health" "" "基本健康检查"
    
    # 2. 就绪检查
    test_api "GET" "/q/health/ready" "" "就绪检查"
    
    # 3. 存活检查
    test_api "GET" "/q/health/live" "" "存活检查"
}

# 测试OpenAPI文档
test_openapi() {
    log_info "========== 测试OpenAPI文档 =========="
    
    # 1. OpenAPI规范
    test_api "GET" "/q/openapi" "" "OpenAPI规范"
    
    # 2. Swagger UI
    test_api "GET" "/q/swagger-ui" "" "Swagger UI" 200
}

# 生成测试报告
generate_report() {
    log_info "========== 生成测试报告 =========="
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 这里可以添加更详细的测试结果统计
    # 目前简化处理
    
    echo "API接口测试报告" > "$TEST_RESULTS_FILE"
    echo "================" >> "$TEST_RESULTS_FILE"
    echo "测试时间: $(date)" >> "$TEST_RESULTS_FILE"
    echo "服务地址: $BASE_URL" >> "$TEST_RESULTS_FILE"
    echo "租户ID: $TENANT_ID" >> "$TEST_RESULTS_FILE"
    echo "" >> "$TEST_RESULTS_FILE"
    echo "测试结果:" >> "$TEST_RESULTS_FILE"
    echo "- 总测试数: $total_tests" >> "$TEST_RESULTS_FILE"
    echo "- 通过测试: $passed_tests" >> "$TEST_RESULTS_FILE"
    echo "- 失败测试: $failed_tests" >> "$TEST_RESULTS_FILE"
    
    log_success "测试报告已生成: $TEST_RESULTS_FILE"
}

# 主函数
main() {
    log_info "开始 Member Center API接口测试"
    
    # 检查服务是否运行
    if ! curl -s "$BASE_URL/q/health" > /dev/null; then
        log_error "Member Center 服务未运行，请先启动服务"
        log_info "可以运行: ./test-startup.sh"
        exit 1
    fi
    
    log_success "Member Center 服务运行正常"
    echo ""
    
    # 运行各项测试
    test_health_api
    test_openapi
    test_role_api
    test_permission_api
    test_menu_api
    test_dictionary_api
    
    # 生成测试报告
    generate_report
    
    log_success "API接口测试完成"
}

# 运行主函数
main "$@"
