# Test Configuration for Member Center Service
quarkus:
  application:
    name: member-center-test
    version: 2.0.0

  # HTTP配置
  http:
    port: 8081
    host: 0.0.0.0
    test-port: 0

  # 测试数据库配置 - 使用H2内存数据库
  datasource:
    db-kind: h2
    username: sa
    password: ""
    jdbc:
      url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    reactive:
      url: vertx-reactive:h2:mem:testdb
      max-size: 5
      idle-timeout: PT2M
      connection-timeout: PT5S

  # Hibernate配置
  hibernate-orm:
    database:
      generation: drop-and-create
    log:
      sql: true
      format-sql: true
    # 禁用多租户配置
    multitenant: NONE

  # Redis配置（测试环境禁用）
  redis:
    hosts: redis://127.0.0.1:6379
    password: zylp
    database: 1
    timeout: PT10S

  # JWT配置
  smallrye-jwt:
    enabled: true
    rsa-sig-provider: BC
    new-token:
      issuer: https://visthink.com
      audience: visthink-erp
      lifespan: 3600

  # 日志配置
  log:
    level:
      ROOT: INFO
      com.visthink: DEBUG
      org.hibernate: WARN
    console:
      enable: true
      format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"

  # 测试配置
  test:
    continuous-testing: disabled
    flat-class-path: true

# 应用自定义配置
app:
  name: "Member Center Test"
  version: "2.0.0"
  description: "Member Center Service Test Environment"
  
  # 多租户配置
  tenant:
    default-tenant-id: 1
    enable-tenant-filter: true
    
  # 安全配置
  security:
    jwt:
      secret: "test-jwt-secret-key-for-member-center-service"
      expiration: 3600
    
  # 缓存配置
  cache:
    enabled: false
    ttl: 300
    
  # 业务配置
  business:
    user:
      default-password: "123456"
      password-expire-days: 90
      max-login-attempts: 5
      lock-duration-minutes: 30
