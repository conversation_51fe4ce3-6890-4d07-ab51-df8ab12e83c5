package com.visthink.member;

import com.visthink.member.config.HibernateReactiveConfig;
import com.visthink.member.config.DatabaseConfig;
import com.visthink.member.repository.UserRepository;
import com.visthink.member.entity.UserAccount;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Hibernate Reactive 修复验证测试
 * 验证 JdbcValuesSourceProcessingState 错误修复效果
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@QuarkusTest
class HibernateReactiveFixTest {

    @Inject
    HibernateReactiveConfig hibernateConfig;

    @Inject
    DatabaseConfig databaseConfig;

    @Inject
    UserRepository userRepository;

    @Test
    @DisplayName("验证Hibernate配置正常")
    void testHibernateConfig() {
        // 验证配置类注入成功
        assertNotNull(hibernateConfig);
        assertTrue(hibernateConfig.isSessionFactoryAvailable());

        // 验证数据库配置
        assertNotNull(databaseConfig);
        assertTrue(databaseConfig.isHealthy());
    }

    @Test
    @DisplayName("验证基础数据库操作不出现JdbcValuesSourceProcessingState错误")
    void testBasicDatabaseOperations() {
        // 创建测试用户
        UserAccount testUser = new UserAccount();
        testUser.setTenantId(1L);
        testUser.setUsername("hibernate_test_" + System.currentTimeMillis());
        testUser.setRealName("Hibernate测试用户");
        testUser.setEmail("<EMAIL>");
        testUser.setPhoneNumber("**********0");
        testUser.setAccountStatus(1);
        testUser.setDeleted(0);
        testUser.setCreateTime(LocalDateTime.now());
        testUser.setUpdateTime(testUser.getCreateTime());

        try {
            // 测试保存操作
            UserAccount savedUser = userRepository.persistUser(testUser)
                .subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem()
                .getItem();

            assertNotNull(savedUser);
            assertNotNull(savedUser.id);
            assertEquals(testUser.getUsername(), savedUser.getUsername());

            // 测试查询操作
            UserAccount foundUser = userRepository.findByIdAndTenantId(savedUser.id, 1L)
                .subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem()
                .getItem();

            assertNotNull(foundUser);
            assertEquals(savedUser.id, foundUser.id);

            // 测试更新操作
            foundUser.setRealName("更新后的用户名");
            UserAccount updatedUser = userRepository.persistUser(foundUser)
                .subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem()
                .getItem();

            assertNotNull(updatedUser);
            assertEquals("更新后的用户名", updatedUser.getRealName());

            System.out.println("✅ Hibernate Reactive 会话管理修复验证成功");
            System.out.println("✅ 未出现 JdbcValuesSourceProcessingState 错误");

        } catch (Exception e) {
            fail("数据库操作失败，可能仍存在 JdbcValuesSourceProcessingState 错误: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("验证多次连续操作的会话稳定性")
    void testMultipleOperationsStability() {
        try {
            // 执行多次操作验证会话稳定性
            for (int i = 0; i < 5; i++) {
                UserAccount testUser = new UserAccount();
                testUser.setTenantId(1L);
                testUser.setUsername("stability_test_" + i + "_" + System.currentTimeMillis());
                testUser.setRealName("稳定性测试用户" + i);
                testUser.setEmail("stability" + i + "@example.com");
                testUser.setPhoneNumber("**********" + i);
                testUser.setAccountStatus(1);
                testUser.setDeleted(0);
                testUser.setCreateTime(LocalDateTime.now());
                testUser.setUpdateTime(testUser.getCreateTime());

                // 保存并查询
                UserAccount savedUser = userRepository.persistUser(testUser)
                    .subscribe().withSubscriber(UniAssertSubscriber.create())
                    .awaitItem()
                    .getItem();

                UserAccount foundUser = userRepository.findByIdAndTenantId(savedUser.id, 1L)
                    .subscribe().withSubscriber(UniAssertSubscriber.create())
                    .awaitItem()
                    .getItem();

                assertNotNull(foundUser);
                assertEquals(savedUser.id, foundUser.id);
            }

            System.out.println("✅ 多次连续操作会话稳定性验证成功");

        } catch (Exception e) {
            fail("多次操作会话稳定性测试失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("验证数据库连接池配置")
    void testDatabaseConnectionPool() {
        // 验证连接池配置
        int maxPoolSize = databaseConfig.getMaxPoolSize();
        String idleTimeout = databaseConfig.getIdleTimeout();
        String maxLifetime = databaseConfig.getMaxLifetime();
        String connectionTimeout = databaseConfig.getConnectionTimeout();

        assertTrue(maxPoolSize >= 5 && maxPoolSize <= 50, "连接池大小应在合理范围内");
        assertNotNull(idleTimeout, "空闲超时配置不应为空");
        assertNotNull(maxLifetime, "最大生命周期配置不应为空");
        assertNotNull(connectionTimeout, "连接超时配置不应为空");

        System.out.println("✅ 数据库连接池配置验证成功");
        System.out.println("   - 最大连接数: " + maxPoolSize);
        System.out.println("   - 空闲超时: " + idleTimeout);
        System.out.println("   - 最大生命周期: " + maxLifetime);
        System.out.println("   - 连接超时: " + connectionTimeout);
    }
}
