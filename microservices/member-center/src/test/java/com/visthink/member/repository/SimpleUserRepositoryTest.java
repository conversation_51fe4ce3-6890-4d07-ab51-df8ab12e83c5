package com.visthink.member.repository;

import com.visthink.member.entity.UserAccount;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化的用户Repository测试类
 * 验证Hibernate Reactive会话管理修复效果
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@QuarkusTest
class SimpleUserRepositoryTest {

    @Inject
    UserRepository userRepository;

    @Test
    @DisplayName("测试用户保存功能 - 验证会话管理修复")
    void testPersistUser() {
        // 创建测试用户
        UserAccount testUser = new UserAccount();
        testUser.setTenantId(1L);
        testUser.setUsername("test_user_" + System.currentTimeMillis());
        testUser.setRealName("测试用户");
        testUser.setEmail("<EMAIL>");
        testUser.setPhoneNumber("***********");
        testUser.setAccountStatus(1);
        testUser.setDeleted(0);
        testUser.setCreateTime(LocalDateTime.now());
        testUser.setUpdateTime(testUser.getCreateTime());

        // 保存用户
        UserAccount savedUser = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 验证保存结果
        assertNotNull(savedUser);
        assertNotNull(savedUser.id);
        assertEquals(testUser.getUsername(), savedUser.getUsername());
        assertEquals(testUser.getTenantId(), savedUser.getTenantId());
        assertNotNull(savedUser.getCreateTime());
        assertNotNull(savedUser.getUpdateTime());
    }

    @Test
    @DisplayName("测试根据用户名查询 - 验证会话管理修复")
    void testFindByUsernameAndTenantId() {
        // 先保存用户
        UserAccount testUser = new UserAccount();
        testUser.setTenantId(1L);
        testUser.setUsername("find_test_" + System.currentTimeMillis());
        testUser.setRealName("查询测试用户");
        testUser.setEmail("<EMAIL>");
        testUser.setPhoneNumber("***********");
        testUser.setAccountStatus(1);
        testUser.setDeleted(0);
        testUser.setCreateTime(LocalDateTime.now());
        testUser.setUpdateTime(testUser.getCreateTime());

        UserAccount savedUser = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 根据用户名查询
        UserAccount foundUser = userRepository.findByUsernameAndTenantId(savedUser.getUsername(), 1L)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 验证查询结果
        assertNotNull(foundUser);
        assertEquals(savedUser.id, foundUser.id);
        assertEquals(savedUser.getUsername(), foundUser.getUsername());
        assertEquals(savedUser.getTenantId(), foundUser.getTenantId());
    }
}
