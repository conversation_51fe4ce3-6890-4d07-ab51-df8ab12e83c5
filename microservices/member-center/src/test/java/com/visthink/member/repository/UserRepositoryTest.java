package com.visthink.member.repository;

import com.visthink.member.entity.UserAccount;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户Repository测试类
 * 验证Hibernate Reactive会话管理修复效果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@QuarkusTest
class UserRepositoryTest {

    @Inject
    UserRepository userRepository;

    private UserAccount testUser;
    private final Long testTenantId = 1L;

    @BeforeEach
    void setUp() {
        // 创建测试用户
        testUser = new UserAccount();
        testUser.tenantId = testTenantId;
        testUser.username = "test_user_" + System.currentTimeMillis();
        testUser.realName = "测试用户";
        testUser.email = "<EMAIL>";
        testUser.phoneNumber = "***********";
        testUser.accountStatus = 1;
        testUser.deleted = 0;
        testUser.createTime = LocalDateTime.now();
        testUser.updateTime = testUser.createTime;
    }

    @Test
    @DisplayName("测试用户保存功能 - 验证会话管理修复")
    void testPersistUser() {
        // 保存用户
        UserAccount savedUser = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 验证保存结果
        assertNotNull(savedUser);
        assertNotNull(savedUser.id);
        assertEquals(testUser.username, savedUser.username);
        assertEquals(testUser.tenantId, savedUser.tenantId);
        assertNotNull(savedUser.createTime);
        assertNotNull(savedUser.updateTime);
    }

    @Test
    @DisplayName("测试根据用户名查询 - 验证会话管理修复")
    void testFindByUsernameAndTenantId() {
        // 先保存用户
        UserAccount savedUser = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 根据用户名查询
        UserAccount foundUser = userRepository.findByUsernameAndTenantId(savedUser.username, testTenantId)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 验证查询结果
        assertNotNull(foundUser);
        assertEquals(savedUser.id, foundUser.id);
        assertEquals(savedUser.username, foundUser.username);
        assertEquals(savedUser.tenantId, foundUser.tenantId);
    }

    @Test
    @DisplayName("测试根据ID查询 - 验证会话管理修复")
    void testFindByIdAndTenantId() {
        // 先保存用户
        UserAccount savedUser = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 根据ID查询
        UserAccount foundUser = userRepository.findByIdAndTenantId(savedUser.id, testTenantId)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 验证查询结果
        assertNotNull(foundUser);
        assertEquals(savedUser.id, foundUser.id);
        assertEquals(savedUser.username, foundUser.username);
        assertEquals(savedUser.tenantId, foundUser.tenantId);
    }

    @Test
    @DisplayName("测试用户更新功能 - 验证会话管理修复")
    void testUpdateUser() {
        // 先保存用户
        UserAccount savedUser = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 更新用户信息
        savedUser.realName = "更新后的用户名";
        savedUser.email = "<EMAIL>";

        UserAccount updatedUser = userRepository.persistUser(savedUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 验证更新结果
        assertNotNull(updatedUser);
        assertEquals(savedUser.id, updatedUser.id);
        assertEquals("更新后的用户名", updatedUser.realName);
        assertEquals("<EMAIL>", updatedUser.email);
        assertTrue(updatedUser.updateTime.isAfter(updatedUser.createTime));
    }

    @Test
    @DisplayName("测试多租户数据隔离")
    void testTenantIsolation() {
        // 保存用户到租户1
        testUser.tenantId = 1L;
        UserAccount user1 = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 尝试从租户2查询用户（应该查不到）
        UserAccount notFoundUser = userRepository.findByIdAndTenantId(user1.id, 2L)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitItem()
            .getItem();

        // 验证租户隔离
        assertNull(notFoundUser);
    }

    @Test
    @DisplayName("测试异常处理 - 租户ID为空")
    void testNullTenantId() {
        testUser.tenantId = null;

        // 尝试保存租户ID为空的用户
        Throwable exception = userRepository.persistUser(testUser)
            .subscribe().withSubscriber(UniAssertSubscriber.create())
            .awaitFailure()
            .getFailure();

        // 验证异常
        assertNotNull(exception);
        assertTrue(exception instanceof IllegalArgumentException);
        assertTrue(exception.getMessage().contains("用户对象必须包含租户ID"));
    }
}
