package com.visthink.member.dto;

import lombok.Data;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户角色分配请求DTO
 * 
 * 用于接收用户角色分配的请求参数
 * 支持批量角色分配和过期时间设置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserRoleAssignRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 角色ID列表
     */
    @NotEmpty(message = "角色ID列表不能为空")
    private List<Long> roleIds;

    /**
     * 角色编码列表（可选，与角色ID列表二选一）
     */
    private List<String> roleCodes;

    /**
     * 操作类型：assign-分配角色，remove-移除角色，replace-替换角色
     */
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "^(assign|remove|replace)$", message = "操作类型只能是assign、remove或replace")
    private String operation;

    /**
     * 过期时间（可选，null表示永不过期）
     */
    private LocalDateTime expireTime;

    /**
     * 过期天数（可选，与过期时间二选一）
     */
    @Min(value = 1, message = "过期天数必须大于0")
    private Integer expireDays;

    /**
     * 授权人ID（可选）
     */
    private Long grantedBy;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    // ==================== 业务方法 ====================

    /**
     * 判断是否为分配角色操作
     */
    public boolean isAssignOperation() {
        return "assign".equals(operation);
    }

    /**
     * 判断是否为移除角色操作
     */
    public boolean isRemoveOperation() {
        return "remove".equals(operation);
    }

    /**
     * 判断是否为替换角色操作
     */
    public boolean isReplaceOperation() {
        return "replace".equals(operation);
    }

    /**
     * 验证角色参数
     */
    public boolean hasValidRoles() {
        return (roleIds != null && !roleIds.isEmpty()) ||
               (roleCodes != null && !roleCodes.isEmpty());
    }

    /**
     * 获取操作描述
     */
    public String getOperationDesc() {
        switch (operation) {
            case "assign":
                return "分配角色";
            case "remove":
                return "移除角色";
            case "replace":
                return "替换角色";
            default:
                return "未知操作";
        }
    }

    /**
     * 判断是否有过期时间设置
     */
    public boolean hasExpireTime() {
        return expireTime != null || expireDays != null;
    }

    /**
     * 获取计算后的过期时间
     */
    public LocalDateTime getCalculatedExpireTime() {
        if (expireTime != null) {
            return expireTime;
        }
        if (expireDays != null && expireDays > 0) {
            return LocalDateTime.now().plusDays(expireDays);
        }
        return null; // 永不过期
    }

    /**
     * 验证请求参数
     */
    public boolean isValid() {
        return hasValidRoles() && 
               (expireTime == null || expireDays == null); // 过期时间和过期天数不能同时设置
    }
}




