package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 用户角色关联实体类
 * 
 * 管理用户与角色的多对多关联关系
 * 支持角色授权时间、过期时间等扩展功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "user_role_relations",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"user_id", "role_id", "tenant_id"})
       },
       indexes = {
           @Index(name = "idx_user_role_user", columnList = "user_id"),
           @Index(name = "idx_user_role_role", columnList = "role_id"),
           @Index(name = "idx_user_role_tenant", columnList = "tenant_id"),
           @Index(name = "idx_user_role_expire", columnList = "expire_time")
       })
public class UserRole extends BaseEntity {

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 角色ID
     */
    @Column(name = "role_id", nullable = false)
    private Long roleId;

    /**
     * 授权人ID
     */
    @Column(name = "granted_by")
    private Long grantedBy;

    /**
     * 授权时间
     */
    @Column(name = "granted_time")
    private LocalDateTime grantedTime;

    /**
     * 过期时间（可选，null表示永不过期）
     */
    @Column(name = "expire_time")
    private LocalDateTime expireTime;

    // ==================== 关联关系 ====================

    /**
     * 关联用户实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private UserAccount user;

    /**
     * 关联角色实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", insertable = false, updatable = false)
    private Role role;

    /**
     * 授权人实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "granted_by", insertable = false, updatable = false)
    private UserAccount grantedByUser;

    // ==================== 业务方法 ====================

    /**
     * 判断角色是否已过期
     */
    public boolean isExpired() {
        if (expireTime == null) {
            return false; // 永不过期
        }
        return LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 判断角色是否有效（未删除且未过期）
     */
    public boolean isValid() {
        return !isDeleted() && !isExpired() && isEnabled();
    }

    /**
     * 设置角色过期
     */
    public void expire() {
        this.expireTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 延长角色有效期
     * 
     * @param days 延长天数
     */
    public void extendExpireTime(int days) {
        if (this.expireTime == null) {
            this.expireTime = LocalDateTime.now().plusDays(days);
        } else {
            this.expireTime = this.expireTime.plusDays(days);
        }
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置永不过期
     */
    public void setNeverExpire() {
        this.expireTime = null;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 获取剩余有效天数
     * 
     * @return 剩余天数，-1表示永不过期，0表示已过期
     */
    public long getRemainingDays() {
        if (expireTime == null) {
            return -1; // 永不过期
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expireTime)) {
            return 0; // 已过期
        }
        
        return java.time.Duration.between(now, expireTime).toDays();
    }

    /**
     * 获取角色状态描述
     */
    public String getStatusDesc() {
        if (isDeleted()) {
            return "已删除";
        }
        if (!isEnabled()) {
            return "已禁用";
        }
        if (isExpired()) {
            return "已过期";
        }
        return "正常";
    }

    // ==================== 便捷构造方法 ====================

    /**
     * 创建用户角色关联
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param grantedBy 授权人ID
     * @param tenantId 租户ID
     * @return 用户角色关联实例
     */
    public static UserRole create(Long userId, Long roleId, Long grantedBy, Long tenantId) {
        UserRole userRole = new UserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);
        userRole.setGrantedBy(grantedBy);
        userRole.setGrantedTime(LocalDateTime.now());
        userRole.setTenantId(tenantId);
        userRole.setCreateTime(LocalDateTime.now());
        userRole.setUpdateTime(LocalDateTime.now());
        return userRole;
    }

    /**
     * 创建带过期时间的用户角色关联
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param grantedBy 授权人ID
     * @param tenantId 租户ID
     * @param expireTime 过期时间
     * @return 用户角色关联实例
     */
    public static UserRole createWithExpire(Long userId, Long roleId, Long grantedBy, 
                                          Long tenantId, LocalDateTime expireTime) {
        UserRole userRole = create(userId, roleId, grantedBy, tenantId);
        userRole.setExpireTime(expireTime);
        return userRole;
    }
}
