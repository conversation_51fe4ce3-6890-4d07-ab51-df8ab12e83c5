package com.visthink.member.service;

import com.visthink.member.entity.Permission;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;

import java.util.List;

/**
 * 权限服务接口
 * 
 * 提供权限管理的业务逻辑接口
 * 包含权限CRUD、权限树构建、用户权限查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PermissionService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建权限
     * 
     * @param tenantId 租户ID
     * @param permission 权限信息
     * @return 创建结果
     */
    Uni<ApiResponse<Permission>> createPermission(Long tenantId, Permission permission);

    /**
     * 更新权限
     * 
     * @param tenantId 租户ID
     * @param permissionId 权限ID
     * @param permission 权限信息
     * @return 更新结果
     */
    Uni<ApiResponse<Permission>> updatePermission(Long tenantId, Long permissionId, Permission permission);

    /**
     * 删除权限
     * 
     * @param tenantId 租户ID
     * @param permissionId 权限ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deletePermission(Long tenantId, Long permissionId);

    /**
     * 批量删除权限
     * 
     * @param tenantId 租户ID
     * @param permissionIds 权限ID列表
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deletePermissions(Long tenantId, List<Long> permissionIds);

    /**
     * 根据ID查询权限
     * 
     * @param tenantId 租户ID
     * @param permissionId 权限ID
     * @return 权限信息
     */
    Uni<ApiResponse<Permission>> getPermissionById(Long tenantId, Long permissionId);

    /**
     * 根据权限编码查询权限
     * 
     * @param tenantId 租户ID
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    Uni<ApiResponse<Permission>> getPermissionByCode(Long tenantId, String permissionCode);

    // ==================== 查询操作 ====================

    /**
     * 分页查询权限
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param permissionType 权限类型
     * @param status 状态
     * @return 分页结果
     */
    Uni<ApiResponse<PageResult<Permission>>> getPermissionList(Long tenantId, PageRequest pageRequest, 
                                                             String keyword, Integer permissionType, Integer status);

    /**
     * 查询所有权限
     * 
     * @param tenantId 租户ID
     * @return 权限列表
     */
    Uni<ApiResponse<List<Permission>>> getAllPermissions(Long tenantId);

    /**
     * 查询权限树
     * 
     * @param tenantId 租户ID
     * @return 权限树结构
     */
    Uni<ApiResponse<List<Permission>>> getPermissionTree(Long tenantId);

    /**
     * 查询根权限
     * 
     * @param tenantId 租户ID
     * @return 根权限列表
     */
    Uni<ApiResponse<List<Permission>>> getRootPermissions(Long tenantId);

    /**
     * 根据父权限ID查询子权限
     * 
     * @param tenantId 租户ID
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    Uni<ApiResponse<List<Permission>>> getChildPermissions(Long tenantId, Long parentId);

    /**
     * 根据权限类型查询权限
     * 
     * @param tenantId 租户ID
     * @param permissionType 权限类型
     * @return 权限列表
     */
    Uni<ApiResponse<List<Permission>>> getPermissionsByType(Long tenantId, Integer permissionType);

    /**
     * 查询系统内置权限
     * 
     * @param tenantId 租户ID
     * @return 系统权限列表
     */
    Uni<ApiResponse<List<Permission>>> getSystemPermissions(Long tenantId);

    // ==================== 用户权限查询 ====================

    /**
     * 查询用户权限
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户权限列表
     */
    Uni<ApiResponse<List<Permission>>> getUserPermissions(Long tenantId, Long userId);

    /**
     * 查询用户权限编码
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户权限编码列表
     */
    Uni<ApiResponse<List<String>>> getUserPermissionCodes(Long tenantId, Long userId);

    /**
     * 检查用户是否有指定权限
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> hasUserPermission(Long tenantId, Long userId, String permissionCode);

    /**
     * 检查用户是否有任一权限
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> hasAnyUserPermission(Long tenantId, Long userId, List<String> permissionCodes);

    /**
     * 检查用户是否有所有权限
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> hasAllUserPermissions(Long tenantId, Long userId, List<String> permissionCodes);

    // ==================== 角色权限查询 ====================

    /**
     * 查询角色权限
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 角色权限列表
     */
    Uni<ApiResponse<List<Permission>>> getRolePermissions(Long tenantId, Long roleId);

    /**
     * 查询角色权限编码
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 角色权限编码列表
     */
    Uni<ApiResponse<List<String>>> getRolePermissionCodes(Long tenantId, Long roleId);

    // ==================== 权限验证 ====================

    /**
     * 验证权限编码格式
     * 
     * @param permissionCode 权限编码
     * @return 验证结果
     */
    ApiResponse<Boolean> validatePermissionCode(String permissionCode);

    /**
     * 检查权限编码是否存在
     * 
     * @param tenantId 租户ID
     * @param permissionCode 权限编码
     * @param excludeId 排除的权限ID
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> checkPermissionCodeExists(Long tenantId, String permissionCode, Long excludeId);

    /**
     * 验证权限是否可以删除
     * 
     * @param tenantId 租户ID
     * @param permissionId 权限ID
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> canDeletePermission(Long tenantId, Long permissionId);

    // ==================== 权限初始化 ====================

    /**
     * 初始化租户权限
     * 
     * @param tenantId 租户ID
     * @return 初始化结果
     */
    Uni<ApiResponse<Void>> initTenantPermissions(Long tenantId);

    /**
     * 同步系统权限
     * 
     * @param tenantId 租户ID
     * @return 同步结果
     */
    Uni<ApiResponse<Void>> syncSystemPermissions(Long tenantId);

    // ==================== 统计方法 ====================

    /**
     * 统计租户下的权限数量
     * 
     * @param tenantId 租户ID
     * @return 权限数量
     */
    Uni<ApiResponse<Long>> countPermissions(Long tenantId);

    /**
     * 统计权限被分配给多少个角色
     * 
     * @param tenantId 租户ID
     * @param permissionId 权限ID
     * @return 角色数量
     */
    Uni<ApiResponse<Long>> countPermissionRoles(Long tenantId, Long permissionId);

    // ==================== 权限导入导出 ====================

    /**
     * 导出权限数据
     * 
     * @param tenantId 租户ID
     * @return 权限数据
     */
    Uni<ApiResponse<String>> exportPermissions(Long tenantId);

    /**
     * 导入权限数据
     * 
     * @param tenantId 租户ID
     * @param permissionData 权限数据
     * @return 导入结果
     */
    Uni<ApiResponse<Void>> importPermissions(Long tenantId, String permissionData);
}
