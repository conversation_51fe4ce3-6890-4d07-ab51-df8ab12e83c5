package com.visthink.member.service.impl;

import com.visthink.member.entity.UserAccount;
import com.visthink.member.repository.UserRepository;
import com.visthink.member.service.UserService;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class UserServiceImpl implements UserService {

    private static final Logger log = Logger.getLogger(UserServiceImpl.class);

    @Inject
    UserRepository userRepository;

    @Override
    public Uni<ApiResponse<UserAccount>> createUser(UserAccount userAccount) {
        return createUserInternal(userAccount)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("创建用户失败", throwable);
                    return ApiResponse.error("创建用户失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<UserAccount>> getUserById(Long id) {
        // 注意：这里需要租户ID，但接口没有提供，使用默认租户ID
        Long defaultTenantId = 1L;
        return userRepository.findByIdAndTenantId(id, defaultTenantId)
                .map(user -> {
                    if (user != null) {
                        return ApiResponse.success(user);
                    } else {
                        return ApiResponse.<UserAccount>error("用户不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.error("查询用户失败", throwable);
                    return ApiResponse.error("查询用户失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<UserAccount>> getUserByUsername(String username) {
        // 注意：这里需要租户ID，但接口没有提供，使用默认租户ID
        Long defaultTenantId = 1L;
        return userRepository.findByUsername(defaultTenantId, username)
                .map(user -> {
                    if (user != null) {
                        return ApiResponse.success(user);
                    } else {
                        return ApiResponse.<UserAccount>error("用户不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.error("查询用户失败", throwable);
                    return ApiResponse.error("查询用户失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<UserAccount>> updateUser(Long id, UserAccount userAccount) {
        userAccount.id = id; // 直接访问public字段
        // BaseEntity的@PreUpdate会自动设置updateTime

        return updateUser(userAccount)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("更新用户失败", throwable);
                    return ApiResponse.error("更新用户失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Void>> deleteUser(Long id) {
        // 注意：这里需要租户ID，但接口没有提供，使用默认租户ID
        Long defaultTenantId = 1L;
        return deleteUser(id, defaultTenantId)
                .map(deleted -> {
                    if (deleted) {
                        return ApiResponse.<Void>success(null);
                    } else {
                        return ApiResponse.<Void>error("删除用户失败");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.error("删除用户失败", throwable);
                    return ApiResponse.error("删除用户失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<PageResult<UserAccount>>> getUserList(PageRequest pageRequest, String keyword, Integer status) {
        // 注意：这里需要租户ID，但接口没有提供，使用默认租户ID
        Long defaultTenantId = 1L;
        return findPageByConditions(defaultTenantId, keyword, status, pageRequest)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    log.error("查询用户列表失败", throwable);
                    return ApiResponse.error("查询用户列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Void>> activateUser(Long id) {
        return updateUserStatus(id, UserAccount.Status.ACTIVE)
                .map(success -> {
                    if (success) {
                        return ApiResponse.<Void>success(null);
                    } else {
                        return ApiResponse.<Void>error("激活用户失败");
                    }
                });
    }

    @Override
    public Uni<ApiResponse<Void>> lockUser(Long id) {
        return updateUserStatus(id, UserAccount.Status.LOCKED)
                .map(success -> {
                    if (success) {
                        return ApiResponse.<Void>success(null);
                    } else {
                        return ApiResponse.<Void>error("锁定用户失败");
                    }
                });
    }

    @Override
    public Uni<ApiResponse<Void>> disableUser(Long id) {
        return updateUserStatus(id, UserAccount.Status.DISABLED)
                .map(success -> {
                    if (success) {
                        return ApiResponse.<Void>success(null);
                    } else {
                        return ApiResponse.<Void>error("禁用用户失败");
                    }
                });
    }

    @Override
    public Uni<ApiResponse<Void>> resetPassword(Long id, String newPassword) {
        // 注意：这里需要租户ID，但接口没有提供，使用默认租户ID
        Long defaultTenantId = 1L;
        return userRepository.findByIdAndTenantId(id, defaultTenantId)
                .flatMap(user -> {
                    if (user != null) {
                        user.setPasswordHash(newPassword); // 实际应用中需要加密
                        user.setUpdateTime(LocalDateTime.now());
                        return userRepository.persistUser(user)
                                .map(savedUser -> ApiResponse.<Void>success(null));
                    } else {
                        return Uni.createFrom().item(ApiResponse.<Void>error("用户不存在"));
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.error("重置密码失败", throwable);
                    return ApiResponse.error("重置密码失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> validatePassword(String username, String password) {
        // 注意：这里需要租户ID，但接口没有提供，使用默认租户ID
        Long defaultTenantId = 1L;
        return userRepository.findByUsername(defaultTenantId, username)
                .map(user -> {
                    if (user != null && user.getPasswordHash().equals(password)) {
                        return ApiResponse.success(true);
                    } else {
                        return ApiResponse.success(false);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.error("验证密码失败", throwable);
                    return ApiResponse.error("验证密码失败: " + throwable.getMessage());
                });
    }

    // ==================== 内部方法实现 ====================

    @Override
    public Uni<PageResult<UserAccount>> findPageByConditions(Long tenantId, String keyword, Integer status, PageRequest pageRequest) {
        return userRepository.findPageByConditions(tenantId, keyword, status, pageRequest);
    }

    @Override
    public Uni<UserAccount> findByIdAndTenantId(Long id, Long tenantId) {
        return userRepository.findByIdAndTenantId(id, tenantId);
    }

    @Override
    public Uni<UserAccount> findByUsername(Long tenantId, String username) {
        return userRepository.findByUsername(tenantId, username);
    }

    @Override
    public Uni<UserAccount> findByEmail(Long tenantId, String email) {
        return userRepository.findByEmail(tenantId, email);
    }

    @Override
    public Uni<UserAccount> createUserInternal(UserAccount userAccount) {
        // BaseEntity的@PrePersist会自动设置createTime和updateTime
        if (userAccount.getAccountStatus() == null) {
            userAccount.setAccountStatus(UserAccount.Status.PENDING_ACTIVATION);
        }
        return userRepository.persistUser(userAccount);
    }

    @Override
    public Uni<UserAccount> updateUser(UserAccount userAccount) {
        return userRepository.persistUser(userAccount);
    }

    @Override
    public Uni<Boolean> deleteUser(Long id, Long tenantId) {
        return userRepository.findByIdAndTenantId(id, tenantId)
                .flatMap(user -> {
                    if (user != null) {
                        return userRepository.delete(user);
                    } else {
                        return Uni.createFrom().item(false);
                    }
                });
    }

    @Override
    public Uni<Boolean> isUsernameAvailable(Long tenantId, String username, Long excludeId) {
        return userRepository.existsByUsername(tenantId, username, excludeId)
                .map(exists -> !exists); // 存在则不可用，不存在则可用
    }

    /**
     * 更新用户状态的辅助方法
     *
     * @param id 用户ID
     * @param status 新状态
     * @return 更新结果
     */
    private Uni<Boolean> updateUserStatus(Long id, Integer status) {
        // 注意：这里需要租户ID，但接口没有提供，使用默认租户ID
        Long defaultTenantId = 1L;
        return userRepository.findByIdAndTenantId(id, defaultTenantId)
                .flatMap(user -> {
                    if (user != null) {
                        user.setAccountStatus(status);
                        user.setUpdateTime(LocalDateTime.now());
                        return userRepository.persistUser(user)
                                .map(savedUser -> true);
                    } else {
                        return Uni.createFrom().item(false);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.error("更新用户状态失败", throwable);
                    return false;
                });
    }
}
