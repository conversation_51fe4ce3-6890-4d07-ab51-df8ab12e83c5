package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.util.List;

/**
 * 字典实体类
 * 
 * 管理系统字典数据，支持层级字典结构
 * 包含字典类型、字典项等功能
 * 支持多租户字典配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "system_dictionaries",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"dict_code", "tenant_id"})
       },
       indexes = {
           @Index(name = "idx_dict_code", columnList = "dict_code"),
           @Index(name = "idx_dict_type", columnList = "dict_type"),
           @Index(name = "idx_dict_parent", columnList = "parent_id"),
           @Index(name = "idx_dict_tenant", columnList = "tenant_id"),
           @Index(name = "idx_dict_sort", columnList = "sort_order")
       })
public class Dictionary extends BaseEntity {

    /**
     * 字典编码（租户内唯一）
     */
    @Column(name = "dict_code", nullable = false, length = 50)
    private String dictCode;

    /**
     * 字典名称
     */
    @Column(name = "dict_name", nullable = false, length = 100)
    private String dictName;

    /**
     * 字典类型：1-字典类型，2-字典项
     */
    @Column(name = "dict_type", nullable = false)
    private Integer dictType = 1;

    /**
     * 父字典ID（构建字典树）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 字典值
     */
    @Column(name = "dict_value", length = 200)
    private String dictValue;

    /**
     * 字典标签
     */
    @Column(name = "dict_label", length = 100)
    private String dictLabel;

    /**
     * 字典描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 是否系统内置字典
     */
    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;

    /**
     * 是否可编辑
     */
    @Column(name = "is_editable", nullable = false)
    private Boolean isEditable = true;

    /**
     * 字典样式（CSS类名）
     */
    @Column(name = "dict_style", length = 100)
    private String dictStyle;

    /**
     * 字典颜色
     */
    @Column(name = "dict_color", length = 20)
    private String dictColor;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_props", columnDefinition = "TEXT")
    private String extraProps;

    // ==================== 关联关系 ====================

    /**
     * 子字典列表（一对多关系）
     */
    @OneToMany(mappedBy = "parentId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    private List<Dictionary> children;

    /**
     * 父字典实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Dictionary parent;

    // ==================== 常量定义 ====================

    /**
     * 字典类型常量
     */
    public static class DictType {
        /** 字典类型 */
        public static final Integer TYPE = 1;
        /** 字典项 */
        public static final Integer ITEM = 2;
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否为字典类型
     */
    public boolean isDictType() {
        return DictType.TYPE.equals(this.dictType);
    }

    /**
     * 判断是否为字典项
     */
    public boolean isDictItem() {
        return DictType.ITEM.equals(this.dictType);
    }

    /**
     * 判断是否为根字典（无父字典）
     */
    public boolean isRootDict() {
        return this.parentId == null;
    }

    /**
     * 判断是否有子字典
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }

    /**
     * 判断是否为系统字典
     */
    public boolean isSystemDict() {
        return Boolean.TRUE.equals(this.isSystem);
    }

    /**
     * 判断是否可编辑
     */
    public boolean isEditableDict() {
        return Boolean.TRUE.equals(this.isEditable) && !isSystemDict();
    }

    /**
     * 获取字典类型描述
     */
    public String getDictTypeDesc() {
        if (this.dictType == null) {
            return "字典类型";
        }
        switch (this.dictType) {
            case 1:
                return "字典类型";
            case 2:
                return "字典项";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取字典显示文本
     */
    public String getDisplayText() {
        if (isDictItem() && dictLabel != null && !dictLabel.trim().isEmpty()) {
            return dictLabel;
        }
        return dictName;
    }

    /**
     * 获取字典完整路径
     */
    public String getFullPath() {
        if (isRootDict()) {
            return dictCode;
        }
        // 实际实现时需要递归构建完整路径
        return dictCode;
    }

    // ==================== 便捷构造方法 ====================

    /**
     * 创建字典类型
     */
    public static Dictionary createDictType(String code, String name, String description, Long tenantId) {
        Dictionary dict = new Dictionary();
        dict.setDictCode(code);
        dict.setDictName(name);
        dict.setDictType(DictType.TYPE);
        dict.setDescription(description);
        dict.setTenantId(tenantId);
        dict.setIsSystem(false);
        dict.setIsEditable(true);
        return dict;
    }

    /**
     * 创建字典项
     */
    public static Dictionary createDictItem(String code, String name, String value, String label,
                                          Long parentId, Integer sortOrder, Long tenantId) {
        Dictionary dict = new Dictionary();
        dict.setDictCode(code);
        dict.setDictName(name);
        dict.setDictType(DictType.ITEM);
        dict.setDictValue(value);
        dict.setDictLabel(label);
        dict.setParentId(parentId);
        dict.setSortOrder(sortOrder);
        dict.setTenantId(tenantId);
        dict.setIsSystem(false);
        dict.setIsEditable(true);
        return dict;
    }

    /**
     * 创建系统字典类型
     */
    public static Dictionary createSystemDictType(String code, String name, String description, Long tenantId) {
        Dictionary dict = createDictType(code, name, description, tenantId);
        dict.setIsSystem(true);
        dict.setIsEditable(false);
        return dict;
    }

    /**
     * 创建系统字典项
     */
    public static Dictionary createSystemDictItem(String code, String name, String value, String label,
                                                Long parentId, Integer sortOrder, Long tenantId) {
        Dictionary dict = createDictItem(code, name, value, label, parentId, sortOrder, tenantId);
        dict.setIsSystem(true);
        dict.setIsEditable(false);
        return dict;
    }

    /**
     * 创建带样式的字典项
     */
    public static Dictionary createStyledDictItem(String code, String name, String value, String label,
                                                Long parentId, Integer sortOrder, String style, String color, Long tenantId) {
        Dictionary dict = createDictItem(code, name, value, label, parentId, sortOrder, tenantId);
        dict.setDictStyle(style);
        dict.setDictColor(color);
        return dict;
    }
}
