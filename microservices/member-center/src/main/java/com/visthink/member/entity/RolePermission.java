package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 角色权限关联实体类
 * 
 * 管理角色与权限的多对多关联关系
 * 支持权限授权时间、授权人等扩展功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "role_permission_relations",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"role_id", "permission_id", "tenant_id"})
       },
       indexes = {
           @Index(name = "idx_role_permission_role", columnList = "role_id"),
           @Index(name = "idx_role_permission_permission", columnList = "permission_id"),
           @Index(name = "idx_role_permission_tenant", columnList = "tenant_id"),
           @Index(name = "idx_role_permission_granted", columnList = "granted_time")
       })
@EqualsAndHashCode(callSuper = true)
public class RolePermission extends BaseEntity {

    /**
     * 角色ID
     */
    @Column(name = "role_id", nullable = false)
    private Long roleId;

    /**
     * 权限ID
     */
    @Column(name = "permission_id", nullable = false)
    private Long permissionId;

    /**
     * 授权人ID
     */
    @Column(name = "granted_by")
    private Long grantedBy;

    /**
     * 授权时间
     */
    @Column(name = "granted_time")
    private LocalDateTime grantedTime;

    // ==================== 关联关系 ====================

    /**
     * 关联角色实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", insertable = false, updatable = false)
    private Role role;

    /**
     * 关联权限实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "permission_id", insertable = false, updatable = false)
    private Permission permission;

    /**
     * 授权人实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "granted_by", insertable = false, updatable = false)
    private UserAccount grantedByUser;

    // ==================== 业务方法 ====================

    /**
     * 判断权限关联是否有效（未删除且启用）
     */
    public boolean isValid() {
        return !isDeleted() && isEnabled();
    }

    /**
     * 获取权限关联状态描述
     */
    public String getStatusDesc() {
        if (isDeleted()) {
            return "已删除";
        }
        if (!isEnabled()) {
            return "已禁用";
        }
        return "正常";
    }

    /**
     * 撤销权限（软删除）
     */
    public void revoke() {
        this.deleted = 1;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 恢复权限
     */
    public void restore() {
        this.deleted = 0;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 禁用权限
     */
    public void disable() {
        this.status = 0;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 启用权限
     */
    public void enable() {
        this.status = 1;
        this.updateTime = LocalDateTime.now();
    }

    // ==================== 便捷构造方法 ====================

    /**
     * 创建角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @param grantedBy 授权人ID
     * @param tenantId 租户ID
     * @return 角色权限关联实例
     */
    public static RolePermission create(Long roleId, Long permissionId, Long grantedBy, Long tenantId) {
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleId(roleId);
        rolePermission.setPermissionId(permissionId);
        rolePermission.setGrantedBy(grantedBy);
        rolePermission.setGrantedTime(LocalDateTime.now());
        rolePermission.setTenantId(tenantId);
        rolePermission.setCreateTime(LocalDateTime.now());
        rolePermission.setUpdateTime(LocalDateTime.now());
        rolePermission.setStatus(1); // 启用状态
        rolePermission.setDeleted(0); // 未删除
        return rolePermission;
    }

    /**
     * 批量创建角色权限关联
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @param grantedBy 授权人ID
     * @param tenantId 租户ID
     * @return 角色权限关联列表
     */
    public static java.util.List<RolePermission> createBatch(Long roleId, java.util.List<Long> permissionIds, 
                                                            Long grantedBy, Long tenantId) {
        return permissionIds.stream()
                .map(permissionId -> create(roleId, permissionId, grantedBy, tenantId))
                .collect(java.util.stream.Collectors.toList());
    }

    // ==================== 查询方法 ====================

    /**
     * 根据角色ID查询权限关联
     */
    public static io.smallrye.mutiny.Uni<java.util.List<RolePermission>> findByRoleId(Long roleId) {
        return find("roleId = ?1 and deleted = 0 and status = 1", roleId).list();
    }

    /**
     * 根据权限ID查询角色关联
     */
    public static io.smallrye.mutiny.Uni<java.util.List<RolePermission>> findByPermissionId(Long permissionId) {
        return find("permissionId = ?1 and deleted = 0 and status = 1", permissionId).list();
    }

    /**
     * 根据角色ID和权限ID查询关联
     */
    public static io.smallrye.mutiny.Uni<RolePermission> findByRoleAndPermission(Long roleId, Long permissionId) {
        return find("roleId = ?1 and permissionId = ?2 and deleted = 0", roleId, permissionId).firstResult();
    }

    /**
     * 根据租户ID查询所有角色权限关联
     */
    public static io.smallrye.mutiny.Uni<java.util.List<RolePermission>> findByTenantId(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0 and status = 1", tenantId).list();
    }

    /**
     * 检查角色是否拥有指定权限
     */
    public static io.smallrye.mutiny.Uni<Boolean> hasPermission(Long roleId, Long permissionId) {
        return count("roleId = ?1 and permissionId = ?2 and deleted = 0 and status = 1", roleId, permissionId)
                .map(count -> count > 0);
    }

    /**
     * 统计角色的权限数量
     */
    public static io.smallrye.mutiny.Uni<Long> countByRoleId(Long roleId) {
        return count("roleId = ?1 and deleted = 0 and status = 1", roleId);
    }

    /**
     * 统计权限被分配给多少个角色
     */
    public static io.smallrye.mutiny.Uni<Long> countByPermissionId(Long permissionId) {
        return count("permissionId = ?1 and deleted = 0 and status = 1", permissionId);
    }
}
