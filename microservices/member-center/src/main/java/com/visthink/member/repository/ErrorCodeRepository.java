package com.visthink.member.repository;

import com.visthink.member.entity.ErrorCode;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 错误码数据访问层
 * 
 * 提供错误码相关的数据库操作方法
 * 支持多租户数据隔离和响应式编程
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class ErrorCodeRepository implements PanacheRepository<ErrorCode> {

    /**
     * 根据ID和租户ID查询错误码
     * 
     * @param id 错误码ID
     * @param tenantId 租户ID
     * @return 错误码信息
     */
    public Uni<ErrorCode> findByIdAndTenantId(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2 and deleted = 0", id, tenantId).firstResult();
    }

    /**
     * 根据错误码和租户ID查询
     * 
     * @param errorCode 错误码
     * @param tenantId 租户ID
     * @return 错误码信息
     */
    public Uni<ErrorCode> findByErrorCodeAndTenantId(String errorCode, Long tenantId) {
        return find("errorCode = ?1 and tenantId = ?2 and deleted = 0", errorCode, tenantId).firstResult();
    }

    /**
     * 根据租户ID查询所有错误码
     * 
     * @param tenantId 租户ID
     * @return 错误码列表
     */
    public Uni<List<ErrorCode>> findByTenantId(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", Sort.by("moduleName", "errorCode"), tenantId).list();
    }

    /**
     * 根据模块名称和租户ID查询错误码
     * 
     * @param moduleName 模块名称
     * @param tenantId 租户ID
     * @return 错误码列表
     */
    public Uni<List<ErrorCode>> findByModuleNameAndTenantId(String moduleName, Long tenantId) {
        return find("moduleName = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("errorCode"), moduleName, tenantId).list();
    }

    /**
     * 根据错误级别和租户ID查询错误码
     * 
     * @param errorLevel 错误级别
     * @param tenantId 租户ID
     * @return 错误码列表
     */
    public Uni<List<ErrorCode>> findByErrorLevelAndTenantId(Integer errorLevel, Long tenantId) {
        return find("errorLevel = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("moduleName", "errorCode"), errorLevel, tenantId).list();
    }

    /**
     * 查询所有模块名称
     *
     * @param tenantId 租户ID
     * @return 模块名称列表
     */
    public Uni<List<String>> findAllModuleNames(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", tenantId)
                .list()
                .map(errorCodes -> errorCodes.stream()
                        .map(errorCode -> ((ErrorCode) errorCode).getModuleName())
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList()));
    }

    /**
     * 分页查询错误码
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param moduleName 模块名称
     * @param errorLevel 错误级别
     * @return 分页结果
     */
    public Uni<PageResult<ErrorCode>> findByPage(Long tenantId, PageRequest pageRequest, 
                                                String keyword, String moduleName, Integer errorLevel) {
        
        // 构建查询条件
        StringBuilder queryBuilder = new StringBuilder("tenantId = ?1 and deleted = 0");
        int paramIndex = 2;
        
        // 添加关键词搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryBuilder.append(" and (errorCode like ?").append(paramIndex)
                       .append(" or errorName like ?").append(paramIndex)
                       .append(" or errorMessage like ?").append(paramIndex)
                       .append(" or description like ?").append(paramIndex).append(")");
            paramIndex++;
        }
        
        // 添加模块名称条件
        if (moduleName != null && !moduleName.trim().isEmpty()) {
            queryBuilder.append(" and moduleName = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加错误级别条件
        if (errorLevel != null) {
            queryBuilder.append(" and errorLevel = ?").append(paramIndex);
            paramIndex++;
        }
        
        String query = queryBuilder.toString();
        
        // 构建参数数组
        Object[] params = buildParams(tenantId, keyword, moduleName, errorLevel);
        
        // 执行分页查询
        Page page = Page.of(pageRequest.getPage() - 1, pageRequest.getSize());
        Sort sort = Sort.by("moduleName").and("errorCode");
        
        return find(query, sort, params).page(page).list()
                .flatMap(errorCodes -> count(query, params)
                        .map(total -> PageResult.of(errorCodes, total, pageRequest.getPage(), pageRequest.getSize())));
    }

    /**
     * 构建查询参数数组
     */
    private Object[] buildParams(Long tenantId, String keyword, String moduleName, Integer errorLevel) {
        java.util.List<Object> paramList = new java.util.ArrayList<>();
        paramList.add(tenantId);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            String likeKeyword = "%" + keyword.trim() + "%";
            paramList.add(likeKeyword);
        }
        
        if (moduleName != null && !moduleName.trim().isEmpty()) {
            paramList.add(moduleName);
        }
        
        if (errorLevel != null) {
            paramList.add(errorLevel);
        }
        
        return paramList.toArray();
    }

    /**
     * 检查错误码是否存在
     * 
     * @param errorCode 错误码
     * @param tenantId 租户ID
     * @param excludeId 排除的错误码ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByErrorCode(String errorCode, Long tenantId, Long excludeId) {
        String query = excludeId != null 
            ? "errorCode = ?1 and tenantId = ?2 and id != ?3 and deleted = 0"
            : "errorCode = ?1 and tenantId = ?2 and deleted = 0";
        
        Object[] params = excludeId != null 
            ? new Object[]{errorCode, tenantId, excludeId}
            : new Object[]{errorCode, tenantId};
        
        return count(query, params).map(count -> count > 0);
    }

    /**
     * 统计租户下的错误码数量
     * 
     * @param tenantId 租户ID
     * @return 错误码数量
     */
    public Uni<Long> countByTenantId(Long tenantId) {
        return count("tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 统计模块错误码数量
     * 
     * @param moduleName 模块名称
     * @param tenantId 租户ID
     * @return 模块错误码数量
     */
    public Uni<Long> countByModuleName(String moduleName, Long tenantId) {
        return count("moduleName = ?1 and tenantId = ?2 and deleted = 0", moduleName, tenantId);
    }

    /**
     * 统计各错误级别数量
     *
     * @param tenantId 租户ID
     * @return 错误级别统计
     */
    public Uni<Map<Integer, Long>> countByErrorLevel(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", tenantId)
                .list()
                .map(errorCodes -> errorCodes.stream()
                        .map(errorCode -> (ErrorCode) errorCode)
                        .collect(Collectors.groupingBy(
                                ErrorCode::getErrorLevel,
                                Collectors.counting()
                        )));
    }

    /**
     * 统计各模块错误码数量
     *
     * @param tenantId 租户ID
     * @return 模块错误码统计
     */
    public Uni<Map<String, Long>> countByModuleName(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", tenantId)
                .list()
                .map(errorCodes -> errorCodes.stream()
                        .map(errorCode -> (ErrorCode) errorCode)
                        .collect(Collectors.groupingBy(
                                ErrorCode::getModuleName,
                                Collectors.counting()
                        )));
    }

    /**
     * 软删除错误码
     * 
     * @param id 错误码ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    public Uni<Boolean> softDelete(Long id, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 批量软删除错误码
     * 
     * @param ids 错误码ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "deleted = 1, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ") and tenantId = ?";
        
        Object[] params = new Object[ids.size() + 1];
        for (int i = 0; i < ids.size(); i++) {
            params[i] = ids.get(i);
        }
        params[ids.size()] = tenantId;
        
        return update(query, params);
    }

    /**
     * 查询系统错误码
     * 
     * @param tenantId 系统租户ID（通常为0）
     * @return 系统错误码列表
     */
    public Uni<List<ErrorCode>> findSystemErrorCodes(Long tenantId) {
        return find("tenantId = ?1 and isSystem = true and deleted = 0", 
                   Sort.by("moduleName", "errorCode"), tenantId).list();
    }

    /**
     * 根据错误码获取错误消息
     * 
     * @param errorCode 错误码
     * @param tenantId 租户ID
     * @param language 语言
     * @return 错误消息
     */
    public Uni<String> getErrorMessage(String errorCode, Long tenantId, String language) {
        return findByErrorCodeAndTenantId(errorCode, tenantId)
                .map(error -> {
                    if (error == null) {
                        return "未知错误";
                    }
                    return error.getErrorMessage(language);
                });
    }

    /**
     * 根据错误码获取HTTP状态码
     * 
     * @param errorCode 错误码
     * @param tenantId 租户ID
     * @return HTTP状态码
     */
    public Uni<Integer> getHttpStatus(String errorCode, Long tenantId) {
        return findByErrorCodeAndTenantId(errorCode, tenantId)
                .map(error -> error != null ? error.getHttpStatus() : 500);
    }

    /**
     * 获取错误码映射表
     * 
     * @param tenantId 租户ID
     * @param moduleName 模块名称（可选）
     * @return 错误码映射表
     */
    public Uni<Map<String, String>> getErrorCodeMap(Long tenantId, String moduleName) {
        Uni<List<ErrorCode>> errorCodesUni;
        if (moduleName != null && !moduleName.trim().isEmpty()) {
            errorCodesUni = findByModuleNameAndTenantId(moduleName, tenantId);
        } else {
            errorCodesUni = findByTenantId(tenantId);
        }
        
        return errorCodesUni.map(errorCodes -> 
                errorCodes.stream()
                        .collect(Collectors.toMap(
                                ErrorCode::getErrorCode,
                                ErrorCode::getErrorMessage,
                                (existing, replacement) -> existing
                        )));
    }
}
