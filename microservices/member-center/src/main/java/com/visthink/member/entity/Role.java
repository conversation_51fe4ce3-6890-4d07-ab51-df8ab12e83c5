package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.util.List;

/**
 * 角色实体类
 * 
 * 管理系统角色信息，支持多租户数据隔离
 * 包含角色基本信息、权限关联、数据范围等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "user_roles", 
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"tenant_id", "role_code"})
       },
       indexes = {
           @Index(name = "idx_role_tenant_code", columnList = "tenant_id, role_code"),
           @Index(name = "idx_role_type", columnList = "role_type"),
           @Index(name = "idx_role_status", columnList = "status")
       })
@EqualsAndHashCode(callSuper = true)
public class Role extends BaseEntity {

    /**
     * 角色编码（租户内唯一）
     */
    @Column(name = "role_code", nullable = false, length = 50)
    private String roleCode;

    /**
     * 角色名称
     */
    @Column(name = "role_name", nullable = false, length = 100)
    private String roleName;

    /**
     * 角色类型：1-普通角色，2-租户管理员，3-系统管理员
     */
    @Column(name = "role_type", nullable = false)
    private Integer roleType = 1;

    /**
     * 角色描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 权限列表（JSON格式存储）
     * 存储格式：["user:read", "user:create", "product:read"]
     */
    @Column(name = "permissions", columnDefinition = "TEXT")
    private String permissions;

    /**
     * 是否系统内置角色
     */
    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;

    /**
     * 是否默认角色（新用户自动分配）
     */
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;

    /**
     * 数据权限范围：1-仅本人，2-本部门，3-本部门及下级，4-全部数据
     */
    @Column(name = "data_scope")
    private Integer dataScope = 1;

    /**
     * 自定义数据权限部门ID列表（JSON格式）
     */
    @Column(name = "data_scope_dept_ids", columnDefinition = "TEXT")
    private String dataScopeDeptIds;

    // ==================== 关联关系 ====================

    /**
     * 角色用户关联列表（一对多关系）
     */
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<UserRole> userRoles;

    /**
     * 角色权限关联列表（一对多关系）
     */
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RolePermission> rolePermissions;

    // ==================== 常量定义 ====================

    /**
     * 角色类型常量
     */
    public static class RoleType {
        /** 普通角色 */
        public static final Integer NORMAL = 1;
        /** 租户管理员 */
        public static final Integer TENANT_ADMIN = 2;
        /** 系统管理员 */
        public static final Integer SYSTEM_ADMIN = 3;
    }

    /**
     * 数据权限范围常量
     */
    public static class DataScope {
        /** 仅本人数据 */
        public static final Integer SELF_ONLY = 1;
        /** 本部门数据 */
        public static final Integer DEPT_ONLY = 2;
        /** 本部门及下级部门数据 */
        public static final Integer DEPT_AND_SUB = 3;
        /** 全部数据 */
        public static final Integer ALL_DATA = 4;
        /** 自定义数据权限 */
        public static final Integer CUSTOM = 5;
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否为系统管理员角色
     */
    public boolean isSystemAdmin() {
        return RoleType.SYSTEM_ADMIN.equals(this.roleType);
    }

    /**
     * 判断是否为租户管理员角色
     */
    public boolean isTenantAdmin() {
        return RoleType.TENANT_ADMIN.equals(this.roleType);
    }

    /**
     * 判断是否为普通角色
     */
    public boolean isNormalRole() {
        return RoleType.NORMAL.equals(this.roleType);
    }

    /**
     * 判断是否有全部数据权限
     */
    public boolean hasAllDataPermission() {
        return DataScope.ALL_DATA.equals(this.dataScope) || isSystemAdmin();
    }

    /**
     * 获取角色类型描述
     */
    public String getRoleTypeDesc() {
        switch (this.roleType) {
            case 1:
                return "普通角色";
            case 2:
                return "租户管理员";
            case 3:
                return "系统管理员";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取数据权限范围描述
     */
    public String getDataScopeDesc() {
        if (this.dataScope == null) {
            return "仅本人";
        }
        switch (this.dataScope) {
            case 1:
                return "仅本人";
            case 2:
                return "本部门";
            case 3:
                return "本部门及下级";
            case 4:
                return "全部数据";
            case 5:
                return "自定义";
            default:
                return "仅本人";
        }
    }
}
