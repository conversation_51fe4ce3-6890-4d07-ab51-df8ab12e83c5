package com.visthink.member.dto;

import lombok.Data;
import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 菜单创建请求DTO
 * 
 * 用于接收创建菜单的请求参数
 * 包含菜单基本信息和权限配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class MenuCreateRequest {

    /**
     * 菜单编码（租户内唯一）
     */
    @NotBlank(message = "菜单编码不能为空")
    @Size(max = 50, message = "菜单编码长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_:-]+$", message = "菜单编码只能包含字母、数字、下划线、冒号和横线")
    private String menuCode;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 100, message = "菜单名称长度不能超过100个字符")
    private String menuName;

    /**
     * 菜单类型：1-目录，2-菜单，3-按钮
     */
    @NotNull(message = "菜单类型不能为空")
    @Min(value = 1, message = "菜单类型值必须大于等于1")
    @Max(value = 3, message = "菜单类型值必须小于等于3")
    private Integer menuType = 2;

    /**
     * 父菜单ID（构建菜单树）
     */
    private Long parentId;

    /**
     * 菜单路径（前端路由路径）
     */
    @Size(max = 200, message = "菜单路径长度不能超过200个字符")
    private String menuPath;

    /**
     * 组件路径（前端组件路径）
     */
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    private String componentPath;

    /**
     * 菜单图标
     */
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    private String menuIcon;

    /**
     * 权限标识（对应权限编码）
     */
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    private String permissionCode;

    /**
     * 是否外链：false-否，true-是
     */
    private Boolean isExternal = false;

    /**
     * 是否缓存：false-否，true-是
     */
    private Boolean isCached = false;

    /**
     * 是否显示：false-隐藏，true-显示
     */
    private Boolean isVisible = true;

    /**
     * 菜单描述
     */
    @Size(max = 500, message = "菜单描述长度不能超过500个字符")
    private String description;

    /**
     * 按钮权限列表
     */
    private List<ButtonPermission> buttonPermissions;

    /**
     * 菜单元数据（JSON格式字符串）
     */
    private String metaData;

    /**
     * 排序号
     */
    @Min(value = 0, message = "排序号不能为负数")
    private Integer sortOrder = 0;

    /**
     * 备注
     */
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    // ==================== 业务验证方法 ====================

    /**
     * 获取菜单类型描述
     */
    public String getMenuTypeDesc() {
        if (menuType == null) {
            return "菜单";
        }
        switch (menuType) {
            case 1:
                return "目录";
            case 2:
                return "菜单";
            case 3:
                return "按钮";
            default:
                return "未知类型";
        }
    }

    /**
     * 判断是否为目录
     */
    public boolean isDirectory() {
        return Integer.valueOf(1).equals(menuType);
    }

    /**
     * 判断是否为菜单
     */
    public boolean isMenu() {
        return Integer.valueOf(2).equals(menuType);
    }

    /**
     * 判断是否为按钮
     */
    public boolean isButton() {
        return Integer.valueOf(3).equals(menuType);
    }

    /**
     * 判断是否为根菜单（无父菜单）
     */
    public boolean isRootMenu() {
        return parentId == null;
    }

    /**
     * 判断是否为外链菜单
     */
    public boolean isExternalLink() {
        return Boolean.TRUE.equals(isExternal);
    }

    /**
     * 判断菜单是否可见
     */
    public boolean isMenuVisible() {
        return Boolean.TRUE.equals(isVisible);
    }

    /**
     * 验证菜单路径格式
     */
    public boolean isValidMenuPath() {
        if (isButton()) {
            return true; // 按钮不需要路径
        }
        
        if (isDirectory() && (menuPath == null || menuPath.trim().isEmpty())) {
            return true; // 目录可以没有路径
        }
        
        if (isMenu() && (menuPath == null || menuPath.trim().isEmpty())) {
            return false; // 菜单必须有路径
        }
        
        if (menuPath != null && !menuPath.trim().isEmpty()) {
            // 外链菜单可以是完整URL，内部菜单必须以/开头
            if (isExternalLink()) {
                return menuPath.startsWith("http://") || menuPath.startsWith("https://");
            } else {
                return menuPath.startsWith("/");
            }
        }
        
        return true;
    }

    /**
     * 验证组件路径格式
     */
    public boolean isValidComponentPath() {
        if (isButton() || isDirectory()) {
            return true; // 按钮和目录不需要组件路径
        }
        
        if (isExternalLink()) {
            return true; // 外链菜单不需要组件路径
        }
        
        // 内部菜单需要组件路径
        return componentPath != null && !componentPath.trim().isEmpty();
    }

    /**
     * 验证权限标识
     */
    public boolean isValidPermissionCode() {
        if (isDirectory()) {
            return true; // 目录可以没有权限标识
        }
        
        // 菜单和按钮需要权限标识
        return permissionCode != null && !permissionCode.trim().isEmpty();
    }

    /**
     * 自动设置默认值
     */
    public void setDefaults() {
        if (isDirectory()) {
            // 目录默认设置
            if (menuIcon == null || menuIcon.trim().isEmpty()) {
                menuIcon = "ant-design:folder-outlined";
            }
            isExternal = false;
            isCached = false;
        } else if (isMenu()) {
            // 菜单默认设置
            if (menuIcon == null || menuIcon.trim().isEmpty()) {
                menuIcon = "ant-design:file-outlined";
            }
            if (isCached == null) {
                isCached = false;
            }
        } else if (isButton()) {
            // 按钮默认设置
            isExternal = false;
            isCached = false;
            isVisible = true;
        }
    }

    /**
     * 按钮权限内部类
     */
    @Data
    public static class ButtonPermission {
        /**
         * 按钮编码
         */
        @NotBlank(message = "按钮编码不能为空")
        private String code;

        /**
         * 按钮名称
         */
        @NotBlank(message = "按钮名称不能为空")
        private String name;

        /**
         * 权限编码
         */
        private String permissionCode;

        /**
         * 按钮图标
         */
        private String icon;

        /**
         * 按钮类型
         */
        private String type;

        /**
         * 是否启用
         */
        private Boolean enabled = true;

        /**
         * 排序号
         */
        private Integer sortOrder = 0;
    }
}

/**
 * 菜单更新请求DTO
 * 
 * 用于接收更新菜单的请求参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
class MenuUpdateRequest {

    /**
     * 菜单ID（必填）
     */
    @NotNull(message = "菜单ID不能为空")
    private Long id;

    /**
     * 菜单编码（租户内唯一）
     */
    @NotBlank(message = "菜单编码不能为空")
    @Size(max = 50, message = "菜单编码长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_:-]+$", message = "菜单编码只能包含字母、数字、下划线、冒号和横线")
    private String menuCode;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 100, message = "菜单名称长度不能超过100个字符")
    private String menuName;

    /**
     * 菜单类型：1-目录，2-菜单，3-按钮
     */
    @NotNull(message = "菜单类型不能为空")
    @Min(value = 1, message = "菜单类型值必须大于等于1")
    @Max(value = 3, message = "菜单类型值必须小于等于3")
    private Integer menuType = 2;

    /**
     * 父菜单ID（构建菜单树）
     */
    private Long parentId;

    /**
     * 菜单路径（前端路由路径）
     */
    @Size(max = 200, message = "菜单路径长度不能超过200个字符")
    private String menuPath;

    /**
     * 组件路径（前端组件路径）
     */
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    private String componentPath;

    /**
     * 菜单图标
     */
    @Size(max = 100, message = "菜单图标长度不能超过100个字符")
    private String menuIcon;

    /**
     * 权限标识（对应权限编码）
     */
    @Size(max = 100, message = "权限标识长度不能超过100个字符")
    private String permissionCode;

    /**
     * 是否外链：false-否，true-是
     */
    private Boolean isExternal = false;

    /**
     * 是否缓存：false-否，true-是
     */
    private Boolean isCached = false;

    /**
     * 是否显示：false-隐藏，true-显示
     */
    private Boolean isVisible = true;

    /**
     * 菜单描述
     */
    @Size(max = 500, message = "菜单描述长度不能超过500个字符")
    private String description;

    /**
     * 按钮权限列表
     */
    private List<MenuCreateRequest.ButtonPermission> buttonPermissions;

    /**
     * 菜单元数据（JSON格式字符串）
     */
    private String metaData;

    /**
     * 排序号
     */
    @Min(value = 0, message = "排序号不能为负数")
    private Integer sortOrder = 0;

    /**
     * 状态：1-启用，0-禁用
     */
    @Min(value = 0, message = "状态值必须大于等于0")
    @Max(value = 1, message = "状态值必须小于等于1")
    private Integer status = 1;

    /**
     * 备注
     */
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    // ==================== 业务方法 ====================

    /**
     * 判断是否启用
     */
    public boolean isEnabled() {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 判断是否禁用
     */
    public boolean isDisabled() {
        return Integer.valueOf(0).equals(status);
    }
}
