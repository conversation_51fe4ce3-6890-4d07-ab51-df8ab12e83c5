package com.visthink.member.dto;

import lombok.Data;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * 用户角色查询请求DTO
 * 
 * 用于接收用户角色查询的请求参数
 * 支持多条件组合查询
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserRoleQueryRequest {

    /**
     * 页码（从1开始）
     */
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 用户ID（可选）
     */
    private Long userId;

    /**
     * 角色ID（可选）
     */
    private Long roleId;

    /**
     * 状态（可选）
     * 1-启用，0-禁用
     */
    private Integer status;

    /**
     * 是否包含已过期的角色关联
     */
    private Boolean includeExpired = false;

    /**
     * 授权人ID（可选）
     */
    private Long grantedBy;

    /**
     * 开始时间（可选）
     * 查询指定时间范围内的角色关联
     */
    private String startTime;

    /**
     * 结束时间（可选）
     * 查询指定时间范围内的角色关联
     */
    private String endTime;

    /**
     * 排序字段（可选）
     * 默认按创建时间倒序
     */
    private String sortField = "createTime";

    /**
     * 排序方向（可选）
     * asc-升序，desc-降序
     */
    private String sortDirection = "desc";

    /**
     * 搜索关键词（可选）
     * 可搜索角色名称、用户名等
     */
    private String keyword;

    /**
     * 验证请求参数是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return page != null && page > 0 && size != null && size > 0;
    }

    /**
     * 获取偏移量
     * 
     * @return 偏移量
     */
    public int getOffset() {
        return (page - 1) * size;
    }

    /**
     * 获取限制数量
     * 
     * @return 限制数量
     */
    public int getLimit() {
        return size;
    }

    /**
     * 是否有用户ID条件
     * 
     * @return 是否有用户ID条件
     */
    public boolean hasUserIdCondition() {
        return userId != null;
    }

    /**
     * 是否有角色ID条件
     * 
     * @return 是否有角色ID条件
     */
    public boolean hasRoleIdCondition() {
        return roleId != null;
    }

    /**
     * 是否有状态条件
     * 
     * @return 是否有状态条件
     */
    public boolean hasStatusCondition() {
        return status != null;
    }

    /**
     * 是否有时间范围条件
     * 
     * @return 是否有时间范围条件
     */
    public boolean hasTimeRangeCondition() {
        return startTime != null && endTime != null;
    }

    /**
     * 是否有关键词搜索条件
     * 
     * @return 是否有关键词搜索条件
     */
    public boolean hasKeywordCondition() {
        return keyword != null && !keyword.trim().isEmpty();
    }

    /**
     * 是否升序排序
     * 
     * @return 是否升序排序
     */
    public boolean isAscending() {
        return "asc".equalsIgnoreCase(sortDirection);
    }

    /**
     * 是否降序排序
     * 
     * @return 是否降序排序
     */
    public boolean isDescending() {
        return "desc".equalsIgnoreCase(sortDirection);
    }
}
