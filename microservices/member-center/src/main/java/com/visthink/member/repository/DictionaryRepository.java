package com.visthink.member.repository;

import com.visthink.member.entity.Dictionary;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * 字典数据访问层
 * 
 * 提供字典相关的数据库操作方法
 * 支持多租户数据隔离和响应式编程
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class DictionaryRepository implements PanacheRepository<Dictionary> {

    /**
     * 根据ID和租户ID查询字典
     * 
     * @param id 字典ID
     * @param tenantId 租户ID
     * @return 字典信息
     */
    public Uni<Dictionary> findByIdAndTenantId(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2 and deleted = 0", id, tenantId).firstResult();
    }

    /**
     * 根据字典编码和租户ID查询字典
     * 
     * @param dictCode 字典编码
     * @param tenantId 租户ID
     * @return 字典信息
     */
    public Uni<Dictionary> findByDictCodeAndTenantId(String dictCode, Long tenantId) {
        return find("dictCode = ?1 and tenantId = ?2 and deleted = 0", dictCode, tenantId).firstResult();
    }

    /**
     * 根据租户ID查询所有字典
     * 
     * @param tenantId 租户ID
     * @return 字典列表
     */
    public Uni<List<Dictionary>> findByTenantId(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 根据字典类型和租户ID查询字典
     * 
     * @param dictType 字典类型
     * @param tenantId 租户ID
     * @return 字典列表
     */
    public Uni<List<Dictionary>> findByDictTypeAndTenantId(Integer dictType, Long tenantId) {
        return find("dictType = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), dictType, tenantId).list();
    }

    /**
     * 查询字典类型
     * 
     * @param tenantId 租户ID
     * @return 字典类型列表
     */
    public Uni<List<Dictionary>> findDictionaryTypes(Long tenantId) {
        return find("dictType = 1 and tenantId = ?1 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 根据父字典ID查询子字典
     * 
     * @param parentId 父字典ID
     * @param tenantId 租户ID
     * @return 子字典列表
     */
    public Uni<List<Dictionary>> findByParentId(Long parentId, Long tenantId) {
        return find("parentId = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), parentId, tenantId).list();
    }

    /**
     * 根据字典类型编码查询字典项
     * 
     * @param dictTypeCode 字典类型编码
     * @param tenantId 租户ID
     * @return 字典项列表
     */
    public Uni<List<Dictionary>> findDictionaryItems(String dictTypeCode, Long tenantId) {
        String query = """
            SELECT d FROM Dictionary d 
            WHERE d.parentId = (
                SELECT dt.id FROM Dictionary dt 
                WHERE dt.dictCode = ?1 and dt.dictType = 1 and dt.tenantId = ?2 and dt.deleted = 0
            ) and d.tenantId = ?2 and d.deleted = 0
            ORDER BY d.sortOrder, d.createTime
            """;
        return find(query, dictTypeCode, tenantId).list();
    }

    /**
     * 根据字典编码和值查询字典项
     * 
     * @param dictCode 字典编码
     * @param dictValue 字典值
     * @param tenantId 租户ID
     * @return 字典项
     */
    public Uni<Dictionary> findByDictCodeAndValue(String dictCode, String dictValue, Long tenantId) {
        String query = """
            SELECT d FROM Dictionary d 
            WHERE d.parentId = (
                SELECT dt.id FROM Dictionary dt 
                WHERE dt.dictCode = ?1 and dt.dictType = 1 and dt.tenantId = ?3 and dt.deleted = 0
            ) and d.dictValue = ?2 and d.tenantId = ?3 and d.deleted = 0
            """;
        return find(query, dictCode, dictValue, tenantId).firstResult();
    }

    /**
     * 查询字典树结构
     * 
     * @param tenantId 租户ID
     * @return 字典树列表
     */
    public Uni<List<Dictionary>> findDictionaryTree(Long tenantId) {
        // 查询所有字典，在服务层构建树结构
        return find("tenantId = ?1 and deleted = 0", Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 分页查询字典
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param dictType 字典类型
     * @param parentId 父字典ID
     * @return 分页结果
     */
    public Uni<PageResult<Dictionary>> findByPage(Long tenantId, PageRequest pageRequest, 
                                                String keyword, Integer dictType, Long parentId) {
        
        // 构建查询条件
        StringBuilder queryBuilder = new StringBuilder("tenantId = ?1 and deleted = 0");
        int paramIndex = 2;
        
        // 添加关键词搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryBuilder.append(" and (dictName like ?").append(paramIndex)
                       .append(" or dictCode like ?").append(paramIndex)
                       .append(" or dictLabel like ?").append(paramIndex)
                       .append(" or description like ?").append(paramIndex).append(")");
            paramIndex++;
        }
        
        // 添加字典类型条件
        if (dictType != null) {
            queryBuilder.append(" and dictType = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加父字典ID条件
        if (parentId != null) {
            queryBuilder.append(" and parentId = ?").append(paramIndex);
            paramIndex++;
        }
        
        String query = queryBuilder.toString();
        
        // 构建参数数组
        Object[] params = buildParams(tenantId, keyword, dictType, parentId);
        
        // 执行分页查询
        Page page = Page.of(pageRequest.getPage() - 1, pageRequest.getSize());
        Sort sort = Sort.by("sortOrder").and("createTime", Sort.Direction.Descending);
        
        return find(query, sort, params).page(page).list()
                .flatMap(dictionaries -> count(query, params)
                        .map(total -> PageResult.of(dictionaries, total, pageRequest.getPage(), pageRequest.getSize())));
    }

    /**
     * 构建查询参数数组
     */
    private Object[] buildParams(Long tenantId, String keyword, Integer dictType, Long parentId) {
        java.util.List<Object> paramList = new java.util.ArrayList<>();
        paramList.add(tenantId);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            String likeKeyword = "%" + keyword.trim() + "%";
            paramList.add(likeKeyword);
        }
        
        if (dictType != null) {
            paramList.add(dictType);
        }
        
        if (parentId != null) {
            paramList.add(parentId);
        }
        
        return paramList.toArray();
    }

    /**
     * 检查字典编码是否存在
     * 
     * @param dictCode 字典编码
     * @param tenantId 租户ID
     * @param excludeId 排除的字典ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByDictCode(String dictCode, Long tenantId, Long excludeId) {
        String query = excludeId != null 
            ? "dictCode = ?1 and tenantId = ?2 and id != ?3 and deleted = 0"
            : "dictCode = ?1 and tenantId = ?2 and deleted = 0";
        
        Object[] params = excludeId != null 
            ? new Object[]{dictCode, tenantId, excludeId}
            : new Object[]{dictCode, tenantId};
        
        return count(query, params).map(count -> count > 0);
    }

    /**
     * 检查字典值是否存在
     * 
     * @param dictCode 字典编码
     * @param dictValue 字典值
     * @param tenantId 租户ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByDictValue(String dictCode, String dictValue, Long tenantId) {
        String query = """
            SELECT COUNT(d) FROM Dictionary d 
            WHERE d.parentId = (
                SELECT dt.id FROM Dictionary dt 
                WHERE dt.dictCode = ?1 and dt.dictType = 1 and dt.tenantId = ?3 and dt.deleted = 0
            ) and d.dictValue = ?2 and d.tenantId = ?3 and d.deleted = 0
            """;
        return find(query, dictCode, dictValue, tenantId).count().map(count -> count > 0);
    }

    /**
     * 统计租户下的字典数量
     * 
     * @param tenantId 租户ID
     * @return 字典数量
     */
    public Uni<Long> countByTenantId(Long tenantId) {
        return count("tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 统计字典类型数量
     * 
     * @param tenantId 租户ID
     * @return 字典类型数量
     */
    public Uni<Long> countDictionaryTypes(Long tenantId) {
        return count("dictType = 1 and tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 统计字典项数量
     * 
     * @param dictTypeCode 字典类型编码
     * @param tenantId 租户ID
     * @return 字典项数量
     */
    public Uni<Long> countDictionaryItems(String dictTypeCode, Long tenantId) {
        String query = """
            SELECT COUNT(d) FROM Dictionary d 
            WHERE d.parentId = (
                SELECT dt.id FROM Dictionary dt 
                WHERE dt.dictCode = ?1 and dt.dictType = 1 and dt.tenantId = ?2 and dt.deleted = 0
            ) and d.tenantId = ?2 and d.deleted = 0
            """;
        return find(query, dictTypeCode, tenantId).count();
    }

    /**
     * 检查是否有子字典
     * 
     * @param parentId 父字典ID
     * @param tenantId 租户ID
     * @return 是否有子字典
     */
    public Uni<Boolean> hasChildren(Long parentId, Long tenantId) {
        return count("parentId = ?1 and tenantId = ?2 and deleted = 0", parentId, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 软删除字典
     * 
     * @param id 字典ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    public Uni<Boolean> softDelete(Long id, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 批量软删除字典
     * 
     * @param ids 字典ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "deleted = 1, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ") and tenantId = ?";
        
        Object[] params = new Object[ids.size() + 1];
        for (int i = 0; i < ids.size(); i++) {
            params[i] = ids.get(i);
        }
        params[ids.size()] = tenantId;
        
        return update(query, params);
    }
}
