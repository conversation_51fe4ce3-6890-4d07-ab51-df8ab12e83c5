package com.visthink.member.service;

import com.visthink.member.entity.ErrorCode;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;

import java.util.List;
import java.util.Map;

/**
 * 错误码服务接口
 * 
 * 提供错误码管理的业务逻辑接口
 * 包含错误码CRUD、模块管理、多语言支持等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ErrorCodeService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建错误码
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码信息
     * @return 创建结果
     */
    Uni<ApiResponse<ErrorCode>> createErrorCode(Long tenantId, ErrorCode errorCode);

    /**
     * 更新错误码
     * 
     * @param tenantId 租户ID
     * @param errorCodeId 错误码ID
     * @param errorCode 错误码信息
     * @return 更新结果
     */
    Uni<ApiResponse<ErrorCode>> updateErrorCode(Long tenantId, Long errorCodeId, ErrorCode errorCode);

    /**
     * 删除错误码
     * 
     * @param tenantId 租户ID
     * @param errorCodeId 错误码ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteErrorCode(Long tenantId, Long errorCodeId);

    /**
     * 批量删除错误码
     * 
     * @param tenantId 租户ID
     * @param errorCodeIds 错误码ID列表
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteErrorCodes(Long tenantId, List<Long> errorCodeIds);

    /**
     * 根据ID查询错误码
     * 
     * @param tenantId 租户ID
     * @param errorCodeId 错误码ID
     * @return 错误码信息
     */
    Uni<ApiResponse<ErrorCode>> getErrorCodeById(Long tenantId, Long errorCodeId);

    /**
     * 根据错误码查询
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码
     * @return 错误码信息
     */
    Uni<ApiResponse<ErrorCode>> getErrorCodeByCode(Long tenantId, String errorCode);

    // ==================== 查询操作 ====================

    /**
     * 分页查询错误码
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param moduleName 模块名称
     * @param errorLevel 错误级别
     * @return 分页结果
     */
    Uni<ApiResponse<PageResult<ErrorCode>>> getErrorCodeList(Long tenantId, PageRequest pageRequest, 
                                                           String keyword, String moduleName, Integer errorLevel);

    /**
     * 查询所有错误码
     * 
     * @param tenantId 租户ID
     * @return 错误码列表
     */
    Uni<ApiResponse<List<ErrorCode>>> getAllErrorCodes(Long tenantId);

    /**
     * 根据模块查询错误码
     * 
     * @param tenantId 租户ID
     * @param moduleName 模块名称
     * @return 错误码列表
     */
    Uni<ApiResponse<List<ErrorCode>>> getErrorCodesByModule(Long tenantId, String moduleName);

    /**
     * 根据错误级别查询错误码
     * 
     * @param tenantId 租户ID
     * @param errorLevel 错误级别
     * @return 错误码列表
     */
    Uni<ApiResponse<List<ErrorCode>>> getErrorCodesByLevel(Long tenantId, Integer errorLevel);

    /**
     * 查询所有模块名称
     * 
     * @param tenantId 租户ID
     * @return 模块名称列表
     */
    Uni<ApiResponse<List<String>>> getAllModuleNames(Long tenantId);

    // ==================== 错误码查询 ====================

    /**
     * 根据错误码获取错误消息
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码
     * @param language 语言（zh-中文，en-英文）
     * @return 错误消息
     */
    Uni<ApiResponse<String>> getErrorMessage(Long tenantId, String errorCode, String language);

    /**
     * 根据错误码获取HTTP状态码
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码
     * @return HTTP状态码
     */
    Uni<ApiResponse<Integer>> getHttpStatus(Long tenantId, String errorCode);

    /**
     * 根据错误码获取解决方案
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码
     * @return 解决方案
     */
    Uni<ApiResponse<String>> getSolution(Long tenantId, String errorCode);

    /**
     * 获取错误码映射表
     * 
     * @param tenantId 租户ID
     * @param moduleName 模块名称（可选）
     * @return 错误码映射表
     */
    Uni<ApiResponse<Map<String, String>>> getErrorCodeMap(Long tenantId, String moduleName);

    // ==================== 错误码验证 ====================

    /**
     * 检查错误码是否存在
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码
     * @param excludeId 排除的错误码ID
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> checkErrorCodeExists(Long tenantId, String errorCode, Long excludeId);

    /**
     * 验证错误码格式
     * 
     * @param errorCode 错误码
     * @return 验证结果
     */
    ApiResponse<Boolean> validateErrorCodeFormat(String errorCode);

    /**
     * 验证错误码是否可以删除
     * 
     * @param tenantId 租户ID
     * @param errorCodeId 错误码ID
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> canDeleteErrorCode(Long tenantId, Long errorCodeId);

    // ==================== 错误码初始化 ====================

    /**
     * 初始化租户错误码
     * 
     * @param tenantId 租户ID
     * @return 初始化结果
     */
    Uni<ApiResponse<Void>> initTenantErrorCodes(Long tenantId);

    /**
     * 同步系统错误码
     * 
     * @param tenantId 租户ID
     * @return 同步结果
     */
    Uni<ApiResponse<Void>> syncSystemErrorCodes(Long tenantId);

    /**
     * 批量创建模块错误码
     * 
     * @param tenantId 租户ID
     * @param moduleName 模块名称
     * @param errorCodes 错误码列表
     * @return 创建结果
     */
    Uni<ApiResponse<List<ErrorCode>>> createModuleErrorCodes(Long tenantId, String moduleName, List<ErrorCode> errorCodes);

    // ==================== 错误码统计 ====================

    /**
     * 统计租户下的错误码数量
     * 
     * @param tenantId 租户ID
     * @return 错误码数量
     */
    Uni<ApiResponse<Long>> countErrorCodes(Long tenantId);

    /**
     * 统计模块错误码数量
     * 
     * @param tenantId 租户ID
     * @param moduleName 模块名称
     * @return 模块错误码数量
     */
    Uni<ApiResponse<Long>> countModuleErrorCodes(Long tenantId, String moduleName);

    /**
     * 统计各错误级别数量
     * 
     * @param tenantId 租户ID
     * @return 错误级别统计
     */
    Uni<ApiResponse<Map<Integer, Long>>> countErrorCodesByLevel(Long tenantId);

    /**
     * 统计各模块错误码数量
     * 
     * @param tenantId 租户ID
     * @return 模块错误码统计
     */
    Uni<ApiResponse<Map<String, Long>>> countErrorCodesByModule(Long tenantId);

    // ==================== 错误码导入导出 ====================

    /**
     * 导出错误码数据
     * 
     * @param tenantId 租户ID
     * @param moduleName 模块名称（可选）
     * @return 错误码数据
     */
    Uni<ApiResponse<String>> exportErrorCodes(Long tenantId, String moduleName);

    /**
     * 导入错误码数据
     * 
     * @param tenantId 租户ID
     * @param errorCodeData 错误码数据
     * @param overwrite 是否覆盖已存在的错误码
     * @return 导入结果
     */
    Uni<ApiResponse<Void>> importErrorCodes(Long tenantId, String errorCodeData, Boolean overwrite);

    // ==================== 错误码使用统计 ====================

    /**
     * 记录错误码使用
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码
     * @param context 使用上下文
     * @return 记录结果
     */
    Uni<ApiResponse<Void>> recordErrorCodeUsage(Long tenantId, String errorCode, String context);

    /**
     * 查询错误码使用统计
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 使用统计
     */
    Uni<ApiResponse<Map<String, Long>>> getErrorCodeUsageStats(Long tenantId, String errorCode, 
                                                             String startTime, String endTime);

    // ==================== 错误码缓存管理 ====================

    /**
     * 刷新错误码缓存
     * 
     * @param tenantId 租户ID
     * @param errorCode 错误码（可选，为空则刷新所有）
     * @return 刷新结果
     */
    Uni<ApiResponse<Void>> refreshErrorCodeCache(Long tenantId, String errorCode);

    /**
     * 预热错误码缓存
     * 
     * @param tenantId 租户ID
     * @return 预热结果
     */
    Uni<ApiResponse<Void>> warmupErrorCodeCache(Long tenantId);
}
