package com.visthink.member.dto;

import lombok.Data;
import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 角色创建请求DTO
 * 
 * 用于接收创建角色的请求参数
 * 包含角色基本信息和权限配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class RoleCreateRequest {

    /**
     * 角色编码（租户内唯一）
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "角色编码只能包含字母、数字、下划线和横线")
    private String roleCode;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    private String roleName;

    /**
     * 角色类型：1-普通角色，2-租户管理员，3-系统管理员
     */
    @NotNull(message = "角色类型不能为空")
    @Min(value = 1, message = "角色类型值必须大于等于1")
    @Max(value = 3, message = "角色类型值必须小于等于3")
    private Integer roleType;

    /**
     * 角色描述
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String description;

    /**
     * 权限编码列表
     */
    private List<String> permissionCodes;

    /**
     * 权限ID列表
     */
    private List<Long> permissionIds;

    /**
     * 是否默认角色（新用户自动分配）
     */
    private Boolean isDefault = false;

    /**
     * 数据权限范围：1-仅本人，2-本部门，3-本部门及下级，4-全部数据，5-自定义
     */
    @Min(value = 1, message = "数据权限范围值必须大于等于1")
    @Max(value = 5, message = "数据权限范围值必须小于等于5")
    private Integer dataScope = 1;

    /**
     * 自定义数据权限部门ID列表
     */
    private List<Long> dataScopeDeptIds;

    /**
     * 排序号
     */
    @Min(value = 0, message = "排序号不能为负数")
    private Integer sortOrder = 0;

    /**
     * 备注
     */
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    // ==================== 业务验证方法 ====================

    /**
     * 验证权限配置
     */
    public boolean hasValidPermissions() {
        return (permissionCodes != null && !permissionCodes.isEmpty()) ||
               (permissionIds != null && !permissionIds.isEmpty());
    }

    /**
     * 验证数据权限配置
     */
    public boolean hasValidDataScope() {
        // 如果是自定义数据权限，必须提供部门ID列表
        if (Integer.valueOf(5).equals(dataScope)) {
            return dataScopeDeptIds != null && !dataScopeDeptIds.isEmpty();
        }
        return true;
    }

    /**
     * 获取角色类型描述
     */
    public String getRoleTypeDesc() {
        if (roleType == null) {
            return "普通角色";
        }
        switch (roleType) {
            case 1:
                return "普通角色";
            case 2:
                return "租户管理员";
            case 3:
                return "系统管理员";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取数据权限范围描述
     */
    public String getDataScopeDesc() {
        if (dataScope == null) {
            return "仅本人";
        }
        switch (dataScope) {
            case 1:
                return "仅本人";
            case 2:
                return "本部门";
            case 3:
                return "本部门及下级";
            case 4:
                return "全部数据";
            case 5:
                return "自定义";
            default:
                return "仅本人";
        }
    }

    /**
     * 判断是否为系统管理员角色
     */
    public boolean isSystemAdmin() {
        return Integer.valueOf(3).equals(roleType);
    }

    /**
     * 判断是否为租户管理员角色
     */
    public boolean isTenantAdmin() {
        return Integer.valueOf(2).equals(roleType);
    }

    /**
     * 判断是否为普通角色
     */
    public boolean isNormalRole() {
        return Integer.valueOf(1).equals(roleType);
    }
}
