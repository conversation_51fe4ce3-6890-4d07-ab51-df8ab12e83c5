package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.util.List;

/**
 * 权限实体类
 * 
 * 管理系统权限信息，支持层级权限结构
 * 包含API权限、菜单权限、按钮权限等多种权限类型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "permissions",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"permission_code", "tenant_id"})
       },
       indexes = {
           @Index(name = "idx_permission_code", columnList = "permission_code"),
           @Index(name = "idx_permission_parent", columnList = "parent_id"),
           @Index(name = "idx_permission_type", columnList = "permission_type"),
           @Index(name = "idx_permission_tenant", columnList = "tenant_id")
       })
@EqualsAndHashCode(callSuper = true)
public class Permission extends BaseEntity {

    /**
     * 权限编码（租户内唯一）
     * 格式：resource:action，如 user:read, order:create
     */
    @Column(name = "permission_code", nullable = false, length = 100)
    private String permissionCode;

    /**
     * 权限名称
     */
    @Column(name = "permission_name", nullable = false, length = 100)
    private String permissionName;

    /**
     * 权限类型：1-API权限，2-菜单权限，3-按钮权限，4-数据权限
     */
    @Column(name = "permission_type", nullable = false)
    private Integer permissionType = 1;

    /**
     * 父权限ID（用于构建权限树）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 资源类型：API、MENU、BUTTON、DATA
     */
    @Column(name = "resource_type", length = 50)
    private String resourceType;

    /**
     * 资源路径（API路径或菜单路径）
     */
    @Column(name = "resource_path", length = 200)
    private String resourcePath;

    /**
     * 操作类型：GET、POST、PUT、DELETE等
     */
    @Column(name = "action", length = 50)
    private String action;

    /**
     * 权限描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 是否系统内置权限
     */
    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;

    // ==================== 关联关系 ====================

    /**
     * 子权限列表（一对多关系）
     */
    @OneToMany(mappedBy = "parentId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Permission> children;

    /**
     * 父权限实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Permission parent;

    /**
     * 角色权限关联列表（一对多关系）
     */
    @OneToMany(mappedBy = "permission", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RolePermission> rolePermissions;

    // ==================== 常量定义 ====================

    /**
     * 权限类型常量
     */
    public static class PermissionType {
        /** API权限 */
        public static final Integer API = 1;
        /** 菜单权限 */
        public static final Integer MENU = 2;
        /** 按钮权限 */
        public static final Integer BUTTON = 3;
        /** 数据权限 */
        public static final Integer DATA = 4;
    }

    /**
     * 资源类型常量
     */
    public static class ResourceType {
        /** API资源 */
        public static final String API = "API";
        /** 菜单资源 */
        public static final String MENU = "MENU";
        /** 按钮资源 */
        public static final String BUTTON = "BUTTON";
        /** 数据资源 */
        public static final String DATA = "DATA";
    }

    /**
     * 操作类型常量
     */
    public static class Action {
        /** 查看 */
        public static final String READ = "read";
        /** 创建 */
        public static final String CREATE = "create";
        /** 更新 */
        public static final String UPDATE = "update";
        /** 删除 */
        public static final String DELETE = "delete";
        /** 导出 */
        public static final String EXPORT = "export";
        /** 导入 */
        public static final String IMPORT = "import";
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否为API权限
     */
    public boolean isApiPermission() {
        return PermissionType.API.equals(this.permissionType);
    }

    /**
     * 判断是否为菜单权限
     */
    public boolean isMenuPermission() {
        return PermissionType.MENU.equals(this.permissionType);
    }

    /**
     * 判断是否为按钮权限
     */
    public boolean isButtonPermission() {
        return PermissionType.BUTTON.equals(this.permissionType);
    }

    /**
     * 判断是否为数据权限
     */
    public boolean isDataPermission() {
        return PermissionType.DATA.equals(this.permissionType);
    }

    /**
     * 判断是否为根权限（无父权限）
     */
    public boolean isRootPermission() {
        return this.parentId == null;
    }

    /**
     * 判断是否有子权限
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }

    /**
     * 获取权限类型描述
     */
    public String getPermissionTypeDesc() {
        if (this.permissionType == null) {
            return "API权限";
        }
        switch (this.permissionType) {
            case 1:
                return "API权限";
            case 2:
                return "菜单权限";
            case 3:
                return "按钮权限";
            case 4:
                return "数据权限";
            default:
                return "未知类型";
        }
    }

    /**
     * 验证权限编码格式是否正确
     * 格式：resource:action
     */
    public boolean isValidPermissionCode() {
        if (this.permissionCode == null || this.permissionCode.trim().isEmpty()) {
            return false;
        }
        
        String[] parts = this.permissionCode.split(":");
        return parts.length == 2 && 
               !parts[0].trim().isEmpty() && 
               !parts[1].trim().isEmpty();
    }

    /**
     * 获取权限编码中的资源部分
     */
    public String getResourceFromCode() {
        if (!isValidPermissionCode()) {
            return null;
        }
        return this.permissionCode.split(":")[0];
    }

    /**
     * 获取权限编码中的操作部分
     */
    public String getActionFromCode() {
        if (!isValidPermissionCode()) {
            return null;
        }
        return this.permissionCode.split(":")[1];
    }

    // ==================== 便捷构造方法 ====================

    /**
     * 创建API权限
     */
    public static Permission createApiPermission(String code, String name, String resourcePath, 
                                               String action, String description, Long tenantId) {
        Permission permission = new Permission();
        permission.setPermissionCode(code);
        permission.setPermissionName(name);
        permission.setPermissionType(PermissionType.API);
        permission.setResourceType(ResourceType.API);
        permission.setResourcePath(resourcePath);
        permission.setAction(action);
        permission.setDescription(description);
        permission.setTenantId(tenantId);
        permission.setIsSystem(false);
        return permission;
    }

    /**
     * 创建菜单权限
     */
    public static Permission createMenuPermission(String code, String name, String resourcePath, 
                                                Long parentId, String description, Long tenantId) {
        Permission permission = new Permission();
        permission.setPermissionCode(code);
        permission.setPermissionName(name);
        permission.setPermissionType(PermissionType.MENU);
        permission.setResourceType(ResourceType.MENU);
        permission.setResourcePath(resourcePath);
        permission.setParentId(parentId);
        permission.setDescription(description);
        permission.setTenantId(tenantId);
        permission.setIsSystem(false);
        return permission;
    }
}
