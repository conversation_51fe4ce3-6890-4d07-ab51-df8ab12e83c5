package com.visthink.member.service;

import com.visthink.member.entity.Region;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;

import java.util.List;
import java.util.Map;

/**
 * 地区服务接口
 * 
 * 提供地区管理的业务逻辑接口
 * 包含地区CRUD、地区树构建、地理信息查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RegionService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建地区
     * 
     * @param region 地区信息
     * @return 创建结果
     */
    Uni<ApiResponse<Region>> createRegion(Region region);

    /**
     * 更新地区
     * 
     * @param regionId 地区ID
     * @param region 地区信息
     * @return 更新结果
     */
    Uni<ApiResponse<Region>> updateRegion(Long regionId, Region region);

    /**
     * 删除地区
     * 
     * @param regionId 地区ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteRegion(Long regionId);

    /**
     * 批量删除地区
     * 
     * @param regionIds 地区ID列表
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteRegions(List<Long> regionIds);

    /**
     * 根据ID查询地区
     * 
     * @param regionId 地区ID
     * @return 地区信息
     */
    Uni<ApiResponse<Region>> getRegionById(Long regionId);

    /**
     * 根据地区编码查询地区
     * 
     * @param regionCode 地区编码
     * @return 地区信息
     */
    Uni<ApiResponse<Region>> getRegionByCode(String regionCode);

    // ==================== 查询操作 ====================

    /**
     * 分页查询地区
     * 
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param regionLevel 地区级别
     * @param parentId 父地区ID
     * @param isHot 是否热门地区
     * @return 分页结果
     */
    Uni<ApiResponse<PageResult<Region>>> getRegionList(PageRequest pageRequest, String keyword, 
                                                     Integer regionLevel, Long parentId, Boolean isHot);

    /**
     * 查询所有地区
     * 
     * @return 地区列表
     */
    Uni<ApiResponse<List<Region>>> getAllRegions();

    /**
     * 查询地区树
     * 
     * @return 地区树结构
     */
    Uni<ApiResponse<List<Region>>> getRegionTree();

    /**
     * 查询指定级别的地区
     * 
     * @param regionLevel 地区级别
     * @return 地区列表
     */
    Uni<ApiResponse<List<Region>>> getRegionsByLevel(Integer regionLevel);

    /**
     * 查询省份列表
     * 
     * @return 省份列表
     */
    Uni<ApiResponse<List<Region>>> getProvinces();

    /**
     * 查询城市列表
     * 
     * @param provinceId 省份ID
     * @return 城市列表
     */
    Uni<ApiResponse<List<Region>>> getCities(Long provinceId);

    /**
     * 查询区县列表
     * 
     * @param cityId 城市ID
     * @return 区县列表
     */
    Uni<ApiResponse<List<Region>>> getDistricts(Long cityId);

    /**
     * 查询街道列表
     * 
     * @param districtId 区县ID
     * @return 街道列表
     */
    Uni<ApiResponse<List<Region>>> getStreets(Long districtId);

    /**
     * 根据父地区ID查询子地区
     * 
     * @param parentId 父地区ID
     * @return 子地区列表
     */
    Uni<ApiResponse<List<Region>>> getChildRegions(Long parentId);

    // ==================== 热门地区查询 ====================

    /**
     * 查询热门省份
     * 
     * @return 热门省份列表
     */
    Uni<ApiResponse<List<Region>>> getHotProvinces();

    /**
     * 查询热门城市
     * 
     * @return 热门城市列表
     */
    Uni<ApiResponse<List<Region>>> getHotCities();

    /**
     * 查询直辖市
     * 
     * @return 直辖市列表
     */
    Uni<ApiResponse<List<Region>>> getMunicipalities();

    /**
     * 设置热门地区
     * 
     * @param regionId 地区ID
     * @param isHot 是否热门
     * @return 设置结果
     */
    Uni<ApiResponse<Void>> setHotRegion(Long regionId, Boolean isHot);

    /**
     * 批量设置热门地区
     * 
     * @param regionIds 地区ID列表
     * @param isHot 是否热门
     * @return 设置结果
     */
    Uni<ApiResponse<Void>> batchSetHotRegions(List<Long> regionIds, Boolean isHot);

    // ==================== 地理信息查询 ====================

    /**
     * 根据坐标查询地区
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param radius 搜索半径（公里）
     * @return 地区列表
     */
    Uni<ApiResponse<List<Region>>> getRegionsByCoordinates(Double longitude, Double latitude, Double radius);

    /**
     * 根据邮政编码查询地区
     * 
     * @param postalCode 邮政编码
     * @return 地区列表
     */
    Uni<ApiResponse<List<Region>>> getRegionsByPostalCode(String postalCode);

    /**
     * 根据区号查询地区
     * 
     * @param areaCode 区号
     * @return 地区列表
     */
    Uni<ApiResponse<List<Region>>> getRegionsByAreaCode(String areaCode);

    /**
     * 更新地区坐标
     * 
     * @param regionId 地区ID
     * @param longitude 经度
     * @param latitude 纬度
     * @return 更新结果
     */
    Uni<ApiResponse<Void>> updateRegionCoordinates(Long regionId, Double longitude, Double latitude);

    // ==================== 地区路径查询 ====================

    /**
     * 获取地区完整路径
     * 
     * @param regionId 地区ID
     * @return 地区路径
     */
    Uni<ApiResponse<List<Region>>> getRegionPath(Long regionId);

    /**
     * 获取地区路径字符串
     * 
     * @param regionId 地区ID
     * @param separator 分隔符
     * @return 地区路径字符串
     */
    Uni<ApiResponse<String>> getRegionPathString(Long regionId, String separator);

    /**
     * 根据地区名称路径查询地区
     * 
     * @param regionNames 地区名称列表（从高级到低级）
     * @return 地区信息
     */
    Uni<ApiResponse<Region>> getRegionByPath(List<String> regionNames);

    // ==================== 地区验证 ====================

    /**
     * 检查地区编码是否存在
     * 
     * @param regionCode 地区编码
     * @param excludeId 排除的地区ID
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> checkRegionCodeExists(String regionCode, Long excludeId);

    /**
     * 验证地区编码格式
     * 
     * @param regionCode 地区编码
     * @return 验证结果
     */
    ApiResponse<Boolean> validateRegionCodeFormat(String regionCode);

    /**
     * 验证地区是否可以删除
     * 
     * @param regionId 地区ID
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> canDeleteRegion(Long regionId);

    /**
     * 验证坐标是否有效
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @return 验证结果
     */
    ApiResponse<Boolean> validateCoordinates(Double longitude, Double latitude);

    // ==================== 地区初始化 ====================

    /**
     * 初始化地区数据
     * 
     * @return 初始化结果
     */
    Uni<ApiResponse<Void>> initRegionData();

    /**
     * 同步地区数据
     * 
     * @return 同步结果
     */
    Uni<ApiResponse<Void>> syncRegionData();

    /**
     * 批量创建地区
     * 
     * @param regions 地区列表
     * @return 创建结果
     */
    Uni<ApiResponse<List<Region>>> createRegions(List<Region> regions);

    // ==================== 地区统计 ====================

    /**
     * 统计地区数量
     * 
     * @return 地区数量
     */
    Uni<ApiResponse<Long>> countRegions();

    /**
     * 统计各级别地区数量
     * 
     * @return 级别地区统计
     */
    Uni<ApiResponse<Map<Integer, Long>>> countRegionsByLevel();

    /**
     * 统计子地区数量
     * 
     * @param parentId 父地区ID
     * @return 子地区数量
     */
    Uni<ApiResponse<Long>> countChildRegions(Long parentId);

    /**
     * 统计热门地区数量
     * 
     * @return 热门地区数量
     */
    Uni<ApiResponse<Long>> countHotRegions();

    // ==================== 地区搜索 ====================

    /**
     * 搜索地区
     * 
     * @param keyword 搜索关键词
     * @param regionLevel 地区级别（可选）
     * @param limit 结果限制数量
     * @return 搜索结果
     */
    Uni<ApiResponse<List<Region>>> searchRegions(String keyword, Integer regionLevel, Integer limit);

    /**
     * 智能搜索地区
     * 
     * @param keyword 搜索关键词
     * @return 搜索结果
     */
    Uni<ApiResponse<List<Region>>> smartSearchRegions(String keyword);

    // ==================== 地区导入导出 ====================

    /**
     * 导出地区数据
     * 
     * @param regionLevel 地区级别（可选）
     * @return 地区数据
     */
    Uni<ApiResponse<String>> exportRegions(Integer regionLevel);

    /**
     * 导入地区数据
     * 
     * @param regionData 地区数据
     * @param overwrite 是否覆盖已存在的地区
     * @return 导入结果
     */
    Uni<ApiResponse<Void>> importRegions(String regionData, Boolean overwrite);
}
