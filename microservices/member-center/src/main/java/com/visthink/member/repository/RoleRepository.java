package com.visthink.member.repository;

import com.visthink.member.entity.Role;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * 角色数据访问层
 * 
 * 提供角色相关的数据库操作方法
 * 支持多租户数据隔离和响应式编程
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class RoleRepository implements PanacheRepository<Role> {

    /**
     * 根据ID和租户ID查询角色
     * 
     * @param id 角色ID
     * @param tenantId 租户ID
     * @return 角色信息
     */
    public Uni<Role> findByIdAndTenantId(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2 and deleted = 0", id, tenantId).firstResult();
    }

    /**
     * 根据角色编码和租户ID查询角色
     * 
     * @param roleCode 角色编码
     * @param tenantId 租户ID
     * @return 角色信息
     */
    public Uni<Role> findByRoleCodeAndTenantId(String roleCode, Long tenantId) {
        return find("roleCode = ?1 and tenantId = ?2 and deleted = 0", roleCode, tenantId).firstResult();
    }

    /**
     * 根据租户ID查询所有角色
     * 
     * @param tenantId 租户ID
     * @return 角色列表
     */
    public Uni<List<Role>> findByTenantId(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 根据租户ID和状态查询角色
     * 
     * @param tenantId 租户ID
     * @param status 状态
     * @return 角色列表
     */
    public Uni<List<Role>> findByTenantIdAndStatus(Long tenantId, Integer status) {
        return find("tenantId = ?1 and status = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), tenantId, status).list();
    }

    /**
     * 根据角色类型和租户ID查询角色
     * 
     * @param roleType 角色类型
     * @param tenantId 租户ID
     * @return 角色列表
     */
    public Uni<List<Role>> findByRoleTypeAndTenantId(Integer roleType, Long tenantId) {
        return find("roleType = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), roleType, tenantId).list();
    }

    /**
     * 查询默认角色
     * 
     * @param tenantId 租户ID
     * @return 默认角色列表
     */
    public Uni<List<Role>> findDefaultRoles(Long tenantId) {
        return find("isDefault = true and tenantId = ?1 and deleted = 0 and status = 1", 
                   Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 分页查询角色
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词（可选）
     * @param roleType 角色类型（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public Uni<PageResult<Role>> findByPage(Long tenantId, PageRequest pageRequest, 
                                          String keyword, Integer roleType, Integer status) {
        
        // 构建查询条件
        StringBuilder queryBuilder = new StringBuilder("tenantId = ?1 and deleted = 0");
        int paramIndex = 2;
        
        // 添加关键词搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryBuilder.append(" and (roleName like ?").append(paramIndex)
                       .append(" or roleCode like ?").append(paramIndex)
                       .append(" or description like ?").append(paramIndex).append(")");
            paramIndex++;
        }
        
        // 添加角色类型条件
        if (roleType != null) {
            queryBuilder.append(" and roleType = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加状态条件
        if (status != null) {
            queryBuilder.append(" and status = ?").append(paramIndex);
            paramIndex++;
        }
        
        String query = queryBuilder.toString();
        
        // 构建参数数组
        Object[] params = buildParams(tenantId, keyword, roleType, status);
        
        // 执行分页查询
        Page page = Page.of(pageRequest.getPage() - 1, pageRequest.getSize());
        Sort sort = Sort.by("sortOrder").and("createTime", Sort.Direction.Descending);
        
        return find(query, sort, params).page(page).list()
                .flatMap(roles -> count(query, params)
                        .map(total -> PageResult.of(roles, total, pageRequest.getPage(), pageRequest.getSize())));
    }

    /**
     * 构建查询参数数组
     */
    private Object[] buildParams(Long tenantId, String keyword, Integer roleType, Integer status) {
        java.util.List<Object> paramList = new java.util.ArrayList<>();
        paramList.add(tenantId);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            String likeKeyword = "%" + keyword.trim() + "%";
            paramList.add(likeKeyword);
        }
        
        if (roleType != null) {
            paramList.add(roleType);
        }
        
        if (status != null) {
            paramList.add(status);
        }
        
        return paramList.toArray();
    }

    /**
     * 检查角色编码是否存在
     * 
     * @param roleCode 角色编码
     * @param tenantId 租户ID
     * @param excludeId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    public Uni<Boolean> existsByRoleCode(String roleCode, Long tenantId, Long excludeId) {
        String query = excludeId != null 
            ? "roleCode = ?1 and tenantId = ?2 and id != ?3 and deleted = 0"
            : "roleCode = ?1 and tenantId = ?2 and deleted = 0";
        
        Object[] params = excludeId != null 
            ? new Object[]{roleCode, tenantId, excludeId}
            : new Object[]{roleCode, tenantId};
        
        return count(query, params).map(count -> count > 0);
    }

    /**
     * 统计租户下的角色数量
     * 
     * @param tenantId 租户ID
     * @return 角色数量
     */
    public Uni<Long> countByTenantId(Long tenantId) {
        return count("tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 统计租户下指定类型的角色数量
     * 
     * @param tenantId 租户ID
     * @param roleType 角色类型
     * @return 角色数量
     */
    public Uni<Long> countByTenantIdAndRoleType(Long tenantId, Integer roleType) {
        return count("tenantId = ?1 and roleType = ?2 and deleted = 0", tenantId, roleType);
    }

    /**
     * 根据用户ID查询用户拥有的角色
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 角色列表
     */
    public Uni<List<Role>> findByUserId(Long userId, Long tenantId) {
        String query = """
            SELECT r FROM Role r 
            JOIN UserRole ur ON r.id = ur.roleId 
            WHERE ur.userId = ?1 and r.tenantId = ?2 and r.deleted = 0 and r.status = 1
            and ur.deleted = 0 and ur.status = 1
            ORDER BY r.sortOrder, r.createTime
            """;
        return find(query, userId, tenantId).list();
    }

    /**
     * 查询系统内置角色
     * 
     * @param tenantId 租户ID
     * @return 系统角色列表
     */
    public Uni<List<Role>> findSystemRoles(Long tenantId) {
        return find("isSystem = true and tenantId = ?1 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 软删除角色
     * 
     * @param id 角色ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    public Uni<Boolean> softDelete(Long id, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 批量软删除角色
     * 
     * @param ids 角色ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "deleted = 1, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ") and tenantId = ?";
        
        Object[] params = new Object[ids.size() + 1];
        for (int i = 0; i < ids.size(); i++) {
            params[i] = ids.get(i);
        }
        params[ids.size()] = tenantId;
        
        return update(query, params);
    }
}
