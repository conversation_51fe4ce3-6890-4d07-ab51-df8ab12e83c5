package com.visthink.member.resource;

import com.visthink.member.service.DictionaryService;
import com.visthink.member.entity.Dictionary;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.context.TenantContext;

import io.smallrye.mutiny.Uni;
import io.quarkus.logging.Log;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * 字典管理REST接口
 * 
 * 提供字典管理的HTTP API接口
 * 包含字典CRUD、字典树查询、字典值查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/dictionaries")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "字典管理", description = "字典管理相关接口")
public class DictionaryResource {

    @Inject
    DictionaryService dictionaryService;

    @Inject
    TenantContext tenantContext;

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建字典
     */
    @POST
    @Operation(summary = "创建字典", description = "创建新的字典")
    public Uni<ApiResponse<Dictionary>> createDictionary(@Valid Dictionary dictionary) {
        Log.infof("创建字典请求: dictCode=%s", dictionary.getDictCode());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.createDictionary(tenantId, dictionary);
    }

    /**
     * 更新字典
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新字典", description = "更新指定字典的信息")
    public Uni<ApiResponse<Dictionary>> updateDictionary(
            @Parameter(description = "字典ID") @PathParam("id") Long id,
            @Valid Dictionary dictionary) {
        Log.infof("更新字典请求: dictionaryId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.updateDictionary(tenantId, id, dictionary);
    }

    /**
     * 删除字典
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除字典", description = "删除指定的字典")
    public Uni<ApiResponse<Void>> deleteDictionary(
            @Parameter(description = "字典ID") @PathParam("id") Long id) {
        Log.infof("删除字典请求: dictionaryId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.deleteDictionary(tenantId, id);
    }

    /**
     * 批量删除字典
     */
    @DELETE
    @Path("/batch")
    @Operation(summary = "批量删除字典", description = "批量删除指定的字典")
    public Uni<ApiResponse<Void>> deleteDictionaries(List<Long> dictionaryIds) {
        Log.infof("批量删除字典请求: dictionaryIds=%s", dictionaryIds);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.deleteDictionaries(tenantId, dictionaryIds);
    }

    /**
     * 查询字典详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询字典详情", description = "根据ID查询字典详细信息")
    public Uni<ApiResponse<Dictionary>> getDictionaryById(
            @Parameter(description = "字典ID") @PathParam("id") Long id) {
        Log.infof("查询字典详情请求: dictionaryId=%d", id);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryById(tenantId, id);
    }

    /**
     * 根据编码查询字典
     */
    @GET
    @Path("/code/{dictCode}")
    @Operation(summary = "根据编码查询字典", description = "根据字典编码查询字典信息")
    public Uni<ApiResponse<Dictionary>> getDictionaryByCode(
            @Parameter(description = "字典编码") @PathParam("dictCode") String dictCode) {
        Log.infof("根据编码查询字典请求: dictCode=%s", dictCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryByCode(tenantId, dictCode);
    }

    // ==================== 查询操作 ====================

    /**
     * 分页查询字典列表
     */
    @GET
    @Operation(summary = "分页查询字典", description = "分页查询字典列表，支持关键词搜索和条件过滤")
    public Uni<ApiResponse<PageResult<Dictionary>>> getDictionaryList(
            @Parameter(description = "页码") @QueryParam("page") @DefaultValue("1") int page,
            @Parameter(description = "每页大小") @QueryParam("size") @DefaultValue("10") int size,
            @Parameter(description = "搜索关键词") @QueryParam("keyword") String keyword,
            @Parameter(description = "字典类型") @QueryParam("dictType") Integer dictType,
            @Parameter(description = "父字典ID") @QueryParam("parentId") Long parentId) {
        
        Log.infof("分页查询字典请求: page=%d, size=%d", page, size);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        PageRequest pageRequest = new PageRequest(page, size);
        return dictionaryService.getDictionaryList(tenantId, pageRequest, keyword, dictType, parentId);
    }

    /**
     * 查询所有字典
     */
    @GET
    @Path("/all")
    @Operation(summary = "查询所有字典", description = "查询租户下的所有字典")
    public Uni<ApiResponse<List<Dictionary>>> getAllDictionaries() {
        Log.info("查询所有字典请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getAllDictionaries(tenantId);
    }

    /**
     * 查询字典树
     */
    @GET
    @Path("/tree")
    @Operation(summary = "查询字典树", description = "查询租户下的字典树结构")
    public Uni<ApiResponse<List<Dictionary>>> getDictionaryTree() {
        Log.info("查询字典树请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryTree(tenantId);
    }

    /**
     * 查询字典类型
     */
    @GET
    @Path("/types")
    @Operation(summary = "查询字典类型", description = "查询租户下的所有字典类型")
    public Uni<ApiResponse<List<Dictionary>>> getDictionaryTypes() {
        Log.info("查询字典类型请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryTypes(tenantId);
    }

    /**
     * 查询字典项
     */
    @GET
    @Path("/items/{dictTypeCode}")
    @Operation(summary = "查询字典项", description = "根据字典类型编码查询字典项")
    public Uni<ApiResponse<List<Dictionary>>> getDictionaryItems(
            @Parameter(description = "字典类型编码") @PathParam("dictTypeCode") String dictTypeCode) {
        Log.infof("查询字典项请求: dictTypeCode=%s", dictTypeCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryItems(tenantId, dictTypeCode);
    }

    /**
     * 查询子字典
     */
    @GET
    @Path("/{parentId}/children")
    @Operation(summary = "查询子字典", description = "查询指定字典的子字典")
    public Uni<ApiResponse<List<Dictionary>>> getChildDictionaries(
            @Parameter(description = "父字典ID") @PathParam("parentId") Long parentId) {
        Log.infof("查询子字典请求: parentId=%d", parentId);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getChildDictionaries(tenantId, parentId);
    }

    // ==================== 字典值查询 ====================

    /**
     * 根据值查询字典项
     */
    @GET
    @Path("/{dictCode}/value/{dictValue}")
    @Operation(summary = "根据值查询字典项", description = "根据字典编码和值查询字典项")
    public Uni<ApiResponse<Dictionary>> getDictionaryByValue(
            @Parameter(description = "字典编码") @PathParam("dictCode") String dictCode,
            @Parameter(description = "字典值") @PathParam("dictValue") String dictValue) {
        Log.infof("根据值查询字典项请求: dictCode=%s, dictValue=%s", dictCode, dictValue);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryByValue(tenantId, dictCode, dictValue);
    }

    /**
     * 获取字典键值对
     */
    @GET
    @Path("/{dictCode}/keyvalue")
    @Operation(summary = "获取字典键值对", description = "获取指定字典的所有键值对")
    public Uni<ApiResponse<Map<String, String>>> getDictionaryKeyValueMap(
            @Parameter(description = "字典编码") @PathParam("dictCode") String dictCode) {
        Log.infof("获取字典键值对请求: dictCode=%s", dictCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryKeyValueMap(tenantId, dictCode);
    }

    /**
     * 获取字典标签列表
     */
    @GET
    @Path("/{dictCode}/labels")
    @Operation(summary = "获取字典标签列表", description = "获取指定字典的所有标签")
    public Uni<ApiResponse<List<String>>> getDictionaryLabels(
            @Parameter(description = "字典编码") @PathParam("dictCode") String dictCode) {
        Log.infof("获取字典标签列表请求: dictCode=%s", dictCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryLabels(tenantId, dictCode);
    }

    /**
     * 获取字典标签
     */
    @GET
    @Path("/{dictCode}/label/{dictValue}")
    @Operation(summary = "获取字典标签", description = "根据字典编码和值获取标签")
    public Uni<ApiResponse<String>> getDictionaryLabel(
            @Parameter(description = "字典编码") @PathParam("dictCode") String dictCode,
            @Parameter(description = "字典值") @PathParam("dictValue") String dictValue) {
        Log.infof("获取字典标签请求: dictCode=%s, dictValue=%s", dictCode, dictValue);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.getDictionaryLabel(tenantId, dictCode, dictValue);
    }

    // ==================== 字典管理操作 ====================

    /**
     * 创建字典类型
     */
    @POST
    @Path("/types")
    @Operation(summary = "创建字典类型", description = "创建新的字典类型")
    public Uni<ApiResponse<Dictionary>> createDictionaryType(
            @Parameter(description = "字典编码") @QueryParam("dictCode") String dictCode,
            @Parameter(description = "字典名称") @QueryParam("dictName") String dictName,
            @Parameter(description = "描述") @QueryParam("description") String description) {
        Log.infof("创建字典类型请求: dictCode=%s", dictCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.createDictionaryType(tenantId, dictCode, dictName, description);
    }

    /**
     * 批量创建字典项
     */
    @POST
    @Path("/items/{dictTypeCode}")
    @Operation(summary = "批量创建字典项", description = "为指定字典类型批量创建字典项")
    public Uni<ApiResponse<List<Dictionary>>> createDictionaryItems(
            @Parameter(description = "字典类型编码") @PathParam("dictTypeCode") String dictTypeCode,
            @Valid List<Dictionary> dictItems) {
        Log.infof("批量创建字典项请求: dictTypeCode=%s, count=%d", dictTypeCode, dictItems.size());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.createDictionaryItems(tenantId, dictTypeCode, dictItems);
    }

    // ==================== 验证接口 ====================

    /**
     * 检查字典编码是否存在
     */
    @GET
    @Path("/check-code")
    @Operation(summary = "检查字典编码", description = "检查字典编码是否已存在")
    public Uni<ApiResponse<Boolean>> checkDictCodeExists(
            @Parameter(description = "字典编码") @QueryParam("dictCode") String dictCode,
            @Parameter(description = "排除的字典ID") @QueryParam("excludeId") Long excludeId) {
        Log.infof("检查字典编码请求: dictCode=%s", dictCode);
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.checkDictCodeExists(tenantId, dictCode, excludeId);
    }

    // ==================== 统计接口 ====================

    /**
     * 统计字典数量
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计字典数量", description = "统计租户下的字典总数")
    public Uni<ApiResponse<Long>> countDictionaries() {
        Log.info("统计字典数量请求");
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return dictionaryService.countDictionaries(tenantId);
    }
}
