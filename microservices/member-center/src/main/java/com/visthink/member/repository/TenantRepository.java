package com.visthink.member.repository;

import com.visthink.common.repository.BaseRepository;
import com.visthink.member.entity.Tenant;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 租户数据访问层
 *
 * 提供租户相关的数据库操作，包括基础CRUD和业务查询
 * 继承BaseRepository获得通用的数据访问能力
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class TenantRepository implements BaseRepository<Tenant> {

    /**
     * 根据租户编码查询租户
     *
     * @param tenantCode 租户编码
     * @return 租户对象
     */
    public Uni<Tenant> findByTenantCode(String tenantCode) {
        return Tenant.findByTenantCode(tenantCode);
    }

    /**
     * 根据域名查询租户
     *
     * @param domain 域名
     * @return 租户对象
     */
    public Uni<Tenant> findByDomain(String domain) {
        return Tenant.findByDomain(domain);
    }

    /**
     * 检查租户编码是否存在
     *
     * @param tenantCode 租户编码
     * @param excludeId 排除的租户ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByTenantCode(String tenantCode, Long excludeId) {
        return Tenant.existsByTenantCode(tenantCode, excludeId);
    }

    /**
     * 检查域名是否存在
     *
     * @param domain 域名
     * @param excludeId 排除的租户ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByDomain(String domain, Long excludeId) {
        return Tenant.existsByDomain(domain, excludeId);
    }

    /**
     * 根据租户类型查询租户列表
     *
     * @param tenantType 租户类型
     * @return 租户列表
     */
    public Uni<List<Tenant>> findByTenantType(Integer tenantType) {
        return find("tenantType = ?1 and deleted = 0 order by id desc", tenantType).list();
    }

    /**
     * 根据租户状态查询租户列表
     *
     * @param tenantStatus 租户状态
     * @return 租户列表
     */
    public Uni<List<Tenant>> findByTenantStatus(Integer tenantStatus) {
        return find("tenantStatus = ?1 and deleted = 0 order by id desc", tenantStatus).list();
    }

    /**
     * 查询正常状态的租户列表
     *
     * @return 正常租户列表
     */
    public Uni<List<Tenant>> findActiveTenants() {
        return find("tenantStatus = 1 and deleted = 0 order by id desc").list();
    }

    /**
     * 查询试用期租户列表
     *
     * @return 试用期租户列表
     */
    public Uni<List<Tenant>> findTrialTenants() {
        LocalDateTime now = LocalDateTime.now();
        return find("trialStartTime <= ?1 and trialEndTime > ?1 and deleted = 0 order by trialEndTime asc", now).list();
    }

    /**
     * 查询试用期即将过期的租户
     *
     * @param days 提前提醒天数
     * @return 租户列表
     */
    public Uni<List<Tenant>> findTrialExpiringSoon(int days) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireTime = now.plusDays(days);
        return find("trialEndTime > ?1 and trialEndTime <= ?2 and deleted = 0 order by trialEndTime asc",
                   now, expireTime).list();
    }

    /**
     * 查询试用期已过期的租户
     *
     * @return 租户列表
     */
    public Uni<List<Tenant>> findTrialExpiredTenants() {
        LocalDateTime now = LocalDateTime.now();
        return find("trialEndTime < ?1 and tenantStatus != 5 and deleted = 0 order by trialEndTime asc", now).list();
    }

    /**
     * 查询订阅即将过期的租户
     *
     * @param days 提前提醒天数
     * @return 租户列表
     */
    public Uni<List<Tenant>> findSubscriptionExpiringSoon(int days) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireTime = now.plusDays(days);
        return find("subscriptionEndTime > ?1 and subscriptionEndTime <= ?2 and deleted = 0 order by subscriptionEndTime asc",
                   now, expireTime).list();
    }

    /**
     * 查询订阅已过期的租户
     *
     * @return 租户列表
     */
    public Uni<List<Tenant>> findSubscriptionExpiredTenants() {
        LocalDateTime now = LocalDateTime.now();
        return find("subscriptionEndTime < ?1 and tenantStatus != 5 and deleted = 0 order by subscriptionEndTime asc", now).list();
    }

    /**
     * 根据关键字搜索租户
     *
     * @param keyword 关键字（租户编码、租户名称、公司名称、联系人）
     * @return 租户列表
     */
    public Uni<List<Tenant>> searchTenants(String keyword) {
        String searchPattern = "%" + keyword + "%";
        return find("(tenantCode like ?1 or tenantName like ?1 or companyName like ?1 or contactName like ?1) and deleted = 0 order by id desc",
                   searchPattern).list();
    }

    /**
     * 查询最近注册的租户
     *
     * @param days 天数
     * @return 租户列表
     */
    public Uni<List<Tenant>> findRecentTenants(int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return find("createTime >= ?1 and deleted = 0 order by createTime desc", since).list();
    }

    /**
     * 查询最近活跃的租户
     *
     * @param days 天数
     * @return 租户列表
     */
    public Uni<List<Tenant>> findRecentActiveTenants(int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return find("lastLoginTime >= ?1 and deleted = 0 order by lastLoginTime desc", since).list();
    }

    /**
     * 查询用户数接近上限的租户
     *
     * @param threshold 阈值（0.8表示80%）
     * @return 租户列表
     */
    public Uni<List<Tenant>> findTenantsNearUserLimit(double threshold) {
        return find("currentUsers >= maxUsers * ?1 and deleted = 0 order by (currentUsers * 1.0 / maxUsers) desc",
                   threshold).list();
    }

    /**
     * 查询存储空间接近上限的租户
     *
     * @param threshold 阈值（0.8表示80%）
     * @return 租户列表
     */
    public Uni<List<Tenant>> findTenantsNearStorageLimit(double threshold) {
        return find("storageUsed >= storageLimit * ?1 and deleted = 0 order by (storageUsed * 1.0 / storageLimit) desc",
                   threshold).list();
    }

    /**
     * 统计租户数量按状态分组
     *
     * @return 统计结果
     */
    public Uni<List<Object[]>> countTenantsByStatus() {
//        return getEntityManager()
//            .createQuery("select t.tenantStatus, count(t) from Tenant t where t.deleted = 0 group by t.tenantStatus")
//            .getResultList();
        return null;
    }

    /**
     * 统计租户数量按类型分组
     *
     * @return 统计结果
     */
    public Uni<List<Object[]>> countTenantsByType() {
//        return getEntityManager()
//            .createQuery("select t.tenantType, count(t) from Tenant t where t.deleted = 0 group by t.tenantType")
//            .getResultList();

        return null;
    }

    /**
     * 统计最近注册租户数量
     *
     * @param days 天数
     * @return 租户数量
     */
    public Uni<Long> countRecentRegistrations(int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return count("createTime >= ?1 and deleted = 0", since);
    }

    /**
     * 统计活跃租户数量
     *
     * @param days 天数
     * @return 租户数量
     */
    public Uni<Long> countActiveTenants(int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        return count("lastLoginTime >= ?1 and deleted = 0", since);
    }

    /**
     * 批量更新租户状态
     *
     * @param tenantIds 租户ID列表
     * @param tenantStatus 租户状态
     * @return 更新数量
     */
    public Uni<Integer> batchUpdateTenantStatus(List<Long> tenantIds, Integer tenantStatus) {
        if (tenantIds == null || tenantIds.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("tenantStatus = ?1, updateTime = current_timestamp where id in ?2 and deleted = 0",
                     tenantStatus, tenantIds);
    }

    /**
     * 批量更新租户类型
     *
     * @param tenantIds 租户ID列表
     * @param tenantType 租户类型
     * @return 更新数量
     */
    public Uni<Integer> batchUpdateTenantType(List<Long> tenantIds, Integer tenantType) {
        if (tenantIds == null || tenantIds.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("tenantType = ?1, updateTime = current_timestamp where id in ?2 and deleted = 0",
                     tenantType, tenantIds);
    }

    /**
     * 自动过期试用期租户
     *
     * @return 过期数量
     */
    public Uni<Integer> expireTrialTenants() {
        LocalDateTime now = LocalDateTime.now();
        return update("tenantStatus = 5, updateTime = current_timestamp where trialEndTime < ?1 and tenantStatus != 5 and deleted = 0",
                     now);
    }

    /**
     * 自动过期订阅租户
     *
     * @return 过期数量
     */
    public Uni<Integer> expireSubscriptionTenants() {
        LocalDateTime now = LocalDateTime.now();
        return update("tenantStatus = 5, updateTime = current_timestamp where subscriptionEndTime < ?1 and tenantStatus != 5 and deleted = 0",
                     now);
    }

    /**
     * 更新租户最后登录时间
     *
     * @param tenantId 租户ID
     * @return 更新结果
     */
    public Uni<Integer> updateLastLoginTime(Long tenantId) {
        return update("lastLoginTime = current_timestamp, updateTime = current_timestamp where id = ?1 and deleted = 0",
                     tenantId);
    }

    /**
     * 增加租户用户数
     *
     * @param tenantId 租户ID
     * @return 更新结果
     */
    public Uni<Integer> incrementUserCount(Long tenantId) {
        return update("currentUsers = currentUsers + 1, updateTime = current_timestamp where id = ?1 and currentUsers < maxUsers and deleted = 0",
                     tenantId);
    }

    /**
     * 减少租户用户数
     *
     * @param tenantId 租户ID
     * @return 更新结果
     */
    public Uni<Integer> decrementUserCount(Long tenantId) {
        return update("currentUsers = case when currentUsers > 0 then currentUsers - 1 else 0 end, updateTime = current_timestamp where id = ?1 and deleted = 0",
                     tenantId);
    }

    /**
     * 增加租户存储使用量
     *
     * @param tenantId 租户ID
     * @param size 增加的大小
     * @return 更新结果
     */
    public Uni<Integer> incrementStorageUsed(Long tenantId, Long size) {
        return update("storageUsed = storageUsed + ?2, updateTime = current_timestamp where id = ?1 and storageUsed + ?2 <= storageLimit and deleted = 0",
                     tenantId, size);
    }

    /**
     * 减少租户存储使用量
     *
     * @param tenantId 租户ID
     * @param size 减少的大小
     * @return 更新结果
     */
    public Uni<Integer> decrementStorageUsed(Long tenantId, Long size) {
        return update("storageUsed = case when storageUsed >= ?2 then storageUsed - ?2 else 0 end, updateTime = current_timestamp where id = ?1 and deleted = 0",
                     tenantId, size);
    }
}
