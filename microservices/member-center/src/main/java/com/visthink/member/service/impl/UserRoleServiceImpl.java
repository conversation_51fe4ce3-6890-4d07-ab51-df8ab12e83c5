package com.visthink.member.service.impl;

import com.visthink.member.service.UserRoleService;
import com.visthink.member.entity.UserRole;
import com.visthink.member.repository.UserRoleRepository;
import com.visthink.member.repository.RoleRepository;
import com.visthink.member.dto.UserRoleAssignRequest;
import com.visthink.member.dto.UserRoleQueryRequest;
import com.visthink.member.dto.UserRoleExpireUpdateRequest;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;

import io.smallrye.mutiny.Uni;
import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 用户角色服务实现类
 * 
 * 实现用户角色关联管理的业务逻辑
 * 包含角色分配、过期时间管理、角色分配历史等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class UserRoleServiceImpl implements UserRoleService {

    @Inject
    UserRoleRepository userRoleRepository;

    @Inject
    RoleRepository roleRepository;

    // ==================== 角色分配管理 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> assignRoles(Long tenantId, UserRoleAssignRequest request) {
        Log.infof("分配角色: tenantId=%d, userId=%d, operation=%s", tenantId, request.getUserId(), request.getOperation());

        if (!request.isValid()) {
            return Uni.createFrom().item(ApiResponse.error("请求参数无效"));
        }

        if (request.isAssignOperation()) {
            return assignRolesInternal(tenantId, request);
        } else if (request.isRemoveOperation()) {
            return removeRolesInternal(tenantId, request);
        } else if (request.isReplaceOperation()) {
            return replaceRolesInternal(tenantId, request);
        } else {
            return Uni.createFrom().item(ApiResponse.error("不支持的操作类型"));
        }
    }

    /**
     * 内部分配角色方法
     */
    private Uni<ApiResponse<Void>> assignRolesInternal(Long tenantId, UserRoleAssignRequest request) {
        List<Long> roleIds = request.getRoleIds();
        if (roleIds == null || roleIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("角色ID列表不能为空"));
        }

        // 1. 验证角色是否存在
        return validateRolesExist(tenantId, roleIds)
                .flatMap(valid -> {
                    if (!valid) {
                        return Uni.createFrom().item(ApiResponse.<Void>error("部分角色不存在"));
                    }

                    // 2. 检查已存在的角色关联
                    return userRoleRepository.findByUserId(request.getUserId(), tenantId)
                            .flatMap(existingUserRoles -> {
                                List<Long> existingRoleIds = existingUserRoles.stream()
                                        .map(UserRole::getRoleId)
                                        .collect(Collectors.toList());

                                // 3. 过滤出需要新增的角色
                                List<Long> newRoleIds = roleIds.stream()
                                        .filter(roleId -> !existingRoleIds.contains(roleId))
                                        .collect(Collectors.toList());

                                if (newRoleIds.isEmpty()) {
                                    return Uni.createFrom().item(ApiResponse.<Void>success(null));
                                }

                                // 4. 创建新的用户角色关联
                                List<UserRole> newUserRoles = createUserRoles(tenantId, request.getUserId(), 
                                        newRoleIds, request.getCalculatedExpireTime(), request.getGrantedBy(), request.getRemark());

                                return UserRole.persist(newUserRoles)
                                        .map(success -> ApiResponse.<Void>success(null));
                            });
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "分配角色失败: tenantId=%d, userId=%d", tenantId, request.getUserId());
                    return ApiResponse.error("分配角色失败: " + throwable.getMessage());
                });
    }

    /**
     * 内部移除角色方法
     */
    private Uni<ApiResponse<Void>> removeRolesInternal(Long tenantId, UserRoleAssignRequest request) {
        List<Long> roleIds = request.getRoleIds();
        if (roleIds == null || roleIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("角色ID列表不能为空"));
        }

        // 查找需要移除的用户角色关联
        return userRoleRepository.findByUserId(request.getUserId(), tenantId)
                .flatMap(userRoles -> {
                    List<Long> userRoleIds = userRoles.stream()
                            .filter(ur -> roleIds.contains(ur.getRoleId()))
                            .map(ur -> ur.id)
                            .collect(Collectors.toList());

                    if (userRoleIds.isEmpty()) {
                        return Uni.createFrom().item(ApiResponse.<Void>success(null));
                    }

                    // 软删除用户角色关联
                    return userRoleRepository.softDeleteBatch(userRoleIds, tenantId)
                            .map(count -> ApiResponse.<Void>success(null));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "移除角色失败: tenantId=%d, userId=%d", tenantId, request.getUserId());
                    return ApiResponse.error("移除角色失败: " + throwable.getMessage());
                });
    }

    /**
     * 内部替换角色方法
     */
    private Uni<ApiResponse<Void>> replaceRolesInternal(Long tenantId, UserRoleAssignRequest request) {
        // 1. 先移除用户的所有角色
        return userRoleRepository.softDeleteByUserId(request.getUserId(), tenantId)
                .flatMap(deletedCount -> {
                    // 2. 再分配新角色
                    UserRoleAssignRequest assignRequest = new UserRoleAssignRequest();
                    assignRequest.setUserId(request.getUserId());
                    assignRequest.setRoleIds(request.getRoleIds());
                    assignRequest.setOperation("assign");
                    assignRequest.setExpireTime(request.getExpireTime());
                    assignRequest.setExpireDays(request.getExpireDays());
                    assignRequest.setGrantedBy(request.getGrantedBy());
                    assignRequest.setRemark(request.getRemark());

                    return assignRolesInternal(tenantId, assignRequest);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "替换角色失败: tenantId=%d, userId=%d", tenantId, request.getUserId());
                    return ApiResponse.error("替换角色失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> removeRoles(Long tenantId, UserRoleAssignRequest request) {
        request.setOperation("remove");
        return assignRoles(tenantId, request);
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> replaceRoles(Long tenantId, UserRoleAssignRequest request) {
        request.setOperation("replace");
        return assignRoles(tenantId, request);
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> batchAssignRoles(Long tenantId, List<Long> userIds, List<Long> roleIds, 
                                                  LocalDateTime expireTime, Long grantedBy) {
        Log.infof("批量分配角色: tenantId=%d, userCount=%d, roleCount=%d", tenantId, userIds.size(), roleIds.size());

        if (userIds == null || userIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("用户ID列表不能为空"));
        }

        if (roleIds == null || roleIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("角色ID列表不能为空"));
        }

        // 验证角色是否存在
        return validateRolesExist(tenantId, roleIds)
                .flatMap(valid -> {
                    if (!valid) {
                        return Uni.createFrom().item(ApiResponse.<Void>error("部分角色不存在"));
                    }

                    // 创建所有用户角色关联
                    List<UserRole> allUserRoles = new ArrayList<>();
                    for (Long userId : userIds) {
                        List<UserRole> userRoles = createUserRoles(tenantId, userId, roleIds, expireTime, grantedBy, "批量分配");
                        allUserRoles.addAll(userRoles);
                    }

                    return UserRole.persist(allUserRoles)
                            .map(success -> ApiResponse.<Void>success(null));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "批量分配角色失败: tenantId=%d", tenantId);
                    return ApiResponse.error("批量分配角色失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> batchRemoveRoles(Long tenantId, List<Long> userIds, List<Long> roleIds) {
        Log.infof("批量移除角色: tenantId=%d, userCount=%d, roleCount=%d", tenantId, userIds.size(), roleIds.size());

        if (userIds == null || userIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("用户ID列表不能为空"));
        }

        if (roleIds == null || roleIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("角色ID列表不能为空"));
        }

        // 查找需要删除的用户角色关联
        String query = "userId in (?1) and roleId in (?2) and tenantId = ?3 and deleted = 0";
        return UserRole.find(query, userIds, roleIds, tenantId).list()
                .flatMap(userRoles -> {
                    List<Long> userRoleIds = userRoles.stream()
                            .map(ur -> ((UserRole) ur).id)
                            .collect(Collectors.toList());

                    if (userRoleIds.isEmpty()) {
                        return Uni.createFrom().item(ApiResponse.<Void>success(null));
                    }

                    return userRoleRepository.softDeleteBatch(userRoleIds, tenantId)
                            .map(count -> ApiResponse.<Void>success(null));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "批量移除角色失败: tenantId=%d", tenantId);
                    return ApiResponse.error("批量移除角色失败: " + throwable.getMessage());
                });
    }

    // ==================== 查询操作 ====================

    @Override
    public Uni<ApiResponse<UserRole>> getUserRoleById(Long tenantId, Long userRoleId) {
        Log.infof("查询用户角色关联详情: tenantId=%d, userRoleId=%d", tenantId, userRoleId);

        return userRoleRepository.findByIdAndTenantId(userRoleId, tenantId)
                .map(userRole -> {
                    if (userRole != null) {
                        return ApiResponse.success(userRole);
                    } else {
                        return ApiResponse.<UserRole>error("用户角色关联不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户角色关联详情失败: tenantId=%d, userRoleId=%d", tenantId, userRoleId);
                    return ApiResponse.error("查询失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<UserRole>>> getUserRoles(Long tenantId, Long userId) {
        Log.infof("查询用户角色关联: tenantId=%d, userId=%d", tenantId, userId);

        return userRoleRepository.findByUserId(userId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户角色关联失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("查询失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<UserRole>>> getValidUserRoles(Long tenantId, Long userId) {
        Log.infof("查询用户有效角色关联: tenantId=%d, userId=%d", tenantId, userId);

        return userRoleRepository.findValidByUserId(userId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户有效角色关联失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("查询失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<UserRole>>> getRoleUsers(Long tenantId, Long roleId) {
        Log.infof("查询角色用户关联: tenantId=%d, roleId=%d", tenantId, roleId);

        return userRoleRepository.findByRoleId(roleId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询角色用户关联失败: tenantId=%d, roleId=%d", tenantId, roleId);
                    return ApiResponse.error("查询失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<PageResult<UserRole>>> getUserRoleList(Long tenantId, UserRoleQueryRequest request) {
        Log.infof("分页查询用户角色关联: tenantId=%d, page=%d, size=%d", tenantId, request.getPage(), request.getSize());

        PageRequest pageRequest = new PageRequest(request.getPage(), request.getSize());
        return userRoleRepository.findByPage(tenantId, pageRequest, request.getUserId(), request.getRoleId(), request.getStatus())
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "分页查询用户角色关联失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> hasUserRole(Long tenantId, Long userId, Long roleId) {
        Log.infof("检查用户角色: tenantId=%d, userId=%d, roleId=%d", tenantId, userId, roleId);

        return userRoleRepository.hasUserRole(userId, roleId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查用户角色失败: tenantId=%d, userId=%d, roleId=%d", tenantId, userId, roleId);
                    return ApiResponse.error("检查失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> hasAnyUserRole(Long tenantId, Long userId, List<Long> roleIds) {
        Log.infof("检查用户任一角色: tenantId=%d, userId=%d, roleIds=%s", tenantId, userId, roleIds);

        if (roleIds == null || roleIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.success(false));
        }

        return userRoleRepository.findValidByUserId(userId, tenantId)
                .map(userRoles -> {
                    boolean hasAny = userRoles.stream()
                            .anyMatch(ur -> roleIds.contains(ur.getRoleId()));
                    return ApiResponse.success(hasAny);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查用户任一角色失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("检查失败: " + throwable.getMessage());
                });
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证角色是否存在
     */
    private Uni<Boolean> validateRolesExist(Long tenantId, List<Long> roleIds) {
        return roleRepository.find("id in (?1) and tenantId = ?2 and deleted = 0", roleIds, tenantId).count()
                .map(count -> count == roleIds.size());
    }

    /**
     * 创建用户角色关联列表
     */
    private List<UserRole> createUserRoles(Long tenantId, Long userId, List<Long> roleIds,
                                         LocalDateTime expireTime, Long grantedBy, String remark) {
        LocalDateTime now = LocalDateTime.now();
        return roleIds.stream()
                .map(roleId -> {
                    UserRole userRole = new UserRole();
                    userRole.setTenantId(tenantId);
                    userRole.setUserId(userId);
                    userRole.setRoleId(roleId);
                    userRole.setExpireTime(expireTime);
                    userRole.setGrantedBy(grantedBy);
                    userRole.setRemark(remark);
                    userRole.setCreateTime(now);
                    userRole.setUpdateTime(now);
                    return userRole;
                })
                .collect(Collectors.toList());
    }

    // ==================== 过期时间管理 ====================

    @Override
    public Uni<ApiResponse<Void>> updateExpireTime(Long tenantId, UserRoleExpireUpdateRequest request) {
        // TODO: 实现过期时间更新逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> extendExpireTime(Long tenantId, List<Long> userRoleIds, Integer extendDays) {
        // TODO: 实现过期时间延长逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> setNeverExpire(Long tenantId, List<Long> userRoleIds) {
        // TODO: 实现设置永不过期逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<List<UserRole>>> getExpiringUserRoles(Long tenantId, Integer days) {
        // TODO: 实现即将过期角色查询逻辑
        return Uni.createFrom().item(ApiResponse.success(new ArrayList<>()));
    }

    @Override
    public Uni<ApiResponse<List<UserRole>>> getExpiredUserRoles(Long tenantId) {
        // TODO: 实现已过期角色查询逻辑
        return Uni.createFrom().item(ApiResponse.success(new ArrayList<>()));
    }

    @Override
    public Uni<ApiResponse<Integer>> processExpiredRoles(Long tenantId) {
        // TODO: 实现过期角色处理逻辑
        return Uni.createFrom().item(ApiResponse.success(0));
    }

    // ==================== 状态管理 ====================

    @Override
    public Uni<ApiResponse<Void>> enableUserRole(Long tenantId, Long userRoleId) {
        // TODO: 实现启用用户角色逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> disableUserRole(Long tenantId, Long userRoleId) {
        // TODO: 实现禁用用户角色逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> batchEnableUserRoles(Long tenantId, List<Long> userRoleIds) {
        // TODO: 实现批量启用用户角色逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> batchDisableUserRoles(Long tenantId, List<Long> userRoleIds) {
        // TODO: 实现批量禁用用户角色逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 删除操作 ====================

    @Override
    public Uni<ApiResponse<Void>> deleteUserRole(Long tenantId, Long userRoleId) {
        // TODO: 实现删除用户角色逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> deleteUserRoles(Long tenantId, List<Long> userRoleIds) {
        // TODO: 实现批量删除用户角色逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> deleteUserAllRoles(Long tenantId, Long userId) {
        // TODO: 实现删除用户所有角色逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> deleteRoleAllUsers(Long tenantId, Long roleId) {
        // TODO: 实现删除角色所有用户逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 统计方法 ====================

    @Override
    public Uni<ApiResponse<Long>> countUserRoles(Long tenantId, Long userId) {
        // TODO: 实现统计用户角色数量逻辑
        return Uni.createFrom().item(ApiResponse.success(0L));
    }

    @Override
    public Uni<ApiResponse<Long>> countRoleUsers(Long tenantId, Long roleId) {
        // TODO: 实现统计角色用户数量逻辑
        return Uni.createFrom().item(ApiResponse.success(0L));
    }

    @Override
    public Uni<ApiResponse<Long>> countExpiringUserRoles(Long tenantId, Integer days) {
        // TODO: 实现统计即将过期角色数量逻辑
        return Uni.createFrom().item(ApiResponse.success(0L));
    }

    @Override
    public Uni<ApiResponse<Long>> countExpiredUserRoles(Long tenantId) {
        // TODO: 实现统计已过期角色数量逻辑
        return Uni.createFrom().item(ApiResponse.success(0L));
    }

    // ==================== 角色分配历史 ====================

    @Override
    public Uni<ApiResponse<PageResult<UserRole>>> getUserRoleHistory(Long tenantId, Long userId, PageRequest pageRequest) {
        // TODO: 实现用户角色分配历史查询逻辑
        return Uni.createFrom().item(ApiResponse.success(PageResult.empty()));
    }

    @Override
    public Uni<ApiResponse<PageResult<UserRole>>> getRoleAssignHistory(Long tenantId, Long roleId, PageRequest pageRequest) {
        // TODO: 实现角色分配历史查询逻辑
        return Uni.createFrom().item(ApiResponse.success(PageResult.empty()));
    }

    @Override
    public Uni<ApiResponse<Void>> recordRoleOperation(Long tenantId, Long userId, Long roleId,
                                                     String operation, Long grantedBy, String remark) {
        Log.infof("记录角色操作: tenantId=%d, userId=%d, roleId=%d, operation=%s", tenantId, userId, roleId, operation);
        // TODO: 实现角色操作记录逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }
}
