package com.visthink.member.repository;

import com.visthink.member.entity.Permission;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * 权限数据访问层
 * 
 * 提供权限相关的数据库操作方法
 * 支持多租户数据隔离和响应式编程
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class PermissionRepository implements PanacheRepository<Permission> {

    /**
     * 根据ID和租户ID查询权限
     * 
     * @param id 权限ID
     * @param tenantId 租户ID
     * @return 权限信息
     */
    public Uni<Permission> findByIdAndTenantId(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2 and deleted = 0", id, tenantId).firstResult();
    }

    /**
     * 根据权限编码和租户ID查询权限
     * 
     * @param permissionCode 权限编码
     * @param tenantId 租户ID
     * @return 权限信息
     */
    public Uni<Permission> findByPermissionCodeAndTenantId(String permissionCode, Long tenantId) {
        return find("permissionCode = ?1 and tenantId = ?2 and deleted = 0", permissionCode, tenantId).firstResult();
    }

    /**
     * 根据租户ID查询所有权限
     * 
     * @param tenantId 租户ID
     * @return 权限列表
     */
    public Uni<List<Permission>> findByTenantId(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 根据权限类型和租户ID查询权限
     * 
     * @param permissionType 权限类型
     * @param tenantId 租户ID
     * @return 权限列表
     */
    public Uni<List<Permission>> findByPermissionTypeAndTenantId(Integer permissionType, Long tenantId) {
        return find("permissionType = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), permissionType, tenantId).list();
    }

    /**
     * 查询根权限（无父权限）
     * 
     * @param tenantId 租户ID
     * @return 根权限列表
     */
    public Uni<List<Permission>> findRootPermissions(Long tenantId) {
        return find("parentId is null and tenantId = ?1 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 根据父权限ID查询子权限
     * 
     * @param parentId 父权限ID
     * @param tenantId 租户ID
     * @return 子权限列表
     */
    public Uni<List<Permission>> findByParentId(Long parentId, Long tenantId) {
        return find("parentId = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), parentId, tenantId).list();
    }

    /**
     * 查询权限树结构
     * 
     * @param tenantId 租户ID
     * @return 权限树列表
     */
    public Uni<List<Permission>> findPermissionTree(Long tenantId) {
        // 查询所有权限，在服务层构建树结构
        return find("tenantId = ?1 and deleted = 0", Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 查询系统内置权限
     * 
     * @param tenantId 租户ID
     * @return 系统权限列表
     */
    public Uni<List<Permission>> findSystemPermissions(Long tenantId) {
        return find("isSystem = true and tenantId = ?1 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 分页查询权限
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词（可选）
     * @param permissionType 权限类型（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public Uni<PageResult<Permission>> findByPage(Long tenantId, PageRequest pageRequest, 
                                                String keyword, Integer permissionType, Integer status) {
        
        // 构建查询条件
        StringBuilder queryBuilder = new StringBuilder("tenantId = ?1 and deleted = 0");
        int paramIndex = 2;
        
        // 添加关键词搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryBuilder.append(" and (permissionName like ?").append(paramIndex)
                       .append(" or permissionCode like ?").append(paramIndex)
                       .append(" or description like ?").append(paramIndex).append(")");
            paramIndex++;
        }
        
        // 添加权限类型条件
        if (permissionType != null) {
            queryBuilder.append(" and permissionType = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加状态条件
        if (status != null) {
            queryBuilder.append(" and status = ?").append(paramIndex);
            paramIndex++;
        }
        
        String query = queryBuilder.toString();
        
        // 构建参数数组
        Object[] params = buildParams(tenantId, keyword, permissionType, status);
        
        // 执行分页查询
        Page page = Page.of(pageRequest.getPage() - 1, pageRequest.getSize());
        Sort sort = Sort.by("sortOrder").and("createTime", Sort.Direction.Descending);
        
        return find(query, sort, params).page(page).list()
                .flatMap(permissions -> count(query, params)
                        .map(total -> PageResult.of(permissions, total, pageRequest.getPage(), pageRequest.getSize())));
    }

    /**
     * 构建查询参数数组
     */
    private Object[] buildParams(Long tenantId, String keyword, Integer permissionType, Integer status) {
        java.util.List<Object> paramList = new java.util.ArrayList<>();
        paramList.add(tenantId);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            String likeKeyword = "%" + keyword.trim() + "%";
            paramList.add(likeKeyword);
        }
        
        if (permissionType != null) {
            paramList.add(permissionType);
        }
        
        if (status != null) {
            paramList.add(status);
        }
        
        return paramList.toArray();
    }

    /**
     * 根据角色ID查询权限
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 权限列表
     */
    public Uni<List<Permission>> findByRoleId(Long roleId, Long tenantId) {
        String query = """
            SELECT p FROM Permission p 
            JOIN RolePermission rp ON p.id = rp.permissionId 
            WHERE rp.roleId = ?1 and p.tenantId = ?2 and p.deleted = 0 and p.status = 1
            and rp.deleted = 0 and rp.status = 1
            ORDER BY p.sortOrder, p.createTime
            """;
        return find(query, roleId, tenantId).list();
    }

    /**
     * 根据用户ID查询权限（通过角色关联）
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 权限列表
     */
    public Uni<List<Permission>> findByUserId(Long userId, Long tenantId) {
        String query = """
            SELECT DISTINCT p FROM Permission p 
            JOIN RolePermission rp ON p.id = rp.permissionId 
            JOIN UserRole ur ON rp.roleId = ur.roleId 
            WHERE ur.userId = ?1 and p.tenantId = ?2 and p.deleted = 0 and p.status = 1
            and rp.deleted = 0 and rp.status = 1 and ur.deleted = 0 and ur.status = 1
            ORDER BY p.sortOrder, p.createTime
            """;
        return find(query, userId, tenantId).list();
    }

    /**
     * 根据权限编码列表查询权限
     * 
     * @param permissionCodes 权限编码列表
     * @param tenantId 租户ID
     * @return 权限列表
     */
    public Uni<List<Permission>> findByPermissionCodes(List<String> permissionCodes, Long tenantId) {
        if (permissionCodes == null || permissionCodes.isEmpty()) {
            return Uni.createFrom().item(List.of());
        }
        
        String inClause = permissionCodes.stream().map(code -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "permissionCode in (" + inClause + ") and tenantId = ? and deleted = 0";
        
        Object[] params = new Object[permissionCodes.size() + 1];
        for (int i = 0; i < permissionCodes.size(); i++) {
            params[i] = permissionCodes.get(i);
        }
        params[permissionCodes.size()] = tenantId;
        
        return find(query, params).list();
    }

    /**
     * 检查权限编码是否存在
     * 
     * @param permissionCode 权限编码
     * @param tenantId 租户ID
     * @param excludeId 排除的权限ID（用于更新时检查）
     * @return 是否存在
     */
    public Uni<Boolean> existsByPermissionCode(String permissionCode, Long tenantId, Long excludeId) {
        String query = excludeId != null 
            ? "permissionCode = ?1 and tenantId = ?2 and id != ?3 and deleted = 0"
            : "permissionCode = ?1 and tenantId = ?2 and deleted = 0";
        
        Object[] params = excludeId != null 
            ? new Object[]{permissionCode, tenantId, excludeId}
            : new Object[]{permissionCode, tenantId};
        
        return count(query, params).map(count -> count > 0);
    }

    /**
     * 统计租户下的权限数量
     * 
     * @param tenantId 租户ID
     * @return 权限数量
     */
    public Uni<Long> countByTenantId(Long tenantId) {
        return count("tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 统计租户下指定类型的权限数量
     * 
     * @param tenantId 租户ID
     * @param permissionType 权限类型
     * @return 权限数量
     */
    public Uni<Long> countByTenantIdAndPermissionType(Long tenantId, Integer permissionType) {
        return count("tenantId = ?1 and permissionType = ?2 and deleted = 0", tenantId, permissionType);
    }

    /**
     * 软删除权限
     * 
     * @param id 权限ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    public Uni<Boolean> softDelete(Long id, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 批量软删除权限
     * 
     * @param ids 权限ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "deleted = 1, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ") and tenantId = ?";
        
        Object[] params = new Object[ids.size() + 1];
        for (int i = 0; i < ids.size(); i++) {
            params[i] = ids.get(i);
        }
        params[ids.size()] = tenantId;
        
        return update(query, params);
    }
}
