package com.visthink.member.service;

import com.visthink.member.entity.Dictionary;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;

import java.util.List;
import java.util.Map;

/**
 * 字典服务接口
 * 
 * 提供字典管理的业务逻辑接口
 * 包含字典CRUD、字典树构建、字典项查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DictionaryService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建字典
     * 
     * @param tenantId 租户ID
     * @param dictionary 字典信息
     * @return 创建结果
     */
    Uni<ApiResponse<Dictionary>> createDictionary(Long tenantId, Dictionary dictionary);

    /**
     * 更新字典
     * 
     * @param tenantId 租户ID
     * @param dictionaryId 字典ID
     * @param dictionary 字典信息
     * @return 更新结果
     */
    Uni<ApiResponse<Dictionary>> updateDictionary(Long tenantId, Long dictionaryId, Dictionary dictionary);

    /**
     * 删除字典
     * 
     * @param tenantId 租户ID
     * @param dictionaryId 字典ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteDictionary(Long tenantId, Long dictionaryId);

    /**
     * 批量删除字典
     * 
     * @param tenantId 租户ID
     * @param dictionaryIds 字典ID列表
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteDictionaries(Long tenantId, List<Long> dictionaryIds);

    /**
     * 根据ID查询字典
     * 
     * @param tenantId 租户ID
     * @param dictionaryId 字典ID
     * @return 字典信息
     */
    Uni<ApiResponse<Dictionary>> getDictionaryById(Long tenantId, Long dictionaryId);

    /**
     * 根据字典编码查询字典
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @return 字典信息
     */
    Uni<ApiResponse<Dictionary>> getDictionaryByCode(Long tenantId, String dictCode);

    // ==================== 查询操作 ====================

    /**
     * 分页查询字典
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param dictType 字典类型
     * @param parentId 父字典ID
     * @return 分页结果
     */
    Uni<ApiResponse<PageResult<Dictionary>>> getDictionaryList(Long tenantId, PageRequest pageRequest, 
                                                             String keyword, Integer dictType, Long parentId);

    /**
     * 查询所有字典
     * 
     * @param tenantId 租户ID
     * @return 字典列表
     */
    Uni<ApiResponse<List<Dictionary>>> getAllDictionaries(Long tenantId);

    /**
     * 查询字典树
     * 
     * @param tenantId 租户ID
     * @return 字典树结构
     */
    Uni<ApiResponse<List<Dictionary>>> getDictionaryTree(Long tenantId);

    /**
     * 查询字典类型
     * 
     * @param tenantId 租户ID
     * @return 字典类型列表
     */
    Uni<ApiResponse<List<Dictionary>>> getDictionaryTypes(Long tenantId);

    /**
     * 根据字典类型查询字典项
     * 
     * @param tenantId 租户ID
     * @param dictTypeCode 字典类型编码
     * @return 字典项列表
     */
    Uni<ApiResponse<List<Dictionary>>> getDictionaryItems(Long tenantId, String dictTypeCode);

    /**
     * 根据父字典ID查询子字典
     * 
     * @param tenantId 租户ID
     * @param parentId 父字典ID
     * @return 子字典列表
     */
    Uni<ApiResponse<List<Dictionary>>> getChildDictionaries(Long tenantId, Long parentId);

    // ==================== 字典值查询 ====================

    /**
     * 根据字典编码和值查询字典项
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @param dictValue 字典值
     * @return 字典项
     */
    Uni<ApiResponse<Dictionary>> getDictionaryByValue(Long tenantId, String dictCode, String dictValue);

    /**
     * 根据字典编码获取所有字典项的键值对
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @return 字典键值对
     */
    Uni<ApiResponse<Map<String, String>>> getDictionaryKeyValueMap(Long tenantId, String dictCode);

    /**
     * 根据字典编码获取字典标签列表
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @return 字典标签列表
     */
    Uni<ApiResponse<List<String>>> getDictionaryLabels(Long tenantId, String dictCode);

    /**
     * 根据字典编码和值获取标签
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @param dictValue 字典值
     * @return 字典标签
     */
    Uni<ApiResponse<String>> getDictionaryLabel(Long tenantId, String dictCode, String dictValue);

    // ==================== 字典验证 ====================

    /**
     * 检查字典编码是否存在
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @param excludeId 排除的字典ID
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> checkDictCodeExists(Long tenantId, String dictCode, Long excludeId);

    /**
     * 验证字典是否可以删除
     * 
     * @param tenantId 租户ID
     * @param dictionaryId 字典ID
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> canDeleteDictionary(Long tenantId, Long dictionaryId);

    /**
     * 验证字典值是否有效
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @param dictValue 字典值
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> validateDictionaryValue(Long tenantId, String dictCode, String dictValue);

    // ==================== 字典初始化 ====================

    /**
     * 初始化租户字典
     * 
     * @param tenantId 租户ID
     * @return 初始化结果
     */
    Uni<ApiResponse<Void>> initTenantDictionaries(Long tenantId);

    /**
     * 同步系统字典
     * 
     * @param tenantId 租户ID
     * @return 同步结果
     */
    Uni<ApiResponse<Void>> syncSystemDictionaries(Long tenantId);

    /**
     * 创建字典类型
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码
     * @param dictName 字典名称
     * @param description 描述
     * @return 创建结果
     */
    Uni<ApiResponse<Dictionary>> createDictionaryType(Long tenantId, String dictCode, String dictName, String description);

    /**
     * 批量创建字典项
     * 
     * @param tenantId 租户ID
     * @param dictTypeCode 字典类型编码
     * @param dictItems 字典项列表
     * @return 创建结果
     */
    Uni<ApiResponse<List<Dictionary>>> createDictionaryItems(Long tenantId, String dictTypeCode, List<Dictionary> dictItems);

    // ==================== 字典缓存管理 ====================

    /**
     * 刷新字典缓存
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码（可选，为空则刷新所有）
     * @return 刷新结果
     */
    Uni<ApiResponse<Void>> refreshDictionaryCache(Long tenantId, String dictCode);

    /**
     * 预热字典缓存
     * 
     * @param tenantId 租户ID
     * @return 预热结果
     */
    Uni<ApiResponse<Void>> warmupDictionaryCache(Long tenantId);

    // ==================== 统计方法 ====================

    /**
     * 统计租户下的字典数量
     * 
     * @param tenantId 租户ID
     * @return 字典数量
     */
    Uni<ApiResponse<Long>> countDictionaries(Long tenantId);

    /**
     * 统计字典类型数量
     * 
     * @param tenantId 租户ID
     * @return 字典类型数量
     */
    Uni<ApiResponse<Long>> countDictionaryTypes(Long tenantId);

    /**
     * 统计字典项数量
     * 
     * @param tenantId 租户ID
     * @param dictTypeCode 字典类型编码
     * @return 字典项数量
     */
    Uni<ApiResponse<Long>> countDictionaryItems(Long tenantId, String dictTypeCode);

    // ==================== 字典导入导出 ====================

    /**
     * 导出字典数据
     * 
     * @param tenantId 租户ID
     * @param dictCode 字典编码（可选）
     * @return 字典数据
     */
    Uni<ApiResponse<String>> exportDictionaries(Long tenantId, String dictCode);

    /**
     * 导入字典数据
     * 
     * @param tenantId 租户ID
     * @param dictionaryData 字典数据
     * @param overwrite 是否覆盖已存在的字典
     * @return 导入结果
     */
    Uni<ApiResponse<Void>> importDictionaries(Long tenantId, String dictionaryData, Boolean overwrite);
}
