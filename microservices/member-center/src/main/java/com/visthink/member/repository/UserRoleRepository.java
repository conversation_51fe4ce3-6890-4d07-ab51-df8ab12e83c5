package com.visthink.member.repository;

import com.visthink.member.entity.UserRole;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import jakarta.enterprise.context.ApplicationScoped;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户角色关联数据访问层
 * 
 * 提供用户角色关联相关的数据库操作方法
 * 支持多租户数据隔离和响应式编程
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class UserRoleRepository implements PanacheRepository<UserRole> {

    /**
     * 根据ID和租户ID查询用户角色关联
     * 
     * @param id 关联ID
     * @param tenantId 租户ID
     * @return 用户角色关联信息
     */
    public Uni<UserRole> findByIdAndTenantId(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2 and deleted = 0", id, tenantId).firstResult();
    }

    /**
     * 根据用户ID和租户ID查询角色关联
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 用户角色关联列表
     */
    public Uni<List<UserRole>> findByUserId(Long userId, Long tenantId) {
        return find("userId = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("createTime", Sort.Direction.Descending), userId, tenantId).list();
    }

    /**
     * 根据用户ID查询有效的角色关联
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 有效的用户角色关联列表
     */
    public Uni<List<UserRole>> findValidByUserId(Long userId, Long tenantId) {
        String query = """
            userId = ?1 and tenantId = ?2 and deleted = 0 and status = 1 
            and (expireTime is null or expireTime > CURRENT_TIMESTAMP)
            """;
        return find(query, Sort.by("createTime", Sort.Direction.Descending), userId, tenantId).list();
    }

    /**
     * 根据角色ID和租户ID查询用户关联
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户角色关联列表
     */
    public Uni<List<UserRole>> findByRoleId(Long roleId, Long tenantId) {
        return find("roleId = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("createTime", Sort.Direction.Descending), roleId, tenantId).list();
    }

    /**
     * 根据用户ID和角色ID查询关联
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户角色关联
     */
    public Uni<UserRole> findByUserAndRole(Long userId, Long roleId, Long tenantId) {
        return find("userId = ?1 and roleId = ?2 and tenantId = ?3 and deleted = 0", 
                   userId, roleId, tenantId).firstResult();
    }

    /**
     * 检查用户是否拥有指定角色
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 是否拥有角色
     */
    public Uni<Boolean> hasUserRole(Long userId, Long roleId, Long tenantId) {
        String query = """
            userId = ?1 and roleId = ?2 and tenantId = ?3 and deleted = 0 and status = 1
            and (expireTime is null or expireTime > CURRENT_TIMESTAMP)
            """;
        return count(query, userId, roleId, tenantId).map(count -> count > 0);
    }

    /**
     * 查询即将过期的用户角色关联
     * 
     * @param tenantId 租户ID
     * @param days 提前天数
     * @return 即将过期的关联列表
     */
    public Uni<List<UserRole>> findExpiringUserRoles(Long tenantId, int days) {
        LocalDateTime expireDate = LocalDateTime.now().plusDays(days);
        String query = """
            tenantId = ?1 and deleted = 0 and status = 1 
            and expireTime is not null and expireTime <= ?2 and expireTime > CURRENT_TIMESTAMP
            """;
        return find(query, Sort.by("expireTime"), tenantId, expireDate).list();
    }

    /**
     * 查询已过期的用户角色关联
     * 
     * @param tenantId 租户ID
     * @return 已过期的关联列表
     */
    public Uni<List<UserRole>> findExpiredUserRoles(Long tenantId) {
        String query = """
            tenantId = ?1 and deleted = 0 and status = 1 
            and expireTime is not null and expireTime <= CURRENT_TIMESTAMP
            """;
        return find(query, Sort.by("expireTime"), tenantId).list();
    }

    /**
     * 分页查询用户角色关联
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param userId 用户ID（可选）
     * @param roleId 角色ID（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public Uni<PageResult<UserRole>> findByPage(Long tenantId, PageRequest pageRequest, 
                                               Long userId, Long roleId, Integer status) {
        
        // 构建查询条件
        StringBuilder queryBuilder = new StringBuilder("tenantId = ?1 and deleted = 0");
        int paramIndex = 2;
        
        // 添加用户ID条件
        if (userId != null) {
            queryBuilder.append(" and userId = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加角色ID条件
        if (roleId != null) {
            queryBuilder.append(" and roleId = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加状态条件
        if (status != null) {
            queryBuilder.append(" and status = ?").append(paramIndex);
            paramIndex++;
        }
        
        String query = queryBuilder.toString();
        
        // 构建参数数组
        Object[] params = buildParams(tenantId, userId, roleId, status);
        
        // 执行分页查询
        Page page = Page.of(pageRequest.getPage() - 1, pageRequest.getSize());
        Sort sort = Sort.by("createTime", Sort.Direction.Descending);
        
        return find(query, sort, params).page(page).list()
                .flatMap(userRoles -> count(query, params)
                        .map(total -> PageResult.of(userRoles, total, pageRequest.getPage(), pageRequest.getSize())));
    }

    /**
     * 构建查询参数数组
     */
    private Object[] buildParams(Long tenantId, Long userId, Long roleId, Integer status) {
        java.util.List<Object> paramList = new java.util.ArrayList<>();
        paramList.add(tenantId);
        
        if (userId != null) {
            paramList.add(userId);
        }
        
        if (roleId != null) {
            paramList.add(roleId);
        }
        
        if (status != null) {
            paramList.add(status);
        }
        
        return paramList.toArray();
    }

    /**
     * 统计用户的角色数量
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 角色数量
     */
    public Uni<Long> countByUserId(Long userId, Long tenantId) {
        return count("userId = ?1 and tenantId = ?2 and deleted = 0 and status = 1", userId, tenantId);
    }

    /**
     * 统计角色的用户数量
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 用户数量
     */
    public Uni<Long> countByRoleId(Long roleId, Long tenantId) {
        return count("roleId = ?1 and tenantId = ?2 and deleted = 0 and status = 1", roleId, tenantId);
    }

    /**
     * 软删除用户角色关联
     * 
     * @param id 关联ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    public Uni<Boolean> softDelete(Long id, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 软删除用户的所有角色关联
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteByUserId(Long userId, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where userId = ?1 and tenantId = ?2", 
                     userId, tenantId);
    }

    /**
     * 软删除角色的所有用户关联
     * 
     * @param roleId 角色ID
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteByRoleId(Long roleId, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where roleId = ?1 and tenantId = ?2", 
                     roleId, tenantId);
    }

    /**
     * 批量软删除用户角色关联
     * 
     * @param ids 关联ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "deleted = 1, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ") and tenantId = ?";
        
        Object[] params = new Object[ids.size() + 1];
        for (int i = 0; i < ids.size(); i++) {
            params[i] = ids.get(i);
        }
        params[ids.size()] = tenantId;
        
        return update(query, params);
    }

    /**
     * 启用用户角色关联
     * 
     * @param id 关联ID
     * @param tenantId 租户ID
     * @return 操作结果
     */
    public Uni<Boolean> enable(Long id, Long tenantId) {
        return update("status = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 禁用用户角色关联
     * 
     * @param id 关联ID
     * @param tenantId 租户ID
     * @return 操作结果
     */
    public Uni<Boolean> disable(Long id, Long tenantId) {
        return update("status = 0, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 更新过期时间
     * 
     * @param id 关联ID
     * @param expireTime 过期时间
     * @param tenantId 租户ID
     * @return 操作结果
     */
    public Uni<Boolean> updateExpireTime(Long id, LocalDateTime expireTime, Long tenantId) {
        return update("expireTime = ?1, updateTime = CURRENT_TIMESTAMP where id = ?2 and tenantId = ?3", 
                     expireTime, id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 批量更新过期时间
     * 
     * @param ids 关联ID列表
     * @param expireTime 过期时间
     * @param tenantId 租户ID
     * @return 更新数量
     */
    public Uni<Integer> batchUpdateExpireTime(List<Long> ids, LocalDateTime expireTime, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "expireTime = ?, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ") and tenantId = ?";
        
        Object[] params = new Object[ids.size() + 2];
        params[0] = expireTime;
        for (int i = 0; i < ids.size(); i++) {
            params[i + 1] = ids.get(i);
        }
        params[ids.size() + 1] = tenantId;
        
        return update(query, params);
    }
}
