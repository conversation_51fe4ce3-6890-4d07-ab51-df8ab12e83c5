package com.visthink.member.config;

import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.util.Optional;

/**
 * 数据库配置类
 * 
 * 优化数据库连接池配置
 * 解决 JdbcValuesSourceProcessingState 错误
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class DatabaseConfig {

    private static final Logger LOG = Logger.getLogger(DatabaseConfig.class);

    @ConfigProperty(name = "quarkus.datasource.reactive.max-size")
    Optional<Integer> maxPoolSize;

    @ConfigProperty(name = "quarkus.datasource.reactive.idle-timeout")
    Optional<String> idleTimeout;

    @ConfigProperty(name = "quarkus.datasource.reactive.max-lifetime")
    Optional<String> maxLifetime;

    @ConfigProperty(name = "quarkus.datasource.reactive.connection-timeout")
    Optional<String> connectionTimeout;

    /**
     * 应用启动时验证数据库配置
     * 
     * @param event 启动事件
     */
    void onStart(@Observes StartupEvent event) {
        LOG.info("验证数据库配置");
        
        try {
            validateDatabaseConfig();
            logDatabaseConfig();
            
            LOG.info("数据库配置验证完成");
            
        } catch (Exception e) {
            LOG.errorf(e, "数据库配置验证失败");
            throw new RuntimeException("数据库配置验证失败", e);
        }
    }

    /**
     * 验证数据库配置
     */
    private void validateDatabaseConfig() {
        // 验证连接池大小
        int poolSize = maxPoolSize.orElse(10);
        if (poolSize < 5 || poolSize > 50) {
            LOG.warnf("连接池大小可能不合适: %d，建议范围: 5-50", poolSize);
        }

        // 验证超时配置
        String idle = idleTimeout.orElse("PT5M");
        String lifetime = maxLifetime.orElse("PT30M");
        String connection = connectionTimeout.orElse("PT10S");

        LOG.debugf("连接池配置 - 大小: %d, 空闲超时: %s, 最大生命周期: %s, 连接超时: %s", 
                  poolSize, idle, lifetime, connection);
    }

    /**
     * 记录数据库配置信息
     */
    private void logDatabaseConfig() {
        LOG.info("=== 数据库配置信息 ===");
        LOG.infof("连接池最大大小: %d", maxPoolSize.orElse(10));
        LOG.infof("连接空闲超时: %s", idleTimeout.orElse("PT5M"));
        LOG.infof("连接最大生命周期: %s", maxLifetime.orElse("PT30M"));
        LOG.infof("连接超时: %s", connectionTimeout.orElse("PT10S"));
        LOG.info("=====================");
    }

    /**
     * 获取连接池最大大小
     * 
     * @return 连接池最大大小
     */
    public int getMaxPoolSize() {
        return maxPoolSize.orElse(10);
    }

    /**
     * 获取连接空闲超时
     * 
     * @return 连接空闲超时
     */
    public String getIdleTimeout() {
        return idleTimeout.orElse("PT5M");
    }

    /**
     * 获取连接最大生命周期
     * 
     * @return 连接最大生命周期
     */
    public String getMaxLifetime() {
        return maxLifetime.orElse("PT30M");
    }

    /**
     * 获取连接超时
     * 
     * @return 连接超时
     */
    public String getConnectionTimeout() {
        return connectionTimeout.orElse("PT10S");
    }

    /**
     * 检查数据库配置是否健康
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        try {
            int poolSize = getMaxPoolSize();
            return poolSize >= 5 && poolSize <= 50;
        } catch (Exception e) {
            LOG.errorf(e, "检查数据库配置健康状态失败");
            return false;
        }
    }
}
