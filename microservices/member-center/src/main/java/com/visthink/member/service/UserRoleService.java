package com.visthink.member.service;

import com.visthink.member.entity.UserRole;
import com.visthink.member.dto.UserRoleAssignRequest;
import com.visthink.member.dto.UserRoleQueryRequest;
import com.visthink.member.dto.UserRoleExpireUpdateRequest;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户角色服务接口
 * 
 * 提供用户角色关联管理的业务逻辑接口
 * 包含角色分配、过期时间管理、角色分配历史等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserRoleService {

    // ==================== 角色分配管理 ====================

    /**
     * 分配角色给用户
     * 
     * @param tenantId 租户ID
     * @param request 角色分配请求
     * @return 分配结果
     */
    Uni<ApiResponse<Void>> assignRoles(Long tenantId, UserRoleAssignRequest request);

    /**
     * 移除用户角色
     * 
     * @param tenantId 租户ID
     * @param request 角色移除请求
     * @return 移除结果
     */
    Uni<ApiResponse<Void>> removeRoles(Long tenantId, UserRoleAssignRequest request);

    /**
     * 替换用户角色
     * 
     * @param tenantId 租户ID
     * @param request 角色替换请求
     * @return 替换结果
     */
    Uni<ApiResponse<Void>> replaceRoles(Long tenantId, UserRoleAssignRequest request);

    /**
     * 批量分配角色
     * 
     * @param tenantId 租户ID
     * @param userIds 用户ID列表
     * @param roleIds 角色ID列表
     * @param expireTime 过期时间
     * @param grantedBy 授权人ID
     * @return 分配结果
     */
    Uni<ApiResponse<Void>> batchAssignRoles(Long tenantId, List<Long> userIds, List<Long> roleIds, 
                                          LocalDateTime expireTime, Long grantedBy);

    /**
     * 批量移除角色
     * 
     * @param tenantId 租户ID
     * @param userIds 用户ID列表
     * @param roleIds 角色ID列表
     * @return 移除结果
     */
    Uni<ApiResponse<Void>> batchRemoveRoles(Long tenantId, List<Long> userIds, List<Long> roleIds);

    // ==================== 查询操作 ====================

    /**
     * 查询用户角色关联详情
     * 
     * @param tenantId 租户ID
     * @param userRoleId 用户角色关联ID
     * @return 关联详情
     */
    Uni<ApiResponse<UserRole>> getUserRoleById(Long tenantId, Long userRoleId);

    /**
     * 查询用户的角色关联
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户角色关联列表
     */
    Uni<ApiResponse<List<UserRole>>> getUserRoles(Long tenantId, Long userId);

    /**
     * 查询用户的有效角色关联
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 有效角色关联列表
     */
    Uni<ApiResponse<List<UserRole>>> getValidUserRoles(Long tenantId, Long userId);

    /**
     * 查询角色的用户关联
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 角色用户关联列表
     */
    Uni<ApiResponse<List<UserRole>>> getRoleUsers(Long tenantId, Long roleId);

    /**
     * 分页查询用户角色关联
     * 
     * @param tenantId 租户ID
     * @param request 查询请求
     * @return 分页结果
     */
    Uni<ApiResponse<PageResult<UserRole>>> getUserRoleList(Long tenantId, UserRoleQueryRequest request);

    /**
     * 检查用户是否拥有指定角色
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> hasUserRole(Long tenantId, Long userId, Long roleId);

    /**
     * 检查用户是否拥有任一角色
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> hasAnyUserRole(Long tenantId, Long userId, List<Long> roleIds);

    // ==================== 过期时间管理 ====================

    /**
     * 更新用户角色过期时间
     * 
     * @param tenantId 租户ID
     * @param request 过期时间更新请求
     * @return 更新结果
     */
    Uni<ApiResponse<Void>> updateExpireTime(Long tenantId, UserRoleExpireUpdateRequest request);

    /**
     * 延长用户角色有效期
     * 
     * @param tenantId 租户ID
     * @param userRoleIds 用户角色关联ID列表
     * @param extendDays 延长天数
     * @return 延长结果
     */
    Uni<ApiResponse<Void>> extendExpireTime(Long tenantId, List<Long> userRoleIds, Integer extendDays);

    /**
     * 设置用户角色永不过期
     * 
     * @param tenantId 租户ID
     * @param userRoleIds 用户角色关联ID列表
     * @return 设置结果
     */
    Uni<ApiResponse<Void>> setNeverExpire(Long tenantId, List<Long> userRoleIds);

    /**
     * 查询即将过期的用户角色
     * 
     * @param tenantId 租户ID
     * @param days 提前天数
     * @return 即将过期的关联列表
     */
    Uni<ApiResponse<List<UserRole>>> getExpiringUserRoles(Long tenantId, Integer days);

    /**
     * 查询已过期的用户角色
     * 
     * @param tenantId 租户ID
     * @return 已过期的关联列表
     */
    Uni<ApiResponse<List<UserRole>>> getExpiredUserRoles(Long tenantId);

    /**
     * 自动处理过期角色
     * 
     * @param tenantId 租户ID
     * @return 处理结果
     */
    Uni<ApiResponse<Integer>> processExpiredRoles(Long tenantId);

    // ==================== 状态管理 ====================

    /**
     * 启用用户角色关联
     * 
     * @param tenantId 租户ID
     * @param userRoleId 用户角色关联ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> enableUserRole(Long tenantId, Long userRoleId);

    /**
     * 禁用用户角色关联
     * 
     * @param tenantId 租户ID
     * @param userRoleId 用户角色关联ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> disableUserRole(Long tenantId, Long userRoleId);

    /**
     * 批量启用用户角色关联
     * 
     * @param tenantId 租户ID
     * @param userRoleIds 用户角色关联ID列表
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> batchEnableUserRoles(Long tenantId, List<Long> userRoleIds);

    /**
     * 批量禁用用户角色关联
     * 
     * @param tenantId 租户ID
     * @param userRoleIds 用户角色关联ID列表
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> batchDisableUserRoles(Long tenantId, List<Long> userRoleIds);

    // ==================== 删除操作 ====================

    /**
     * 删除用户角色关联
     * 
     * @param tenantId 租户ID
     * @param userRoleId 用户角色关联ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteUserRole(Long tenantId, Long userRoleId);

    /**
     * 批量删除用户角色关联
     * 
     * @param tenantId 租户ID
     * @param userRoleIds 用户角色关联ID列表
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteUserRoles(Long tenantId, List<Long> userRoleIds);

    /**
     * 删除用户的所有角色关联
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteUserAllRoles(Long tenantId, Long userId);

    /**
     * 删除角色的所有用户关联
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteRoleAllUsers(Long tenantId, Long roleId);

    // ==================== 统计方法 ====================

    /**
     * 统计用户的角色数量
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 角色数量
     */
    Uni<ApiResponse<Long>> countUserRoles(Long tenantId, Long userId);

    /**
     * 统计角色的用户数量
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 用户数量
     */
    Uni<ApiResponse<Long>> countRoleUsers(Long tenantId, Long roleId);

    /**
     * 统计即将过期的角色关联数量
     * 
     * @param tenantId 租户ID
     * @param days 提前天数
     * @return 即将过期数量
     */
    Uni<ApiResponse<Long>> countExpiringUserRoles(Long tenantId, Integer days);

    /**
     * 统计已过期的角色关联数量
     * 
     * @param tenantId 租户ID
     * @return 已过期数量
     */
    Uni<ApiResponse<Long>> countExpiredUserRoles(Long tenantId);

    // ==================== 角色分配历史 ====================

    /**
     * 查询用户角色分配历史
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 分配历史
     */
    Uni<ApiResponse<PageResult<UserRole>>> getUserRoleHistory(Long tenantId, Long userId, PageRequest pageRequest);

    /**
     * 查询角色分配历史
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @param pageRequest 分页请求
     * @return 分配历史
     */
    Uni<ApiResponse<PageResult<UserRole>>> getRoleAssignHistory(Long tenantId, Long roleId, PageRequest pageRequest);

    /**
     * 记录角色分配操作
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param roleId 角色ID
     * @param operation 操作类型
     * @param grantedBy 授权人ID
     * @param remark 备注
     * @return 记录结果
     */
    Uni<ApiResponse<Void>> recordRoleOperation(Long tenantId, Long userId, Long roleId, 
                                             String operation, Long grantedBy, String remark);
}
