package com.visthink.member.repository;

import com.visthink.member.entity.Menu;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;

/**
 * 菜单数据访问层
 * 
 * 提供菜单相关的数据库操作方法
 * 支持多租户数据隔离和响应式编程
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class MenuRepository implements PanacheRepository<Menu> {

    /**
     * 根据ID和租户ID查询菜单
     * 
     * @param id 菜单ID
     * @param tenantId 租户ID
     * @return 菜单信息
     */
    public Uni<Menu> findByIdAndTenantId(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2 and deleted = 0", id, tenantId).firstResult();
    }

    /**
     * 根据菜单编码和租户ID查询菜单
     * 
     * @param menuCode 菜单编码
     * @param tenantId 租户ID
     * @return 菜单信息
     */
    public Uni<Menu> findByMenuCodeAndTenantId(String menuCode, Long tenantId) {
        return find("menuCode = ?1 and tenantId = ?2 and deleted = 0", menuCode, tenantId).firstResult();
    }

    /**
     * 根据租户ID查询所有菜单
     * 
     * @param tenantId 租户ID
     * @return 菜单列表
     */
    public Uni<List<Menu>> findByTenantId(Long tenantId) {
        return find("tenantId = ?1 and deleted = 0", Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 根据菜单类型和租户ID查询菜单
     * 
     * @param menuType 菜单类型
     * @param tenantId 租户ID
     * @return 菜单列表
     */
    public Uni<List<Menu>> findByMenuTypeAndTenantId(Integer menuType, Long tenantId) {
        return find("menuType = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), menuType, tenantId).list();
    }

    /**
     * 查询根菜单（无父菜单）
     * 
     * @param tenantId 租户ID
     * @return 根菜单列表
     */
    public Uni<List<Menu>> findRootMenus(Long tenantId) {
        return find("parentId is null and tenantId = ?1 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 根据父菜单ID查询子菜单
     * 
     * @param parentId 父菜单ID
     * @param tenantId 租户ID
     * @return 子菜单列表
     */
    public Uni<List<Menu>> findByParentId(Long parentId, Long tenantId) {
        return find("parentId = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), parentId, tenantId).list();
    }

    /**
     * 查询可见菜单
     * 
     * @param tenantId 租户ID
     * @return 可见菜单列表
     */
    public Uni<List<Menu>> findVisibleMenus(Long tenantId) {
        return find("tenantId = ?1 and isVisible = true and status = 1 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 查询菜单树结构
     * 
     * @param tenantId 租户ID
     * @return 菜单树列表
     */
    public Uni<List<Menu>> findMenuTree(Long tenantId) {
        // 查询所有菜单，在服务层构建树结构
        return find("tenantId = ?1 and deleted = 0", Sort.by("sortOrder", "createTime"), tenantId).list();
    }

    /**
     * 查询用户可访问的菜单
     * 
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @return 用户菜单列表
     */
    public Uni<List<Menu>> findUserMenus(Long userId, Long tenantId) {
        String query = """
            SELECT DISTINCT m FROM Menu m 
            JOIN Permission p ON m.permissionCode = p.permissionCode 
            JOIN RolePermission rp ON p.id = rp.permissionId 
            JOIN UserRole ur ON rp.roleId = ur.roleId 
            WHERE ur.userId = ?1 and m.tenantId = ?2 
            and m.isVisible = true and m.status = 1 and m.deleted = 0
            and p.deleted = 0 and p.status = 1
            and rp.deleted = 0 and rp.status = 1 
            and ur.deleted = 0 and ur.status = 1
            ORDER BY m.sortOrder, m.createTime
            """;
        return find(query, userId, tenantId).list();
    }

    /**
     * 根据权限编码查询菜单
     * 
     * @param permissionCode 权限编码
     * @param tenantId 租户ID
     * @return 菜单列表
     */
    public Uni<List<Menu>> findByPermissionCode(String permissionCode, Long tenantId) {
        return find("permissionCode = ?1 and tenantId = ?2 and deleted = 0", 
                   Sort.by("sortOrder", "createTime"), permissionCode, tenantId).list();
    }

    /**
     * 分页查询菜单
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词（可选）
     * @param menuType 菜单类型（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public Uni<PageResult<Menu>> findByPage(Long tenantId, PageRequest pageRequest, 
                                          String keyword, Integer menuType, Integer status) {
        
        // 构建查询条件
        StringBuilder queryBuilder = new StringBuilder("tenantId = ?1 and deleted = 0");
        int paramIndex = 2;
        
        // 添加关键词搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryBuilder.append(" and (menuName like ?").append(paramIndex)
                       .append(" or menuCode like ?").append(paramIndex)
                       .append(" or description like ?").append(paramIndex).append(")");
            paramIndex++;
        }
        
        // 添加菜单类型条件
        if (menuType != null) {
            queryBuilder.append(" and menuType = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加状态条件
        if (status != null) {
            queryBuilder.append(" and status = ?").append(paramIndex);
            paramIndex++;
        }
        
        String query = queryBuilder.toString();
        
        // 构建参数数组
        Object[] params = buildParams(tenantId, keyword, menuType, status);
        
        // 执行分页查询
        Page page = Page.of(pageRequest.getPage() - 1, pageRequest.getSize());
        Sort sort = Sort.by("sortOrder").and("createTime", Sort.Direction.Descending);
        
        return find(query, sort, params).page(page).list()
                .flatMap(menus -> count(query, params)
                        .map(total -> PageResult.of(menus, total, pageRequest.getPage(), pageRequest.getSize())));
    }

    /**
     * 构建查询参数数组
     */
    private Object[] buildParams(Long tenantId, String keyword, Integer menuType, Integer status) {
        java.util.List<Object> paramList = new java.util.ArrayList<>();
        paramList.add(tenantId);
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            String likeKeyword = "%" + keyword.trim() + "%";
            paramList.add(likeKeyword);
        }
        
        if (menuType != null) {
            paramList.add(menuType);
        }
        
        if (status != null) {
            paramList.add(status);
        }
        
        return paramList.toArray();
    }

    /**
     * 检查菜单编码是否存在
     * 
     * @param menuCode 菜单编码
     * @param tenantId 租户ID
     * @param excludeId 排除的菜单ID（用于更新时检查）
     * @return 是否存在
     */
    public Uni<Boolean> existsByMenuCode(String menuCode, Long tenantId, Long excludeId) {
        String query = excludeId != null 
            ? "menuCode = ?1 and tenantId = ?2 and id != ?3 and deleted = 0"
            : "menuCode = ?1 and tenantId = ?2 and deleted = 0";
        
        Object[] params = excludeId != null 
            ? new Object[]{menuCode, tenantId, excludeId}
            : new Object[]{menuCode, tenantId};
        
        return count(query, params).map(count -> count > 0);
    }

    /**
     * 统计租户下的菜单数量
     * 
     * @param tenantId 租户ID
     * @return 菜单数量
     */
    public Uni<Long> countByTenantId(Long tenantId) {
        return count("tenantId = ?1 and deleted = 0", tenantId);
    }

    /**
     * 统计租户下指定类型的菜单数量
     * 
     * @param tenantId 租户ID
     * @param menuType 菜单类型
     * @return 菜单数量
     */
    public Uni<Long> countByTenantIdAndMenuType(Long tenantId, Integer menuType) {
        return count("tenantId = ?1 and menuType = ?2 and deleted = 0", tenantId, menuType);
    }

    /**
     * 检查是否有子菜单
     * 
     * @param parentId 父菜单ID
     * @param tenantId 租户ID
     * @return 是否有子菜单
     */
    public Uni<Boolean> hasChildren(Long parentId, Long tenantId) {
        return count("parentId = ?1 and tenantId = ?2 and deleted = 0", parentId, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 软删除菜单
     * 
     * @param id 菜单ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    public Uni<Boolean> softDelete(Long id, Long tenantId) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", id, tenantId)
                .map(count -> count > 0);
    }

    /**
     * 批量软删除菜单
     * 
     * @param ids 菜单ID列表
     * @param tenantId 租户ID
     * @return 删除数量
     */
    public Uni<Integer> softDeleteBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "deleted = 1, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ") and tenantId = ?";
        
        Object[] params = new Object[ids.size() + 1];
        for (int i = 0; i < ids.size(); i++) {
            params[i] = ids.get(i);
        }
        params[ids.size()] = tenantId;
        
        return update(query, params);
    }
}
