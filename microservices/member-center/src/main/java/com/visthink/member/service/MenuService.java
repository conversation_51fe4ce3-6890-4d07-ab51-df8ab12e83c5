package com.visthink.member.service;

import com.visthink.member.entity.Menu;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;

import java.util.List;

/**
 * 菜单服务接口
 * 
 * 提供菜单管理的业务逻辑接口
 * 包含菜单CRUD、菜单树构建、用户菜单查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MenuService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建菜单
     * 
     * @param tenantId 租户ID
     * @param menu 菜单信息
     * @return 创建结果
     */
    Uni<ApiResponse<Menu>> createMenu(Long tenantId, Menu menu);

    /**
     * 更新菜单
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @param menu 菜单信息
     * @return 更新结果
     */
    Uni<ApiResponse<Menu>> updateMenu(Long tenantId, Long menuId, Menu menu);

    /**
     * 删除菜单
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteMenu(Long tenantId, Long menuId);

    /**
     * 批量删除菜单
     * 
     * @param tenantId 租户ID
     * @param menuIds 菜单ID列表
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteMenus(Long tenantId, List<Long> menuIds);

    /**
     * 根据ID查询菜单
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    Uni<ApiResponse<Menu>> getMenuById(Long tenantId, Long menuId);

    /**
     * 根据菜单编码查询菜单
     * 
     * @param tenantId 租户ID
     * @param menuCode 菜单编码
     * @return 菜单信息
     */
    Uni<ApiResponse<Menu>> getMenuByCode(Long tenantId, String menuCode);

    // ==================== 查询操作 ====================

    /**
     * 分页查询菜单
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param menuType 菜单类型
     * @param status 状态
     * @return 分页结果
     */
    Uni<ApiResponse<PageResult<Menu>>> getMenuList(Long tenantId, PageRequest pageRequest, 
                                                  String keyword, Integer menuType, Integer status);

    /**
     * 查询所有菜单
     * 
     * @param tenantId 租户ID
     * @return 菜单列表
     */
    Uni<ApiResponse<List<Menu>>> getAllMenus(Long tenantId);

    /**
     * 查询菜单树
     * 
     * @param tenantId 租户ID
     * @return 菜单树结构
     */
    Uni<ApiResponse<List<Menu>>> getMenuTree(Long tenantId);

    /**
     * 查询可见菜单树
     * 
     * @param tenantId 租户ID
     * @return 可见菜单树结构
     */
    Uni<ApiResponse<List<Menu>>> getVisibleMenuTree(Long tenantId);

    /**
     * 查询根菜单
     * 
     * @param tenantId 租户ID
     * @return 根菜单列表
     */
    Uni<ApiResponse<List<Menu>>> getRootMenus(Long tenantId);

    /**
     * 根据父菜单ID查询子菜单
     * 
     * @param tenantId 租户ID
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    Uni<ApiResponse<List<Menu>>> getChildMenus(Long tenantId, Long parentId);

    /**
     * 根据菜单类型查询菜单
     * 
     * @param tenantId 租户ID
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    Uni<ApiResponse<List<Menu>>> getMenusByType(Long tenantId, Integer menuType);

    // ==================== 用户菜单查询 ====================

    /**
     * 查询用户菜单
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户菜单列表
     */
    Uni<ApiResponse<List<Menu>>> getUserMenus(Long tenantId, Long userId);

    /**
     * 查询用户菜单树
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户菜单树结构
     */
    Uni<ApiResponse<List<Menu>>> getUserMenuTree(Long tenantId, Long userId);

    /**
     * 查询用户可访问的菜单编码
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 菜单编码列表
     */
    Uni<ApiResponse<List<String>>> getUserMenuCodes(Long tenantId, Long userId);

    /**
     * 检查用户是否可访问菜单
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param menuCode 菜单编码
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> hasUserMenuAccess(Long tenantId, Long userId, String menuCode);

    // ==================== 菜单权限管理 ====================

    /**
     * 根据权限编码查询菜单
     * 
     * @param tenantId 租户ID
     * @param permissionCode 权限编码
     * @return 菜单列表
     */
    Uni<ApiResponse<List<Menu>>> getMenusByPermission(Long tenantId, String permissionCode);

    /**
     * 查询菜单的按钮权限
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 按钮权限列表
     */
    Uni<ApiResponse<List<Menu>>> getMenuButtons(Long tenantId, Long menuId);

    /**
     * 查询用户在指定菜单下的按钮权限
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param menuId 菜单ID
     * @return 用户按钮权限列表
     */
    Uni<ApiResponse<List<Menu>>> getUserMenuButtons(Long tenantId, Long userId, Long menuId);

    // ==================== 菜单验证 ====================

    /**
     * 检查菜单编码是否存在
     * 
     * @param tenantId 租户ID
     * @param menuCode 菜单编码
     * @param excludeId 排除的菜单ID
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> checkMenuCodeExists(Long tenantId, String menuCode, Long excludeId);

    /**
     * 验证菜单是否可以删除
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> canDeleteMenu(Long tenantId, Long menuId);

    /**
     * 验证菜单路径是否有效
     * 
     * @param menuPath 菜单路径
     * @return 验证结果
     */
    ApiResponse<Boolean> validateMenuPath(String menuPath);

    // ==================== 菜单状态管理 ====================

    /**
     * 启用菜单
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> enableMenu(Long tenantId, Long menuId);

    /**
     * 禁用菜单
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> disableMenu(Long tenantId, Long menuId);

    /**
     * 显示菜单
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> showMenu(Long tenantId, Long menuId);

    /**
     * 隐藏菜单
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> hideMenu(Long tenantId, Long menuId);

    // ==================== 菜单排序 ====================

    /**
     * 更新菜单排序
     * 
     * @param tenantId 租户ID
     * @param menuId 菜单ID
     * @param sortOrder 排序号
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> updateMenuSort(Long tenantId, Long menuId, Integer sortOrder);

    /**
     * 批量更新菜单排序
     * 
     * @param tenantId 租户ID
     * @param menuSorts 菜单排序列表（菜单ID和排序号的映射）
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> batchUpdateMenuSort(Long tenantId, java.util.Map<Long, Integer> menuSorts);

    // ==================== 菜单初始化 ====================

    /**
     * 初始化租户菜单
     * 
     * @param tenantId 租户ID
     * @return 初始化结果
     */
    Uni<ApiResponse<Void>> initTenantMenus(Long tenantId);

    /**
     * 同步系统菜单
     * 
     * @param tenantId 租户ID
     * @return 同步结果
     */
    Uni<ApiResponse<Void>> syncSystemMenus(Long tenantId);

    // ==================== 统计方法 ====================

    /**
     * 统计租户下的菜单数量
     * 
     * @param tenantId 租户ID
     * @return 菜单数量
     */
    Uni<ApiResponse<Long>> countMenus(Long tenantId);

    /**
     * 统计菜单下的子菜单数量
     * 
     * @param tenantId 租户ID
     * @param parentId 父菜单ID
     * @return 子菜单数量
     */
    Uni<ApiResponse<Long>> countChildMenus(Long tenantId, Long parentId);

    // ==================== 菜单导入导出 ====================

    /**
     * 导出菜单数据
     * 
     * @param tenantId 租户ID
     * @return 菜单数据
     */
    Uni<ApiResponse<String>> exportMenus(Long tenantId);

    /**
     * 导入菜单数据
     * 
     * @param tenantId 租户ID
     * @param menuData 菜单数据
     * @return 导入结果
     */
    Uni<ApiResponse<Void>> importMenus(Long tenantId, String menuData);
}
