package com.visthink.member.service.impl;

import com.visthink.member.service.DictionaryService;
import com.visthink.member.entity.Dictionary;
import com.visthink.member.repository.DictionaryRepository;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;

import io.smallrye.mutiny.Uni;
import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典服务实现类
 * 
 * 实现字典管理的业务逻辑
 * 包含字典CRUD、字典树构建、字典项查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class DictionaryServiceImpl implements DictionaryService {

    @Inject
    DictionaryRepository dictionaryRepository;

    // ==================== 基础CRUD操作 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Dictionary>> createDictionary(Long tenantId, Dictionary dictionary) {
        Log.infof("创建字典: tenantId=%d, dictCode=%s", tenantId, dictionary.getDictCode());

        // 1. 验证字典编码是否已存在
        return dictionaryRepository.existsByDictCode(dictionary.getDictCode(), tenantId, null)
                .flatMap(exists -> {
                    if (exists) {
                        return Uni.createFrom().item(ApiResponse.<Dictionary>error("字典编码已存在"));
                    }

                    // 2. 验证父字典是否存在（如果有）
                    if (dictionary.getParentId() != null) {
                        return dictionaryRepository.findByIdAndTenantId(dictionary.getParentId(), tenantId)
                                .flatMap(parentDict -> {
                                    if (parentDict == null) {
                                        return Uni.createFrom().item(ApiResponse.<Dictionary>error("父字典不存在"));
                                    }
                                    if (!parentDict.isDictType()) {
                                        return Uni.createFrom().item(ApiResponse.<Dictionary>error("父字典必须是字典类型"));
                                    }
                                    return createDictionaryInternal(tenantId, dictionary);
                                });
                    } else {
                        return createDictionaryInternal(tenantId, dictionary);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "创建字典失败: tenantId=%d, dictCode=%s", tenantId, dictionary.getDictCode());
                    return ApiResponse.error("创建字典失败: " + throwable.getMessage());
                });
    }

    /**
     * 内部创建字典方法
     */
    private Uni<ApiResponse<Dictionary>> createDictionaryInternal(Long tenantId, Dictionary dictionary) {
        // 设置字典信息
        dictionary.setTenantId(tenantId);
        dictionary.setCreateTime(LocalDateTime.now());
        dictionary.setUpdateTime(LocalDateTime.now());

        // 设置默认值
        if (dictionary.getIsSystem() == null) {
            dictionary.setIsSystem(false);
        }
        if (dictionary.getIsEditable() == null) {
            dictionary.setIsEditable(true);
        }

        // 保存字典
        return dictionaryRepository.persist(dictionary)
                .map(ApiResponse::success);
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Dictionary>> updateDictionary(Long tenantId, Long dictionaryId, Dictionary dictionary) {
        Log.infof("更新字典: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);

        // 1. 查询字典是否存在
        return dictionaryRepository.findByIdAndTenantId(dictionaryId, tenantId)
                .flatMap(existingDict -> {
                    if (existingDict == null) {
                        return Uni.createFrom().item(ApiResponse.<Dictionary>error("字典不存在"));
                    }

                    // 2. 检查是否可编辑
                    if (!existingDict.isEditableDict()) {
                        return Uni.createFrom().item(ApiResponse.<Dictionary>error("系统字典不可编辑"));
                    }

                    // 3. 检查字典编码是否重复
                    return dictionaryRepository.existsByDictCode(dictionary.getDictCode(), tenantId, dictionaryId)
                            .flatMap(exists -> {
                                if (exists) {
                                    return Uni.createFrom().item(ApiResponse.<Dictionary>error("字典编码已存在"));
                                }

                                // 4. 更新字典信息
                                return updateDictionaryInternal(existingDict, dictionary);
                            });
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "更新字典失败: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);
                    return ApiResponse.error("更新字典失败: " + throwable.getMessage());
                });
    }

    /**
     * 内部更新字典方法
     */
    private Uni<ApiResponse<Dictionary>> updateDictionaryInternal(Dictionary existingDict, Dictionary dictionary) {
        // 更新字典信息
        existingDict.setDictCode(dictionary.getDictCode());
        existingDict.setDictName(dictionary.getDictName());
        existingDict.setDictType(dictionary.getDictType());
        existingDict.setParentId(dictionary.getParentId());
        existingDict.setDictValue(dictionary.getDictValue());
        existingDict.setDictLabel(dictionary.getDictLabel());
        existingDict.setDescription(dictionary.getDescription());
        existingDict.setDictStyle(dictionary.getDictStyle());
        existingDict.setDictColor(dictionary.getDictColor());
        existingDict.setExtraProps(dictionary.getExtraProps());
        existingDict.setSortOrder(dictionary.getSortOrder());
        existingDict.setStatus(dictionary.getStatus());
        existingDict.setRemark(dictionary.getRemark());
        existingDict.setUpdateTime(LocalDateTime.now());

        // 保存更新
        return dictionaryRepository.persist(existingDict)
                .map(ApiResponse::success);
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> deleteDictionary(Long tenantId, Long dictionaryId) {
        Log.infof("删除字典: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);

        // 1. 检查字典是否可以删除
        return canDeleteDictionary(tenantId, dictionaryId)
                .flatMap(canDelete -> {
                    if (!canDelete.getData()) {
                        return Uni.createFrom().item(ApiResponse.<Void>error("字典不能删除，可能存在子字典或为系统字典"));
                    }

                    // 2. 软删除字典
                    return dictionaryRepository.softDelete(dictionaryId, tenantId)
                            .map(success -> {
                                if (success) {
                                    return ApiResponse.<Void>success(null);
                                } else {
                                    return ApiResponse.<Void>error("删除字典失败");
                                }
                            });
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "删除字典失败: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);
                    return ApiResponse.error("删除字典失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> deleteDictionaries(Long tenantId, List<Long> dictionaryIds) {
        Log.infof("批量删除字典: tenantId=%d, dictionaryIds=%s", tenantId, dictionaryIds);

        if (dictionaryIds == null || dictionaryIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.<Void>error("字典ID列表不能为空"));
        }

        // 批量软删除字典
        return dictionaryRepository.softDeleteBatch(dictionaryIds, tenantId)
                .map(count -> {
                    if (count > 0) {
                        return ApiResponse.<Void>success(null);
                    } else {
                        return ApiResponse.<Void>error("删除字典失败");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "批量删除字典失败: tenantId=%d, dictionaryIds=%s", tenantId, dictionaryIds);
                    return ApiResponse.error("批量删除字典失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Dictionary>> getDictionaryById(Long tenantId, Long dictionaryId) {
        Log.infof("查询字典详情: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);

        return dictionaryRepository.findByIdAndTenantId(dictionaryId, tenantId)
                .map(dictionary -> {
                    if (dictionary != null) {
                        return ApiResponse.success(dictionary);
                    } else {
                        return ApiResponse.<Dictionary>error("字典不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询字典详情失败: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);
                    return ApiResponse.error("查询字典详情失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Dictionary>> getDictionaryByCode(Long tenantId, String dictCode) {
        Log.infof("根据编码查询字典: tenantId=%d, dictCode=%s", tenantId, dictCode);

        return dictionaryRepository.findByDictCodeAndTenantId(dictCode, tenantId)
                .map(dictionary -> {
                    if (dictionary != null) {
                        return ApiResponse.success(dictionary);
                    } else {
                        return ApiResponse.<Dictionary>error("字典不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "根据编码查询字典失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.error("查询字典失败: " + throwable.getMessage());
                });
    }

    // ==================== 查询操作 ====================

    @Override
    public Uni<ApiResponse<PageResult<Dictionary>>> getDictionaryList(Long tenantId, PageRequest pageRequest, 
                                                                    String keyword, Integer dictType, Long parentId) {
        Log.infof("分页查询字典: tenantId=%d, page=%d, size=%d", tenantId, pageRequest.getPage(), pageRequest.getSize());

        return dictionaryRepository.findByPage(tenantId, pageRequest, keyword, dictType, parentId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "分页查询字典失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询字典列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Dictionary>>> getAllDictionaries(Long tenantId) {
        Log.infof("查询所有字典: tenantId=%d", tenantId);

        return dictionaryRepository.findByTenantId(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询所有字典失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询字典列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Dictionary>>> getDictionaryTree(Long tenantId) {
        Log.infof("查询字典树: tenantId=%d", tenantId);

        return dictionaryRepository.findDictionaryTree(tenantId)
                .map(dictionaries -> {
                    // 构建字典树结构
                    List<Dictionary> tree = buildDictionaryTree(dictionaries);
                    return ApiResponse.success(tree);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询字典树失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询字典树失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Dictionary>>> getDictionaryTypes(Long tenantId) {
        Log.infof("查询字典类型: tenantId=%d", tenantId);

        return dictionaryRepository.findDictionaryTypes(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询字典类型失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询字典类型失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Dictionary>>> getDictionaryItems(Long tenantId, String dictTypeCode) {
        Log.infof("查询字典项: tenantId=%d, dictTypeCode=%s", tenantId, dictTypeCode);

        return dictionaryRepository.findDictionaryItems(dictTypeCode, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询字典项失败: tenantId=%d, dictTypeCode=%s", tenantId, dictTypeCode);
                    return ApiResponse.error("查询字典项失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Dictionary>>> getChildDictionaries(Long tenantId, Long parentId) {
        Log.infof("查询子字典: tenantId=%d, parentId=%d", tenantId, parentId);

        return dictionaryRepository.findByParentId(parentId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询子字典失败: tenantId=%d, parentId=%d", tenantId, parentId);
                    return ApiResponse.error("查询子字典失败: " + throwable.getMessage());
                });
    }

    // ==================== 字典值查询 ====================

    @Override
    public Uni<ApiResponse<Dictionary>> getDictionaryByValue(Long tenantId, String dictCode, String dictValue) {
        Log.infof("根据值查询字典项: tenantId=%d, dictCode=%s, dictValue=%s", tenantId, dictCode, dictValue);

        return dictionaryRepository.findByDictCodeAndValue(dictCode, dictValue, tenantId)
                .map(dictionary -> {
                    if (dictionary != null) {
                        return ApiResponse.success(dictionary);
                    } else {
                        return ApiResponse.<Dictionary>error("字典项不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "根据值查询字典项失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.error("查询字典项失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Map<String, String>>> getDictionaryKeyValueMap(Long tenantId, String dictCode) {
        Log.infof("获取字典键值对: tenantId=%d, dictCode=%s", tenantId, dictCode);

        return dictionaryRepository.findDictionaryItems(dictCode, tenantId)
                .map(dictionaries -> {
                    Map<String, String> keyValueMap = dictionaries.stream()
                            .filter(dict -> dict.getDictValue() != null && dict.getDictLabel() != null)
                            .collect(Collectors.toMap(
                                    Dictionary::getDictValue,
                                    Dictionary::getDictLabel,
                                    (existing, replacement) -> existing // 如果有重复key，保留第一个
                            ));
                    return ApiResponse.success(keyValueMap);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "获取字典键值对失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.error("获取字典键值对失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<String>>> getDictionaryLabels(Long tenantId, String dictCode) {
        Log.infof("获取字典标签列表: tenantId=%d, dictCode=%s", tenantId, dictCode);

        return dictionaryRepository.findDictionaryItems(dictCode, tenantId)
                .map(dictionaries -> {
                    List<String> labels = dictionaries.stream()
                            .map(Dictionary::getDictLabel)
                            .filter(label -> label != null && !label.trim().isEmpty())
                            .collect(Collectors.toList());
                    return ApiResponse.success(labels);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "获取字典标签列表失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.error("获取字典标签列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<String>> getDictionaryLabel(Long tenantId, String dictCode, String dictValue) {
        Log.infof("获取字典标签: tenantId=%d, dictCode=%s, dictValue=%s", tenantId, dictCode, dictValue);

        return dictionaryRepository.findByDictCodeAndValue(dictCode, dictValue, tenantId)
                .map(dictionary -> {
                    if (dictionary != null && dictionary.getDictLabel() != null) {
                        return ApiResponse.<String>success(dictionary.getDictLabel());
                    } else {
                        return ApiResponse.<String>success(dictValue); // 如果找不到标签，返回原值
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "获取字典标签失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.<String>error("获取字典标签失败: " + throwable.getMessage());
                });
    }

    // ==================== 私有辅助方法 ====================

    // ==================== 字典验证 ====================

    @Override
    public Uni<ApiResponse<Boolean>> checkDictCodeExists(Long tenantId, String dictCode, Long excludeId) {
        Log.infof("检查字典编码是否存在: tenantId=%d, dictCode=%s", tenantId, dictCode);

        return dictionaryRepository.existsByDictCode(dictCode, tenantId, excludeId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查字典编码失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.error("检查字典编码失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> canDeleteDictionary(Long tenantId, Long dictionaryId) {
        Log.infof("验证字典是否可删除: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);

        // 1. 查询字典信息
        return dictionaryRepository.findByIdAndTenantId(dictionaryId, tenantId)
                .flatMap(dictionary -> {
                    if (dictionary == null) {
                        return Uni.createFrom().item(ApiResponse.success(false));
                    }

                    // 2. 检查是否为系统字典
                    if (dictionary.isSystemDict()) {
                        return Uni.createFrom().item(ApiResponse.success(false));
                    }

                    // 3. 检查是否有子字典
                    return dictionaryRepository.hasChildren(dictionaryId, tenantId)
                            .map(hasChildren -> ApiResponse.success(!hasChildren));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "验证字典删除失败: tenantId=%d, dictionaryId=%d", tenantId, dictionaryId);
                    return ApiResponse.error("验证失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> validateDictionaryValue(Long tenantId, String dictCode, String dictValue) {
        Log.infof("验证字典值: tenantId=%d, dictCode=%s, dictValue=%s", tenantId, dictCode, dictValue);

        return dictionaryRepository.existsByDictValue(dictCode, dictValue, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "验证字典值失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.error("验证字典值失败: " + throwable.getMessage());
                });
    }

    // ==================== 字典初始化 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> initTenantDictionaries(Long tenantId) {
        Log.infof("初始化租户字典: tenantId=%d", tenantId);

        // 从系统字典复制到租户字典
        return dictionaryRepository.find("tenantId = 0 and deleted = 0").list()
                .flatMap(systemDictionaries -> {
                    List<Dictionary> tenantDictionaries = systemDictionaries.stream()
                            .map(dict -> copyDictionaryForTenant((Dictionary) dict))
                            .peek(d -> d.setTenantId(tenantId))
                            .collect(Collectors.toList());

                    return Dictionary.persist(tenantDictionaries)
                            .map(success -> ApiResponse.<Void>success(null));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "初始化租户字典失败: tenantId=%d", tenantId);
                    return ApiResponse.error("初始化字典失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> syncSystemDictionaries(Long tenantId) {
        Log.infof("同步系统字典: tenantId=%d", tenantId);

        // 实现系统字典同步逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Dictionary>> createDictionaryType(Long tenantId, String dictCode, String dictName, String description) {
        Log.infof("创建字典类型: tenantId=%d, dictCode=%s", tenantId, dictCode);

        Dictionary dictType = Dictionary.createDictType(dictCode, dictName, description, tenantId);
        return createDictionary(tenantId, dictType);
    }

    @Override
    @Transactional
    public Uni<ApiResponse<List<Dictionary>>> createDictionaryItems(Long tenantId, String dictTypeCode, List<Dictionary> dictItems) {
        Log.infof("批量创建字典项: tenantId=%d, dictTypeCode=%s, count=%d", tenantId, dictTypeCode, dictItems.size());

        // 1. 查询字典类型
        return dictionaryRepository.findByDictCodeAndTenantId(dictTypeCode, tenantId)
                .flatMap(dictType -> {
                    if (dictType == null || !dictType.isDictType()) {
                        return Uni.createFrom().item(ApiResponse.<List<Dictionary>>error("字典类型不存在"));
                    }

                    // 2. 设置字典项的父ID和租户ID
                    dictItems.forEach(item -> {
                        item.setParentId(dictType.id);
                        item.setTenantId(tenantId);
                        item.setDictType(Dictionary.DictType.ITEM);
                        item.setCreateTime(LocalDateTime.now());
                        item.setUpdateTime(LocalDateTime.now());
                    });

                    // 3. 批量保存字典项
                    return Dictionary.persist(dictItems)
                            .map(success -> ApiResponse.success(dictItems));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "批量创建字典项失败: tenantId=%d, dictTypeCode=%s", tenantId, dictTypeCode);
                    return ApiResponse.error("批量创建字典项失败: " + throwable.getMessage());
                });
    }

    // ==================== 字典缓存管理 ====================

    @Override
    public Uni<ApiResponse<Void>> refreshDictionaryCache(Long tenantId, String dictCode) {
        Log.infof("刷新字典缓存: tenantId=%d, dictCode=%s", tenantId, dictCode);

        // 实现字典缓存刷新逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    @Override
    public Uni<ApiResponse<Void>> warmupDictionaryCache(Long tenantId) {
        Log.infof("预热字典缓存: tenantId=%d", tenantId);

        // 实现字典缓存预热逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 统计方法 ====================

    @Override
    public Uni<ApiResponse<Long>> countDictionaries(Long tenantId) {
        Log.infof("统计字典数量: tenantId=%d", tenantId);

        return dictionaryRepository.countByTenantId(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "统计字典数量失败: tenantId=%d", tenantId);
                    return ApiResponse.error("统计失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Long>> countDictionaryTypes(Long tenantId) {
        Log.infof("统计字典类型数量: tenantId=%d", tenantId);

        return dictionaryRepository.countDictionaryTypes(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "统计字典类型数量失败: tenantId=%d", tenantId);
                    return ApiResponse.error("统计失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Long>> countDictionaryItems(Long tenantId, String dictTypeCode) {
        Log.infof("统计字典项数量: tenantId=%d, dictTypeCode=%s", tenantId, dictTypeCode);

        return dictionaryRepository.countDictionaryItems(dictTypeCode, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "统计字典项数量失败: tenantId=%d, dictTypeCode=%s", tenantId, dictTypeCode);
                    return ApiResponse.error("统计失败: " + throwable.getMessage());
                });
    }

    // ==================== 字典导入导出 ====================

    @Override
    public Uni<ApiResponse<String>> exportDictionaries(Long tenantId, String dictCode) {
        Log.infof("导出字典数据: tenantId=%d, dictCode=%s", tenantId, dictCode);

        Uni<List<Dictionary>> dictionariesUni;
        if (dictCode != null && !dictCode.trim().isEmpty()) {
            dictionariesUni = dictionaryRepository.findDictionaryItems(dictCode, tenantId);
        } else {
            dictionariesUni = dictionaryRepository.findByTenantId(tenantId);
        }

        return dictionariesUni
                .map(dictionaries -> {
                    // 简化实现，实际应该转换为JSON或其他格式
                    String data = dictionaries.stream()
                            .map(d -> d.getDictCode() + ":" + d.getDictName() + ":" + d.getDictValue())
                            .collect(Collectors.joining("\n"));
                    return ApiResponse.<String>success(data);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "导出字典数据失败: tenantId=%d, dictCode=%s", tenantId, dictCode);
                    return ApiResponse.<String>error("导出失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> importDictionaries(Long tenantId, String dictionaryData, Boolean overwrite) {
        Log.infof("导入字典数据: tenantId=%d, overwrite=%s", tenantId, overwrite);

        // 简化实现，实际应该解析JSON或其他格式
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建字典树结构
     */
    private List<Dictionary> buildDictionaryTree(List<Dictionary> dictionaries) {
        if (dictionaries == null || dictionaries.isEmpty()) {
            return new ArrayList<>();
        }

        // 按父ID分组
        Map<Long, List<Dictionary>> parentMap = dictionaries.stream()
                .filter(d -> d.getParentId() != null)
                .collect(Collectors.groupingBy(Dictionary::getParentId));

        // 设置子字典
        dictionaries.forEach(dictionary -> {
            List<Dictionary> children = parentMap.get(dictionary.id);
            if (children != null) {
                // 按排序号排序
                children.sort((d1, d2) -> {
                    int sort1 = d1.getSortOrder() != null ? d1.getSortOrder() : 0;
                    int sort2 = d2.getSortOrder() != null ? d2.getSortOrder() : 0;
                    return Integer.compare(sort1, sort2);
                });
                dictionary.setChildren(children);
            }
        });

        // 返回根字典，按排序号排序
        return dictionaries.stream()
                .filter(d -> d.getParentId() == null)
                .sorted((d1, d2) -> {
                    int sort1 = d1.getSortOrder() != null ? d1.getSortOrder() : 0;
                    int sort2 = d2.getSortOrder() != null ? d2.getSortOrder() : 0;
                    return Integer.compare(sort1, sort2);
                })
                .collect(Collectors.toList());
    }

    /**
     * 复制字典给租户
     */
    private Dictionary copyDictionaryForTenant(Dictionary source) {
        Dictionary copy = new Dictionary();
        copy.setDictCode(source.getDictCode());
        copy.setDictName(source.getDictName());
        copy.setDictType(source.getDictType());
        copy.setParentId(source.getParentId());
        copy.setDictValue(source.getDictValue());
        copy.setDictLabel(source.getDictLabel());
        copy.setDescription(source.getDescription());
        copy.setIsSystem(false); // 租户字典不是系统字典
        copy.setIsEditable(true);
        copy.setDictStyle(source.getDictStyle());
        copy.setDictColor(source.getDictColor());
        copy.setExtraProps(source.getExtraProps());
        copy.setSortOrder(source.getSortOrder());
        copy.setCreateTime(LocalDateTime.now());
        copy.setUpdateTime(LocalDateTime.now());
        return copy;
    }
}
