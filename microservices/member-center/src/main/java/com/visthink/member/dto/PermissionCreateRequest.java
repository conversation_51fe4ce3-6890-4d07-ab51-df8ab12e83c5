package com.visthink.member.dto;

import lombok.Data;
import jakarta.validation.constraints.*;

/**
 * 权限创建请求DTO
 * 
 * 用于接收创建权限的请求参数
 * 包含权限基本信息和层级关系配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PermissionCreateRequest {

    /**
     * 权限编码（租户内唯一）
     * 格式：resource:action，如 user:read, order:create
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+:[a-zA-Z0-9_-]+$", message = "权限编码格式错误，应为 resource:action")
    private String permissionCode;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;

    /**
     * 权限类型：1-API权限，2-菜单权限，3-按钮权限，4-数据权限
     */
    @NotNull(message = "权限类型不能为空")
    @Min(value = 1, message = "权限类型值必须大于等于1")
    @Max(value = 4, message = "权限类型值必须小于等于4")
    private Integer permissionType = 1;

    /**
     * 父权限ID（用于构建权限树）
     */
    private Long parentId;

    /**
     * 资源类型：API、MENU、BUTTON、DATA
     */
    @Size(max = 50, message = "资源类型长度不能超过50个字符")
    private String resourceType;

    /**
     * 资源路径（API路径或菜单路径）
     */
    @Size(max = 200, message = "资源路径长度不能超过200个字符")
    private String resourcePath;

    /**
     * 操作类型：GET、POST、PUT、DELETE等
     */
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    private String action;

    /**
     * 权限描述
     */
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    private String description;

    /**
     * 是否系统内置权限
     */
    private Boolean isSystem = false;

    /**
     * 排序号
     */
    @Min(value = 0, message = "排序号不能为负数")
    private Integer sortOrder = 0;

    /**
     * 备注
     */
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    // ==================== 业务验证方法 ====================

    /**
     * 验证权限编码格式
     */
    public boolean isValidPermissionCode() {
        if (permissionCode == null || permissionCode.trim().isEmpty()) {
            return false;
        }
        
        String[] parts = permissionCode.split(":");
        return parts.length == 2 && 
               !parts[0].trim().isEmpty() && 
               !parts[1].trim().isEmpty();
    }

    /**
     * 获取权限编码中的资源部分
     */
    public String getResourceFromCode() {
        if (!isValidPermissionCode()) {
            return null;
        }
        return permissionCode.split(":")[0];
    }

    /**
     * 获取权限编码中的操作部分
     */
    public String getActionFromCode() {
        if (!isValidPermissionCode()) {
            return null;
        }
        return permissionCode.split(":")[1];
    }

    /**
     * 获取权限类型描述
     */
    public String getPermissionTypeDesc() {
        if (permissionType == null) {
            return "API权限";
        }
        switch (permissionType) {
            case 1:
                return "API权限";
            case 2:
                return "菜单权限";
            case 3:
                return "按钮权限";
            case 4:
                return "数据权限";
            default:
                return "未知类型";
        }
    }

    /**
     * 判断是否为API权限
     */
    public boolean isApiPermission() {
        return Integer.valueOf(1).equals(permissionType);
    }

    /**
     * 判断是否为菜单权限
     */
    public boolean isMenuPermission() {
        return Integer.valueOf(2).equals(permissionType);
    }

    /**
     * 判断是否为按钮权限
     */
    public boolean isButtonPermission() {
        return Integer.valueOf(3).equals(permissionType);
    }

    /**
     * 判断是否为数据权限
     */
    public boolean isDataPermission() {
        return Integer.valueOf(4).equals(permissionType);
    }

    /**
     * 判断是否为根权限（无父权限）
     */
    public boolean isRootPermission() {
        return parentId == null;
    }

    /**
     * 自动设置资源类型和操作
     */
    public void autoSetResourceAndAction() {
        if (isValidPermissionCode()) {
            if (resourceType == null || resourceType.trim().isEmpty()) {
                if (isApiPermission()) {
                    resourceType = "API";
                } else if (isMenuPermission()) {
                    resourceType = "MENU";
                } else if (isButtonPermission()) {
                    resourceType = "BUTTON";
                } else if (isDataPermission()) {
                    resourceType = "DATA";
                }
            }
            
            if (action == null || action.trim().isEmpty()) {
                action = getActionFromCode();
            }
        }
    }
}

/**
 * 权限更新请求DTO
 * 
 * 用于接收更新权限的请求参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
class PermissionUpdateRequest {

    /**
     * 权限ID（必填）
     */
    @NotNull(message = "权限ID不能为空")
    private Long id;

    /**
     * 权限编码（租户内唯一）
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+:[a-zA-Z0-9_-]+$", message = "权限编码格式错误，应为 resource:action")
    private String permissionCode;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    private String permissionName;

    /**
     * 权限类型：1-API权限，2-菜单权限，3-按钮权限，4-数据权限
     */
    @NotNull(message = "权限类型不能为空")
    @Min(value = 1, message = "权限类型值必须大于等于1")
    @Max(value = 4, message = "权限类型值必须小于等于4")
    private Integer permissionType = 1;

    /**
     * 父权限ID（用于构建权限树）
     */
    private Long parentId;

    /**
     * 资源类型：API、MENU、BUTTON、DATA
     */
    @Size(max = 50, message = "资源类型长度不能超过50个字符")
    private String resourceType;

    /**
     * 资源路径（API路径或菜单路径）
     */
    @Size(max = 200, message = "资源路径长度不能超过200个字符")
    private String resourcePath;

    /**
     * 操作类型：GET、POST、PUT、DELETE等
     */
    @Size(max = 50, message = "操作类型长度不能超过50个字符")
    private String action;

    /**
     * 权限描述
     */
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    private String description;

    /**
     * 排序号
     */
    @Min(value = 0, message = "排序号不能为负数")
    private Integer sortOrder = 0;

    /**
     * 状态：1-启用，0-禁用
     */
    @Min(value = 0, message = "状态值必须大于等于0")
    @Max(value = 1, message = "状态值必须小于等于1")
    private Integer status = 1;

    /**
     * 备注
     */
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    // ==================== 业务方法 ====================

    /**
     * 判断是否启用
     */
    public boolean isEnabled() {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 判断是否禁用
     */
    public boolean isDisabled() {
        return Integer.valueOf(0).equals(status);
    }
}
