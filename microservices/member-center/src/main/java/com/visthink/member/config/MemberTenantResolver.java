package com.visthink.member.config;

import com.visthink.common.context.TenantContext;
import io.quarkus.hibernate.orm.runtime.tenant.TenantResolver;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

/**
 * 多租户解析器
 * 
 * 为 Hibernate ORM 提供租户标识符解析
 * 支持 Schema 级别的多租户隔离
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class MemberTenantResolver implements TenantResolver {

    private static final Logger LOG = Logger.getLogger(MemberTenantResolver.class);

    @Inject
    TenantContext tenantContext;

    /**
     * 解析当前请求的租户标识符
     * 
     * @return 租户标识符
     */
    @Override
    public String getDefaultTenantId() {
        try {
            // 获取当前租户ID
            Long tenantId = tenantContext.getCurrentTenantIdSync();
            String tenantIdentifier = getTenantIdentifier(tenantId);
            
            LOG.debugf("解析租户标识符: %s (租户ID: %d)", tenantIdentifier, tenantId);
            return tenantIdentifier;
            
        } catch (Exception e) {
            LOG.warnf(e, "解析租户标识符失败，使用默认租户");
            return "default";
        }
    }

    /**
     * 解析指定请求的租户标识符
     * 
     * @return 租户标识符
     */
    @Override
    public String resolveTenantId() {
        return getDefaultTenantId();
    }

    /**
     * 根据租户ID获取租户标识符
     * 
     * @param tenantId 租户ID
     * @return 租户标识符
     */
    private String getTenantIdentifier(Long tenantId) {
        if (tenantId == null || tenantId == 1L) {
            return "default";
        }
        return "tenant_" + tenantId;
    }

    /**
     * 验证租户标识符是否有效
     * 
     * @param tenantIdentifier 租户标识符
     * @return 是否有效
     */
    public boolean isValidTenantIdentifier(String tenantIdentifier) {
        if (tenantIdentifier == null || tenantIdentifier.trim().isEmpty()) {
            return false;
        }
        
        // 默认租户
        if ("default".equals(tenantIdentifier)) {
            return true;
        }
        
        // 租户格式验证
        return tenantIdentifier.matches("^tenant_\\d+$");
    }

    /**
     * 从租户标识符提取租户ID
     * 
     * @param tenantIdentifier 租户标识符
     * @return 租户ID
     */
    public Long extractTenantId(String tenantIdentifier) {
        if ("default".equals(tenantIdentifier)) {
            return 1L;
        }
        
        if (tenantIdentifier != null && tenantIdentifier.startsWith("tenant_")) {
            try {
                String idStr = tenantIdentifier.substring("tenant_".length());
                return Long.parseLong(idStr);
            } catch (NumberFormatException e) {
                LOG.warnf("无效的租户标识符格式: %s", tenantIdentifier);
            }
        }
        
        return 1L; // 默认租户ID
    }
}
