package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.util.List;

/**
 * 地区实体类
 * 
 * 管理系统地区信息，支持层级地区结构
 * 包含省、市、区县、街道等行政区划
 * 支持地区编码、邮政编码、经纬度等信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "system_regions",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"region_code"})
       },
       indexes = {
           @Index(name = "idx_region_code", columnList = "region_code"),
           @Index(name = "idx_region_name", columnList = "region_name"),
           @Index(name = "idx_region_level", columnList = "region_level"),
           @Index(name = "idx_region_parent", columnList = "parent_id"),
           @Index(name = "idx_region_postal", columnList = "postal_code")
       })
public class Region extends BaseEntity {

    /**
     * 地区编码（全国唯一，如行政区划代码）
     */
    @Column(name = "region_code", nullable = false, length = 20)
    private String regionCode;

    /**
     * 地区名称
     */
    @Column(name = "region_name", nullable = false, length = 100)
    private String regionName;

    /**
     * 地区简称
     */
    @Column(name = "region_short_name", length = 50)
    private String regionShortName;

    /**
     * 地区级别：1-国家，2-省/直辖市，3-市，4-区县，5-街道/乡镇
     */
    @Column(name = "region_level", nullable = false)
    private Integer regionLevel;

    /**
     * 父地区ID（构建地区树）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 地区类型：province-省，city-市，district-区县，street-街道
     */
    @Column(name = "region_type", length = 20)
    private String regionType;

    /**
     * 邮政编码
     */
    @Column(name = "postal_code", length = 10)
    private String postalCode;

    /**
     * 区号
     */
    @Column(name = "area_code", length = 10)
    private String areaCode;

    /**
     * 经度
     */
    @Column(name = "longitude")
    private Double longitude;

    /**
     * 纬度
     */
    @Column(name = "latitude")
    private Double latitude;

    /**
     * 是否直辖市
     */
    @Column(name = "is_municipality", nullable = false)
    private Boolean isMunicipality = false;

    /**
     * 是否热门地区
     */
    @Column(name = "is_hot", nullable = false)
    private Boolean isHot = false;

    /**
     * 地区描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_props", columnDefinition = "TEXT")
    private String extraProps;

    // ==================== 关联关系 ====================

    /**
     * 子地区列表（一对多关系）
     */
    @OneToMany(mappedBy = "parentId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    private List<Region> children;

    /**
     * 父地区实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Region parent;

    // ==================== 常量定义 ====================

    /**
     * 地区级别常量
     */
    public static class RegionLevel {
        /** 国家 */
        public static final Integer COUNTRY = 1;
        /** 省/直辖市 */
        public static final Integer PROVINCE = 2;
        /** 市 */
        public static final Integer CITY = 3;
        /** 区县 */
        public static final Integer DISTRICT = 4;
        /** 街道/乡镇 */
        public static final Integer STREET = 5;
    }

    /**
     * 地区类型常量
     */
    public static class RegionType {
        /** 国家 */
        public static final String COUNTRY = "country";
        /** 省 */
        public static final String PROVINCE = "province";
        /** 市 */
        public static final String CITY = "city";
        /** 区县 */
        public static final String DISTRICT = "district";
        /** 街道 */
        public static final String STREET = "street";
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否为国家级别
     */
    public boolean isCountryLevel() {
        return RegionLevel.COUNTRY.equals(this.regionLevel);
    }

    /**
     * 判断是否为省级别
     */
    public boolean isProvinceLevel() {
        return RegionLevel.PROVINCE.equals(this.regionLevel);
    }

    /**
     * 判断是否为市级别
     */
    public boolean isCityLevel() {
        return RegionLevel.CITY.equals(this.regionLevel);
    }

    /**
     * 判断是否为区县级别
     */
    public boolean isDistrictLevel() {
        return RegionLevel.DISTRICT.equals(this.regionLevel);
    }

    /**
     * 判断是否为街道级别
     */
    public boolean isStreetLevel() {
        return RegionLevel.STREET.equals(this.regionLevel);
    }

    /**
     * 判断是否为根地区（无父地区）
     */
    public boolean isRootRegion() {
        return this.parentId == null;
    }

    /**
     * 判断是否有子地区
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }

    /**
     * 判断是否为直辖市
     */
    public boolean isMunicipalityRegion() {
        return Boolean.TRUE.equals(this.isMunicipality);
    }

    /**
     * 判断是否为热门地区
     */
    public boolean isHotRegion() {
        return Boolean.TRUE.equals(this.isHot);
    }

    /**
     * 获取地区级别描述
     */
    public String getRegionLevelDesc() {
        if (this.regionLevel == null) {
            return "未知级别";
        }
        switch (this.regionLevel) {
            case 1:
                return "国家";
            case 2:
                return "省/直辖市";
            case 3:
                return "市";
            case 4:
                return "区县";
            case 5:
                return "街道/乡镇";
            default:
                return "未知级别";
        }
    }

    /**
     * 获取地区显示名称
     */
    public String getDisplayName() {
        if (regionShortName != null && !regionShortName.trim().isEmpty()) {
            return regionShortName;
        }
        return regionName;
    }

    /**
     * 获取完整地区路径
     */
    public String getFullPath() {
        if (isRootRegion()) {
            return regionName;
        }
        // 实际实现时需要递归构建完整路径
        return regionName;
    }

    /**
     * 验证地区编码格式
     */
    public boolean isValidRegionCode() {
        if (regionCode == null || regionCode.trim().isEmpty()) {
            return false;
        }
        
        // 地区编码格式验证（如6位行政区划代码）
        return regionCode.matches("^\\d{6}$") || regionCode.matches("^\\d{9}$") || regionCode.matches("^\\d{12}$");
    }

    /**
     * 验证坐标是否有效
     */
    public boolean hasValidCoordinates() {
        return longitude != null && latitude != null &&
               longitude >= -180 && longitude <= 180 &&
               latitude >= -90 && latitude <= 90;
    }

    // ==================== 便捷构造方法 ====================

    /**
     * 创建国家
     */
    public static Region createCountry(String code, String name, String shortName) {
        Region region = new Region();
        region.setRegionCode(code);
        region.setRegionName(name);
        region.setRegionShortName(shortName);
        region.setRegionLevel(RegionLevel.COUNTRY);
        region.setRegionType(RegionType.COUNTRY);
        region.setIsMunicipality(false);
        region.setIsHot(false);
        return region;
    }

    /**
     * 创建省份
     */
    public static Region createProvince(String code, String name, String shortName, Long parentId, boolean isMunicipality) {
        Region region = new Region();
        region.setRegionCode(code);
        region.setRegionName(name);
        region.setRegionShortName(shortName);
        region.setRegionLevel(RegionLevel.PROVINCE);
        region.setRegionType(RegionType.PROVINCE);
        region.setParentId(parentId);
        region.setIsMunicipality(isMunicipality);
        region.setIsHot(false);
        return region;
    }

    /**
     * 创建城市
     */
    public static Region createCity(String code, String name, String shortName, Long parentId, 
                                  String postalCode, String areaCode) {
        Region region = new Region();
        region.setRegionCode(code);
        region.setRegionName(name);
        region.setRegionShortName(shortName);
        region.setRegionLevel(RegionLevel.CITY);
        region.setRegionType(RegionType.CITY);
        region.setParentId(parentId);
        region.setPostalCode(postalCode);
        region.setAreaCode(areaCode);
        region.setIsMunicipality(false);
        region.setIsHot(false);
        return region;
    }

    /**
     * 创建区县
     */
    public static Region createDistrict(String code, String name, Long parentId, String postalCode) {
        Region region = new Region();
        region.setRegionCode(code);
        region.setRegionName(name);
        region.setRegionLevel(RegionLevel.DISTRICT);
        region.setRegionType(RegionType.DISTRICT);
        region.setParentId(parentId);
        region.setPostalCode(postalCode);
        region.setIsMunicipality(false);
        region.setIsHot(false);
        return region;
    }

    /**
     * 创建街道
     */
    public static Region createStreet(String code, String name, Long parentId) {
        Region region = new Region();
        region.setRegionCode(code);
        region.setRegionName(name);
        region.setRegionLevel(RegionLevel.STREET);
        region.setRegionType(RegionType.STREET);
        region.setParentId(parentId);
        region.setIsMunicipality(false);
        region.setIsHot(false);
        return region;
    }

    /**
     * 创建带坐标的地区
     */
    public static Region createWithCoordinates(String code, String name, Integer level, String type,
                                             Long parentId, Double longitude, Double latitude) {
        Region region = new Region();
        region.setRegionCode(code);
        region.setRegionName(name);
        region.setRegionLevel(level);
        region.setRegionType(type);
        region.setParentId(parentId);
        region.setLongitude(longitude);
        region.setLatitude(latitude);
        region.setIsMunicipality(false);
        region.setIsHot(false);
        return region;
    }
}
