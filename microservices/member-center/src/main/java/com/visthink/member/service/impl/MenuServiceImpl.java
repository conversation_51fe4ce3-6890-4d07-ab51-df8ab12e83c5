package com.visthink.member.service.impl;

import com.visthink.member.service.MenuService;
import com.visthink.member.entity.Menu;
import com.visthink.member.repository.MenuRepository;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.exception.BaseException;

import io.smallrye.mutiny.Uni;
import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 菜单服务实现类
 * 
 * 实现菜单管理的业务逻辑
 * 包含菜单CRUD、菜单树构建、用户菜单查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class MenuServiceImpl implements MenuService {

    @Inject
    MenuRepository menuRepository;

    // ==================== 基础CRUD操作 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Menu>> createMenu(Long tenantId, Menu menu) {
        Log.infof("创建菜单: tenantId=%d, menuCode=%s", tenantId, menu.getMenuCode());

        // 1. 验证菜单编码是否已存在
        return menuRepository.existsByMenuCode(menu.getMenuCode(), tenantId, null)
                .flatMap(exists -> {
                    if (exists) {
                        return Uni.createFrom().item(ApiResponse.<Menu>error("菜单编码已存在"));
                    }

                    // 2. 验证父菜单是否存在
                    if (menu.getParentId() != null) {
                        return menuRepository.findByIdAndTenantId(menu.getParentId(), tenantId)
                                .flatMap(parentMenu -> {
                                    if (parentMenu == null) {
                                        return Uni.createFrom().item(ApiResponse.<Menu>error("父菜单不存在"));
                                    }
                                    return createMenuInternal(tenantId, menu);
                                });
                    } else {
                        return createMenuInternal(tenantId, menu);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "创建菜单失败: tenantId=%d, menuCode=%s", tenantId, menu.getMenuCode());
                    return ApiResponse.error("创建菜单失败: " + throwable.getMessage());
                });
    }

    /**
     * 内部创建菜单方法
     */
    private Uni<ApiResponse<Menu>> createMenuInternal(Long tenantId, Menu menu) {
        // 设置菜单信息
        menu.setTenantId(tenantId);
        menu.setCreateTime(LocalDateTime.now());
        menu.setUpdateTime(LocalDateTime.now());

        // 设置默认值
        if (menu.getIsVisible() == null) {
            menu.setIsVisible(true);
        }
        if (menu.getIsExternal() == null) {
            menu.setIsExternal(false);
        }
        if (menu.getIsCached() == null) {
            menu.setIsCached(false);
        }

        // 保存菜单
        return menuRepository.persist(menu)
                .map(ApiResponse::success);
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Menu>> updateMenu(Long tenantId, Long menuId, Menu menu) {
        Log.infof("更新菜单: tenantId=%d, menuId=%d", tenantId, menuId);

        // 1. 查询菜单是否存在
        return menuRepository.findByIdAndTenantId(menuId, tenantId)
                .flatMap(existingMenu -> {
                    if (existingMenu == null) {
                        return Uni.createFrom().item(ApiResponse.<Menu>error("菜单不存在"));
                    }

                    // 2. 检查菜单编码是否重复
                    return menuRepository.existsByMenuCode(menu.getMenuCode(), tenantId, menuId)
                            .flatMap(exists -> {
                                if (exists) {
                                    return Uni.createFrom().item(ApiResponse.<Menu>error("菜单编码已存在"));
                                }

                                // 3. 验证父菜单（如果有）
                                if (menu.getParentId() != null && !menu.getParentId().equals(existingMenu.getParentId())) {
                                    return validateParentMenu(tenantId, menu.getParentId(), menuId)
                                            .flatMap(valid -> {
                                                if (!valid) {
                                                    return Uni.createFrom().item(ApiResponse.<Menu>error("父菜单无效或会形成循环引用"));
                                                }
                                                return updateMenuInternal(existingMenu, menu);
                                            });
                                } else {
                                    return updateMenuInternal(existingMenu, menu);
                                }
                            });
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "更新菜单失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("更新菜单失败: " + throwable.getMessage());
                });
    }

    /**
     * 内部更新菜单方法
     */
    private Uni<ApiResponse<Menu>> updateMenuInternal(Menu existingMenu, Menu menu) {
        // 更新菜单信息
        existingMenu.setMenuCode(menu.getMenuCode());
        existingMenu.setMenuName(menu.getMenuName());
        existingMenu.setMenuType(menu.getMenuType());
        existingMenu.setParentId(menu.getParentId());
        existingMenu.setMenuPath(menu.getMenuPath());
        existingMenu.setComponentPath(menu.getComponentPath());
        existingMenu.setMenuIcon(menu.getMenuIcon());
        existingMenu.setPermissionCode(menu.getPermissionCode());
        existingMenu.setIsExternal(menu.getIsExternal());
        existingMenu.setIsCached(menu.getIsCached());
        existingMenu.setIsVisible(menu.getIsVisible());
        existingMenu.setDescription(menu.getDescription());
        existingMenu.setButtonPermissions(menu.getButtonPermissions());
        existingMenu.setMetaData(menu.getMetaData());
        existingMenu.setSortOrder(menu.getSortOrder());
        existingMenu.setStatus(menu.getStatus());
        existingMenu.setRemark(menu.getRemark());
        existingMenu.setUpdateTime(LocalDateTime.now());

        // 保存更新
        return menuRepository.persist(existingMenu)
                .map(ApiResponse::success);
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> deleteMenu(Long tenantId, Long menuId) {
        Log.infof("删除菜单: tenantId=%d, menuId=%d", tenantId, menuId);

        // 1. 检查菜单是否可以删除
        return canDeleteMenu(tenantId, menuId)
                .flatMap(canDelete -> {
                    if (!canDelete.getData()) {
                        return Uni.createFrom().item(ApiResponse.<Void>error("菜单不能删除，存在子菜单"));
                    }

                    // 2. 软删除菜单
                    return menuRepository.softDelete(menuId, tenantId)
                            .map(success -> {
                                if (success) {
                                    return ApiResponse.<Void>success(null);
                                } else {
                                    return ApiResponse.<Void>error("删除菜单失败");
                                }
                            });
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "删除菜单失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("删除菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> deleteMenus(Long tenantId, List<Long> menuIds) {
        Log.infof("批量删除菜单: tenantId=%d, menuIds=%s", tenantId, menuIds);

        if (menuIds == null || menuIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.<Void>error("菜单ID列表不能为空"));
        }

        // 批量软删除菜单
        return menuRepository.softDeleteBatch(menuIds, tenantId)
                .map(count -> {
                    if (count > 0) {
                        return ApiResponse.<Void>success(null);
                    } else {
                        return ApiResponse.<Void>error("删除菜单失败");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "批量删除菜单失败: tenantId=%d, menuIds=%s", tenantId, menuIds);
                    return ApiResponse.error("批量删除菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Menu>> getMenuById(Long tenantId, Long menuId) {
        Log.infof("查询菜单详情: tenantId=%d, menuId=%d", tenantId, menuId);

        return menuRepository.findByIdAndTenantId(menuId, tenantId)
                .map(menu -> {
                    if (menu != null) {
                        return ApiResponse.success(menu);
                    } else {
                        return ApiResponse.<Menu>error("菜单不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询菜单详情失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("查询菜单详情失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Menu>> getMenuByCode(Long tenantId, String menuCode) {
        Log.infof("根据编码查询菜单: tenantId=%d, menuCode=%s", tenantId, menuCode);

        return menuRepository.findByMenuCodeAndTenantId(menuCode, tenantId)
                .map(menu -> {
                    if (menu != null) {
                        return ApiResponse.success(menu);
                    } else {
                        return ApiResponse.<Menu>error("菜单不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "根据编码查询菜单失败: tenantId=%d, menuCode=%s", tenantId, menuCode);
                    return ApiResponse.error("查询菜单失败: " + throwable.getMessage());
                });
    }

    // ==================== 查询操作 ====================

    @Override
    public Uni<ApiResponse<PageResult<Menu>>> getMenuList(Long tenantId, PageRequest pageRequest, 
                                                        String keyword, Integer menuType, Integer status) {
        Log.infof("分页查询菜单: tenantId=%d, page=%d, size=%d", tenantId, pageRequest.getPage(), pageRequest.getSize());

        return menuRepository.findByPage(tenantId, pageRequest, keyword, menuType, status)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "分页查询菜单失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询菜单列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getAllMenus(Long tenantId) {
        Log.infof("查询所有菜单: tenantId=%d", tenantId);

        return menuRepository.findByTenantId(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询所有菜单失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询菜单列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getMenuTree(Long tenantId) {
        Log.infof("查询菜单树: tenantId=%d", tenantId);

        return menuRepository.findMenuTree(tenantId)
                .map(menus -> {
                    // 构建菜单树结构
                    List<Menu> tree = buildMenuTree(menus);
                    return ApiResponse.success(tree);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询菜单树失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询菜单树失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getVisibleMenuTree(Long tenantId) {
        Log.infof("查询可见菜单树: tenantId=%d", tenantId);

        return menuRepository.findVisibleMenus(tenantId)
                .map(menus -> {
                    // 构建可见菜单树结构
                    List<Menu> tree = buildMenuTree(menus);
                    return ApiResponse.success(tree);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询可见菜单树失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询可见菜单树失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getRootMenus(Long tenantId) {
        Log.infof("查询根菜单: tenantId=%d", tenantId);

        return menuRepository.findRootMenus(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询根菜单失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询根菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getChildMenus(Long tenantId, Long parentId) {
        Log.infof("查询子菜单: tenantId=%d, parentId=%d", tenantId, parentId);

        return menuRepository.findByParentId(parentId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询子菜单失败: tenantId=%d, parentId=%d", tenantId, parentId);
                    return ApiResponse.error("查询子菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getMenusByType(Long tenantId, Integer menuType) {
        Log.infof("根据类型查询菜单: tenantId=%d, menuType=%d", tenantId, menuType);

        return menuRepository.findByMenuTypeAndTenantId(menuType, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "根据类型查询菜单失败: tenantId=%d, menuType=%d", tenantId, menuType);
                    return ApiResponse.error("查询菜单失败: " + throwable.getMessage());
                });
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建菜单树结构
     */
    private List<Menu> buildMenuTree(List<Menu> menus) {
        if (menus == null || menus.isEmpty()) {
            return new ArrayList<>();
        }

        // 按父ID分组
        Map<Long, List<Menu>> parentMap = menus.stream()
                .filter(m -> m.getParentId() != null)
                .collect(Collectors.groupingBy(Menu::getParentId));

        // 设置子菜单
        menus.forEach(menu -> {
            List<Menu> children = parentMap.get(menu.id);
            if (children != null) {
                // 按排序号排序
                children.sort((m1, m2) -> {
                    int sort1 = m1.getSortOrder() != null ? m1.getSortOrder() : 0;
                    int sort2 = m2.getSortOrder() != null ? m2.getSortOrder() : 0;
                    return Integer.compare(sort1, sort2);
                });
                menu.setChildren(children);
            }
        });

        // 返回根菜单，按排序号排序
        return menus.stream()
                .filter(m -> m.getParentId() == null)
                .sorted((m1, m2) -> {
                    int sort1 = m1.getSortOrder() != null ? m1.getSortOrder() : 0;
                    int sort2 = m2.getSortOrder() != null ? m2.getSortOrder() : 0;
                    return Integer.compare(sort1, sort2);
                })
                .collect(Collectors.toList());
    }

    /**
     * 验证父菜单是否有效（避免循环引用）
     */
    private Uni<Boolean> validateParentMenu(Long tenantId, Long parentId, Long currentMenuId) {
        if (parentId == null) {
            return Uni.createFrom().item(true);
        }

        if (parentId.equals(currentMenuId)) {
            return Uni.createFrom().item(false); // 不能设置自己为父菜单
        }

        // 检查是否会形成循环引用
        return checkCircularReference(tenantId, parentId, currentMenuId);
    }

    /**
     * 检查循环引用
     */
    private Uni<Boolean> checkCircularReference(Long tenantId, Long parentId, Long currentMenuId) {
        return menuRepository.findByIdAndTenantId(parentId, tenantId)
                .flatMap(parentMenu -> {
                    if (parentMenu == null) {
                        return Uni.createFrom().item(false); // 父菜单不存在
                    }

                    if (parentMenu.getParentId() == null) {
                        return Uni.createFrom().item(true); // 到达根菜单，无循环
                    }

                    if (parentMenu.getParentId().equals(currentMenuId)) {
                        return Uni.createFrom().item(false); // 发现循环引用
                    }

                    // 递归检查上级菜单
                    return checkCircularReference(tenantId, parentMenu.getParentId(), currentMenuId);
                });
    }

    // ==================== 用户菜单查询 ====================

    @Override
    public Uni<ApiResponse<List<Menu>>> getUserMenus(Long tenantId, Long userId) {
        Log.infof("查询用户菜单: tenantId=%d, userId=%d", tenantId, userId);

        return menuRepository.findUserMenus(userId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户菜单失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("查询用户菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getUserMenuTree(Long tenantId, Long userId) {
        Log.infof("查询用户菜单树: tenantId=%d, userId=%d", tenantId, userId);

        return menuRepository.findUserMenus(userId, tenantId)
                .map(menus -> {
                    // 构建用户菜单树结构
                    List<Menu> tree = buildMenuTree(menus);
                    return ApiResponse.success(tree);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户菜单树失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("查询用户菜单树失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<String>>> getUserMenuCodes(Long tenantId, Long userId) {
        Log.infof("查询用户菜单编码: tenantId=%d, userId=%d", tenantId, userId);

        return menuRepository.findUserMenus(userId, tenantId)
                .map(menus -> {
                    List<String> menuCodes = menus.stream()
                            .map(Menu::getMenuCode)
                            .collect(Collectors.toList());
                    return ApiResponse.success(menuCodes);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户菜单编码失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("查询用户菜单编码失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> hasUserMenuAccess(Long tenantId, Long userId, String menuCode) {
        Log.infof("检查用户菜单访问权限: tenantId=%d, userId=%d, menuCode=%s", tenantId, userId, menuCode);

        return getUserMenuCodes(tenantId, userId)
                .map(response -> {
                    if (response.isSuccess()) {
                        boolean hasAccess = response.getData().contains(menuCode);
                        return ApiResponse.success(hasAccess);
                    } else {
                        return ApiResponse.success(false);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查用户菜单访问权限失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("检查菜单访问权限失败: " + throwable.getMessage());
                });
    }

    // ==================== 菜单权限管理 ====================

    @Override
    public Uni<ApiResponse<List<Menu>>> getMenusByPermission(Long tenantId, String permissionCode) {
        Log.infof("根据权限查询菜单: tenantId=%d, permissionCode=%s", tenantId, permissionCode);

        return menuRepository.findByPermissionCode(permissionCode, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "根据权限查询菜单失败: tenantId=%d, permissionCode=%s", tenantId, permissionCode);
                    return ApiResponse.error("查询菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getMenuButtons(Long tenantId, Long menuId) {
        Log.infof("查询菜单按钮: tenantId=%d, menuId=%d", tenantId, menuId);

        return menuRepository.find("parentId = ?1 and menuType = 3 and tenantId = ?2 and deleted = 0", menuId, tenantId)
                .list()
                .map(buttons -> {
                    List<Menu> menuButtons = buttons.stream()
                            .map(button -> (Menu) button)
                            .collect(Collectors.toList());
                    return ApiResponse.success(menuButtons);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询菜单按钮失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("查询菜单按钮失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Menu>>> getUserMenuButtons(Long tenantId, Long userId, Long menuId) {
        Log.infof("查询用户菜单按钮: tenantId=%d, userId=%d, menuId=%d", tenantId, userId, menuId);

        // 查询用户在指定菜单下的按钮权限
        String query = """
            SELECT DISTINCT m FROM Menu m
            JOIN Permission p ON m.permissionCode = p.permissionCode
            JOIN RolePermission rp ON p.id = rp.permissionId
            JOIN UserRole ur ON rp.roleId = ur.roleId
            WHERE ur.userId = ?1 and m.parentId = ?2 and m.menuType = 3 and m.tenantId = ?3
            and m.isVisible = true and m.status = 1 and m.deleted = 0
            and p.deleted = 0 and p.status = 1
            and rp.deleted = 0 and rp.status = 1
            and ur.deleted = 0 and ur.status = 1
            ORDER BY m.sortOrder, m.createTime
            """;

        return Menu.find(query, userId, menuId, tenantId).list()
                .map(buttons -> {
                    List<Menu> menuButtons = buttons.stream()
                            .map(button -> (Menu) button)
                            .collect(Collectors.toList());
                    return ApiResponse.success(menuButtons);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户菜单按钮失败: tenantId=%d, userId=%d, menuId=%d", tenantId, userId, menuId);
                    return ApiResponse.error("查询用户菜单按钮失败: " + throwable.getMessage());
                });
    }

    // ==================== 菜单验证 ====================

    @Override
    public Uni<ApiResponse<Boolean>> checkMenuCodeExists(Long tenantId, String menuCode, Long excludeId) {
        Log.infof("检查菜单编码是否存在: tenantId=%d, menuCode=%s", tenantId, menuCode);

        return menuRepository.existsByMenuCode(menuCode, tenantId, excludeId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查菜单编码失败: tenantId=%d, menuCode=%s", tenantId, menuCode);
                    return ApiResponse.error("检查菜单编码失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> canDeleteMenu(Long tenantId, Long menuId) {
        Log.infof("验证菜单是否可删除: tenantId=%d, menuId=%d", tenantId, menuId);

        // 检查是否有子菜单
        return menuRepository.hasChildren(menuId, tenantId)
                .map(hasChildren -> ApiResponse.success(!hasChildren))
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "验证菜单删除失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("验证失败: " + throwable.getMessage());
                });
    }

    @Override
    public ApiResponse<Boolean> validateMenuPath(String menuPath) {
        Log.infof("验证菜单路径: menuPath=%s", menuPath);

        if (menuPath == null || menuPath.trim().isEmpty()) {
            return ApiResponse.success(true); // 空路径是有效的（目录或按钮）
        }

        String path = menuPath.trim();

        // 外链菜单验证
        if (path.startsWith("http://") || path.startsWith("https://")) {
            return ApiResponse.success(true);
        }

        // 内部菜单路径验证
        if (path.startsWith("/")) {
            // 检查路径格式
            if (path.matches("^/[a-zA-Z0-9/_-]*$")) {
                return ApiResponse.success(true);
            } else {
                return ApiResponse.error("菜单路径格式错误，只能包含字母、数字、下划线、横线和斜杠");
            }
        } else {
            return ApiResponse.error("内部菜单路径必须以/开头");
        }
    }

    // ==================== 菜单状态管理 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> enableMenu(Long tenantId, Long menuId) {
        Log.infof("启用菜单: tenantId=%d, menuId=%d", tenantId, menuId);

        return menuRepository.update("status = 1, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", menuId, tenantId)
                .map(count -> count > 0 ? ApiResponse.<Void>success(null) : ApiResponse.<Void>error("启用菜单失败"))
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "启用菜单失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("启用菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> disableMenu(Long tenantId, Long menuId) {
        Log.infof("禁用菜单: tenantId=%d, menuId=%d", tenantId, menuId);

        return menuRepository.update("status = 0, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", menuId, tenantId)
                .map(count -> count > 0 ? ApiResponse.<Void>success(null) : ApiResponse.<Void>error("禁用菜单失败"))
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "禁用菜单失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("禁用菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> showMenu(Long tenantId, Long menuId) {
        Log.infof("显示菜单: tenantId=%d, menuId=%d", tenantId, menuId);

        return menuRepository.update("isVisible = true, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", menuId, tenantId)
                .map(count -> count > 0 ? ApiResponse.<Void>success(null) : ApiResponse.<Void>error("显示菜单失败"))
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "显示菜单失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("显示菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> hideMenu(Long tenantId, Long menuId) {
        Log.infof("隐藏菜单: tenantId=%d, menuId=%d", tenantId, menuId);

        return menuRepository.update("isVisible = false, updateTime = CURRENT_TIMESTAMP where id = ?1 and tenantId = ?2", menuId, tenantId)
                .map(count -> count > 0 ? ApiResponse.<Void>success(null) : ApiResponse.<Void>error("隐藏菜单失败"))
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "隐藏菜单失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("隐藏菜单失败: " + throwable.getMessage());
                });
    }

    // ==================== 菜单排序 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> updateMenuSort(Long tenantId, Long menuId, Integer sortOrder) {
        Log.infof("更新菜单排序: tenantId=%d, menuId=%d, sortOrder=%d", tenantId, menuId, sortOrder);

        return menuRepository.update("sortOrder = ?1, updateTime = CURRENT_TIMESTAMP where id = ?2 and tenantId = ?3",
                sortOrder, menuId, tenantId)
                .map(count -> count > 0 ? ApiResponse.<Void>success(null) : ApiResponse.<Void>error("更新菜单排序失败"))
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "更新菜单排序失败: tenantId=%d, menuId=%d", tenantId, menuId);
                    return ApiResponse.error("更新菜单排序失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> batchUpdateMenuSort(Long tenantId, Map<Long, Integer> menuSorts) {
        Log.infof("批量更新菜单排序: tenantId=%d, menuSorts=%s", tenantId, menuSorts);

        if (menuSorts == null || menuSorts.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.<Void>error("菜单排序数据不能为空"));
        }

        // 批量更新排序
        List<Uni<Integer>> updateUnis = menuSorts.entrySet().stream()
                .map(entry -> menuRepository.update(
                        "sortOrder = ?1, updateTime = CURRENT_TIMESTAMP where id = ?2 and tenantId = ?3",
                        entry.getValue(), entry.getKey(), tenantId))
                .collect(Collectors.toList());

        return Uni.combine().all().unis(updateUnis).combinedWith(results -> {
            int totalUpdated = results.stream().mapToInt(result -> (Integer) result).sum();
            return totalUpdated > 0 ? ApiResponse.<Void>success(null) : ApiResponse.<Void>error("批量更新菜单排序失败");
        }).onFailure().recoverWithItem(throwable -> {
            Log.errorf(throwable, "批量更新菜单排序失败: tenantId=%d", tenantId);
            return ApiResponse.error("批量更新菜单排序失败: " + throwable.getMessage());
        });
    }

    // ==================== 菜单初始化 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> initTenantMenus(Long tenantId) {
        Log.infof("初始化租户菜单: tenantId=%d", tenantId);

        // 从系统菜单复制到租户菜单
        return menuRepository.find("tenantId = 0 and deleted = 0").list()
                .flatMap(systemMenus -> {
                    List<Menu> tenantMenus = systemMenus.stream()
                            .map(menu -> copyMenuForTenant((Menu) menu))
                            .peek(m -> m.setTenantId(tenantId))
                            .collect(Collectors.toList());

                    return Menu.persist(tenantMenus)
                            .map(success -> ApiResponse.<Void>success(null));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "初始化租户菜单失败: tenantId=%d", tenantId);
                    return ApiResponse.error("初始化菜单失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> syncSystemMenus(Long tenantId) {
        Log.infof("同步系统菜单: tenantId=%d", tenantId);

        // 实现系统菜单同步逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 统计方法 ====================

    @Override
    public Uni<ApiResponse<Long>> countMenus(Long tenantId) {
        Log.infof("统计菜单数量: tenantId=%d", tenantId);

        return menuRepository.countByTenantId(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "统计菜单数量失败: tenantId=%d", tenantId);
                    return ApiResponse.error("统计失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Long>> countChildMenus(Long tenantId, Long parentId) {
        Log.infof("统计子菜单数量: tenantId=%d, parentId=%d", tenantId, parentId);

        return menuRepository.count("parentId = ?1 and tenantId = ?2 and deleted = 0", parentId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "统计子菜单数量失败: tenantId=%d, parentId=%d", tenantId, parentId);
                    return ApiResponse.error("统计失败: " + throwable.getMessage());
                });
    }

    // ==================== 菜单导入导出 ====================

    @Override
    public Uni<ApiResponse<String>> exportMenus(Long tenantId) {
        Log.infof("导出菜单数据: tenantId=%d", tenantId);

        return menuRepository.findByTenantId(tenantId)
                .map(menus -> {
                    // 简化实现，实际应该转换为JSON或其他格式
                    String data = menus.stream()
                            .map(m -> m.getMenuCode() + ":" + m.getMenuName())
                            .collect(Collectors.joining("\n"));
                    return ApiResponse.<String>success(data);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "导出菜单数据失败: tenantId=%d", tenantId);
                    return ApiResponse.<String>error("导出失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> importMenus(Long tenantId, String menuData) {
        Log.infof("导入菜单数据: tenantId=%d", tenantId);

        // 简化实现，实际应该解析JSON或其他格式
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 复制菜单给租户
     */
    private Menu copyMenuForTenant(Menu source) {
        Menu copy = new Menu();
        copy.setMenuCode(source.getMenuCode());
        copy.setMenuName(source.getMenuName());
        copy.setMenuType(source.getMenuType());
        copy.setParentId(source.getParentId());
        copy.setMenuPath(source.getMenuPath());
        copy.setComponentPath(source.getComponentPath());
        copy.setMenuIcon(source.getMenuIcon());
        copy.setPermissionCode(source.getPermissionCode());
        copy.setIsExternal(source.getIsExternal());
        copy.setIsCached(source.getIsCached());
        copy.setIsVisible(source.getIsVisible());
        copy.setDescription(source.getDescription());
        copy.setButtonPermissions(source.getButtonPermissions());
        copy.setMetaData(source.getMetaData());
        copy.setSortOrder(source.getSortOrder());
        copy.setCreateTime(LocalDateTime.now());
        copy.setUpdateTime(LocalDateTime.now());
        return copy;
    }
}
