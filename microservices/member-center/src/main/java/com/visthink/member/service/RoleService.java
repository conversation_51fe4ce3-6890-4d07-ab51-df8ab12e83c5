package com.visthink.member.service;

import com.visthink.member.entity.Role;
import com.visthink.member.dto.RoleCreateRequest;
import com.visthink.member.dto.RoleUpdateRequest;
import com.visthink.member.dto.RolePermissionRequest;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;

import java.util.List;

/**
 * 角色服务接口
 * 
 * 提供角色管理的业务逻辑接口
 * 包含角色CRUD、权限分配、用户角色管理等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RoleService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 创建角色
     * 
     * @param tenantId 租户ID
     * @param request 创建请求
     * @return 创建结果
     */
    Uni<ApiResponse<Role>> createRole(Long tenantId, RoleCreateRequest request);

    /**
     * 更新角色
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @param request 更新请求
     * @return 更新结果
     */
    Uni<ApiResponse<Role>> updateRole(Long tenantId, Long roleId, RoleUpdateRequest request);

    /**
     * 删除角色
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteRole(Long tenantId, Long roleId);

    /**
     * 批量删除角色
     * 
     * @param tenantId 租户ID
     * @param roleIds 角色ID列表
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteRoles(Long tenantId, List<Long> roleIds);

    /**
     * 根据ID查询角色
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 角色信息
     */
    Uni<ApiResponse<Role>> getRoleById(Long tenantId, Long roleId);

    /**
     * 根据角色编码查询角色
     * 
     * @param tenantId 租户ID
     * @param roleCode 角色编码
     * @return 角色信息
     */
    Uni<ApiResponse<Role>> getRoleByCode(Long tenantId, String roleCode);

    // ==================== 查询操作 ====================

    /**
     * 分页查询角色
     * 
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param roleType 角色类型
     * @param status 状态
     * @return 分页结果
     */
    Uni<ApiResponse<PageResult<Role>>> getRoleList(Long tenantId, PageRequest pageRequest, 
                                                  String keyword, Integer roleType, Integer status);

    /**
     * 查询所有角色
     * 
     * @param tenantId 租户ID
     * @return 角色列表
     */
    Uni<ApiResponse<List<Role>>> getAllRoles(Long tenantId);

    /**
     * 查询启用的角色
     * 
     * @param tenantId 租户ID
     * @return 启用角色列表
     */
    Uni<ApiResponse<List<Role>>> getEnabledRoles(Long tenantId);

    /**
     * 查询默认角色
     * 
     * @param tenantId 租户ID
     * @return 默认角色列表
     */
    Uni<ApiResponse<List<Role>>> getDefaultRoles(Long tenantId);

    /**
     * 根据角色类型查询角色
     * 
     * @param tenantId 租户ID
     * @param roleType 角色类型
     * @return 角色列表
     */
    Uni<ApiResponse<List<Role>>> getRolesByType(Long tenantId, Integer roleType);

    // ==================== 权限管理 ====================

    /**
     * 分配权限给角色
     * 
     * @param tenantId 租户ID
     * @param request 权限分配请求
     * @return 分配结果
     */
    Uni<ApiResponse<Void>> assignPermissions(Long tenantId, RolePermissionRequest request);

    /**
     * 撤销角色权限
     * 
     * @param tenantId 租户ID
     * @param request 权限撤销请求
     * @return 撤销结果
     */
    Uni<ApiResponse<Void>> revokePermissions(Long tenantId, RolePermissionRequest request);

    /**
     * 查询角色的权限列表
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 权限列表
     */
    Uni<ApiResponse<List<String>>> getRolePermissions(Long tenantId, Long roleId);

    /**
     * 检查角色是否拥有指定权限
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @param permissionCode 权限编码
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> hasPermission(Long tenantId, Long roleId, String permissionCode);

    // ==================== 用户角色管理 ====================

    /**
     * 查询用户的角色列表
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 角色列表
     */
    Uni<ApiResponse<List<Role>>> getUserRoles(Long tenantId, Long userId);

    /**
     * 分配角色给用户
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 分配结果
     */
    Uni<ApiResponse<Void>> assignRolesToUser(Long tenantId, Long userId, List<Long> roleIds);

    /**
     * 移除用户角色
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 移除结果
     */
    Uni<ApiResponse<Void>> removeUserRoles(Long tenantId, Long userId, List<Long> roleIds);

    /**
     * 查询角色下的用户列表
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    Uni<ApiResponse<List<Long>>> getRoleUsers(Long tenantId, Long roleId);

    // ==================== 状态管理 ====================

    /**
     * 启用角色
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> enableRole(Long tenantId, Long roleId);

    /**
     * 禁用角色
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> disableRole(Long tenantId, Long roleId);

    // ==================== 验证方法 ====================

    /**
     * 检查角色编码是否存在
     * 
     * @param tenantId 租户ID
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 检查结果
     */
    Uni<ApiResponse<Boolean>> checkRoleCodeExists(Long tenantId, String roleCode, Long excludeId);

    /**
     * 验证角色是否可以删除
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> canDeleteRole(Long tenantId, Long roleId);

    // ==================== 统计方法 ====================

    /**
     * 统计租户下的角色数量
     * 
     * @param tenantId 租户ID
     * @return 角色数量
     */
    Uni<ApiResponse<Long>> countRoles(Long tenantId);

    /**
     * 统计角色下的用户数量
     * 
     * @param tenantId 租户ID
     * @param roleId 角色ID
     * @return 用户数量
     */
    Uni<ApiResponse<Long>> countRoleUsers(Long tenantId, Long roleId);
}
