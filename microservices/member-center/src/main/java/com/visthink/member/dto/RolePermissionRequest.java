package com.visthink.member.dto;

import lombok.Data;
import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 角色权限分配请求DTO
 * 
 * 用于接收角色权限分配的请求参数
 * 支持批量权限分配和撤销
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class RolePermissionRequest {

    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    /**
     * 权限ID列表
     */
    @NotEmpty(message = "权限ID列表不能为空")
    private List<Long> permissionIds;

    /**
     * 权限编码列表（可选，与权限ID列表二选一）
     */
    private List<String> permissionCodes;

    /**
     * 操作类型：grant-分配权限，revoke-撤销权限
     */
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "^(grant|revoke)$", message = "操作类型只能是grant或revoke")
    private String operation;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    // ==================== 业务方法 ====================

    /**
     * 判断是否为分配权限操作
     */
    public boolean isGrantOperation() {
        return "grant".equals(operation);
    }

    /**
     * 判断是否为撤销权限操作
     */
    public boolean isRevokeOperation() {
        return "revoke".equals(operation);
    }

    /**
     * 验证权限参数
     */
    public boolean hasValidPermissions() {
        return (permissionIds != null && !permissionIds.isEmpty()) ||
               (permissionCodes != null && !permissionCodes.isEmpty());
    }

    /**
     * 获取操作描述
     */
    public String getOperationDesc() {
        if (isGrantOperation()) {
            return "分配权限";
        } else if (isRevokeOperation()) {
            return "撤销权限";
        } else {
            return "未知操作";
        }
    }
}

/**
 * 用户角色分配请求DTO
 * 
 * 用于接收用户角色分配的请求参数
 * 支持批量角色分配和撤销
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
class UserRoleRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 角色ID列表
     */
    @NotEmpty(message = "角色ID列表不能为空")
    private List<Long> roleIds;

    /**
     * 角色编码列表（可选，与角色ID列表二选一）
     */
    private List<String> roleCodes;

    /**
     * 操作类型：assign-分配角色，remove-移除角色
     */
    @NotBlank(message = "操作类型不能为空")
    @Pattern(regexp = "^(assign|remove)$", message = "操作类型只能是assign或remove")
    private String operation;

    /**
     * 过期时间（可选，格式：yyyy-MM-dd HH:mm:ss）
     */
    private String expireTime;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    // ==================== 业务方法 ====================

    /**
     * 判断是否为分配角色操作
     */
    public boolean isAssignOperation() {
        return "assign".equals(operation);
    }

    /**
     * 判断是否为移除角色操作
     */
    public boolean isRemoveOperation() {
        return "remove".equals(operation);
    }

    /**
     * 验证角色参数
     */
    public boolean hasValidRoles() {
        return (roleIds != null && !roleIds.isEmpty()) ||
               (roleCodes != null && !roleCodes.isEmpty());
    }

    /**
     * 获取操作描述
     */
    public String getOperationDesc() {
        if (isAssignOperation()) {
            return "分配角色";
        } else if (isRemoveOperation()) {
            return "移除角色";
        } else {
            return "未知操作";
        }
    }

    /**
     * 判断是否有过期时间
     */
    public boolean hasExpireTime() {
        return expireTime != null && !expireTime.trim().isEmpty();
    }
}

/**
 * 角色查询请求DTO
 * 
 * 用于接收角色查询的请求参数
 * 支持多条件组合查询
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
class RoleQueryRequest {

    /**
     * 搜索关键词（角色名称、角色编码、描述）
     */
    private String keyword;

    /**
     * 角色类型：1-普通角色，2-租户管理员，3-系统管理员
     */
    private Integer roleType;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 是否默认角色
     */
    private Boolean isDefault;

    /**
     * 是否系统角色
     */
    private Boolean isSystem;

    /**
     * 数据权限范围
     */
    private Integer dataScope;

    /**
     * 创建时间开始（格式：yyyy-MM-dd）
     */
    private String createTimeStart;

    /**
     * 创建时间结束（格式：yyyy-MM-dd）
     */
    private String createTimeEnd;

    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向：asc-升序，desc-降序
     */
    @Pattern(regexp = "^(asc|desc)$", message = "排序方向只能是asc或desc")
    private String sortDirection = "desc";

    // ==================== 业务方法 ====================

    /**
     * 判断是否有搜索关键词
     */
    public boolean hasKeyword() {
        return keyword != null && !keyword.trim().isEmpty();
    }

    /**
     * 判断是否有时间范围查询
     */
    public boolean hasTimeRange() {
        return (createTimeStart != null && !createTimeStart.trim().isEmpty()) ||
               (createTimeEnd != null && !createTimeEnd.trim().isEmpty());
    }

    /**
     * 判断是否为升序排序
     */
    public boolean isAscending() {
        return "asc".equals(sortDirection);
    }

    /**
     * 判断是否为降序排序
     */
    public boolean isDescending() {
        return "desc".equals(sortDirection);
    }
}
