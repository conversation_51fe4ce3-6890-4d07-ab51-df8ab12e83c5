package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.util.List;

/**
 * 菜单实体类
 * 
 * 管理系统菜单信息，支持树形菜单结构
 * 包含菜单基本信息、权限关联、按钮权限等功能
 * 支持多租户菜单配置和动态菜单生成
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "system_menus",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"menu_code", "tenant_id"})
       },
       indexes = {
           @Index(name = "idx_menu_code", columnList = "menu_code"),
           @Index(name = "idx_menu_parent", columnList = "parent_id"),
           @Index(name = "idx_menu_type", columnList = "menu_type"),
           @Index(name = "idx_menu_tenant", columnList = "tenant_id"),
           @Index(name = "idx_menu_sort", columnList = "sort_order")
       })
@EqualsAndHashCode(callSuper = true)
public class Menu extends BaseEntity {

    /**
     * 菜单编码（租户内唯一）
     */
    @Column(name = "menu_code", nullable = false, length = 50)
    private String menuCode;

    /**
     * 菜单名称
     */
    @Column(name = "menu_name", nullable = false, length = 100)
    private String menuName;

    /**
     * 菜单类型：1-目录，2-菜单，3-按钮
     */
    @Column(name = "menu_type", nullable = false)
    private Integer menuType = 2;

    /**
     * 父菜单ID（构建菜单树）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 菜单路径（前端路由路径）
     */
    @Column(name = "menu_path", length = 200)
    private String menuPath;

    /**
     * 组件路径（前端组件路径）
     */
    @Column(name = "component_path", length = 200)
    private String componentPath;

    /**
     * 菜单图标
     */
    @Column(name = "menu_icon", length = 100)
    private String menuIcon;

    /**
     * 权限标识（对应权限编码）
     */
    @Column(name = "permission_code", length = 100)
    private String permissionCode;

    /**
     * 是否外链：0-否，1-是
     */
    @Column(name = "is_external", nullable = false)
    private Boolean isExternal = false;

    /**
     * 是否缓存：0-否，1-是
     */
    @Column(name = "is_cached", nullable = false)
    private Boolean isCached = false;

    /**
     * 是否显示：0-隐藏，1-显示
     */
    @Column(name = "is_visible", nullable = false)
    private Boolean isVisible = true;

    /**
     * 菜单描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 按钮权限列表（JSON格式）
     * 存储格式：[{"code":"add","name":"新增"},{"code":"edit","name":"编辑"}]
     */
    @Column(name = "button_permissions", columnDefinition = "TEXT")
    private String buttonPermissions;

    /**
     * 菜单元数据（JSON格式）
     * 存储额外的菜单配置信息
     */
    @Column(name = "meta_data", columnDefinition = "TEXT")
    private String metaData;

    // ==================== 关联关系 ====================

    /**
     * 子菜单列表（一对多关系）
     */
    @OneToMany(mappedBy = "parentId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("sortOrder ASC")
    private List<Menu> children;

    /**
     * 父菜单实体（多对一关系）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Menu parent;

    // ==================== 常量定义 ====================

    /**
     * 菜单类型常量
     */
    public static class MenuType {
        /** 目录 */
        public static final Integer DIRECTORY = 1;
        /** 菜单 */
        public static final Integer MENU = 2;
        /** 按钮 */
        public static final Integer BUTTON = 3;
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否为目录
     */
    public boolean isDirectory() {
        return MenuType.DIRECTORY.equals(this.menuType);
    }

    /**
     * 判断是否为菜单
     */
    public boolean isMenu() {
        return MenuType.MENU.equals(this.menuType);
    }

    /**
     * 判断是否为按钮
     */
    public boolean isButton() {
        return MenuType.BUTTON.equals(this.menuType);
    }

    /**
     * 判断是否为根菜单（无父菜单）
     */
    public boolean isRootMenu() {
        return this.parentId == null;
    }

    /**
     * 判断是否有子菜单
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }

    /**
     * 判断是否为外链菜单
     */
    public boolean isExternalLink() {
        return Boolean.TRUE.equals(this.isExternal);
    }

    /**
     * 判断菜单是否可见
     */
    public boolean isMenuVisible() {
        return Boolean.TRUE.equals(this.isVisible) && isEnabled();
    }

    /**
     * 获取菜单类型描述
     */
    public String getMenuTypeDesc() {
        if (this.menuType == null) {
            return "菜单";
        }
        switch (this.menuType) {
            case 1:
                return "目录";
            case 2:
                return "菜单";
            case 3:
                return "按钮";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取菜单层级（从根菜单开始计算）
     */
    public int getMenuLevel() {
        if (isRootMenu()) {
            return 1;
        }
        // 这里需要递归查询父菜单，实际实现时可能需要在服务层处理
        return 2; // 简化实现
    }

    /**
     * 获取完整菜单路径（包含父菜单路径）
     */
    public String getFullMenuPath() {
        if (isRootMenu() || this.menuPath == null) {
            return this.menuPath;
        }
        // 实际实现时需要递归构建完整路径
        return this.menuPath;
    }

    // ==================== 便捷构造方法 ====================

    /**
     * 创建目录菜单
     */
    public static Menu createDirectory(String code, String name, String icon, 
                                     Long parentId, Integer sortOrder, Long tenantId) {
        Menu menu = new Menu();
        menu.setMenuCode(code);
        menu.setMenuName(name);
        menu.setMenuType(MenuType.DIRECTORY);
        menu.setMenuIcon(icon);
        menu.setParentId(parentId);
        menu.setSortOrder(sortOrder);
        menu.setTenantId(tenantId);
        menu.setIsVisible(true);
        menu.setIsExternal(false);
        menu.setIsCached(false);
        return menu;
    }

    /**
     * 创建菜单
     */
    public static Menu createMenu(String code, String name, String path, String component,
                                String icon, String permissionCode, Long parentId, 
                                Integer sortOrder, Long tenantId) {
        Menu menu = new Menu();
        menu.setMenuCode(code);
        menu.setMenuName(name);
        menu.setMenuType(MenuType.MENU);
        menu.setMenuPath(path);
        menu.setComponentPath(component);
        menu.setMenuIcon(icon);
        menu.setPermissionCode(permissionCode);
        menu.setParentId(parentId);
        menu.setSortOrder(sortOrder);
        menu.setTenantId(tenantId);
        menu.setIsVisible(true);
        menu.setIsExternal(false);
        menu.setIsCached(false);
        return menu;
    }

    /**
     * 创建按钮权限
     */
    public static Menu createButton(String code, String name, String permissionCode,
                                  Long parentId, Integer sortOrder, Long tenantId) {
        Menu menu = new Menu();
        menu.setMenuCode(code);
        menu.setMenuName(name);
        menu.setMenuType(MenuType.BUTTON);
        menu.setPermissionCode(permissionCode);
        menu.setParentId(parentId);
        menu.setSortOrder(sortOrder);
        menu.setTenantId(tenantId);
        menu.setIsVisible(true);
        menu.setIsExternal(false);
        menu.setIsCached(false);
        return menu;
    }

    /**
     * 创建外链菜单
     */
    public static Menu createExternalMenu(String code, String name, String url, String icon,
                                        Long parentId, Integer sortOrder, Long tenantId) {
        Menu menu = new Menu();
        menu.setMenuCode(code);
        menu.setMenuName(name);
        menu.setMenuType(MenuType.MENU);
        menu.setMenuPath(url);
        menu.setMenuIcon(icon);
        menu.setParentId(parentId);
        menu.setSortOrder(sortOrder);
        menu.setTenantId(tenantId);
        menu.setIsVisible(true);
        menu.setIsExternal(true);
        menu.setIsCached(false);
        return menu;
    }
}
