package com.visthink.member.config;

import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;
import org.jboss.logging.Logger;

/**
 * Hibernate Reactive 配置类
 * 
 * 解决 JdbcValuesSourceProcessingState 错误
 * 优化会话管理和连接池配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class HibernateReactiveConfig {

    private static final Logger LOG = Logger.getLogger(HibernateReactiveConfig.class);

    @Inject
    Mutiny.SessionFactory sessionFactory;

    /**
     * 应用启动时初始化Hibernate配置
     * 
     * @param event 启动事件
     */
    void onStart(@Observes StartupEvent event) {
        LOG.info("初始化 Hibernate Reactive 配置");
        
        try {
            // 验证SessionFactory是否正常工作
            validateSessionFactory();
            
            LOG.info("Hibernate Reactive 配置初始化完成");
            
        } catch (Exception e) {
            LOG.errorf(e, "Hibernate Reactive 配置初始化失败");
            throw new RuntimeException("Hibernate Reactive 配置初始化失败", e);
        }
    }

    /**
     * 验证SessionFactory配置
     */
    private void validateSessionFactory() {
        if (sessionFactory == null) {
            throw new IllegalStateException("SessionFactory 未正确注入");
        }
        
        LOG.info("SessionFactory 验证通过");
    }

    /**
     * 获取SessionFactory实例
     * 
     * @return SessionFactory实例
     */
    public Mutiny.SessionFactory getSessionFactory() {
        return sessionFactory;
    }

    /**
     * 检查SessionFactory是否可用
     * 
     * @return 是否可用
     */
    public boolean isSessionFactoryAvailable() {
        return sessionFactory != null;
    }
}
