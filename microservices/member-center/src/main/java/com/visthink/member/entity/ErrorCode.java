package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;

/**
 * 错误码实体类
 * 
 * 管理系统错误码信息
 * 包含错误码定义、错误消息、错误级别等功能
 * 支持多语言错误消息和多租户配置
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "system_error_codes",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"error_code", "tenant_id"})
       },
       indexes = {
           @Index(name = "idx_error_code", columnList = "error_code"),
           @Index(name = "idx_error_module", columnList = "module_name"),
           @Index(name = "idx_error_level", columnList = "error_level"),
           @Index(name = "idx_error_tenant", columnList = "tenant_id")
       })
public class ErrorCode extends BaseEntity {

    /**
     * 错误码（租户内唯一）
     * 格式：模块前缀 + 错误编号，如 USER_001, ORDER_002
     */
    @Column(name = "error_code", nullable = false, length = 50)
    private String errorCode;

    /**
     * 错误名称
     */
    @Column(name = "error_name", nullable = false, length = 100)
    private String errorName;

    /**
     * 模块名称
     */
    @Column(name = "module_name", nullable = false, length = 50)
    private String moduleName;

    /**
     * 错误级别：1-信息，2-警告，3-错误，4-严重错误
     */
    @Column(name = "error_level", nullable = false)
    private Integer errorLevel = 3;

    /**
     * HTTP状态码
     */
    @Column(name = "http_status", nullable = false)
    private Integer httpStatus = 500;

    /**
     * 错误消息（中文）
     */
    @Column(name = "error_message", nullable = false, length = 500)
    private String errorMessage;

    /**
     * 错误消息（英文）
     */
    @Column(name = "error_message_en", length = 500)
    private String errorMessageEn;

    /**
     * 错误描述
     */
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 解决方案
     */
    @Column(name = "solution", length = 1000)
    private String solution;

    /**
     * 是否系统内置错误码
     */
    @Column(name = "is_system", nullable = false)
    private Boolean isSystem = false;

    /**
     * 是否可编辑
     */
    @Column(name = "is_editable", nullable = false)
    private Boolean isEditable = true;

    /**
     * 是否记录日志
     */
    @Column(name = "is_logged", nullable = false)
    private Boolean isLogged = true;

    /**
     * 是否发送通知
     */
    @Column(name = "is_notified", nullable = false)
    private Boolean isNotified = false;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_props", columnDefinition = "TEXT")
    private String extraProps;

    // ==================== 常量定义 ====================

    /**
     * 错误级别常量
     */
    public static class ErrorLevel {
        /** 信息 */
        public static final Integer INFO = 1;
        /** 警告 */
        public static final Integer WARN = 2;
        /** 错误 */
        public static final Integer ERROR = 3;
        /** 严重错误 */
        public static final Integer FATAL = 4;
    }

    /**
     * 常用HTTP状态码
     */
    public static class HttpStatus {
        /** 成功 */
        public static final Integer OK = 200;
        /** 客户端错误 */
        public static final Integer BAD_REQUEST = 400;
        /** 未授权 */
        public static final Integer UNAUTHORIZED = 401;
        /** 禁止访问 */
        public static final Integer FORBIDDEN = 403;
        /** 资源不存在 */
        public static final Integer NOT_FOUND = 404;
        /** 服务器错误 */
        public static final Integer INTERNAL_ERROR = 500;
    }

    /**
     * 模块名称常量
     */
    public static class Module {
        /** 用户模块 */
        public static final String USER = "USER";
        /** 角色模块 */
        public static final String ROLE = "ROLE";
        /** 权限模块 */
        public static final String PERMISSION = "PERMISSION";
        /** 菜单模块 */
        public static final String MENU = "MENU";
        /** 订单模块 */
        public static final String ORDER = "ORDER";
        /** 商品模块 */
        public static final String PRODUCT = "PRODUCT";
        /** 库存模块 */
        public static final String INVENTORY = "INVENTORY";
        /** 系统模块 */
        public static final String SYSTEM = "SYSTEM";
    }

    // ==================== 业务方法 ====================

    /**
     * 判断是否为信息级别
     */
    public boolean isInfoLevel() {
        return ErrorLevel.INFO.equals(this.errorLevel);
    }

    /**
     * 判断是否为警告级别
     */
    public boolean isWarnLevel() {
        return ErrorLevel.WARN.equals(this.errorLevel);
    }

    /**
     * 判断是否为错误级别
     */
    public boolean isErrorLevel() {
        return ErrorLevel.ERROR.equals(this.errorLevel);
    }

    /**
     * 判断是否为严重错误级别
     */
    public boolean isFatalLevel() {
        return ErrorLevel.FATAL.equals(this.errorLevel);
    }

    /**
     * 判断是否为系统错误码
     */
    public boolean isSystemErrorCode() {
        return Boolean.TRUE.equals(this.isSystem);
    }

    /**
     * 判断是否可编辑
     */
    public boolean isEditableErrorCode() {
        return Boolean.TRUE.equals(this.isEditable) && !isSystemErrorCode();
    }

    /**
     * 判断是否需要记录日志
     */
    public boolean shouldLog() {
        return Boolean.TRUE.equals(this.isLogged);
    }

    /**
     * 判断是否需要发送通知
     */
    public boolean shouldNotify() {
        return Boolean.TRUE.equals(this.isNotified);
    }

    /**
     * 获取错误级别描述
     */
    public String getErrorLevelDesc() {
        if (this.errorLevel == null) {
            return "错误";
        }
        switch (this.errorLevel) {
            case 1:
                return "信息";
            case 2:
                return "警告";
            case 3:
                return "错误";
            case 4:
                return "严重错误";
            default:
                return "未知级别";
        }
    }

    /**
     * 获取错误级别颜色
     */
    public String getErrorLevelColor() {
        if (this.errorLevel == null) {
            return "#f56565"; // 红色
        }
        switch (this.errorLevel) {
            case 1:
                return "#38b2ac"; // 青色
            case 2:
                return "#ed8936"; // 橙色
            case 3:
                return "#f56565"; // 红色
            case 4:
                return "#9f1239"; // 深红色
            default:
                return "#718096"; // 灰色
        }
    }

    /**
     * 根据语言获取错误消息
     */
    public String getErrorMessage(String language) {
        if ("en".equalsIgnoreCase(language) && errorMessageEn != null && !errorMessageEn.trim().isEmpty()) {
            return errorMessageEn;
        }
        return errorMessage;
    }

    /**
     * 验证错误码格式
     */
    public boolean isValidErrorCode() {
        if (errorCode == null || errorCode.trim().isEmpty()) {
            return false;
        }
        
        // 错误码格式：模块前缀_数字编号
        return errorCode.matches("^[A-Z]+_\\d{3,}$");
    }

    /**
     * 从错误码中提取模块名
     */
    public String extractModuleFromCode() {
        if (!isValidErrorCode()) {
            return null;
        }
        
        int underscoreIndex = errorCode.indexOf('_');
        if (underscoreIndex > 0) {
            return errorCode.substring(0, underscoreIndex);
        }
        return null;
    }

    /**
     * 从错误码中提取编号
     */
    public String extractNumberFromCode() {
        if (!isValidErrorCode()) {
            return null;
        }
        
        int underscoreIndex = errorCode.indexOf('_');
        if (underscoreIndex > 0 && underscoreIndex < errorCode.length() - 1) {
            return errorCode.substring(underscoreIndex + 1);
        }
        return null;
    }

    // ==================== 便捷构造方法 ====================

    /**
     * 创建错误码
     */
    public static ErrorCode create(String errorCode, String errorName, String moduleName,
                                 Integer errorLevel, Integer httpStatus, String errorMessage, Long tenantId) {
        ErrorCode error = new ErrorCode();
        error.setErrorCode(errorCode);
        error.setErrorName(errorName);
        error.setModuleName(moduleName);
        error.setErrorLevel(errorLevel);
        error.setHttpStatus(httpStatus);
        error.setErrorMessage(errorMessage);
        error.setTenantId(tenantId);
        error.setIsSystem(false);
        error.setIsEditable(true);
        error.setIsLogged(true);
        error.setIsNotified(false);
        return error;
    }

    /**
     * 创建系统错误码
     */
    public static ErrorCode createSystemError(String errorCode, String errorName, String moduleName,
                                            Integer errorLevel, Integer httpStatus, String errorMessage, Long tenantId) {
        ErrorCode error = create(errorCode, errorName, moduleName, errorLevel, httpStatus, errorMessage, tenantId);
        error.setIsSystem(true);
        error.setIsEditable(false);
        return error;
    }

    /**
     * 创建用户模块错误码
     */
    public static ErrorCode createUserError(String errorCode, String errorName, Integer httpStatus, 
                                          String errorMessage, Long tenantId) {
        return create(errorCode, errorName, Module.USER, ErrorLevel.ERROR, httpStatus, errorMessage, tenantId);
    }

    /**
     * 创建业务错误码
     */
    public static ErrorCode createBusinessError(String errorCode, String errorName, String moduleName,
                                              String errorMessage, Long tenantId) {
        return create(errorCode, errorName, moduleName, ErrorLevel.ERROR, HttpStatus.BAD_REQUEST, errorMessage, tenantId);
    }

    /**
     * 创建系统内部错误码
     */
    public static ErrorCode createInternalError(String errorCode, String errorName, String moduleName,
                                              String errorMessage, Long tenantId) {
        return create(errorCode, errorName, moduleName, ErrorLevel.FATAL, HttpStatus.INTERNAL_ERROR, errorMessage, tenantId);
    }
}
