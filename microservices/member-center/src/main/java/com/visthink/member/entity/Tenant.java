package com.visthink.member.entity;

import com.visthink.common.entity.BaseEntity;
import io.smallrye.mutiny.Uni;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 租户实体
 *
 * 管理多租户系统中的租户信息，包括租户基本信息、配置和状态
 * 支持租户的生命周期管理和配置管理
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "tenants",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"tenant_code"}),
           @UniqueConstraint(columnNames = {"domain"})
       },
       indexes = {
           @Index(name = "idx_tenant_code", columnList = "tenant_code"),
           @Index(name = "idx_tenant_domain", columnList = "domain"),
           @Index(name = "idx_tenant_status", columnList = "tenant_status"),
           @Index(name = "idx_tenant_type", columnList = "tenant_type")
       })
@Data
@EqualsAndHashCode(callSuper = true)
public class Tenant extends BaseEntity {

    /**
     * 租户编码（全局唯一）
     */
    @Column(name = "tenant_code", nullable = false, length = 50)
    private String tenantCode;

    /**
     * 租户名称
     */
    @Column(name = "tenant_name", nullable = false, length = 100)
    private String tenantName;

    /**
     * 租户简称
     */
    @Column(name = "short_name", length = 50)
    private String shortName;

    /**
     * 租户类型：1-试用版，2-标准版，3-专业版，4-企业版
     */
    @Column(name = "tenant_type", nullable = false)
    private Integer tenantType = 1;

    /**
     * 租户状态：1-正常，2-暂停，3-禁用，4-待激活，5-已过期
     */
    @Column(name = "tenant_status", nullable = false)
    private Integer tenantStatus = 4;

    /**
     * 租户域名
     */
    @Column(name = "domain", length = 100)
    private String domain;

    /**
     * 租户Logo URL
     */
    @Column(name = "logo_url", length = 500)
    private String logoUrl;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name", length = 50)
    private String contactName;

    /**
     * 联系人邮箱
     */
    @Column(name = "contact_email", length = 100)
    private String contactEmail;

    /**
     * 联系人电话
     */
    @Column(name = "contact_phone", length = 20)
    private String contactPhone;

    /**
     * 公司名称
     */
    @Column(name = "company_name", length = 200)
    private String companyName;

    /**
     * 统一社会信用代码
     */
    @Column(name = "credit_code", length = 50)
    private String creditCode;

    /**
     * 公司地址
     */
    @Column(name = "company_address", length = 500)
    private String companyAddress;

    /**
     * 行业类型
     */
    @Column(name = "industry_type", length = 50)
    private String industryType;

    /**
     * 公司规模：1-10人以下，2-10-50人，3-50-200人，4-200-1000人，5-1000人以上
     */
    @Column(name = "company_scale")
    private Integer companyScale;

    /**
     * 最大用户数限制
     */
    @Column(name = "max_users")
    private Integer maxUsers = 10;

    /**
     * 当前用户数
     */
    @Column(name = "current_users")
    private Integer currentUsers = 0;

    /**
     * 存储空间限制（MB）
     */
    @Column(name = "storage_limit")
    private Long storageLimit = 1024L;

    /**
     * 已使用存储空间（MB）
     */
    @Column(name = "storage_used")
    private Long storageUsed = 0L;

    /**
     * 激活时间
     */
    @Column(name = "activated_time")
    private LocalDateTime activatedTime;

    /**
     * 试用开始时间
     */
    @Column(name = "trial_start_time")
    private LocalDateTime trialStartTime;

    /**
     * 试用结束时间
     */
    @Column(name = "trial_end_time")
    private LocalDateTime trialEndTime;

    /**
     * 订阅开始时间
     */
    @Column(name = "subscription_start_time")
    private LocalDateTime subscriptionStartTime;

    /**
     * 订阅结束时间
     */
    @Column(name = "subscription_end_time")
    private LocalDateTime subscriptionEndTime;

    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 数据库配置（JSON格式）
     */
    @Column(name = "database_config", columnDefinition = "TEXT")
    private String databaseConfig;

    /**
     * 功能配置（JSON格式）
     */
    @Column(name = "feature_config", columnDefinition = "TEXT")
    private String featureConfig;

    /**
     * 主题配置（JSON格式）
     */
    @Column(name = "theme_config", columnDefinition = "TEXT")
    private String themeConfig;

    /**
     * 扩展配置（JSON格式）
     */
    @Column(name = "extra_config", columnDefinition = "TEXT")
    private String extraConfig;

    // 查询方法

    /**
     * 根据租户编码查询租户
     */
    public static Uni<Tenant> findByTenantCode(String tenantCode) {
        return find("tenantCode = ?1 and deleted = 0", tenantCode).firstResult();
    }

    /**
     * 根据域名查询租户
     */
    public static Uni<Tenant> findByDomain(String domain) {
        return find("domain = ?1 and deleted = 0", domain).firstResult();
    }

    /**
     * 检查租户编码是否存在
     */
    public static Uni<Boolean> existsByTenantCode(String tenantCode, Long excludeId) {
        String query = excludeId != null
            ? "tenantCode = ?1 and id != ?2 and deleted = 0"
            : "tenantCode = ?1 and deleted = 0";
        Object[] params = excludeId != null
            ? new Object[]{tenantCode, excludeId}
            : new Object[]{tenantCode};
        return count(query, params).map(count -> count > 0);
    }

    /**
     * 检查域名是否存在
     */
    public static Uni<Boolean> existsByDomain(String domain, Long excludeId) {
        String query = excludeId != null
            ? "domain = ?1 and id != ?2 and deleted = 0"
            : "domain = ?1 and deleted = 0";
        Object[] params = excludeId != null
            ? new Object[]{domain, excludeId}
            : new Object[]{domain};
        return count(query, params).map(count -> count > 0);
    }

    // 业务方法

    /**
     * 激活租户
     */
    public void activate() {
        this.tenantStatus = 1; // 正常状态
        this.activatedTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 暂停租户
     */
    public void suspend() {
        this.tenantStatus = 2; // 暂停状态
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 禁用租户
     */
    public void ban() {
        this.tenantStatus = 3; // 禁用状态
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置为过期状态
     */
    public void expire() {
        this.tenantStatus = 5; // 已过期状态
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 开始试用
     */
    public void startTrial(int trialDays) {
        this.tenantStatus = 1; // 正常状态
        this.trialStartTime = LocalDateTime.now();
        this.trialEndTime = this.trialStartTime.plusDays(trialDays);
        this.activatedTime = this.trialStartTime;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 订阅服务
     */
    public void subscribe(LocalDateTime startTime, LocalDateTime endTime, Integer tenantType) {
        this.tenantStatus = 1; // 正常状态
        this.subscriptionStartTime = startTime;
        this.subscriptionEndTime = endTime;
        this.tenantType = tenantType;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 增加用户数
     */
    public boolean addUser() {
        if (this.currentUsers < this.maxUsers) {
            this.currentUsers = this.currentUsers + 1;
            this.updateTime = LocalDateTime.now();
            return true;
        }
        return false;
    }

    /**
     * 减少用户数
     */
    public void removeUser() {
        if (this.currentUsers > 0) {
            this.currentUsers = this.currentUsers - 1;
            this.updateTime = LocalDateTime.now();
        }
    }

    /**
     * 增加存储使用量
     */
    public boolean addStorageUsed(long size) {
        if (this.storageUsed + size <= this.storageLimit) {
            this.storageUsed = this.storageUsed + size;
            this.updateTime = LocalDateTime.now();
            return true;
        }
        return false;
    }

    /**
     * 减少存储使用量
     */
    public void removeStorageUsed(long size) {
        this.storageUsed = Math.max(0, this.storageUsed - size);
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 记录最后登录时间
     */
    public void recordLastLogin() {
        this.lastLoginTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    // 状态判断方法

    /**
     * 判断租户是否正常
     */
    public boolean isNormal() {
        return this.tenantStatus != null && this.tenantStatus == 1;
    }

    /**
     * 判断租户是否暂停
     */
    public boolean isSuspended() {
        return this.tenantStatus != null && this.tenantStatus == 2;
    }

    /**
     * 判断租户是否禁用
     */
    public boolean isBanned() {
        return this.tenantStatus != null && this.tenantStatus == 3;
    }

    /**
     * 判断租户是否待激活
     */
    public boolean isPendingActivation() {
        return this.tenantStatus != null && this.tenantStatus == 4;
    }

    /**
     * 判断租户是否已过期
     */
    public boolean isExpired() {
        return this.tenantStatus != null && this.tenantStatus == 5;
    }

    /**
     * 判断是否在试用期
     */
    public boolean isInTrial() {
        LocalDateTime now = LocalDateTime.now();
        return this.trialStartTime != null && this.trialEndTime != null
               && now.isAfter(this.trialStartTime) && now.isBefore(this.trialEndTime);
    }

    /**
     * 判断试用是否过期
     */
    public boolean isTrialExpired() {
        LocalDateTime now = LocalDateTime.now();
        return this.trialEndTime != null && now.isAfter(this.trialEndTime);
    }

    /**
     * 判断订阅是否有效
     */
    public boolean isSubscriptionValid() {
        LocalDateTime now = LocalDateTime.now();
        return this.subscriptionStartTime != null && this.subscriptionEndTime != null
               && now.isAfter(this.subscriptionStartTime) && now.isBefore(this.subscriptionEndTime);
    }

    /**
     * 判断订阅是否过期
     */
    public boolean isSubscriptionExpired() {
        LocalDateTime now = LocalDateTime.now();
        return this.subscriptionEndTime != null && now.isAfter(this.subscriptionEndTime);
    }

    /**
     * 判断用户数是否已满
     */
    public boolean isUserLimitReached() {
        return this.currentUsers >= this.maxUsers;
    }

    /**
     * 判断存储空间是否已满
     */
    public boolean isStorageLimitReached() {
        return this.storageUsed >= this.storageLimit;
    }

    /**
     * 获取存储使用率
     */
    public double getStorageUsageRate() {
        return this.storageLimit > 0 ? (double) this.storageUsed / this.storageLimit : 0.0;
    }

    /**
     * 获取用户使用率
     */
    public double getUserUsageRate() {
        return this.maxUsers > 0 ? (double) this.currentUsers / this.maxUsers : 0.0;
    }

}
