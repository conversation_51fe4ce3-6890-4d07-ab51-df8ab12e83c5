package com.visthink.member.service.impl;

import com.visthink.member.service.PermissionService;
import com.visthink.member.entity.Permission;
import com.visthink.member.entity.RolePermission;
import com.visthink.member.repository.PermissionRepository;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.exception.BaseException;

import io.smallrye.mutiny.Uni;
import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 * 
 * 实现权限管理的业务逻辑
 * 包含权限CRUD、权限树构建、用户权限查询等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class PermissionServiceImpl implements PermissionService {

    @Inject
    PermissionRepository permissionRepository;

    // ==================== 基础CRUD操作 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Permission>> createPermission(Long tenantId, Permission permission) {
        Log.infof("创建权限: tenantId=%d, permissionCode=%s", tenantId, permission.getPermissionCode());

        // 1. 验证权限编码格式
        if (!permission.isValidPermissionCode()) {
            return Uni.createFrom().item(ApiResponse.error("权限编码格式错误，应为 resource:action"));
        }

        // 2. 验证权限编码是否已存在
        return permissionRepository.existsByPermissionCode(permission.getPermissionCode(), tenantId, null)
                .flatMap(exists -> {
                    if (exists) {
                        return Uni.createFrom().item(ApiResponse.<Permission>error("权限编码已存在"));
                    }

                    // 3. 设置权限信息
                    permission.setTenantId(tenantId);
                    permission.setCreateTime(LocalDateTime.now());
                    permission.setUpdateTime(LocalDateTime.now());

                    // 4. 自动设置资源类型和操作
                    if (permission.getResourceType() == null) {
                        permission.setResourceType(getDefaultResourceType(permission.getPermissionType()));
                    }
                    if (permission.getAction() == null) {
                        permission.setAction(permission.getActionFromCode());
                    }

                    // 5. 保存权限
                    return permissionRepository.persist(permission)
                            .map(ApiResponse::success);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "创建权限失败: tenantId=%d, permissionCode=%s", tenantId, permission.getPermissionCode());
                    return ApiResponse.error("创建权限失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Permission>> updatePermission(Long tenantId, Long permissionId, Permission permission) {
        Log.infof("更新权限: tenantId=%d, permissionId=%d", tenantId, permissionId);

        // 1. 查询权限是否存在
        return permissionRepository.findByIdAndTenantId(permissionId, tenantId)
                .flatMap(existingPermission -> {
                    if (existingPermission == null) {
                        return Uni.createFrom().item(ApiResponse.<Permission>error("权限不存在"));
                    }

                    // 2. 验证权限编码格式
                    if (!permission.isValidPermissionCode()) {
                        return Uni.createFrom().item(ApiResponse.<Permission>error("权限编码格式错误"));
                    }

                    // 3. 检查权限编码是否重复
                    return permissionRepository.existsByPermissionCode(permission.getPermissionCode(), tenantId, permissionId)
                            .flatMap(exists -> {
                                if (exists) {
                                    return Uni.createFrom().item(ApiResponse.<Permission>error("权限编码已存在"));
                                }

                                // 4. 更新权限信息
                                existingPermission.setPermissionCode(permission.getPermissionCode());
                                existingPermission.setPermissionName(permission.getPermissionName());
                                existingPermission.setPermissionType(permission.getPermissionType());
                                existingPermission.setParentId(permission.getParentId());
                                existingPermission.setResourceType(permission.getResourceType());
                                existingPermission.setResourcePath(permission.getResourcePath());
                                existingPermission.setAction(permission.getAction());
                                existingPermission.setDescription(permission.getDescription());
                                existingPermission.setSortOrder(permission.getSortOrder());
                                existingPermission.setStatus(permission.getStatus());
                                existingPermission.setRemark(permission.getRemark());
                                existingPermission.setUpdateTime(LocalDateTime.now());

                                // 5. 保存更新
                                return permissionRepository.persist(existingPermission)
                                        .map(ApiResponse::success);
                            });
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "更新权限失败: tenantId=%d, permissionId=%d", tenantId, permissionId);
                    return ApiResponse.error("更新权限失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> deletePermission(Long tenantId, Long permissionId) {
        Log.infof("删除权限: tenantId=%d, permissionId=%d", tenantId, permissionId);

        // 1. 检查权限是否可以删除
        return canDeletePermission(tenantId, permissionId)
                .flatMap(canDelete -> {
                    if (!canDelete.getData()) {
                        return Uni.createFrom().item(ApiResponse.<Void>error("权限不能删除，可能存在关联角色或子权限"));
                    }

                    // 2. 软删除权限
                    return permissionRepository.softDelete(permissionId, tenantId)
                            .map(success -> {
                                if (success) {
                                    return ApiResponse.<Void>success(null);
                                } else {
                                    return ApiResponse.<Void>error("删除权限失败");
                                }
                            });
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "删除权限失败: tenantId=%d, permissionId=%d", tenantId, permissionId);
                    return ApiResponse.error("删除权限失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> deletePermissions(Long tenantId, List<Long> permissionIds) {
        Log.infof("批量删除权限: tenantId=%d, permissionIds=%s", tenantId, permissionIds);

        if (permissionIds == null || permissionIds.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.<Void>error("权限ID列表不能为空"));
        }

        // 批量软删除权限
        return permissionRepository.softDeleteBatch(permissionIds, tenantId)
                .map(count -> {
                    if (count > 0) {
                        return ApiResponse.<Void>success(null);
                    } else {
                        return ApiResponse.<Void>error("删除权限失败");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "批量删除权限失败: tenantId=%d, permissionIds=%s", tenantId, permissionIds);
                    return ApiResponse.error("批量删除权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Permission>> getPermissionById(Long tenantId, Long permissionId) {
        Log.infof("查询权限详情: tenantId=%d, permissionId=%d", tenantId, permissionId);

        return permissionRepository.findByIdAndTenantId(permissionId, tenantId)
                .map(permission -> {
                    if (permission != null) {
                        return ApiResponse.success(permission);
                    } else {
                        return ApiResponse.<Permission>error("权限不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询权限详情失败: tenantId=%d, permissionId=%d", tenantId, permissionId);
                    return ApiResponse.error("查询权限详情失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Permission>> getPermissionByCode(Long tenantId, String permissionCode) {
        Log.infof("根据编码查询权限: tenantId=%d, permissionCode=%s", tenantId, permissionCode);

        return permissionRepository.findByPermissionCodeAndTenantId(permissionCode, tenantId)
                .map(permission -> {
                    if (permission != null) {
                        return ApiResponse.success(permission);
                    } else {
                        return ApiResponse.<Permission>error("权限不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "根据编码查询权限失败: tenantId=%d, permissionCode=%s", tenantId, permissionCode);
                    return ApiResponse.error("查询权限失败: " + throwable.getMessage());
                });
    }

    // ==================== 查询操作 ====================

    @Override
    public Uni<ApiResponse<PageResult<Permission>>> getPermissionList(Long tenantId, PageRequest pageRequest, 
                                                                    String keyword, Integer permissionType, Integer status) {
        Log.infof("分页查询权限: tenantId=%d, page=%d, size=%d", tenantId, pageRequest.getPage(), pageRequest.getSize());

        return permissionRepository.findByPage(tenantId, pageRequest, keyword, permissionType, status)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "分页查询权限失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询权限列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Permission>>> getAllPermissions(Long tenantId) {
        Log.infof("查询所有权限: tenantId=%d", tenantId);

        return permissionRepository.findByTenantId(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询所有权限失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询权限列表失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Permission>>> getPermissionTree(Long tenantId) {
        Log.infof("查询权限树: tenantId=%d", tenantId);

        return permissionRepository.findPermissionTree(tenantId)
                .map(permissions -> {
                    // 构建权限树结构
                    List<Permission> tree = buildPermissionTree(permissions);
                    return ApiResponse.success(tree);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询权限树失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询权限树失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Permission>>> getRootPermissions(Long tenantId) {
        Log.infof("查询根权限: tenantId=%d", tenantId);

        return permissionRepository.findRootPermissions(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询根权限失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询根权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Permission>>> getChildPermissions(Long tenantId, Long parentId) {
        Log.infof("查询子权限: tenantId=%d, parentId=%d", tenantId, parentId);

        return permissionRepository.findByParentId(parentId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询子权限失败: tenantId=%d, parentId=%d", tenantId, parentId);
                    return ApiResponse.error("查询子权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Permission>>> getPermissionsByType(Long tenantId, Integer permissionType) {
        Log.infof("根据类型查询权限: tenantId=%d, permissionType=%d", tenantId, permissionType);

        return permissionRepository.findByPermissionTypeAndTenantId(permissionType, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "根据类型查询权限失败: tenantId=%d, permissionType=%d", tenantId, permissionType);
                    return ApiResponse.error("查询权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<Permission>>> getSystemPermissions(Long tenantId) {
        Log.infof("查询系统权限: tenantId=%d", tenantId);

        return permissionRepository.findSystemPermissions(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询系统权限失败: tenantId=%d", tenantId);
                    return ApiResponse.error("查询系统权限失败: " + throwable.getMessage());
                });
    }

    // ==================== 用户权限查询 ====================

    @Override
    public Uni<ApiResponse<List<Permission>>> getUserPermissions(Long tenantId, Long userId) {
        Log.infof("查询用户权限: tenantId=%d, userId=%d", tenantId, userId);

        return permissionRepository.findByUserId(userId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户权限失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("查询用户权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<String>>> getUserPermissionCodes(Long tenantId, Long userId) {
        Log.infof("查询用户权限编码: tenantId=%d, userId=%d", tenantId, userId);

        return permissionRepository.findByUserId(userId, tenantId)
                .map(permissions -> {
                    List<String> permissionCodes = permissions.stream()
                            .map(Permission::getPermissionCode)
                            .collect(Collectors.toList());
                    return ApiResponse.success(permissionCodes);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询用户权限编码失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("查询用户权限编码失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> hasUserPermission(Long tenantId, Long userId, String permissionCode) {
        Log.infof("检查用户权限: tenantId=%d, userId=%d, permissionCode=%s", tenantId, userId, permissionCode);

        return getUserPermissionCodes(tenantId, userId)
                .map(response -> {
                    if (response.isSuccess()) {
                        boolean hasPermission = response.getData().contains(permissionCode);
                        return ApiResponse.success(hasPermission);
                    } else {
                        return ApiResponse.success(false);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查用户权限失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("检查用户权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> hasAnyUserPermission(Long tenantId, Long userId, List<String> permissionCodes) {
        Log.infof("检查用户任一权限: tenantId=%d, userId=%d, permissionCodes=%s", tenantId, userId, permissionCodes);

        if (permissionCodes == null || permissionCodes.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.success(false));
        }

        return getUserPermissionCodes(tenantId, userId)
                .map(response -> {
                    if (response.isSuccess()) {
                        List<String> userPermissions = response.getData();
                        boolean hasAny = permissionCodes.stream()
                                .anyMatch(userPermissions::contains);
                        return ApiResponse.success(hasAny);
                    } else {
                        return ApiResponse.success(false);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查用户任一权限失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("检查用户权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> hasAllUserPermissions(Long tenantId, Long userId, List<String> permissionCodes) {
        Log.infof("检查用户所有权限: tenantId=%d, userId=%d, permissionCodes=%s", tenantId, userId, permissionCodes);

        if (permissionCodes == null || permissionCodes.isEmpty()) {
            return Uni.createFrom().item(ApiResponse.success(true));
        }

        return getUserPermissionCodes(tenantId, userId)
                .map(response -> {
                    if (response.isSuccess()) {
                        List<String> userPermissions = response.getData();
                        boolean hasAll = userPermissions.containsAll(permissionCodes);
                        return ApiResponse.success(hasAll);
                    } else {
                        return ApiResponse.success(false);
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查用户所有权限失败: tenantId=%d, userId=%d", tenantId, userId);
                    return ApiResponse.error("检查用户权限失败: " + throwable.getMessage());
                });
    }

    // ==================== 角色权限查询 ====================

    @Override
    public Uni<ApiResponse<List<Permission>>> getRolePermissions(Long tenantId, Long roleId) {
        Log.infof("查询角色权限: tenantId=%d, roleId=%d", tenantId, roleId);

        return permissionRepository.findByRoleId(roleId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询角色权限失败: tenantId=%d, roleId=%d", tenantId, roleId);
                    return ApiResponse.error("查询角色权限失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<List<String>>> getRolePermissionCodes(Long tenantId, Long roleId) {
        Log.infof("查询角色权限编码: tenantId=%d, roleId=%d", tenantId, roleId);

        return permissionRepository.findByRoleId(roleId, tenantId)
                .map(permissions -> {
                    List<String> permissionCodes = permissions.stream()
                            .map(Permission::getPermissionCode)
                            .collect(Collectors.toList());
                    return ApiResponse.success(permissionCodes);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "查询角色权限编码失败: tenantId=%d, roleId=%d", tenantId, roleId);
                    return ApiResponse.error("查询角色权限编码失败: " + throwable.getMessage());
                });
    }

    // ==================== 权限验证 ====================

    @Override
    public ApiResponse<Boolean> validatePermissionCode(String permissionCode) {
        Log.infof("验证权限编码格式: permissionCode=%s", permissionCode);

        if (permissionCode == null || permissionCode.trim().isEmpty()) {
            return ApiResponse.error("权限编码不能为空");
        }

        String[] parts = permissionCode.split(":");
        if (parts.length != 2 || parts[0].trim().isEmpty() || parts[1].trim().isEmpty()) {
            return ApiResponse.error("权限编码格式错误，应为 resource:action");
        }

        return ApiResponse.success(true);
    }

    @Override
    public Uni<ApiResponse<Boolean>> checkPermissionCodeExists(Long tenantId, String permissionCode, Long excludeId) {
        Log.infof("检查权限编码是否存在: tenantId=%d, permissionCode=%s", tenantId, permissionCode);

        return permissionRepository.existsByPermissionCode(permissionCode, tenantId, excludeId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "检查权限编码失败: tenantId=%d, permissionCode=%s", tenantId, permissionCode);
                    return ApiResponse.error("检查权限编码失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Boolean>> canDeletePermission(Long tenantId, Long permissionId) {
        Log.infof("验证权限是否可删除: tenantId=%d, permissionId=%d", tenantId, permissionId);

        // 检查是否有角色关联此权限
        return RolePermission.count("permissionId = ?1 and tenantId = ?2 and deleted = 0", permissionId, tenantId)
                .flatMap(roleCount -> {
                    if (roleCount > 0) {
                        return Uni.createFrom().item(ApiResponse.success(false));
                    }

                    // 检查是否有子权限
                    return permissionRepository.count("parentId = ?1 and tenantId = ?2 and deleted = 0", permissionId, tenantId)
                            .map(childCount -> ApiResponse.success(childCount == 0));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "验证权限删除失败: tenantId=%d, permissionId=%d", tenantId, permissionId);
                    return ApiResponse.error("验证失败: " + throwable.getMessage());
                });
    }

    // ==================== 权限初始化 ====================

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> initTenantPermissions(Long tenantId) {
        Log.infof("初始化租户权限: tenantId=%d", tenantId);

        // 从系统权限复制到租户权限
        return permissionRepository.findSystemPermissions(0L)
                .flatMap(systemPermissions -> {
                    List<Permission> tenantPermissions = systemPermissions.stream()
                            .map(this::copyPermissionForTenant)
                            .peek(p -> p.setTenantId(tenantId))
                            .collect(Collectors.toList());

                    return Permission.persist(tenantPermissions)
                            .map(success -> ApiResponse.<Void>success(null));
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "初始化租户权限失败: tenantId=%d", tenantId);
                    return ApiResponse.error("初始化权限失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> syncSystemPermissions(Long tenantId) {
        Log.infof("同步系统权限: tenantId=%d", tenantId);

        // 实现系统权限同步逻辑
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 统计方法 ====================

    @Override
    public Uni<ApiResponse<Long>> countPermissions(Long tenantId) {
        Log.infof("统计权限数量: tenantId=%d", tenantId);

        return permissionRepository.countByTenantId(tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "统计权限数量失败: tenantId=%d", tenantId);
                    return ApiResponse.error("统计失败: " + throwable.getMessage());
                });
    }

    @Override
    public Uni<ApiResponse<Long>> countPermissionRoles(Long tenantId, Long permissionId) {
        Log.infof("统计权限角色数量: tenantId=%d, permissionId=%d", tenantId, permissionId);

        return RolePermission.count("permissionId = ?1 and tenantId = ?2 and deleted = 0 and status = 1", permissionId, tenantId)
                .map(ApiResponse::success)
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "统计权限角色数量失败: tenantId=%d, permissionId=%d", tenantId, permissionId);
                    return ApiResponse.error("统计失败: " + throwable.getMessage());
                });
    }

    // ==================== 权限导入导出 ====================

    @Override
    public Uni<ApiResponse<String>> exportPermissions(Long tenantId) {
        Log.infof("导出权限数据: tenantId=%d", tenantId);

        return permissionRepository.findByTenantId(tenantId)
                .map(permissions -> {
                    // 简化实现，实际应该转换为JSON或其他格式
                    String data = permissions.stream()
                            .map(p -> p.getPermissionCode() + ":" + p.getPermissionName())
                            .collect(Collectors.joining("\n"));
                    return ApiResponse.<String>success(data);
                })
                .onFailure().recoverWithItem(throwable -> {
                    Log.errorf(throwable, "导出权限数据失败: tenantId=%d", tenantId);
                    return ApiResponse.<String>error("导出失败: " + throwable.getMessage());
                });
    }

    @Override
    @Transactional
    public Uni<ApiResponse<Void>> importPermissions(Long tenantId, String permissionData) {
        Log.infof("导入权限数据: tenantId=%d", tenantId);

        // 简化实现，实际应该解析JSON或其他格式
        return Uni.createFrom().item(ApiResponse.<Void>success(null));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建权限树结构
     */
    private List<Permission> buildPermissionTree(List<Permission> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            return new ArrayList<>();
        }

        // 按父ID分组
        Map<Long, List<Permission>> parentMap = permissions.stream()
                .filter(p -> p.getParentId() != null)
                .collect(Collectors.groupingBy(Permission::getParentId));

        // 设置子权限
        permissions.forEach(permission -> {
            List<Permission> children = parentMap.get(permission.id);
            if (children != null) {
                permission.setChildren(children);
            }
        });

        // 返回根权限
        return permissions.stream()
                .filter(p -> p.getParentId() == null)
                .collect(Collectors.toList());
    }

    /**
     * 获取默认资源类型
     */
    private String getDefaultResourceType(Integer permissionType) {
        if (permissionType == null) {
            return "API";
        }
        switch (permissionType) {
            case 1:
                return "API";
            case 2:
                return "MENU";
            case 3:
                return "BUTTON";
            case 4:
                return "DATA";
            default:
                return "API";
        }
    }

    /**
     * 复制权限给租户
     */
    private Permission copyPermissionForTenant(Permission source) {
        Permission copy = new Permission();
        copy.setPermissionCode(source.getPermissionCode());
        copy.setPermissionName(source.getPermissionName());
        copy.setPermissionType(source.getPermissionType());
        copy.setParentId(source.getParentId());
        copy.setResourceType(source.getResourceType());
        copy.setResourcePath(source.getResourcePath());
        copy.setAction(source.getAction());
        copy.setDescription(source.getDescription());
        copy.setIsSystem(false); // 租户权限不是系统权限
        copy.setSortOrder(source.getSortOrder());
        copy.setCreateTime(LocalDateTime.now());
        copy.setUpdateTime(LocalDateTime.now());
        return copy;
    }
}
