package com.visthink.member.dto;

import lombok.Data;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户角色过期时间更新请求DTO
 * 
 * 用于接收更新用户角色过期时间的请求参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class UserRoleExpireUpdateRequest {

    /**
     * 用户角色关联ID列表
     */
    @NotEmpty(message = "用户角色关联ID列表不能为空")
    private List<Long> userRoleIds;

    /**
     * 新的过期时间
     * 如果为null，表示设置为永不过期
     */
    private LocalDateTime expireTime;

    /**
     * 延长天数
     * 如果设置了此字段，则在当前过期时间基础上延长指定天数
     */
    private Integer extendDays;

    /**
     * 是否设置为永不过期
     */
    private Boolean neverExpire = false;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    private Long operatorId;

    /**
     * 操作备注
     */
    private String remark;

    /**
     * 验证请求参数是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (userRoleIds == null || userRoleIds.isEmpty()) {
            return false;
        }

        if (operatorId == null) {
            return false;
        }

        // 检查操作类型是否明确
        int operationCount = 0;
        if (expireTime != null) operationCount++;
        if (extendDays != null && extendDays > 0) operationCount++;
        if (neverExpire != null && neverExpire) operationCount++;

        // 只能选择一种操作类型
        return operationCount == 1;
    }

    /**
     * 是否设置具体过期时间
     * 
     * @return 是否设置具体过期时间
     */
    public boolean isSetExpireTime() {
        return expireTime != null;
    }

    /**
     * 是否延长过期时间
     * 
     * @return 是否延长过期时间
     */
    public boolean isExtendExpireTime() {
        return extendDays != null && extendDays > 0;
    }

    /**
     * 是否设置永不过期
     * 
     * @return 是否设置永不过期
     */
    public boolean isSetNeverExpire() {
        return neverExpire != null && neverExpire;
    }

    /**
     * 获取操作类型描述
     * 
     * @return 操作类型描述
     */
    public String getOperationType() {
        if (isSetExpireTime()) {
            return "设置过期时间";
        } else if (isExtendExpireTime()) {
            return "延长过期时间";
        } else if (isSetNeverExpire()) {
            return "设置永不过期";
        } else {
            return "未知操作";
        }
    }

    /**
     * 计算新的过期时间
     * 
     * @param currentExpireTime 当前过期时间
     * @return 新的过期时间
     */
    public LocalDateTime calculateNewExpireTime(LocalDateTime currentExpireTime) {
        if (isSetExpireTime()) {
            return expireTime;
        } else if (isExtendExpireTime()) {
            if (currentExpireTime != null) {
                return currentExpireTime.plusDays(extendDays);
            } else {
                // 如果当前没有过期时间，从现在开始延长
                return LocalDateTime.now().plusDays(extendDays);
            }
        } else if (isSetNeverExpire()) {
            return null; // null表示永不过期
        } else {
            return currentExpireTime; // 保持不变
        }
    }

    /**
     * 获取用户角色关联ID数量
     * 
     * @return 用户角色关联ID数量
     */
    public int getUserRoleCount() {
        return userRoleIds != null ? userRoleIds.size() : 0;
    }

    /**
     * 是否有备注
     * 
     * @return 是否有备注
     */
    public boolean hasRemark() {
        return remark != null && !remark.trim().isEmpty();
    }

    /**
     * 获取格式化的备注
     * 
     * @return 格式化的备注
     */
    public String getFormattedRemark() {
        StringBuilder sb = new StringBuilder();
        sb.append(getOperationType());
        
        if (isSetExpireTime()) {
            sb.append("：").append(expireTime);
        } else if (isExtendExpireTime()) {
            sb.append("：延长").append(extendDays).append("天");
        }
        
        if (hasRemark()) {
            sb.append("，").append(remark);
        }
        
        return sb.toString();
    }
}
