package com.visthink.member.repository;

import com.visthink.member.entity.Region;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 地区数据访问层
 * 
 * 提供地区相关的数据库操作方法
 * 支持响应式编程和地理信息查询
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class RegionRepository implements PanacheRepository<Region> {

    /**
     * 根据ID查询地区
     * 
     * @param id 地区ID
     * @return 地区信息
     */
    public Uni<Region> findByIdAndNotDeleted(Long id) {
        return find("id = ?1 and deleted = 0", id).firstResult();
    }

    /**
     * 根据地区编码查询地区
     * 
     * @param regionCode 地区编码
     * @return 地区信息
     */
    public Uni<Region> findByRegionCode(String regionCode) {
        return find("regionCode = ?1 and deleted = 0", regionCode).firstResult();
    }

    /**
     * 查询所有地区
     * 
     * @return 地区列表
     */
    public Uni<List<Region>> findAllRegions() {
        return find("deleted = 0", Sort.by("regionLevel", "sortOrder", "regionName")).list();
    }

    /**
     * 根据地区级别查询地区
     * 
     * @param regionLevel 地区级别
     * @return 地区列表
     */
    public Uni<List<Region>> findByRegionLevel(Integer regionLevel) {
        return find("regionLevel = ?1 and deleted = 0", 
                   Sort.by("sortOrder", "regionName"), regionLevel).list();
    }

    /**
     * 根据父地区ID查询子地区
     * 
     * @param parentId 父地区ID
     * @return 子地区列表
     */
    public Uni<List<Region>> findByParentId(Long parentId) {
        return find("parentId = ?1 and deleted = 0", 
                   Sort.by("sortOrder", "regionName"), parentId).list();
    }

    /**
     * 查询省份列表
     * 
     * @return 省份列表
     */
    public Uni<List<Region>> findProvinces() {
        return find("regionLevel = 2 and deleted = 0", 
                   Sort.by("sortOrder", "regionName")).list();
    }

    /**
     * 查询热门省份
     * 
     * @return 热门省份列表
     */
    public Uni<List<Region>> findHotProvinces() {
        return find("regionLevel = 2 and isHot = true and deleted = 0", 
                   Sort.by("sortOrder", "regionName")).list();
    }

    /**
     * 查询热门城市
     * 
     * @return 热门城市列表
     */
    public Uni<List<Region>> findHotCities() {
        return find("regionLevel = 3 and isHot = true and deleted = 0", 
                   Sort.by("sortOrder", "regionName")).list();
    }

    /**
     * 查询直辖市
     * 
     * @return 直辖市列表
     */
    public Uni<List<Region>> findMunicipalities() {
        return find("regionLevel = 2 and isMunicipality = true and deleted = 0", 
                   Sort.by("sortOrder", "regionName")).list();
    }

    /**
     * 分页查询地区
     * 
     * @param pageRequest 分页请求
     * @param keyword 搜索关键词
     * @param regionLevel 地区级别
     * @param parentId 父地区ID
     * @param isHot 是否热门地区
     * @return 分页结果
     */
    public Uni<PageResult<Region>> findByPage(PageRequest pageRequest, String keyword, 
                                            Integer regionLevel, Long parentId, Boolean isHot) {
        
        // 构建查询条件
        StringBuilder queryBuilder = new StringBuilder("deleted = 0");
        int paramIndex = 1;
        
        // 添加关键词搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryBuilder.append(" and (regionName like ?").append(paramIndex)
                       .append(" or regionCode like ?").append(paramIndex)
                       .append(" or regionShortName like ?").append(paramIndex).append(")");
            paramIndex++;
        }
        
        // 添加地区级别条件
        if (regionLevel != null) {
            queryBuilder.append(" and regionLevel = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加父地区ID条件
        if (parentId != null) {
            queryBuilder.append(" and parentId = ?").append(paramIndex);
            paramIndex++;
        }
        
        // 添加热门地区条件
        if (isHot != null) {
            queryBuilder.append(" and isHot = ?").append(paramIndex);
            paramIndex++;
        }
        
        String query = queryBuilder.toString();
        
        // 构建参数数组
        Object[] params = buildParams(keyword, regionLevel, parentId, isHot);
        
        // 执行分页查询
        Page page = Page.of(pageRequest.getPage() - 1, pageRequest.getSize());
        Sort sort = Sort.by("regionLevel").and("sortOrder").and("regionName");
        
        return find(query, sort, params).page(page).list()
                .flatMap(regions -> count(query, params)
                        .map(total -> PageResult.of(regions, total, pageRequest.getPage(), pageRequest.getSize())));
    }

    /**
     * 构建查询参数数组
     */
    private Object[] buildParams(String keyword, Integer regionLevel, Long parentId, Boolean isHot) {
        java.util.List<Object> paramList = new java.util.ArrayList<>();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            String likeKeyword = "%" + keyword.trim() + "%";
            paramList.add(likeKeyword);
        }
        
        if (regionLevel != null) {
            paramList.add(regionLevel);
        }
        
        if (parentId != null) {
            paramList.add(parentId);
        }
        
        if (isHot != null) {
            paramList.add(isHot);
        }
        
        return paramList.toArray();
    }

    /**
     * 根据坐标查询附近地区
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @param radius 搜索半径（公里）
     * @return 地区列表
     */
    public Uni<List<Region>> findByCoordinates(Double longitude, Double latitude, Double radius) {
        // 使用Haversine公式计算距离
        String query = """
            SELECT r FROM Region r 
            WHERE r.longitude IS NOT NULL AND r.latitude IS NOT NULL 
            AND r.deleted = 0
            AND (6371 * acos(cos(radians(?2)) * cos(radians(r.latitude)) * 
                cos(radians(r.longitude) - radians(?1)) + 
                sin(radians(?2)) * sin(radians(r.latitude)))) <= ?3
            ORDER BY (6371 * acos(cos(radians(?2)) * cos(radians(r.latitude)) * 
                cos(radians(r.longitude) - radians(?1)) + 
                sin(radians(?2)) * sin(radians(r.latitude))))
            """;
        
        return find(query, longitude, latitude, radius).list();
    }

    /**
     * 根据邮政编码查询地区
     * 
     * @param postalCode 邮政编码
     * @return 地区列表
     */
    public Uni<List<Region>> findByPostalCode(String postalCode) {
        return find("postalCode = ?1 and deleted = 0", 
                   Sort.by("regionLevel", "regionName"), postalCode).list();
    }

    /**
     * 根据区号查询地区
     * 
     * @param areaCode 区号
     * @return 地区列表
     */
    public Uni<List<Region>> findByAreaCode(String areaCode) {
        return find("areaCode = ?1 and deleted = 0", 
                   Sort.by("regionLevel", "regionName"), areaCode).list();
    }

    /**
     * 搜索地区
     * 
     * @param keyword 搜索关键词
     * @param regionLevel 地区级别（可选）
     * @param limit 结果限制数量
     * @return 搜索结果
     */
    public Uni<List<Region>> searchRegions(String keyword, Integer regionLevel, Integer limit) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("(regionName like ?1 or regionCode like ?1 or regionShortName like ?1) and deleted = 0");
        
        if (regionLevel != null) {
            queryBuilder.append(" and regionLevel = ?2");
        }
        
        String query = queryBuilder.toString();
        String likeKeyword = "%" + keyword + "%";
        
        Page page = Page.of(0, limit != null ? limit : 20);
        Sort sort = Sort.by("regionLevel").and("regionName");
        
        if (regionLevel != null) {
            return find(query, sort, likeKeyword, regionLevel).page(page).list();
        } else {
            return find(query, sort, likeKeyword).page(page).list();
        }
    }

    /**
     * 检查地区编码是否存在
     * 
     * @param regionCode 地区编码
     * @param excludeId 排除的地区ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByRegionCode(String regionCode, Long excludeId) {
        String query = excludeId != null 
            ? "regionCode = ?1 and id != ?2 and deleted = 0"
            : "regionCode = ?1 and deleted = 0";
        
        Object[] params = excludeId != null 
            ? new Object[]{regionCode, excludeId}
            : new Object[]{regionCode};
        
        return count(query, params).map(count -> count > 0);
    }

    /**
     * 检查是否有子地区
     * 
     * @param parentId 父地区ID
     * @return 是否有子地区
     */
    public Uni<Boolean> hasChildren(Long parentId) {
        return count("parentId = ?1 and deleted = 0", parentId).map(count -> count > 0);
    }

    /**
     * 统计地区数量
     * 
     * @return 地区数量
     */
    public Uni<Long> countAllRegions() {
        return count("deleted = 0");
    }

    /**
     * 统计各级别地区数量
     *
     * @return 级别地区统计
     */
    public Uni<Map<Integer, Long>> countByRegionLevel() {
        return find("deleted = 0")
                .list()
                .map(regions -> regions.stream()
                        .map(region -> (Region) region)
                        .collect(Collectors.groupingBy(
                                Region::getRegionLevel,
                                Collectors.counting()
                        )));
    }

    /**
     * 统计子地区数量
     * 
     * @param parentId 父地区ID
     * @return 子地区数量
     */
    public Uni<Long> countChildRegions(Long parentId) {
        return count("parentId = ?1 and deleted = 0", parentId);
    }

    /**
     * 统计热门地区数量
     * 
     * @return 热门地区数量
     */
    public Uni<Long> countHotRegions() {
        return count("isHot = true and deleted = 0");
    }

    /**
     * 软删除地区
     * 
     * @param id 地区ID
     * @return 删除结果
     */
    public Uni<Boolean> softDelete(Long id) {
        return update("deleted = 1, updateTime = CURRENT_TIMESTAMP where id = ?1", id)
                .map(count -> count > 0);
    }

    /**
     * 批量软删除地区
     * 
     * @param ids 地区ID列表
     * @return 删除数量
     */
    public Uni<Integer> softDeleteBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "deleted = 1, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ")";
        
        return update(query, ids.toArray());
    }

    /**
     * 更新地区坐标
     * 
     * @param id 地区ID
     * @param longitude 经度
     * @param latitude 纬度
     * @return 更新结果
     */
    public Uni<Boolean> updateCoordinates(Long id, Double longitude, Double latitude) {
        return update("longitude = ?1, latitude = ?2, updateTime = CURRENT_TIMESTAMP where id = ?3", 
                     longitude, latitude, id)
                .map(count -> count > 0);
    }

    /**
     * 设置热门地区
     * 
     * @param id 地区ID
     * @param isHot 是否热门
     * @return 设置结果
     */
    public Uni<Boolean> setHotRegion(Long id, Boolean isHot) {
        return update("isHot = ?1, updateTime = CURRENT_TIMESTAMP where id = ?2", isHot, id)
                .map(count -> count > 0);
    }

    /**
     * 批量设置热门地区
     * 
     * @param ids 地区ID列表
     * @param isHot 是否热门
     * @return 更新数量
     */
    public Uni<Integer> batchSetHotRegions(List<Long> ids, Boolean isHot) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        
        String inClause = ids.stream().map(id -> "?").collect(java.util.stream.Collectors.joining(","));
        String query = "isHot = ?, updateTime = CURRENT_TIMESTAMP where id in (" + inClause + ")";
        
        Object[] params = new Object[ids.size() + 1];
        params[0] = isHot;
        for (int i = 0; i < ids.size(); i++) {
            params[i + 1] = ids.get(i);
        }
        
        return update(query, params);
    }
}
