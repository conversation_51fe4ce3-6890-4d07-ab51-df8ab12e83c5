-- Member Center Service Database Migration
-- Version: 1.1
-- Description: 创建菜单管理相关表

-- 1. 系统菜单表
CREATE TABLE IF NOT EXISTS system_menus (
    id BIGSERIAL PRIMARY KEY,
    menu_code VARCHAR(50) NOT NULL,
    menu_name VARCHAR(100) NOT NULL,
    menu_type INTEGER NOT NULL DEFAULT 2,
    parent_id BIGINT,
    menu_path VARCHAR(200),
    component_path VARCHAR(200),
    menu_icon VARCHAR(100),
    permission_code VARCHAR(100),
    is_external BOOLEAN NOT NULL DEFAULT FALSE,
    is_cached BOOLEAN NOT NULL DEFAULT FALSE,
    is_visible BOOLEAN NOT NULL DEFAULT TRUE,
    description VARCHAR(500),
    button_permissions TEXT,
    meta_data TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name <PERSON><PERSON><PERSON><PERSON>(100),
    update_name VA<PERSON>HA<PERSON>(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 2. 创建菜单表索引
CREATE INDEX IF NOT EXISTS idx_menu_code ON system_menus(menu_code);
CREATE INDEX IF NOT EXISTS idx_menu_parent ON system_menus(parent_id);
CREATE INDEX IF NOT EXISTS idx_menu_type ON system_menus(menu_type);
CREATE INDEX IF NOT EXISTS idx_menu_tenant ON system_menus(tenant_id);
CREATE INDEX IF NOT EXISTS idx_menu_sort ON system_menus(sort_order);
CREATE INDEX IF NOT EXISTS idx_menu_permission ON system_menus(permission_code);
CREATE INDEX IF NOT EXISTS idx_menu_visible ON system_menus(is_visible);

-- 3. 创建菜单表唯一约束
ALTER TABLE system_menus ADD CONSTRAINT uk_menu_tenant_code UNIQUE (tenant_id, menu_code);

-- 4. 添加菜单表外键约束
ALTER TABLE system_menus ADD CONSTRAINT fk_menu_parent FOREIGN KEY (parent_id) REFERENCES system_menus(id);

-- 5. 插入系统默认菜单数据
INSERT INTO system_menus (menu_code, menu_name, menu_type, parent_id, menu_path, component_path, menu_icon, permission_code, is_visible, description, tenant_id, create_by, create_name) VALUES
-- 系统管理目录
('system', '系统管理', 1, NULL, '/system', NULL, 'ant-design:setting-outlined', NULL, true, '系统管理目录', 0, 'system', '系统'),

-- 用户管理
('system:user', '用户管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'system' AND tenant_id = 0), '/system/user', '/system/user/index', 'ant-design:user-outlined', 'user:read', true, '用户管理页面', 0, 'system', '系统'),
('system:user:add', '新增用户', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:user' AND tenant_id = 0), NULL, NULL, NULL, 'user:create', true, '新增用户按钮', 0, 'system', '系统'),
('system:user:edit', '编辑用户', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:user' AND tenant_id = 0), NULL, NULL, NULL, 'user:update', true, '编辑用户按钮', 0, 'system', '系统'),
('system:user:delete', '删除用户', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:user' AND tenant_id = 0), NULL, NULL, NULL, 'user:delete', true, '删除用户按钮', 0, 'system', '系统'),
('system:user:export', '导出用户', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:user' AND tenant_id = 0), NULL, NULL, NULL, 'user:export', true, '导出用户按钮', 0, 'system', '系统'),

-- 角色管理
('system:role', '角色管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'system' AND tenant_id = 0), '/system/role', '/system/role/index', 'ant-design:team-outlined', 'role:read', true, '角色管理页面', 0, 'system', '系统'),
('system:role:add', '新增角色', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:role' AND tenant_id = 0), NULL, NULL, NULL, 'role:create', true, '新增角色按钮', 0, 'system', '系统'),
('system:role:edit', '编辑角色', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:role' AND tenant_id = 0), NULL, NULL, NULL, 'role:update', true, '编辑角色按钮', 0, 'system', '系统'),
('system:role:delete', '删除角色', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:role' AND tenant_id = 0), NULL, NULL, NULL, 'role:delete', true, '删除角色按钮', 0, 'system', '系统'),
('system:role:permission', '分配权限', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:role' AND tenant_id = 0), NULL, NULL, NULL, 'role:permission', true, '分配权限按钮', 0, 'system', '系统'),

-- 菜单管理
('system:menu', '菜单管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'system' AND tenant_id = 0), '/system/menu', '/system/menu/index', 'ant-design:menu-outlined', 'menu:read', true, '菜单管理页面', 0, 'system', '系统'),
('system:menu:add', '新增菜单', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:menu' AND tenant_id = 0), NULL, NULL, NULL, 'menu:create', true, '新增菜单按钮', 0, 'system', '系统'),
('system:menu:edit', '编辑菜单', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:menu' AND tenant_id = 0), NULL, NULL, NULL, 'menu:update', true, '编辑菜单按钮', 0, 'system', '系统'),
('system:menu:delete', '删除菜单', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:menu' AND tenant_id = 0), NULL, NULL, NULL, 'menu:delete', true, '删除菜单按钮', 0, 'system', '系统'),

-- 权限管理
('system:permission', '权限管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'system' AND tenant_id = 0), '/system/permission', '/system/permission/index', 'ant-design:safety-certificate-outlined', 'permission:read', true, '权限管理页面', 0, 'system', '系统'),
('system:permission:add', '新增权限', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:permission' AND tenant_id = 0), NULL, NULL, NULL, 'permission:create', true, '新增权限按钮', 0, 'system', '系统'),
('system:permission:edit', '编辑权限', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:permission' AND tenant_id = 0), NULL, NULL, NULL, 'permission:update', true, '编辑权限按钮', 0, 'system', '系统'),
('system:permission:delete', '删除权限', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:permission' AND tenant_id = 0), NULL, NULL, NULL, 'permission:delete', true, '删除权限按钮', 0, 'system', '系统'),

-- 租户管理
('system:tenant', '租户管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'system' AND tenant_id = 0), '/system/tenant', '/system/tenant/index', 'ant-design:apartment-outlined', 'tenant:read', true, '租户管理页面', 0, 'system', '系统'),
('system:tenant:add', '新增租户', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:tenant' AND tenant_id = 0), NULL, NULL, NULL, 'tenant:create', true, '新增租户按钮', 0, 'system', '系统'),
('system:tenant:edit', '编辑租户', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:tenant' AND tenant_id = 0), NULL, NULL, NULL, 'tenant:update', true, '编辑租户按钮', 0, 'system', '系统'),
('system:tenant:delete', '删除租户', 3, (SELECT id FROM system_menus WHERE menu_code = 'system:tenant' AND tenant_id = 0), NULL, NULL, NULL, 'tenant:delete', true, '删除租户按钮', 0, 'system', '系统'),

-- 业务管理目录
('business', '业务管理', 1, NULL, '/business', NULL, 'ant-design:shop-outlined', NULL, true, '业务管理目录', 0, 'system', '系统'),

-- 商品管理
('business:product', '商品管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'business' AND tenant_id = 0), '/business/product', '/business/product/index', 'ant-design:shopping-outlined', 'product:read', true, '商品管理页面', 0, 'system', '系统'),
('business:product:add', '新增商品', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:product' AND tenant_id = 0), NULL, NULL, NULL, 'product:create', true, '新增商品按钮', 0, 'system', '系统'),
('business:product:edit', '编辑商品', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:product' AND tenant_id = 0), NULL, NULL, NULL, 'product:update', true, '编辑商品按钮', 0, 'system', '系统'),
('business:product:delete', '删除商品', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:product' AND tenant_id = 0), NULL, NULL, NULL, 'product:delete', true, '删除商品按钮', 0, 'system', '系统'),

-- 库存管理
('business:inventory', '库存管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'business' AND tenant_id = 0), '/business/inventory', '/business/inventory/index', 'ant-design:inbox-outlined', 'inventory:read', true, '库存管理页面', 0, 'system', '系统'),
('business:inventory:add', '库存入库', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:inventory' AND tenant_id = 0), NULL, NULL, NULL, 'inventory:create', true, '库存入库按钮', 0, 'system', '系统'),
('business:inventory:edit', '库存调整', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:inventory' AND tenant_id = 0), NULL, NULL, NULL, 'inventory:update', true, '库存调整按钮', 0, 'system', '系统'),

-- 订单管理
('business:order', '订单管理', 2, (SELECT id FROM system_menus WHERE menu_code = 'business' AND tenant_id = 0), '/business/order', '/business/order/index', 'ant-design:file-text-outlined', 'order:read', true, '订单管理页面', 0, 'system', '系统'),
('business:order:add', '新增订单', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:order' AND tenant_id = 0), NULL, NULL, NULL, 'order:create', true, '新增订单按钮', 0, 'system', '系统'),
('business:order:edit', '编辑订单', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:order' AND tenant_id = 0), NULL, NULL, NULL, 'order:update', true, '编辑订单按钮', 0, 'system', '系统'),
('business:order:cancel', '取消订单', 3, (SELECT id FROM system_menus WHERE menu_code = 'business:order' AND tenant_id = 0), NULL, NULL, NULL, 'order:cancel', true, '取消订单按钮', 0, 'system', '系统')

ON CONFLICT (tenant_id, menu_code) DO NOTHING;

-- 6. 更新菜单排序
UPDATE system_menus SET sort_order = 1 WHERE menu_code = 'system' AND tenant_id = 0;
UPDATE system_menus SET sort_order = 2 WHERE menu_code = 'business' AND tenant_id = 0;

-- 系统管理子菜单排序
UPDATE system_menus SET sort_order = 1 WHERE menu_code = 'system:user' AND tenant_id = 0;
UPDATE system_menus SET sort_order = 2 WHERE menu_code = 'system:role' AND tenant_id = 0;
UPDATE system_menus SET sort_order = 3 WHERE menu_code = 'system:menu' AND tenant_id = 0;
UPDATE system_menus SET sort_order = 4 WHERE menu_code = 'system:permission' AND tenant_id = 0;
UPDATE system_menus SET sort_order = 5 WHERE menu_code = 'system:tenant' AND tenant_id = 0;

-- 业务管理子菜单排序
UPDATE system_menus SET sort_order = 1 WHERE menu_code = 'business:product' AND tenant_id = 0;
UPDATE system_menus SET sort_order = 2 WHERE menu_code = 'business:inventory' AND tenant_id = 0;
UPDATE system_menus SET sort_order = 3 WHERE menu_code = 'business:order' AND tenant_id = 0;

-- 7. 插入更多系统权限到权限表（如果权限表存在的话）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'permissions') THEN
        INSERT INTO permissions (permission_code, permission_name, permission_type, resource_type, resource_path, action, description, is_system, create_by, create_name, tenant_id)
        SELECT * FROM (VALUES
            -- 角色管理权限
            ('role:read', '查看角色', 1, 'API', '/api/roles', 'GET', '查看角色信息权限', true, 'system', '系统', 0),
            ('role:create', '创建角色', 1, 'API', '/api/roles', 'POST', '创建角色权限', true, 'system', '系统', 0),
            ('role:update', '更新角色', 1, 'API', '/api/roles', 'PUT', '更新角色权限', true, 'system', '系统', 0),
            ('role:delete', '删除角色', 1, 'API', '/api/roles', 'DELETE', '删除角色权限', true, 'system', '系统', 0),
            ('role:permission', '分配权限', 1, 'API', '/api/roles/permissions', 'POST', '角色权限分配权限', true, 'system', '系统', 0),

            -- 菜单管理权限
            ('menu:read', '查看菜单', 1, 'API', '/api/menus', 'GET', '查看菜单信息权限', true, 'system', '系统', 0),
            ('menu:create', '创建菜单', 1, 'API', '/api/menus', 'POST', '创建菜单权限', true, 'system', '系统', 0),
            ('menu:update', '更新菜单', 1, 'API', '/api/menus', 'PUT', '更新菜单权限', true, 'system', '系统', 0),
            ('menu:delete', '删除菜单', 1, 'API', '/api/menus', 'DELETE', '删除菜单权限', true, 'system', '系统', 0),

            -- 权限管理权限
            ('permission:read', '查看权限', 1, 'API', '/api/permissions', 'GET', '查看权限信息权限', true, 'system', '系统', 0),
            ('permission:create', '创建权限', 1, 'API', '/api/permissions', 'POST', '创建权限权限', true, 'system', '系统', 0),
            ('permission:update', '更新权限', 1, 'API', '/api/permissions', 'PUT', '更新权限权限', true, 'system', '系统', 0),
            ('permission:delete', '删除权限', 1, 'API', '/api/permissions', 'DELETE', '删除权限权限', true, 'system', '系统', 0),

            -- 商品管理权限
            ('product:read', '查看商品', 1, 'API', '/api/products', 'GET', '查看商品信息权限', true, 'system', '系统', 0),
            ('product:create', '创建商品', 1, 'API', '/api/products', 'POST', '创建商品权限', true, 'system', '系统', 0),
            ('product:update', '更新商品', 1, 'API', '/api/products', 'PUT', '更新商品权限', true, 'system', '系统', 0),
            ('product:delete', '删除商品', 1, 'API', '/api/products', 'DELETE', '删除商品权限', true, 'system', '系统', 0),
            ('product:export', '导出商品', 1, 'API', '/api/products/export', 'GET', '导出商品权限', true, 'system', '系统', 0),

            -- 库存管理权限
            ('inventory:read', '查看库存', 1, 'API', '/api/inventory', 'GET', '查看库存信息权限', true, 'system', '系统', 0),
            ('inventory:create', '库存入库', 1, 'API', '/api/inventory', 'POST', '库存入库权限', true, 'system', '系统', 0),
            ('inventory:update', '库存调整', 1, 'API', '/api/inventory', 'PUT', '库存调整权限', true, 'system', '系统', 0),

            -- 订单管理权限
            ('order:read', '查看订单', 1, 'API', '/api/orders', 'GET', '查看订单信息权限', true, 'system', '系统', 0),
            ('order:create', '创建订单', 1, 'API', '/api/orders', 'POST', '创建订单权限', true, 'system', '系统', 0),
            ('order:update', '更新订单', 1, 'API', '/api/orders', 'PUT', '更新订单权限', true, 'system', '系统', 0),
            ('order:cancel', '取消订单', 1, 'API', '/api/orders/cancel', 'POST', '取消订单权限', true, 'system', '系统', 0)
        ) AS new_permissions(permission_code, permission_name, permission_type, resource_type, resource_path, action, description, is_system, create_by, create_name, tenant_id)
        WHERE NOT EXISTS (
            SELECT 1 FROM permissions p
            WHERE p.permission_code = new_permissions.permission_code
            AND p.tenant_id = new_permissions.tenant_id
        );
    END IF;
END $$;
