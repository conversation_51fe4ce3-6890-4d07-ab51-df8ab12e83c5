-- =====================================================
-- 错误码管理表创建脚本
-- 版本: V1.3
-- 描述: 创建错误码管理相关表和基础数据
-- 作者: visthink
-- 日期: 2024-12-19
-- =====================================================

-- 创建错误码表
CREATE TABLE IF NOT EXISTS system_error_codes (
    id BIGSERIAL PRIMARY KEY,
    error_code VARCHAR(50) NOT NULL,
    error_name VARCHAR(100) NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    error_level INTEGER NOT NULL DEFAULT 3,
    http_status INTEGER NOT NULL DEFAULT 500,
    error_message VARCHAR(500) NOT NULL,
    error_message_en VARCHAR(500),
    description VARCHAR(1000),
    solution VARCHAR(1000),
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    is_editable BOOLEAN NOT NULL DEFAULT TRUE,
    is_logged BOOLEAN NOT NULL DEFAULT TRUE,
    is_notified BOOLEAN NOT NULL DEFAULT FALSE,
    extra_props TEXT,

    -- 基础字段
    tenant_id BIGINT NOT NULL DEFAULT 0,
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    remark VARCHAR(1000),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT
);

-- 创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_error_code_tenant ON system_error_codes(error_code, tenant_id);

-- 创建普通索引
CREATE INDEX IF NOT EXISTS idx_error_module ON system_error_codes(module_name);
CREATE INDEX IF NOT EXISTS idx_error_level ON system_error_codes(error_level);
CREATE INDEX IF NOT EXISTS idx_error_tenant ON system_error_codes(tenant_id);
CREATE INDEX IF NOT EXISTS idx_error_status ON system_error_codes(status, deleted);

-- =====================================================
-- 插入系统错误码数据
-- =====================================================

-- 通用错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('COMMON_001', '参数错误', 'COMMON', 2, 400, '请求参数错误', 'Invalid request parameters', '客户端传递的参数不符合要求', '检查请求参数格式和内容', TRUE, FALSE, 0),
('COMMON_002', '数据不存在', 'COMMON', 3, 404, '请求的数据不存在', 'Requested data not found', '查询的数据记录不存在', '确认数据ID是否正确', TRUE, FALSE, 0),
('COMMON_003', '权限不足', 'COMMON', 3, 403, '没有权限执行此操作', 'Insufficient permissions', '用户没有执行当前操作的权限', '联系管理员分配相应权限', TRUE, FALSE, 0),
('COMMON_004', '系统繁忙', 'COMMON', 4, 500, '系统繁忙，请稍后重试', 'System busy, please try again later', '系统当前负载过高', '稍后重试或联系技术支持', TRUE, FALSE, 0),
('COMMON_005', '操作失败', 'COMMON', 3, 500, '操作执行失败', 'Operation failed', '业务操作执行失败', '检查操作条件和数据状态', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;

-- 用户模块错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('USER_001', '用户不存在', 'USER', 3, 404, '用户不存在', 'User not found', '指定的用户ID不存在', '确认用户ID是否正确', TRUE, FALSE, 0),
('USER_002', '用户名已存在', 'USER', 2, 400, '用户名已存在', 'Username already exists', '注册的用户名已被使用', '更换其他用户名', TRUE, FALSE, 0),
('USER_003', '邮箱已存在', 'USER', 2, 400, '邮箱已存在', 'Email already exists', '注册的邮箱已被使用', '更换其他邮箱地址', TRUE, FALSE, 0),
('USER_004', '手机号已存在', 'USER', 2, 400, '手机号已存在', 'Phone number already exists', '注册的手机号已被使用', '更换其他手机号', TRUE, FALSE, 0),
('USER_005', '密码错误', 'USER', 3, 401, '用户名或密码错误', 'Invalid username or password', '登录密码不正确', '确认密码是否正确', TRUE, FALSE, 0),
('USER_006', '账号已禁用', 'USER', 3, 403, '账号已被禁用', 'Account has been disabled', '用户账号被管理员禁用', '联系管理员解除禁用', TRUE, FALSE, 0),
('USER_007', '账号已锁定', 'USER', 3, 423, '账号已被锁定', 'Account has been locked', '用户账号因多次错误登录被锁定', '等待解锁时间或联系管理员', TRUE, FALSE, 0),
('USER_008', '密码强度不足', 'USER', 2, 400, '密码强度不足', 'Password strength insufficient', '设置的密码不符合安全要求', '使用更复杂的密码组合', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;

-- 角色模块错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('ROLE_001', '角色不存在', 'ROLE', 3, 404, '角色不存在', 'Role not found', '指定的角色ID不存在', '确认角色ID是否正确', TRUE, FALSE, 0),
('ROLE_002', '角色编码已存在', 'ROLE', 2, 400, '角色编码已存在', 'Role code already exists', '创建的角色编码已被使用', '更换其他角色编码', TRUE, FALSE, 0),
('ROLE_003', '角色名称已存在', 'ROLE', 2, 400, '角色名称已存在', 'Role name already exists', '创建的角色名称已被使用', '更换其他角色名称', TRUE, FALSE, 0),
('ROLE_004', '角色正在使用', 'ROLE', 3, 400, '角色正在使用中，无法删除', 'Role is in use, cannot be deleted', '角色已分配给用户，无法删除', '先取消用户角色分配', TRUE, FALSE, 0),
('ROLE_005', '系统角色不可删除', 'ROLE', 3, 403, '系统角色不可删除', 'System role cannot be deleted', '系统内置角色不允许删除', '只能删除自定义角色', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;

-- 权限模块错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('PERMISSION_001', '权限不存在', 'PERMISSION', 3, 404, '权限不存在', 'Permission not found', '指定的权限ID不存在', '确认权限ID是否正确', TRUE, FALSE, 0),
('PERMISSION_002', '权限编码已存在', 'PERMISSION', 2, 400, '权限编码已存在', 'Permission code already exists', '创建的权限编码已被使用', '更换其他权限编码', TRUE, FALSE, 0),
('PERMISSION_003', '权限编码格式错误', 'PERMISSION', 2, 400, '权限编码格式错误', 'Invalid permission code format', '权限编码格式不符合规范', '使用resource:action格式', TRUE, FALSE, 0),
('PERMISSION_004', '权限正在使用', 'PERMISSION', 3, 400, '权限正在使用中，无法删除', 'Permission is in use, cannot be deleted', '权限已分配给角色，无法删除', '先取消角色权限分配', TRUE, FALSE, 0),
('PERMISSION_005', '系统权限不可删除', 'PERMISSION', 3, 403, '系统权限不可删除', 'System permission cannot be deleted', '系统内置权限不允许删除', '只能删除自定义权限', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;

-- 菜单模块错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('MENU_001', '菜单不存在', 'MENU', 3, 404, '菜单不存在', 'Menu not found', '指定的菜单ID不存在', '确认菜单ID是否正确', TRUE, FALSE, 0),
('MENU_002', '菜单编码已存在', 'MENU', 2, 400, '菜单编码已存在', 'Menu code already exists', '创建的菜单编码已被使用', '更换其他菜单编码', TRUE, FALSE, 0),
('MENU_003', '父菜单不存在', 'MENU', 3, 400, '父菜单不存在', 'Parent menu not found', '指定的父菜单不存在', '确认父菜单ID是否正确', TRUE, FALSE, 0),
('MENU_004', '菜单层级过深', 'MENU', 2, 400, '菜单层级过深', 'Menu hierarchy too deep', '菜单层级超过系统限制', '减少菜单层级深度', TRUE, FALSE, 0),
('MENU_005', '存在子菜单', 'MENU', 3, 400, '存在子菜单，无法删除', 'Has child menus, cannot be deleted', '菜单下存在子菜单，无法删除', '先删除所有子菜单', TRUE, FALSE, 0),
('MENU_006', '菜单路径格式错误', 'MENU', 2, 400, '菜单路径格式错误', 'Invalid menu path format', '菜单路径格式不符合规范', '使用正确的路径格式', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;

-- 字典模块错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('DICT_001', '字典不存在', 'DICT', 3, 404, '字典不存在', 'Dictionary not found', '指定的字典ID不存在', '确认字典ID是否正确', TRUE, FALSE, 0),
('DICT_002', '字典编码已存在', 'DICT', 2, 400, '字典编码已存在', 'Dictionary code already exists', '创建的字典编码已被使用', '更换其他字典编码', TRUE, FALSE, 0),
('DICT_003', '字典类型不存在', 'DICT', 3, 400, '字典类型不存在', 'Dictionary type not found', '指定的字典类型不存在', '确认字典类型是否正确', TRUE, FALSE, 0),
('DICT_004', '存在子字典', 'DICT', 3, 400, '存在子字典，无法删除', 'Has child dictionaries, cannot be deleted', '字典下存在子字典，无法删除', '先删除所有子字典', TRUE, FALSE, 0),
('DICT_005', '系统字典不可删除', 'DICT', 3, 403, '系统字典不可删除', 'System dictionary cannot be deleted', '系统内置字典不允许删除', '只能删除自定义字典', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;

-- 租户模块错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('TENANT_001', '租户不存在', 'TENANT', 3, 404, '租户不存在', 'Tenant not found', '指定的租户ID不存在', '确认租户ID是否正确', TRUE, FALSE, 0),
('TENANT_002', '租户编码已存在', 'TENANT', 2, 400, '租户编码已存在', 'Tenant code already exists', '创建的租户编码已被使用', '更换其他租户编码', TRUE, FALSE, 0),
('TENANT_003', '租户已过期', 'TENANT', 3, 403, '租户已过期', 'Tenant has expired', '租户服务已过期', '联系管理员续费', TRUE, FALSE, 0),
('TENANT_004', '租户已禁用', 'TENANT', 3, 403, '租户已被禁用', 'Tenant has been disabled', '租户被管理员禁用', '联系管理员解除禁用', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;

-- 文件模块错误码
INSERT INTO system_error_codes (error_code, error_name, module_name, error_level, http_status, error_message, error_message_en, description, solution, is_system, is_editable, tenant_id) VALUES
('FILE_001', '文件不存在', 'FILE', 3, 404, '文件不存在', 'File not found', '指定的文件不存在', '确认文件路径是否正确', TRUE, FALSE, 0),
('FILE_002', '文件格式不支持', 'FILE', 2, 400, '文件格式不支持', 'File format not supported', '上传的文件格式不被支持', '使用支持的文件格式', TRUE, FALSE, 0),
('FILE_003', '文件大小超限', 'FILE', 2, 400, '文件大小超过限制', 'File size exceeds limit', '上传的文件大小超过系统限制', '压缩文件或分割上传', TRUE, FALSE, 0),
('FILE_004', '文件上传失败', 'FILE', 3, 500, '文件上传失败', 'File upload failed', '文件上传过程中发生错误', '检查网络连接和存储空间', TRUE, FALSE, 0),
('FILE_005', '存储空间不足', 'FILE', 4, 507, '存储空间不足', 'Insufficient storage space', '系统存储空间不足', '清理存储空间或扩容', TRUE, FALSE, 0)
ON CONFLICT (error_code, tenant_id) DO NOTHING;
