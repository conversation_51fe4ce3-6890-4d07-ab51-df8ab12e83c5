-- Member Center Service Database Migration
-- Version: 1.0
-- Description: 创建用户与租户管理相关表

-- 1. 租户表
CREATE TABLE IF NOT EXISTS tenants (
    id BIGSERIAL PRIMARY KEY,
    tenant_code VARCHAR(50) UNIQUE NOT NULL,
    tenant_name VARCHAR(100) NOT NULL,
    short_name VARCHAR(50),
    tenant_type INTEGER NOT NULL DEFAULT 1,
    tenant_status INTEGER NOT NULL DEFAULT 4,
    domain VARCHAR(100) UNIQUE,
    logo_url VARCHAR(500),
    contact_name VARCHAR(50),
    contact_email VARCHAR(100),
    contact_phone VARCHAR(20),
    company_name VARCHAR(200),
    credit_code VARCHAR(50),
    company_address VARCHAR(500),
    industry_type VARCHAR(50),
    company_scale INTEGER,
    max_users INTEGER DEFAULT 10,
    current_users INTEGER DEFAULT 0,
    storage_limit BIGINT DEFAULT 1024,
    storage_used BIGINT DEFAULT 0,
    activated_time TIMESTAMP,
    trial_start_time TIMESTAMP,
    trial_end_time TIMESTAMP,
    subscription_start_time TIMESTAMP,
    subscription_end_time TIMESTAMP,
    last_login_time TIMESTAMP,
    database_config TEXT,
    feature_config TEXT,
    theme_config TEXT,
    extra_config TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 2. 用户账户表
CREATE TABLE IF NOT EXISTS user_accounts (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    password_salt VARCHAR(32),
    real_name VARCHAR(50),
    nickname VARCHAR(50),
    avatar_url VARCHAR(500),
    gender INTEGER DEFAULT 0,
    birthday TIMESTAMP,
    user_type INTEGER NOT NULL DEFAULT 1,
    account_status INTEGER NOT NULL DEFAULT 4,
    is_activated BOOLEAN NOT NULL DEFAULT FALSE,
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    phone_verified BOOLEAN NOT NULL DEFAULT FALSE,
    last_login_time TIMESTAMP,
    last_login_ip VARCHAR(45),
    login_count BIGINT DEFAULT 0,
    password_error_count INTEGER DEFAULT 0,
    locked_time TIMESTAMP,
    password_expire_time TIMESTAMP,
    account_expire_time TIMESTAMP,
    department_id BIGINT,
    position VARCHAR(100),
    employee_no VARCHAR(50),
    join_time TIMESTAMP,
    preferences TEXT,
    extra_attributes TEXT,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 3. 用户角色表
CREATE TABLE IF NOT EXISTS user_roles (
    id BIGSERIAL PRIMARY KEY,
    role_code VARCHAR(50) NOT NULL,
    role_name VARCHAR(100) NOT NULL,
    role_type INTEGER NOT NULL DEFAULT 1,
    description VARCHAR(500),
    permissions TEXT,
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 4. 用户角色关联表
CREATE TABLE IF NOT EXISTS user_role_relations (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    granted_by BIGINT,
    granted_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expire_time TIMESTAMP,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 5. 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id BIGSERIAL PRIMARY KEY,
    permission_code VARCHAR(100) NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    permission_type INTEGER NOT NULL DEFAULT 1,
    parent_id BIGINT,
    resource_type VARCHAR(50),
    resource_path VARCHAR(200),
    action VARCHAR(50),
    description VARCHAR(500),
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 6. 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permission_relations (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    granted_by BIGINT,
    granted_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 7. 用户登录日志表
CREATE TABLE IF NOT EXISTS user_login_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    username VARCHAR(50),
    login_type INTEGER NOT NULL DEFAULT 1,
    login_ip VARCHAR(45),
    login_location VARCHAR(100),
    user_agent VARCHAR(500),
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP,
    session_id VARCHAR(100),
    login_result INTEGER NOT NULL DEFAULT 1,
    failure_reason VARCHAR(200),
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 8. 账户激活令牌表
CREATE TABLE IF NOT EXISTS account_activation_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    token_type INTEGER NOT NULL DEFAULT 1,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    create_by VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_name VARCHAR(100),
    update_name VARCHAR(100),
    update_by VARCHAR(50),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id BIGINT,
    remark VARCHAR(1000),
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0
);

-- 创建索引
-- 租户表索引
CREATE INDEX IF NOT EXISTS idx_tenant_code ON tenants(tenant_code);
CREATE INDEX IF NOT EXISTS idx_tenant_domain ON tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenant_status ON tenants(tenant_status);
CREATE INDEX IF NOT EXISTS idx_tenant_type ON tenants(tenant_type);

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_user_tenant_username ON user_accounts(tenant_id, username);
CREATE INDEX IF NOT EXISTS idx_user_tenant_email ON user_accounts(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_user_tenant_phone ON user_accounts(tenant_id, phone_number);
CREATE INDEX IF NOT EXISTS idx_user_last_login ON user_accounts(last_login_time);
CREATE INDEX IF NOT EXISTS idx_user_account_status ON user_accounts(account_status);
CREATE INDEX IF NOT EXISTS idx_user_type ON user_accounts(user_type);

-- 用户角色表索引
CREATE INDEX IF NOT EXISTS idx_role_tenant_code ON user_roles(tenant_id, role_code);
CREATE INDEX IF NOT EXISTS idx_role_type ON user_roles(role_type);

-- 用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_role_user ON user_role_relations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_role ON user_role_relations(role_id);
CREATE INDEX IF NOT EXISTS idx_user_role_tenant ON user_role_relations(tenant_id);

-- 权限表索引
CREATE INDEX IF NOT EXISTS idx_permission_code ON permissions(permission_code);
CREATE INDEX IF NOT EXISTS idx_permission_parent ON permissions(parent_id);
CREATE INDEX IF NOT EXISTS idx_permission_type ON permissions(permission_type);

-- 角色权限关联表索引
CREATE INDEX IF NOT EXISTS idx_role_permission_role ON role_permission_relations(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permission_permission ON role_permission_relations(permission_id);

-- 登录日志表索引
CREATE INDEX IF NOT EXISTS idx_login_log_user ON user_login_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_login_log_time ON user_login_logs(login_time);
CREATE INDEX IF NOT EXISTS idx_login_log_ip ON user_login_logs(login_ip);

-- 激活令牌表索引
CREATE INDEX IF NOT EXISTS idx_activation_token_user ON account_activation_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_activation_token_token ON account_activation_tokens(token);
CREATE INDEX IF NOT EXISTS idx_activation_token_expires ON account_activation_tokens(expires_at);

-- 添加唯一约束
ALTER TABLE user_accounts ADD CONSTRAINT uk_user_tenant_username UNIQUE (tenant_id, username);
ALTER TABLE user_accounts ADD CONSTRAINT uk_user_tenant_email UNIQUE (tenant_id, email);
ALTER TABLE user_accounts ADD CONSTRAINT uk_user_tenant_phone UNIQUE (tenant_id, phone_number);
ALTER TABLE user_roles ADD CONSTRAINT uk_role_tenant_code UNIQUE (tenant_id, role_code);
ALTER TABLE user_role_relations ADD CONSTRAINT uk_user_role UNIQUE (user_id, role_id);
ALTER TABLE role_permission_relations ADD CONSTRAINT uk_role_permission UNIQUE (role_id, permission_id);

-- 添加外键约束
ALTER TABLE user_role_relations ADD CONSTRAINT fk_user_role_user FOREIGN KEY (user_id) REFERENCES user_accounts(id);
ALTER TABLE user_role_relations ADD CONSTRAINT fk_user_role_role FOREIGN KEY (role_id) REFERENCES user_roles(id);
ALTER TABLE role_permission_relations ADD CONSTRAINT fk_role_permission_role FOREIGN KEY (role_id) REFERENCES user_roles(id);
ALTER TABLE role_permission_relations ADD CONSTRAINT fk_role_permission_permission FOREIGN KEY (permission_id) REFERENCES permissions(id);
ALTER TABLE user_login_logs ADD CONSTRAINT fk_login_log_user FOREIGN KEY (user_id) REFERENCES user_accounts(id);
ALTER TABLE account_activation_tokens ADD CONSTRAINT fk_activation_token_user FOREIGN KEY (user_id) REFERENCES user_accounts(id);

-- 插入初始数据
-- 插入默认租户
INSERT INTO tenants (tenant_code, tenant_name, tenant_type, tenant_status, max_users, current_users, storage_limit, storage_used, create_by, create_name, tenant_id) 
VALUES ('default', '默认租户', 1, 1, 100, 0, 10240, 0, 'system', '系统', 0)
ON CONFLICT (tenant_code) DO NOTHING;

-- 插入系统权限
INSERT INTO permissions (permission_code, permission_name, permission_type, resource_type, resource_path, action, description, is_system, create_by, create_name, tenant_id) VALUES
('user:read', '查看用户', 1, 'API', '/api/users', 'GET', '查看用户信息权限', true, 'system', '系统', 0),
('user:create', '创建用户', 1, 'API', '/api/users', 'POST', '创建用户权限', true, 'system', '系统', 0),
('user:update', '更新用户', 1, 'API', '/api/users', 'PUT', '更新用户权限', true, 'system', '系统', 0),
('user:delete', '删除用户', 1, 'API', '/api/users', 'DELETE', '删除用户权限', true, 'system', '系统', 0),
('tenant:read', '查看租户', 1, 'API', '/api/tenants', 'GET', '查看租户信息权限', true, 'system', '系统', 0),
('tenant:create', '创建租户', 1, 'API', '/api/tenants', 'POST', '创建租户权限', true, 'system', '系统', 0),
('tenant:update', '更新租户', 1, 'API', '/api/tenants', 'PUT', '更新租户权限', true, 'system', '系统', 0),
('tenant:delete', '删除租户', 1, 'API', '/api/tenants', 'DELETE', '删除租户权限', true, 'system', '系统', 0)
ON CONFLICT (permission_code) DO NOTHING;

-- 插入默认角色
INSERT INTO user_roles (role_code, role_name, role_type, description, is_system, is_default, create_by, create_name, tenant_id) VALUES
('SUPER_ADMIN', '超级管理员', 3, '系统超级管理员，拥有所有权限', true, false, 'system', '系统', 0),
('TENANT_ADMIN', '租户管理员', 2, '租户管理员，拥有租户内所有权限', true, false, 'system', '系统', 0),
('USER', '普通用户', 1, '普通用户，基础权限', true, true, 'system', '系统', 0)
ON CONFLICT (tenant_id, role_code) DO NOTHING;
