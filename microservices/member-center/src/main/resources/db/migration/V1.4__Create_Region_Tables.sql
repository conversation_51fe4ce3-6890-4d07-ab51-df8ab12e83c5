-- =====================================================
-- 地区管理表创建脚本
-- 版本: V1.4
-- 描述: 创建地区管理相关表和基础数据
-- 作者: visthink
-- 日期: 2024-12-19
-- =====================================================

-- 创建地区表
CREATE TABLE IF NOT EXISTS system_regions (
    id BIGSERIAL PRIMARY KEY,
    region_code VARCHAR(20) NOT NULL,
    region_name VARCHAR(100) NOT NULL,
    region_short_name VARCHAR(50),
    region_level INTEGER NOT NULL,
    parent_id BIGINT,
    region_type VARCHAR(20),
    postal_code VARCHAR(10),
    area_code VARCHAR(10),
    longitude DOUBLE PRECISION,
    latitude DOUBLE PRECISION,
    is_municipality BOOLEAN NOT NULL DEFAULT FALSE,
    is_hot BOOLEAN NOT NULL DEFAULT FALSE,
    description VARCHAR(500),
    extra_props TEXT,

    -- 基础字段
    tenant_id BIGINT NOT NULL DEFAULT 0,
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    remark VARCHAR(1000),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT
);

-- 创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_region_code ON system_regions(region_code);

-- 创建普通索引
CREATE INDEX IF NOT EXISTS idx_region_name ON system_regions(region_name);
CREATE INDEX IF NOT EXISTS idx_region_level ON system_regions(region_level);
CREATE INDEX IF NOT EXISTS idx_region_parent ON system_regions(parent_id);
CREATE INDEX IF NOT EXISTS idx_region_postal ON system_regions(postal_code);
CREATE INDEX IF NOT EXISTS idx_region_type ON system_regions(region_type);
CREATE INDEX IF NOT EXISTS idx_region_hot ON system_regions(is_hot);
CREATE INDEX IF NOT EXISTS idx_region_status ON system_regions(status, deleted);

-- =====================================================
-- 插入基础地区数据（中国行政区划）
-- =====================================================

-- 插入国家数据
DO $$
DECLARE
    china_id BIGINT;
    guangdong_id BIGINT;
    shenzhen_id BIGINT;
    beijing_id BIGINT;
    shanghai_id BIGINT;
BEGIN
    INSERT INTO system_regions (region_code, region_name, region_short_name, region_level, region_type, description, tenant_id, sort_order)
    VALUES ('100000', '中华人民共和国', '中国', 1, 'country', '中华人民共和国', 0, 1)
    ON CONFLICT (region_code) DO NOTHING
    RETURNING id INTO china_id;

    IF china_id IS NULL THEN
        SELECT id INTO china_id FROM system_regions WHERE region_code = '100000';
    END IF;

    -- 插入省级行政区数据
    INSERT INTO system_regions (region_code, region_name, region_short_name, region_level, parent_id, region_type, postal_code, area_code, is_municipality, is_hot, tenant_id, sort_order) VALUES
    -- 直辖市
    ('110000', '北京市', '北京', 2, china_id, 'province', '100000', '010', TRUE, TRUE, 0, 1),
    ('120000', '天津市', '天津', 2, china_id, 'province', '300000', '022', TRUE, TRUE, 0, 2),
    ('310000', '上海市', '上海', 2, china_id, 'province', '200000', '021', TRUE, TRUE, 0, 3),
    ('500000', '重庆市', '重庆', 2, china_id, 'province', '400000', '023', TRUE, TRUE, 0, 4),

    -- 省份
    ('130000', '河北省', '河北', 2, china_id, 'province', '050000', '0311', FALSE, FALSE, 0, 5),
    ('140000', '山西省', '山西', 2, china_id, 'province', '030000', '0351', FALSE, FALSE, 0, 6),
    ('150000', '内蒙古自治区', '内蒙古', 2, china_id, 'province', '010000', '0471', FALSE, FALSE, 0, 7),
    ('210000', '辽宁省', '辽宁', 2, china_id, 'province', '110000', '024', FALSE, FALSE, 0, 8),
    ('220000', '吉林省', '吉林', 2, china_id, 'province', '130000', '0431', FALSE, FALSE, 0, 9),
    ('230000', '黑龙江省', '黑龙江', 2, china_id, 'province', '150000', '0451', FALSE, FALSE, 0, 10),
    ('320000', '江苏省', '江苏', 2, china_id, 'province', '210000', '025', FALSE, TRUE, 0, 11),
    ('330000', '浙江省', '浙江', 2, china_id, 'province', '310000', '0571', FALSE, TRUE, 0, 12),
    ('340000', '安徽省', '安徽', 2, china_id, 'province', '230000', '0551', FALSE, FALSE, 0, 13),
    ('350000', '福建省', '福建', 2, china_id, 'province', '350000', '0591', FALSE, TRUE, 0, 14),
    ('360000', '江西省', '江西', 2, china_id, 'province', '330000', '0791', FALSE, FALSE, 0, 15),
    ('370000', '山东省', '山东', 2, china_id, 'province', '250000', '0531', FALSE, TRUE, 0, 16),
    ('410000', '河南省', '河南', 2, china_id, 'province', '450000', '0371', FALSE, FALSE, 0, 17),
    ('420000', '湖北省', '湖北', 2, china_id, 'province', '430000', '027', FALSE, FALSE, 0, 18),
    ('430000', '湖南省', '湖南', 2, china_id, 'province', '410000', '0731', FALSE, FALSE, 0, 19),
    ('440000', '广东省', '广东', 2, china_id, 'province', '510000', '020', FALSE, TRUE, 0, 20),
    ('450000', '广西壮族自治区', '广西', 2, china_id, 'province', '530000', '0771', FALSE, FALSE, 0, 21),
    ('460000', '海南省', '海南', 2, china_id, 'province', '570000', '0898', FALSE, FALSE, 0, 22),
    ('510000', '四川省', '四川', 2, china_id, 'province', '610000', '028', FALSE, TRUE, 0, 23),
    ('520000', '贵州省', '贵州', 2, china_id, 'province', '550000', '0851', FALSE, FALSE, 0, 24),
    ('530000', '云南省', '云南', 2, china_id, 'province', '650000', '0871', FALSE, FALSE, 0, 25),
    ('540000', '西藏自治区', '西藏', 2, china_id, 'province', '850000', '0891', FALSE, FALSE, 0, 26),
    ('610000', '陕西省', '陕西', 2, china_id, 'province', '710000', '029', FALSE, FALSE, 0, 27),
    ('620000', '甘肃省', '甘肃', 2, china_id, 'province', '730000', '0931', FALSE, FALSE, 0, 28),
    ('630000', '青海省', '青海', 2, china_id, 'province', '810000', '0971', FALSE, FALSE, 0, 29),
    ('640000', '宁夏回族自治区', '宁夏', 2, china_id, 'province', '750000', '0951', FALSE, FALSE, 0, 30),
    ('650000', '新疆维吾尔自治区', '新疆', 2, china_id, 'province', '830000', '0991', FALSE, FALSE, 0, 31),

    -- 特别行政区
    ('710000', '台湾省', '台湾', 2, china_id, 'province', '100000', '00886', FALSE, FALSE, 0, 32),
    ('810000', '香港特别行政区', '香港', 2, china_id, 'province', '999077', '00852', FALSE, TRUE, 0, 33),
    ('820000', '澳门特别行政区', '澳门', 2, china_id, 'province', '999078', '00853', FALSE, FALSE, 0, 34)
    ON CONFLICT (region_code) DO NOTHING;

    -- 插入部分热门城市数据（以广东省为例）
    SELECT id INTO guangdong_id FROM system_regions WHERE region_code = '440000';

    INSERT INTO system_regions (region_code, region_name, region_short_name, region_level, parent_id, region_type, postal_code, area_code, is_hot, tenant_id, sort_order) VALUES
    ('440100', '广州市', '广州', 3, guangdong_id, 'city', '510000', '020', TRUE, 0, 1),
    ('440300', '深圳市', '深圳', 3, guangdong_id, 'city', '518000', '0755', TRUE, 0, 3),
    ('440400', '珠海市', '珠海', 3, guangdong_id, 'city', '519000', '0756', TRUE, 0, 4),
    ('440600', '佛山市', '佛山', 3, guangdong_id, 'city', '528000', '0757', TRUE, 0, 6),
    ('441900', '东莞市', '东莞', 3, guangdong_id, 'city', '523000', '0769', TRUE, 0, 17),
    ('442000', '中山市', '中山', 3, guangdong_id, 'city', '528400', '0760', TRUE, 0, 18)
    ON CONFLICT (region_code) DO NOTHING;

END $$;
