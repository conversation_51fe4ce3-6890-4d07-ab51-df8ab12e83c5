-- =====================================================
-- 字典管理表创建脚本
-- 版本: V1.2
-- 描述: 创建字典管理相关表和基础数据
-- 作者: visthink
-- 日期: 2024-12-19
-- =====================================================

-- 创建字典表
CREATE TABLE IF NOT EXISTS system_dictionaries (
    id BIGSERIAL PRIMARY KEY,
    dict_code VARCHAR(50) NOT NULL,
    dict_name VARCHAR(100) NOT NULL,
    dict_type INTEGER NOT NULL DEFAULT 1,
    parent_id BIGINT,
    dict_value VARCHAR(200),
    dict_label VARCHAR(100),
    description VARCHAR(500),
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    is_editable BOOLEAN NOT NULL DEFAULT TRUE,
    dict_style VARCHAR(100),
    dict_color VARCHAR(20),
    extra_props TEXT,

    -- 基础字段
    tenant_id BIGINT NOT NULL DEFAULT 0,
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    deleted INTEGER NOT NULL DEFAULT 0,
    version BIGINT NOT NULL DEFAULT 0,
    remark VARCHAR(1000),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_by BIGINT,
    update_by BIGINT
);

-- 创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_dict_code_tenant ON system_dictionaries(dict_code, tenant_id);

-- 创建普通索引
CREATE INDEX IF NOT EXISTS idx_dict_type ON system_dictionaries(dict_type);
CREATE INDEX IF NOT EXISTS idx_dict_parent ON system_dictionaries(parent_id);
CREATE INDEX IF NOT EXISTS idx_dict_tenant ON system_dictionaries(tenant_id);
CREATE INDEX IF NOT EXISTS idx_dict_sort ON system_dictionaries(sort_order);
CREATE INDEX IF NOT EXISTS idx_dict_status ON system_dictionaries(status, deleted);

-- =====================================================
-- 插入系统字典类型数据
-- =====================================================

-- 用户状态字典
DO $$
DECLARE
    user_status_id BIGINT;
    gender_id BIGINT;
    role_type_id BIGINT;
    permission_type_id BIGINT;
    menu_type_id BIGINT;
    data_scope_id BIGINT;
    common_status_id BIGINT;
    yes_no_id BIGINT;
    operation_type_id BIGINT;
    log_level_id BIGINT;
BEGIN
    -- 用户状态字典
    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, description, is_system, is_editable, tenant_id, sort_order)
    VALUES ('user_status', '用户状态', 1, '用户账号状态字典', TRUE, FALSE, 0, 1)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING
    RETURNING id INTO user_status_id;

    IF user_status_id IS NULL THEN
        SELECT id INTO user_status_id FROM system_dictionaries WHERE dict_code = 'user_status' AND tenant_id = 0;
    END IF;

    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, parent_id, dict_value, dict_label, dict_color, is_system, is_editable, tenant_id, sort_order) VALUES
    ('user_status_active', '正常', 2, user_status_id, '1', '正常', '#52c41a', TRUE, FALSE, 0, 1),
    ('user_status_inactive', '禁用', 2, user_status_id, '0', '禁用', '#f5222d', TRUE, FALSE, 0, 2),
    ('user_status_locked', '锁定', 2, user_status_id, '2', '锁定', '#fa8c16', TRUE, FALSE, 0, 3),
    ('user_status_expired', '过期', 2, user_status_id, '3', '过期', '#722ed1', TRUE, FALSE, 0, 4)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING;

    -- 性别字典
    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, description, is_system, is_editable, tenant_id, sort_order)
    VALUES ('gender', '性别', 1, '用户性别字典', TRUE, FALSE, 0, 2)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING
    RETURNING id INTO gender_id;

    IF gender_id IS NULL THEN
        SELECT id INTO gender_id FROM system_dictionaries WHERE dict_code = 'gender' AND tenant_id = 0;
    END IF;

    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, parent_id, dict_value, dict_label, dict_color, is_system, is_editable, tenant_id, sort_order) VALUES
    ('gender_male', '男', 2, gender_id, '1', '男', '#1890ff', TRUE, FALSE, 0, 1),
    ('gender_female', '女', 2, gender_id, '2', '女', '#eb2f96', TRUE, FALSE, 0, 2),
    ('gender_unknown', '未知', 2, gender_id, '0', '未知', '#8c8c8c', TRUE, FALSE, 0, 3)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING;

    -- 角色类型字典
    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, description, is_system, is_editable, tenant_id, sort_order)
    VALUES ('role_type', '角色类型', 1, '系统角色类型字典', TRUE, FALSE, 0, 3)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING
    RETURNING id INTO role_type_id;

    IF role_type_id IS NULL THEN
        SELECT id INTO role_type_id FROM system_dictionaries WHERE dict_code = 'role_type' AND tenant_id = 0;
    END IF;

    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, parent_id, dict_value, dict_label, dict_color, is_system, is_editable, tenant_id, sort_order) VALUES
    ('role_type_system', '系统角色', 2, role_type_id, '1', '系统角色', '#722ed1', TRUE, FALSE, 0, 1),
    ('role_type_business', '业务角色', 2, role_type_id, '2', '业务角色', '#1890ff', TRUE, FALSE, 0, 2),
    ('role_type_custom', '自定义角色', 2, role_type_id, '3', '自定义角色', '#52c41a', TRUE, FALSE, 0, 3)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING;

    -- 通用状态字典
    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, description, is_system, is_editable, tenant_id, sort_order)
    VALUES ('common_status', '通用状态', 1, '通用状态字典', TRUE, FALSE, 0, 4)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING
    RETURNING id INTO common_status_id;

    IF common_status_id IS NULL THEN
        SELECT id INTO common_status_id FROM system_dictionaries WHERE dict_code = 'common_status' AND tenant_id = 0;
    END IF;

    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, parent_id, dict_value, dict_label, dict_color, is_system, is_editable, tenant_id, sort_order) VALUES
    ('common_status_enable', '启用', 2, common_status_id, '1', '启用', '#52c41a', TRUE, FALSE, 0, 1),
    ('common_status_disable', '禁用', 2, common_status_id, '0', '禁用', '#f5222d', TRUE, FALSE, 0, 2)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING;

    -- 是否字典
    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, description, is_system, is_editable, tenant_id, sort_order)
    VALUES ('yes_no', '是否', 1, '是否选择字典', TRUE, FALSE, 0, 5)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING
    RETURNING id INTO yes_no_id;

    IF yes_no_id IS NULL THEN
        SELECT id INTO yes_no_id FROM system_dictionaries WHERE dict_code = 'yes_no' AND tenant_id = 0;
    END IF;

    INSERT INTO system_dictionaries (dict_code, dict_name, dict_type, parent_id, dict_value, dict_label, dict_color, is_system, is_editable, tenant_id, sort_order) VALUES
    ('yes_no_yes', '是', 2, yes_no_id, '1', '是', '#52c41a', TRUE, FALSE, 0, 1),
    ('yes_no_no', '否', 2, yes_no_id, '0', '否', '#f5222d', TRUE, FALSE, 0, 2)
    ON CONFLICT (dict_code, tenant_id) DO NOTHING;

END $$;