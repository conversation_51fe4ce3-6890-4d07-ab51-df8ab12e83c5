# Member Center Service Configuration
DB_USERNAME: "postgres"
DB_PASSWORD: "zylp"
DB_JDBC_URL: "**********************************************"
DB_URL: "vertx-reactive:postgresql://*************:35432/postgres"
JWT_SECRET: "p3K9X7s2$v!YgTq4wZb5rDcWfzJhNmU6xL8A0e1RtIyO"
# 应用基本配置
quarkus:
  application:
    name: member-center
    version: 2.0.0

  # HTTP配置
  http:
    port: 8081
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "GET,PUT,POST,DELETE,OPTIONS"
      headers: "accept, authorization, content-type, x-requested-with"
      access-control-max-age: 86400

  # 数据库配置
  datasource:
    db-kind: postgresql
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:zylp}
    jdbc: false
    reactive:
      url: ${DB_URL:vertx-reactive:postgresql://*************:35432/visthink_member}
      max-size: 10
      idle-timeout: PT5M
      max-lifetime: PT30M
      connection-timeout: PT10S
      # 添加连接池稳定性配置
      shared: false
      trust-all: false
      reconnect-attempts: 3
      reconnect-interval: PT1S

  # Hibernate配置
  hibernate-orm:
    database:
      generation: update
    log:
      sql: false
      format-sql: true
      bind-parameters: false
    # 完全禁用Hibernate多租户，使用应用层隔离
    # 移除所有多租户相关配置避免冲突
    jdbc:
      statement-batch-size: 20
      # 添加会话管理配置
      timezone: UTC
    # 添加响应式特定配置
    reactive:
      # 禁用自动刷新避免会话状态冲突
      auto-flush: false

  # Flyway数据库迁移
  flyway:
    migrate-at-start: true
    baseline-on-migrate: true
    locations: classpath:db/migration

  # JWT配置 - 使用MicroProfile JWT标准配置
  # 注意：SmallRye JWT在Quarkus中需要使用mp.jwt前缀的配置

  # 缓存配置
  cache:
    caffeine:
      "user-cache":
        initial-capacity: 100
        maximum-size: 1000
        expire-after-write: PT30M
      "tenant-cache":
        initial-capacity: 50
        maximum-size: 500
        expire-after-write: PT1H

  # Redis配置
  redis:
    hosts: ${REDIS_URL:redis://127.0.0.1:6379}
    password: ${REDIS_PASSWORD:zylp}
    timeout: PT10S
    max-pool-size: 20
    max-pool-waiting: 50

  # 健康检查配置
  smallrye-health:
    root-path: /health

  # 指标监控配置
  micrometer:
    export:
      prometheus:
        enabled: true
        path: /metrics

  # OpenAPI文档配置
  smallrye-openapi:
    info-title: Member Center API
    info-version: 2.0.0
    info-description: 用户与租户管理服务API
    info-contact-name: Visthink Team
    info-contact-email: <EMAIL>
    servers:
      - url: http://localhost:8081
        description: 开发环境
      - url: https://api.visthink.com/member
        description: 生产环境

  # 日志配置
  log:
    level:
      ROOT: INFO
      com.visthink: DEBUG
      org.hibernate.SQL: DEBUG
    console:
      enable: true
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"
    file:
      enable: true
      path: logs/member-center.log
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"
      rotation:
        max-file-size: 100M
        max-backup-index: 10

# MicroProfile JWT配置（SmallRye JWT标准配置）
mp:
  jwt:
    verify:
      # 使用对称密钥验证JWT（HMAC-SHA256）
      secretkey: ${JWT_SECRET:p3K9X7s2$v!YgTq4wZb5rDcWfzJhNmU6xL8A0e1RtIyO}
      issuer: ${JWT_ISSUER:https://api.visthink.com}

# 业务配置
app:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:your-super-secret-jwt-key-at-least-256-bits-long}
    expiration: ${JWT_EXPIRATION:86400000} # 24小时
    issuer: ${JWT_ISSUER:https://api.visthink.com}
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800000} # 7天

  # 密码配置
  password:
    min-length: 8
    max-length: 32
    require-uppercase: true
    require-lowercase: true
    require-digit: true
    require-special: false
    max-error-count: 5
    lock-duration: PT2H # 锁定2小时
    expire-days: 90 # 密码90天过期

  # 用户配置
  user:
    default-avatar: /images/default-avatar.png
    max-login-attempts: 5
    session-timeout: PT30M
    activation-token-expire: PT24H # 激活令牌24小时过期

  # 租户配置
  tenant:
    default-trial-days: 30
    default-max-users: 10
    default-storage-limit: 1024 # MB
    code-pattern: "^[a-zA-Z0-9_-]{3,20}$"
    domain-pattern: "^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"

  # 验证码配置
  captcha:
    enabled: true
    length: 6
    expire-minutes: 5
    max-attempts: 3

  # 邮件配置
  mail:
    enabled: ${MAIL_ENABLED:false}
    smtp-host: ${MAIL_SMTP_HOST:smtp.qq.com}
    smtp-port: ${MAIL_SMTP_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    from: ${MAIL_FROM:<EMAIL>}
    from-name: ${MAIL_FROM_NAME:Visthink ERP}

  # 短信配置
  sms:
    enabled: ${SMS_ENABLED:false}
    provider: ${SMS_PROVIDER:aliyun}
    access-key: ${SMS_ACCESS_KEY:}
    secret-key: ${SMS_SECRET_KEY:}
    sign-name: ${SMS_SIGN_NAME:Visthink}
    template-code: ${SMS_TEMPLATE_CODE:}

# 环境特定配置
"%dev":
  quarkus:
    log:
      level:
        ROOT: DEBUG
        com.visthink: TRACE
    hibernate-orm:
      log:
        sql: true
        format-sql: true
        bind-parameters: true

"%test":
  quarkus:
    datasource:
      db-kind: h2
      username: sa
      password:
      jdbc:
        url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    hibernate-orm:
      database:
        generation: drop-and-create
      log:
        sql: true

"%prod":
  quarkus:
    log:
      level:
        ROOT: WARN
        com.visthink: INFO
    hibernate-orm:
      database:
        generation: validate
      log:
        sql: false
    datasource:
      reactive:
        max-size: 50
        idle-timeout: PT30M
        max-lifetime: PT2H
