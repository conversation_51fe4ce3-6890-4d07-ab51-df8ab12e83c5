#!/bin/bash

# =====================================================
# Member Center 微服务启动测试脚本
# 版本: 1.0
# 描述: 测试微服务的编译、启动和基本功能
# 作者: visthink
# 日期: 2024-12-19
# =====================================================

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令不存在，请先安装"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        log_warning "端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_wait=60
    local count=0
    
    log_info "等待 $service_name 服务启动 (端口: $port)..."
    
    while [ $count -lt $max_wait ]; do
        if curl -s http://localhost:$port/q/health > /dev/null 2>&1; then
            log_success "$service_name 服务启动成功"
            return 0
        fi
        sleep 2
        count=$((count + 2))
        echo -n "."
    done
    
    echo ""
    log_error "$service_name 服务启动超时"
    return 1
}

# 测试API接口
test_api() {
    local url=$1
    local description=$2
    
    log_info "测试 $description: $url"
    
    response=$(curl -s -w "%{http_code}" -o /tmp/api_response.json "$url")
    http_code="${response: -3}"
    
    if [ "$http_code" -eq 200 ] || [ "$http_code" -eq 404 ]; then
        log_success "$description 测试通过 (HTTP $http_code)"
        return 0
    else
        log_error "$description 测试失败 (HTTP $http_code)"
        cat /tmp/api_response.json
        return 1
    fi
}

# 主函数
main() {
    log_info "开始 Member Center 微服务启动测试"
    
    # 1. 检查必要的命令
    log_info "检查必要的命令..."
    check_command "java"
    check_command "mvn"
    check_command "curl"
    check_command "lsof"
    
    # 2. 检查当前目录
    if [ ! -f "pom.xml" ]; then
        log_error "请在 member-center 项目根目录下运行此脚本"
        exit 1
    fi
    
    # 3. 清理和编译
    log_info "清理和编译项目..."
    mvn clean compile -q
    if [ $? -ne 0 ]; then
        log_error "项目编译失败"
        exit 1
    fi
    log_success "项目编译成功"
    
    # 4. 运行测试
    log_info "运行单元测试..."
    mvn test -q
    if [ $? -ne 0 ]; then
        log_warning "单元测试失败，但继续启动测试"
    else
        log_success "单元测试通过"
    fi
    
    # 5. 检查端口
    SERVICE_PORT=8082
    if ! check_port $SERVICE_PORT; then
        log_error "端口 $SERVICE_PORT 被占用，请先停止相关服务"
        exit 1
    fi
    
    # 6. 启动服务
    log_info "启动 Member Center 服务..."
    mvn quarkus:dev -Dquarkus.http.port=$SERVICE_PORT > /tmp/member-center.log 2>&1 &
    SERVICE_PID=$!
    
    # 7. 等待服务启动
    if wait_for_service $SERVICE_PORT "Member Center"; then
        log_success "Member Center 服务启动成功 (PID: $SERVICE_PID)"
    else
        log_error "Member Center 服务启动失败"
        kill $SERVICE_PID 2>/dev/null
        exit 1
    fi
    
    # 8. 测试基本API接口
    log_info "开始API接口测试..."
    
    BASE_URL="http://localhost:$SERVICE_PORT"
    
    # 健康检查
    test_api "$BASE_URL/q/health" "健康检查接口"
    
    # OpenAPI文档
    test_api "$BASE_URL/q/openapi" "OpenAPI文档接口"
    
    # Swagger UI
    test_api "$BASE_URL/q/swagger-ui" "Swagger UI接口"
    
    # 角色管理接口
    test_api "$BASE_URL/api/roles" "角色管理接口"
    
    # 权限管理接口
    test_api "$BASE_URL/api/permissions" "权限管理接口"
    
    # 菜单管理接口
    test_api "$BASE_URL/api/menus" "菜单管理接口"
    
    # 字典管理接口
    test_api "$BASE_URL/api/dictionaries" "字典管理接口"
    
    # 9. 测试数据库连接
    log_info "测试数据库连接..."
    test_api "$BASE_URL/q/health/ready" "数据库连接检查"
    
    # 10. 显示服务信息
    log_info "服务信息:"
    echo "  - 服务地址: http://localhost:$SERVICE_PORT"
    echo "  - 健康检查: http://localhost:$SERVICE_PORT/q/health"
    echo "  - API文档: http://localhost:$SERVICE_PORT/q/swagger-ui"
    echo "  - 进程ID: $SERVICE_PID"
    echo "  - 日志文件: /tmp/member-center.log"
    
    # 11. 询问是否停止服务
    echo ""
    read -p "是否停止服务? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "停止 Member Center 服务..."
        kill $SERVICE_PID
        sleep 3
        if kill -0 $SERVICE_PID 2>/dev/null; then
            log_warning "强制停止服务..."
            kill -9 $SERVICE_PID
        fi
        log_success "服务已停止"
    else
        log_info "服务继续运行，PID: $SERVICE_PID"
        log_info "要停止服务，请运行: kill $SERVICE_PID"
    fi
    
    log_success "Member Center 微服务启动测试完成"
}

# 捕获中断信号
trap 'log_warning "测试被中断"; kill $SERVICE_PID 2>/dev/null; exit 1' INT TERM

# 运行主函数
main "$@"
