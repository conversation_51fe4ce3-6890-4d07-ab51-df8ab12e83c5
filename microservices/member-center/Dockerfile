# Member Center Service Dockerfile
# 多阶段构建，优化镜像大小

# 构建阶段
FROM maven:3.9.4-eclipse-temurin-17 AS builder

# 设置工作目录
WORKDIR /app

# 复制父POM文件
COPY ../pom.xml ./parent-pom.xml

# 复制共享模块
COPY ../shared-common ./shared-common

# 复制当前服务的POM文件
COPY pom.xml .

# 下载依赖（利用Docker缓存层）
RUN mvn dependency:go-offline -B

# 复制源代码
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests -B

# 运行阶段
FROM registry.access.redhat.com/ubi8/openjdk-17:1.15

# 设置环境变量
ENV LANGUAGE='en_US:en'
ENV JAVA_OPTS="-Dquarkus.http.host=0.0.0.0 -Djava.util.logging.manager=org.jboss.logmanager.LogManager"
ENV JAVA_APP_JAR="/deployments/quarkus-run.jar"

# 创建应用目录
USER root
RUN mkdir -p /deployments/logs && chown -R 185:185 /deployments
USER 185

# 复制构建产物
COPY --from=builder --chown=185 /app/target/quarkus-app/lib/ /deployments/lib/
COPY --from=builder --chown=185 /app/target/quarkus-app/*.jar /deployments/
COPY --from=builder --chown=185 /app/target/quarkus-app/app/ /deployments/app/
COPY --from=builder --chown=185 /app/target/quarkus-app/quarkus/ /deployments/quarkus/

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/q/health || exit 1

# 启动命令
ENTRYPOINT ["java", "-jar", "/deployments/quarkus-run.jar"]
