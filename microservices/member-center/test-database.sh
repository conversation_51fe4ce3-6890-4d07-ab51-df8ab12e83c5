#!/bin/bash

# =====================================================
# Member Center 数据库迁移测试脚本
# 版本: 1.0
# 描述: 测试数据库连接、迁移脚本和基础数据
# 作者: visthink
# 日期: 2024-12-19
# =====================================================

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="visthink_member"
DB_USER="root"
DB_PASSWORD="zylp"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装"
        return 1
    fi
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null; then
        log_success "MySQL连接成功"
        return 0
    else
        log_error "MySQL连接失败，请检查数据库配置"
        return 1
    fi
}

# 检查数据库是否存在
check_database_exists() {
    log_info "检查数据库 $DB_NAME 是否存在..."
    
    result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SHOW DATABASES LIKE '$DB_NAME';" 2>/dev/null | grep "$DB_NAME")
    
    if [ -n "$result" ]; then
        log_success "数据库 $DB_NAME 存在"
        return 0
    else
        log_warning "数据库 $DB_NAME 不存在"
        return 1
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库 $DB_NAME..."
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "数据库 $DB_NAME 创建成功"
        return 0
    else
        log_error "数据库 $DB_NAME 创建失败"
        return 1
    fi
}

# 执行迁移脚本
execute_migration() {
    local script_file=$1
    local description=$2
    
    log_info "执行迁移脚本: $description"
    echo "  文件: $script_file"
    
    if [ ! -f "$script_file" ]; then
        log_error "迁移脚本文件不存在: $script_file"
        return 1
    fi
    
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$script_file" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "$description 执行成功"
        return 0
    else
        log_error "$description 执行失败"
        return 1
    fi
}

# 验证表结构
verify_tables() {
    log_info "验证表结构..."
    
    # 期望的表列表
    expected_tables=(
        "system_roles"
        "system_permissions" 
        "system_role_permissions"
        "system_menus"
        "system_user_roles"
        "system_dictionaries"
        "system_error_codes"
        "system_regions"
    )
    
    for table in "${expected_tables[@]}"; do
        result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SHOW TABLES LIKE '$table';" 2>/dev/null | grep "$table")
        
        if [ -n "$result" ]; then
            log_success "表 $table 存在"
        else
            log_error "表 $table 不存在"
        fi
    done
}

# 验证基础数据
verify_data() {
    log_info "验证基础数据..."
    
    # 检查字典数据
    dict_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) FROM system_dictionaries WHERE tenant_id = 0;" 2>/dev/null | tail -n 1)
    if [ "$dict_count" -gt 0 ]; then
        log_success "字典基础数据存在 ($dict_count 条记录)"
    else
        log_warning "字典基础数据为空"
    fi
    
    # 检查错误码数据
    error_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) FROM system_error_codes WHERE tenant_id = 0;" 2>/dev/null | tail -n 1)
    if [ "$error_count" -gt 0 ]; then
        log_success "错误码基础数据存在 ($error_count 条记录)"
    else
        log_warning "错误码基础数据为空"
    fi
    
    # 检查地区数据
    region_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT COUNT(*) FROM system_regions;" 2>/dev/null | tail -n 1)
    if [ "$region_count" -gt 0 ]; then
        log_success "地区基础数据存在 ($region_count 条记录)"
    else
        log_warning "地区基础数据为空"
    fi
}

# 测试数据库操作
test_database_operations() {
    log_info "测试数据库操作..."
    
    # 测试插入操作
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        INSERT INTO system_roles (role_code, role_name, role_type, data_scope, tenant_id, create_time, update_time) 
        VALUES ('test_role', '测试角色', 3, 4, 999, NOW(), NOW());
    " 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "数据插入测试通过"
        
        # 测试查询操作
        result=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT role_name FROM system_roles WHERE role_code = 'test_role' AND tenant_id = 999;" 2>/dev/null | tail -n 1)
        
        if [ "$result" = "测试角色" ]; then
            log_success "数据查询测试通过"
        else
            log_error "数据查询测试失败"
        fi
        
        # 清理测试数据
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "DELETE FROM system_roles WHERE role_code = 'test_role' AND tenant_id = 999;" 2>/dev/null
        log_info "测试数据已清理"
    else
        log_error "数据插入测试失败"
    fi
}

# 生成数据库报告
generate_database_report() {
    log_info "生成数据库报告..."
    
    local report_file="/tmp/database_report.txt"
    
    echo "Member Center 数据库报告" > "$report_file"
    echo "========================" >> "$report_file"
    echo "生成时间: $(date)" >> "$report_file"
    echo "数据库: $DB_NAME" >> "$report_file"
    echo "" >> "$report_file"
    
    # 表信息
    echo "表信息:" >> "$report_file"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        SELECT 
            TABLE_NAME as '表名',
            TABLE_ROWS as '记录数',
            ROUND(DATA_LENGTH/1024/1024, 2) as '数据大小(MB)'
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '$DB_NAME' 
        ORDER BY TABLE_NAME;
    " 2>/dev/null >> "$report_file"
    
    echo "" >> "$report_file"
    
    # 索引信息
    echo "索引信息:" >> "$report_file"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" -e "
        SELECT 
            TABLE_NAME as '表名',
            INDEX_NAME as '索引名',
            COLUMN_NAME as '列名',
            NON_UNIQUE as '非唯一'
        FROM information_schema.STATISTICS 
        WHERE TABLE_SCHEMA = '$DB_NAME' 
        ORDER BY TABLE_NAME, INDEX_NAME;
    " 2>/dev/null >> "$report_file"
    
    log_success "数据库报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始 Member Center 数据库迁移测试"
    
    # 1. 检查MySQL连接
    if ! check_mysql_connection; then
        exit 1
    fi
    
    # 2. 检查或创建数据库
    if ! check_database_exists; then
        if ! create_database; then
            exit 1
        fi
    fi
    
    # 3. 执行迁移脚本
    migration_dir="src/main/resources/db/migration"
    
    if [ -d "$migration_dir" ]; then
        log_info "执行数据库迁移脚本..."
        
        # 按版本顺序执行迁移脚本
        for script in "$migration_dir"/V*.sql; do
            if [ -f "$script" ]; then
                script_name=$(basename "$script")
                execute_migration "$script" "$script_name"
            fi
        done
    else
        log_warning "迁移脚本目录不存在: $migration_dir"
    fi
    
    # 4. 验证表结构
    verify_tables
    
    # 5. 验证基础数据
    verify_data
    
    # 6. 测试数据库操作
    test_database_operations
    
    # 7. 生成数据库报告
    generate_database_report
    
    log_success "数据库迁移测试完成"
}

# 运行主函数
main "$@"
