# Nginx API网关配置 - 微服务架构

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /var/run/nginx.pid;

# 事件配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP配置
http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    # 访问日志
    access_log /var/log/nginx/access.log main;

    # 基础配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 上游服务器配置
    upstream member-center {
        least_conn;
        server member-center:8081 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream product-service {
        least_conn;
        server product-service:8082 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream inventory-service {
        least_conn;
        server inventory-service:8083 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream order-service {
        least_conn;
        server order-service:8084 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream platform-integration {
        least_conn;
        server platform-integration:8085 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream file-service {
        least_conn;
        server file-service:8086 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream api-log-service {
        least_conn;
        server api-log-service:8087 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream workflow-service {
        least_conn;
        server workflow-service:8088 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream config-center {
        least_conn;
        server config-center:8888 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # 主服务器配置
    server {
        listen 80;
        server_name localhost api.visthink.com;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 用户与租户服务
        location /api/v1/members/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://member-center/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 登录接口特殊限流
        location /api/v1/members/auth/login {
            limit_req zone=login burst=3 nodelay;
            proxy_pass http://member-center/auth/login;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 商品服务
        location /api/v1/products/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://product-service/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 库存服务
        location /api/v1/inventory/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://inventory-service/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 订单服务
        location /api/v1/orders/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://order-service/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 平台集成服务
        location /api/v1/platforms/ {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://platform-integration/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 文件服务
        location /api/v1/files/ {
            limit_req zone=api burst=30 nodelay;
            proxy_pass http://file-service/;
            include /etc/nginx/conf.d/proxy.conf;
            
            # 文件上传特殊配置
            client_max_body_size 100M;
            proxy_request_buffering off;
        }

        # API日志服务
        location /api/v1/logs/ {
            limit_req zone=api burst=10 nodelay;
            proxy_pass http://api-log-service/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 工作流服务
        location /api/v1/workflows/ {
            limit_req zone=api burst=15 nodelay;
            proxy_pass http://workflow-service/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 配置中心
        location /api/v1/configs/ {
            limit_req zone=api burst=5 nodelay;
            proxy_pass http://config-center/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 监控端点
        location /metrics {
            allow **********/16;
            deny all;
            proxy_pass http://prometheus:9090/metrics;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # 静态文件
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 默认错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # HTTPS配置 (生产环境)
    server {
        listen 443 ssl http2;
        server_name api.visthink.com;

        # SSL证书配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;

        # SSL安全配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # HSTS
        add_header Strict-Transport-Security "max-age=63072000" always;

        # 其他配置与HTTP相同
        include /etc/nginx/conf.d/locations.conf;
    }

    # 管理界面 (内部访问)
    server {
        listen 8080;
        server_name localhost;
        
        allow **********/16;
        deny all;

        # Consul UI
        location /consul/ {
            proxy_pass http://consul:8500/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # Prometheus UI
        location /prometheus/ {
            proxy_pass http://prometheus:9090/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # Grafana UI
        location /grafana/ {
            proxy_pass http://grafana:3000/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # Jaeger UI
        location /jaeger/ {
            proxy_pass http://jaeger:16686/;
            include /etc/nginx/conf.d/proxy.conf;
        }

        # MinIO Console
        location /minio/ {
            proxy_pass http://minio:9001/;
            include /etc/nginx/conf.d/proxy.conf;
        }
    }

    # 包含其他配置文件
    include /etc/nginx/conf.d/*.conf;
}
