package com.visthink.security.config;

import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全配置验证器测试类
 * 
 * 测试SecurityConfigValidator的功能：
 * 1. 应用启动时的配置验证
 * 2. 密码强度检查
 * 3. 不安全默认值检测
 * 4. JWT密钥安全级别验证
 * 5. 配置格式验证
 * 
 * <AUTHOR>
 */
@QuarkusTest
@DisplayName("安全配置验证器测试")
class SecurityConfigValidatorTest {

    @Inject
    SecurityConfigValidator securityConfigValidator;

    @Inject
    SecureConfigManager secureConfigManager;

    @BeforeEach
    void setUp() {
        // 测试前的准备工作
    }

    @Test
    @DisplayName("测试手动配置验证")
    void testManualConfigValidation() {
        // 测试手动触发配置验证
        assertDoesNotThrow(() -> {
            securityConfigValidator.validateConfiguration();
        }, "手动配置验证应该成功");
    }

    @Test
    @DisplayName("测试必需配置验证")
    void testRequiredConfigValidation() {
        // 在测试环境中，JWT配置应该存在
        assertDoesNotThrow(() -> {
            // 通过反射调用私有方法进行测试
            java.lang.reflect.Method method = SecurityConfigValidator.class
                    .getDeclaredMethod("validateRequiredConfigs");
            method.setAccessible(true);
            method.invoke(securityConfigValidator);
        }, "必需配置验证应该通过");
    }

    @Test
    @DisplayName("测试不安全默认值检查")
    void testInsecureDefaultsCheck() {
        // 测试不安全默认值检查
        assertDoesNotThrow(() -> {
            // 通过反射调用私有方法进行测试
            java.lang.reflect.Method method = SecurityConfigValidator.class
                    .getDeclaredMethod("checkInsecureDefaults");
            method.setAccessible(true);
            method.invoke(securityConfigValidator);
        }, "不安全默认值检查应该正常执行");
    }

    @Test
    @DisplayName("测试配置格式验证")
    void testConfigFormatValidation() {
        // 测试配置格式验证
        assertDoesNotThrow(() -> {
            // 通过反射调用私有方法进行测试
            java.lang.reflect.Method method = SecurityConfigValidator.class
                    .getDeclaredMethod("validateConfigFormats");
            method.setAccessible(true);
            method.invoke(securityConfigValidator);
        }, "配置格式验证应该通过");
    }

    @Test
    @DisplayName("测试JWT配置验证")
    void testJwtConfigValidation() {
        // 测试JWT配置验证
        assertDoesNotThrow(() -> {
            // 通过反射调用私有方法进行测试
            java.lang.reflect.Method method = SecurityConfigValidator.class
                    .getDeclaredMethod("validateJwtConfig");
            method.setAccessible(true);
            method.invoke(securityConfigValidator);
        }, "JWT配置验证应该通过");
    }

    @Test
    @DisplayName("测试JWT安全级别检查")
    void testJwtSecurityLevelCheck() {
        // 测试JWT安全级别检查
        assertDoesNotThrow(() -> {
            // 通过反射调用私有方法进行测试
            java.lang.reflect.Method method = SecurityConfigValidator.class
                    .getDeclaredMethod("checkJwtSecurityLevel");
            method.setAccessible(true);
            method.invoke(securityConfigValidator);
        }, "JWT安全级别检查应该正常执行");
    }

    @Test
    @DisplayName("测试数据库安全检查")
    void testDatabaseSecurityCheck() {
        // 测试数据库安全检查
        assertDoesNotThrow(() -> {
            // 通过反射调用私有方法进行测试
            java.lang.reflect.Method method = SecurityConfigValidator.class
                    .getDeclaredMethod("checkDatabaseSecurity");
            method.setAccessible(true);
            method.invoke(securityConfigValidator);
        }, "数据库安全检查应该正常执行");
    }

    @Test
    @DisplayName("测试配置摘要输出")
    void testConfigSummaryOutput() {
        // 测试配置摘要输出
        assertDoesNotThrow(() -> {
            // 通过反射调用私有方法进行测试
            java.lang.reflect.Method method = SecurityConfigValidator.class
                    .getDeclaredMethod("logConfigSummary");
            method.setAccessible(true);
            method.invoke(securityConfigValidator);
        }, "配置摘要输出应该正常执行");
    }

    @Test
    @DisplayName("测试JWT密钥强度验证")
    void testJwtKeyStrengthValidation() {
        // 在测试环境中，JWT密钥可能不存在
        try {
            String jwtSecret = secureConfigManager.getJwtSecret();

            // 验证JWT密钥长度
            assertTrue(jwtSecret.length() >= 32,
                    "JWT密钥长度应该至少32位");

            // 验证JWT密钥不包含常见的不安全字符串
            String lowerSecret = jwtSecret.toLowerCase();
            String[] insecurePatterns = {"secret", "key", "password", "123456", "admin", "test"};

            boolean hasInsecurePattern = false;
            for (String pattern : insecurePatterns) {
                if (lowerSecret.contains(pattern)) {
                    hasInsecurePattern = true;
                    System.out.printf("警告：JWT密钥包含不安全字符串: %s%n", pattern);
                    break;
                }
            }

            // 在测试环境中，我们允许包含test等字符串
            // 但在生产环境中应该避免
        } catch (IllegalStateException e) {
            // 在测试环境中，JWT密钥未配置是正常的
            assertTrue(e.getMessage().contains("JWT密钥未配置"), "应该抛出JWT密钥未配置的异常");
        }
    }

    @Test
    @DisplayName("测试配置验证的异常处理")
    void testConfigValidationExceptionHandling() {
        // 测试配置验证过程中的异常处理
        assertDoesNotThrow(() -> {
            securityConfigValidator.validateConfiguration();
        }, "配置验证应该能够处理异常情况");
    }

    @Test
    @DisplayName("测试配置验证的性能")
    void testConfigValidationPerformance() {
        // 测试配置验证的性能
        long startTime = System.currentTimeMillis();
        
        // 执行多次配置验证
        for (int i = 0; i < 100; i++) {
            securityConfigValidator.validateConfiguration();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 100次配置验证应该在合理时间内完成
        assertTrue(duration < 5000, 
                String.format("配置验证性能测试失败，耗时: %d ms", duration));
        
        System.out.printf("配置验证性能测试通过，100次验证耗时: %d ms%n", duration);
    }

    @Test
    @DisplayName("测试配置验证的幂等性")
    void testConfigValidationIdempotency() {
        // 测试多次配置验证的幂等性
        assertDoesNotThrow(() -> {
            securityConfigValidator.validateConfiguration();
            securityConfigValidator.validateConfiguration();
            securityConfigValidator.validateConfiguration();
        }, "多次配置验证应该是幂等的");
    }

    @Test
    @DisplayName("测试配置验证的线程安全性")
    void testConfigValidationThreadSafety() throws Exception {
        // 测试并发配置验证的线程安全性
        Thread[] threads = new Thread[5];
        Exception[] exceptions = new Exception[5];
        
        for (int i = 0; i < 5; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                try {
                    securityConfigValidator.validateConfiguration();
                } catch (Exception e) {
                    exceptions[index] = e;
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join(5000);
        }
        
        // 检查是否有异常
        for (int i = 0; i < 5; i++) {
            assertNull(exceptions[i], "线程 " + i + " 出现异常: " + 
                      (exceptions[i] != null ? exceptions[i].getMessage() : ""));
        }
    }

    @Test
    @DisplayName("测试配置验证的完整性")
    void testConfigValidationCompleteness() {
        // 验证配置验证器检查了所有必要的配置项
        assertDoesNotThrow(() -> {
            securityConfigValidator.validateConfiguration();
            
            // 验证JWT配置
            assertNotNull(secureConfigManager.getJwtExpiration());
            assertNotNull(secureConfigManager.getJwtIssuer());
            
            // 验证Redis配置
            assertNotNull(secureConfigManager.getRedisUrl());
            
            // 验证配置摘要
            String configSummary = secureConfigManager.getConfigSummary();
            assertNotNull(configSummary);
            assertFalse(configSummary.trim().isEmpty());
            
        }, "配置验证应该检查所有必要的配置项");
    }

    @Test
    @DisplayName("测试配置验证的日志输出")
    void testConfigValidationLogging() {
        // 测试配置验证过程中的日志输出
        // 这里主要是确保日志方法不会抛出异常
        assertDoesNotThrow(() -> {
            securityConfigValidator.validateConfiguration();
        }, "配置验证的日志输出应该正常");
    }

    @Test
    @DisplayName("测试配置验证的边界情况")
    void testConfigValidationEdgeCases() {
        // 测试配置验证的边界情况
        assertDoesNotThrow(() -> {
            // 多次快速调用配置验证
            for (int i = 0; i < 10; i++) {
                securityConfigValidator.validateConfiguration();
            }
        }, "快速多次配置验证应该正常");
    }
}
