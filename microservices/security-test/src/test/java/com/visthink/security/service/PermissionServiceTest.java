package com.visthink.security.service;

import com.visthink.security.context.TenantContext;
import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;

import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 权限服务测试类
 * 
 * 测试PermissionService的核心功能：
 * 1. hasPermission()方法的权限验证逻辑
 * 2. 不同角色的权限检查
 * 3. 权限格式验证
 * 4. 批量权限验证
 * 5. 边界情况测试
 * 
 * <AUTHOR>
 */
@QuarkusTest
@DisplayName("权限服务测试")
class PermissionServiceTest {

    @Inject
    PermissionService permissionService;

    @Inject
    TenantContext tenantContext;

    // 模拟JWT token
    private JsonWebToken mockJwt;

    @BeforeEach
    void setUp() {
        // 清理租户上下文
        tenantContext.clear();
        
        // 创建模拟JWT token
        mockJwt = mock(JsonWebToken.class);
        
        // 设置测试租户
        tenantContext.setCurrentTenantId(100L);
    }

    @AfterEach
    void tearDown() {
        // 清理测试环境
        tenantContext.clear();
        reset(mockJwt);
    }

    @Test
    @DisplayName("测试系统管理员权限 - 应该拥有所有权限")
    void testSystemAdminPermissions() {
        // 模拟系统管理员角色
        when(mockJwt.getGroups()).thenReturn(Set.of("SYSTEM_ADMIN"));
        
        // 测试各种权限
        String[] permissions = {
            "user:read", "user:create", "user:update", "user:delete",
            "product:read", "product:create", "product:update", "product:delete",
            "order:read", "order:create", "order:update", "order:delete",
            "system:config", "tenant:create", "tenant:delete"
        };
        
        for (String permission : permissions) {
            Boolean hasPermission = createMockPermissionService(Set.of("SYSTEM_ADMIN"))
                    .hasPermission(permission)
                    .await().indefinitely();
            
            assertTrue(hasPermission, 
                    String.format("系统管理员应该拥有权限: %s", permission));
        }
    }

    @Test
    @DisplayName("测试租户管理员权限 - 应该有租户内管理权限")
    void testTenantAdminPermissions() {
        // 测试租户管理员有权限的操作
        String[] allowedPermissions = {
            "user:read", "user:create", "user:update", "user:delete",
            "product:read", "product:create", "product:update", "product:delete",
            "order:read", "order:create", "order:update", "order:delete",
            "customer:read", "customer:create", "customer:update", "customer:delete",
            "inventory:read", "inventory:create", "inventory:update", "inventory:delete"
        };
        
        for (String permission : allowedPermissions) {
            Boolean hasPermission = createMockPermissionService(Set.of("TENANT_ADMIN"))
                    .hasPermission(permission)
                    .await().indefinitely();
            
            assertTrue(hasPermission, 
                    String.format("租户管理员应该拥有权限: %s", permission));
        }
        
        // 测试租户管理员没有权限的操作
        String[] deniedPermissions = {
            "system:config", "tenant:create", "tenant:delete"
        };
        
        for (String permission : deniedPermissions) {
            Boolean hasPermission = createMockPermissionService(Set.of("TENANT_ADMIN"))
                    .hasPermission(permission)
                    .await().indefinitely();
            
            assertFalse(hasPermission, 
                    String.format("租户管理员不应该拥有权限: %s", permission));
        }
    }

    @Test
    @DisplayName("测试普通用户权限 - 应该只有基本读取权限")
    void testUserPermissions() {
        // 测试普通用户有权限的操作
        String[] allowedPermissions = {
            "product:read", "inventory:read", "order:read", "customer:read", "user:update"
        };
        
        for (String permission : allowedPermissions) {
            Boolean hasPermission = createMockPermissionService(Set.of("USER"))
                    .hasPermission(permission)
                    .await().indefinitely();
            
            assertTrue(hasPermission, 
                    String.format("普通用户应该拥有权限: %s", permission));
        }
        
        // 测试普通用户没有权限的操作
        String[] deniedPermissions = {
            "user:create", "user:delete", 
            "product:create", "product:update", "product:delete",
            "order:create", "order:update", "order:delete",
            "system:config", "tenant:create"
        };
        
        for (String permission : deniedPermissions) {
            Boolean hasPermission = createMockPermissionService(Set.of("USER"))
                    .hasPermission(permission)
                    .await().indefinitely();
            
            assertFalse(hasPermission, 
                    String.format("普通用户不应该拥有权限: %s", permission));
        }
    }

    @Test
    @DisplayName("测试批量权限验证 - hasAllPermissions")
    void testHasAllPermissions() {
        // 测试系统管理员的批量权限
        Boolean hasAllPermissions = createMockPermissionService(Set.of("SYSTEM_ADMIN"))
                .hasAllPermissions("user:read", "user:create", "user:update", "user:delete")
                .await().indefinitely();
        
        assertTrue(hasAllPermissions, "系统管理员应该拥有所有用户管理权限");
        
        // 测试普通用户的批量权限（应该失败）
        Boolean userHasAllPermissions = createMockPermissionService(Set.of("USER"))
                .hasAllPermissions("user:read", "user:create", "user:delete")
                .await().indefinitely();
        
        assertFalse(userHasAllPermissions, "普通用户不应该拥有所有用户管理权限");
        
        // 测试普通用户的部分权限（应该成功）
        Boolean userHasReadPermissions = createMockPermissionService(Set.of("USER"))
                .hasAllPermissions("product:read", "order:read")
                .await().indefinitely();
        
        assertTrue(userHasReadPermissions, "普通用户应该拥有基本读取权限");
    }

    @Test
    @DisplayName("测试任一权限验证 - hasAnyPermission")
    void testHasAnyPermission() {
        // 测试普通用户的任一权限（应该成功）
        Boolean hasAnyPermission = createMockPermissionService(Set.of("USER"))
                .hasAnyPermission("user:create", "user:delete", "product:read")
                .await().indefinitely();

        assertTrue(hasAnyPermission, "普通用户应该至少拥有product:read权限");

        // 测试普通用户的任一权限（应该失败）
        Boolean hasNoPermission = createMockPermissionService(Set.of("USER"))
                .hasAnyPermission("user:create", "user:delete", "system:config")
                .await().indefinitely();

        assertFalse(hasNoPermission, "普通用户不应该拥有这些管理权限");
    }

    @Test
    @DisplayName("测试批量权限验证的格式检查")
    void testBatchPermissionFormatValidation() {
        PermissionService mockService = createMockPermissionService(Set.of("SYSTEM_ADMIN"));

        // 测试hasAllPermissions - 包含无效格式应该返回false
        Boolean hasAllInvalid = mockService.hasAllPermissions("user:read", "invalid", "product:create")
                .await().indefinitely();
        assertFalse(hasAllInvalid, "包含无效格式的批量权限验证应该返回false");

        // 测试hasAnyPermission - 包含无效格式应该返回false
        Boolean hasAnyInvalid = mockService.hasAnyPermission("invalid", "user:", ":read")
                .await().indefinitely();
        assertFalse(hasAnyInvalid, "全部为无效格式的任一权限验证应该返回false");

        // 测试hasAllPermissions - 全部有效格式应该返回true（系统管理员）
        Boolean hasAllValid = mockService.hasAllPermissions("user:read", "product:create", "order:update")
                .await().indefinitely();
        assertTrue(hasAllValid, "全部有效格式的批量权限验证应该返回true（系统管理员）");

        // 测试hasAnyPermission - 全部有效格式应该返回true（系统管理员）
        Boolean hasAnyValid = mockService.hasAnyPermission("user:read", "product:create", "order:update")
                .await().indefinitely();
        assertTrue(hasAnyValid, "全部有效格式的任一权限验证应该返回true（系统管理员）");
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "", "   ", "invalid", "user", "user:", ":read", "user:read:extra", "user::read", "user:read:", ":user:read"
    })
    @DisplayName("测试权限格式验证 - 无效格式应该返回false（即使是系统管理员）")
    void testInvalidPermissionFormats(String invalidPermission) {
        // 即使是系统管理员，无效格式也应该返回false
        Boolean hasPermission = createMockPermissionService(Set.of("SYSTEM_ADMIN"))
                .hasPermission(invalidPermission)
                .await().indefinitely();

        assertFalse(hasPermission,
                String.format("无效权限格式应该返回false（即使是系统管理员）: '%s'", invalidPermission));

        // 普通用户也应该返回false
        Boolean userHasPermission = createMockPermissionService(Set.of("USER"))
                .hasPermission(invalidPermission)
                .await().indefinitely();

        assertFalse(userHasPermission,
                String.format("无效权限格式应该返回false（普通用户）: '%s'", invalidPermission));
    }

    @Test
    @DisplayName("测试空权限参数")
    void testNullPermission() {
        Boolean hasPermission = createMockPermissionService(Set.of("SYSTEM_ADMIN"))
                .hasPermission(null)
                .await().indefinitely();
        
        assertFalse(hasPermission, "空权限应该返回false");
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "user:read", "product:create", "order:update", "inventory:delete", "customer:export",
        "user-profile:read", "order_item:create", "product-category:update"
    })
    @DisplayName("测试有效权限格式")
    void testValidPermissionFormats(String validPermission) {
        Boolean hasPermission = createMockPermissionService(Set.of("SYSTEM_ADMIN"))
                .hasPermission(validPermission)
                .await().indefinitely();

        assertTrue(hasPermission,
                String.format("系统管理员应该拥有有效权限: %s", validPermission));
    }

    @Test
    @DisplayName("测试权限格式验证的边界情况")
    void testPermissionFormatEdgeCases() {
        PermissionService mockService = createMockPermissionService(Set.of("SYSTEM_ADMIN"));

        // 测试包含特殊字符的无效格式
        String[] invalidFormats = {
            "user@read", "user:read!", "user read", "user#read", "user$read",
            "user%read", "user^read", "user&read", "user*read", "user(read)",
            "user[read]", "user{read}", "user|read", "user\\read", "user/read"
        };

        for (String invalidFormat : invalidFormats) {
            Boolean hasPermission = mockService.hasPermission(invalidFormat).await().indefinitely();
            assertFalse(hasPermission,
                    String.format("包含特殊字符的权限格式应该无效: '%s'", invalidFormat));
        }

        // 测试有效的标识符字符
        String[] validFormats = {
            "user:read", "user_profile:read", "user-profile:read",
            "user123:read", "user_123:read", "user-123:read"
        };

        for (String validFormat : validFormats) {
            Boolean hasPermission = mockService.hasPermission(validFormat).await().indefinitely();
            assertTrue(hasPermission,
                    String.format("有效的权限格式应该被接受: '%s'", validFormat));
        }
    }

    @Test
    @DisplayName("测试权限验证性能")
    void testPermissionPerformance() {
        PermissionService mockService = createMockPermissionService(Set.of("USER"));
        
        // 测试1000次权限验证的性能
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 1000; i++) {
            mockService.hasPermission("product:read").await().indefinitely();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 验证性能（1000次调用应该在5秒内完成）
        assertTrue(duration < 5000, 
                String.format("权限验证性能测试失败，耗时: %d ms", duration));
        
        System.out.printf("权限验证性能测试通过，1000次调用耗时: %d ms%n", duration);
    }

    @Test
    @DisplayName("测试并发权限验证")
    void testConcurrentPermissionValidation() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        try {
            // 提交100个并发权限验证任务
            CompletableFuture<?>[] futures = new CompletableFuture[100];
            
            for (int i = 0; i < 100; i++) {
                futures[i] = CompletableFuture.runAsync(() -> {
                    try {
                        PermissionService mockService = createMockPermissionService(Set.of("USER"));
                        Boolean hasPermission = mockService.hasPermission("product:read")
                                .await().indefinitely();
                        
                        if (hasPermission) {
                            successCount.incrementAndGet();
                        } else {
                            errorCount.incrementAndGet();
                        }
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                    }
                }, executor);
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
            
            // 验证结果
            assertEquals(100, successCount.get(), "所有并发权限验证都应该成功");
            assertEquals(0, errorCount.get(), "不应该有错误发生");
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    @Test
    @DisplayName("测试租户权限验证")
    void testTenantPermissionValidation() {
        // 设置当前租户
        tenantContext.setCurrentTenantId(100L);
        
        PermissionService mockService = createMockPermissionService(Set.of("USER"));
        
        // 测试访问自己租户的数据
        Boolean hasAccess = mockService.hasPermissionForTenant(100L, "product:read")
                .await().indefinitely();
        assertTrue(hasAccess, "应该可以访问自己租户的数据");
        
        // 测试访问其他租户的数据
        Boolean noAccess = mockService.hasPermissionForTenant(200L, "product:read")
                .await().indefinitely();
        assertFalse(noAccess, "不应该可以访问其他租户的数据");
    }

    /**
     * 创建模拟的权限服务
     */
    private PermissionService createMockPermissionService(Set<String> roles) {
        PermissionService mockService = new PermissionService() {
            @Override
            protected Uni<Set<String>> getCurrentUserRoles() {
                return Uni.createFrom().item(roles);
            }
        };

        // 注入依赖
        try {
            java.lang.reflect.Field tenantContextField = PermissionService.class.getDeclaredField("tenantContext");
            tenantContextField.setAccessible(true);
            tenantContextField.set(mockService, tenantContext);
        } catch (Exception e) {
            // 忽略注入失败
        }

        return mockService;
    }
}
