package com.visthink.security.context;

import io.quarkus.test.junit.QuarkusTest;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.AfterEach;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 租户上下文测试类
 * 
 * 测试TenantContext的线程安全增强功能：
 * 1. InheritableThreadLocal的子线程继承功能
 * 2. 内存泄漏防护机制
 * 3. 并发访问的线程安全性
 * 4. 租户权限验证
 * 
 * <AUTHOR>
 */
@QuarkusTest
@DisplayName("租户上下文测试")
class TenantContextTest {

    @Inject
    TenantContext tenantContext;

    @BeforeEach
    void setUp() {
        // 清理租户上下文
        tenantContext.clear();
    }

    @AfterEach
    void tearDown() {
        // 测试后清理
        tenantContext.clear();
    }

    @Test
    @DisplayName("测试基本租户ID设置和获取")
    void testBasicTenantIdOperations() {
        // 测试设置租户ID
        tenantContext.setCurrentTenantId(100L);
        
        // 测试同步获取
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        assertEquals(100L, tenantId);
        
        // 测试响应式获取
        Long asyncTenantId = tenantContext.getCurrentTenantId().await().indefinitely();
        assertEquals(100L, asyncTenantId);
        
        // 测试Optional获取
        assertTrue(tenantContext.getCurrentTenantIdOptional().isPresent());
        assertEquals(100L, tenantContext.getCurrentTenantIdOptional().get());
    }

    @Test
    @DisplayName("测试默认租户ID处理")
    void testDefaultTenantId() {
        // 清理上下文
        tenantContext.clear();
        
        // 获取租户ID应该返回默认值
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        assertEquals(TenantContext.getDefaultTenantId(), tenantId);
        
        // 测试默认租户判断
        assertTrue(tenantContext.isDefaultTenant());
        assertFalse(tenantContext.isPlatformAdmin());
    }

    @Test
    @DisplayName("测试平台管理员上下文")
    void testPlatformAdminContext() {
        // 设置为平台管理员
        tenantContext.setPlatformAdminContext();
        
        // 验证平台管理员状态
        assertTrue(tenantContext.isPlatformAdmin());
        assertFalse(tenantContext.isDefaultTenant());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        assertEquals(TenantContext.getPlatformAdminTenantId(), tenantId);
    }

    @Test
    @DisplayName("测试租户信息设置和获取")
    void testTenantInfo() {
        // 设置租户信息
        tenantContext.setTenantInfo(200L, "test-tenant", "测试租户");
        
        // 获取租户信息
        TenantContext.TenantInfo tenantInfo = tenantContext.getCurrentTenantInfo();
        assertNotNull(tenantInfo);
        assertEquals(200L, tenantInfo.getTenantId());
        assertEquals("test-tenant", tenantInfo.getTenantCode());
        assertEquals("测试租户", tenantInfo.getTenantName());
    }

    @Test
    @DisplayName("测试子线程继承租户上下文")
    void testThreadInheritance() throws Exception {
        // 在主线程设置租户ID
        tenantContext.setCurrentTenantId(300L);
        
        // 用于存储子线程结果
        AtomicReference<Long> childThreadTenantId = new AtomicReference<>();
        AtomicReference<Exception> childThreadException = new AtomicReference<>();
        
        // 创建子线程
        Thread childThread = new Thread(() -> {
            try {
                // 子线程应该继承父线程的租户ID
                Long tenantId = tenantContext.getCurrentTenantIdSync();
                childThreadTenantId.set(tenantId);
            } catch (Exception e) {
                childThreadException.set(e);
            }
        });
        
        childThread.start();
        childThread.join(5000); // 等待最多5秒
        
        // 验证子线程继承了租户ID
        assertNull(childThreadException.get(), "子线程执行出现异常");
        assertEquals(300L, childThreadTenantId.get(), "子线程未正确继承租户ID");
    }

    @Test
    @DisplayName("测试并发访问的线程安全性（1000次并发操作）")
    void testConcurrentAccess() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        try {
            // 提交1000个并发任务
            CompletableFuture<?>[] futures = new CompletableFuture[1000];
            
            for (int i = 0; i < 1000; i++) {
                final int tenantId = i + 1000;
                futures[i] = CompletableFuture.runAsync(() -> {
                    try {
                        // 每个线程设置不同的租户ID
                        tenantContext.setCurrentTenantId((long) tenantId);
                        
                        // 短暂等待
                        Thread.sleep(1);
                        
                        // 验证租户ID是否正确
                        Long currentTenantId = tenantContext.getCurrentTenantIdSync();
                        if (currentTenantId.equals((long) tenantId)) {
                            successCount.incrementAndGet();
                        } else {
                            errorCount.incrementAndGet();
                        }
                        
                        // 清理上下文
                        tenantContext.clear();
                        
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                    }
                }, executor);
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures).get(30, TimeUnit.SECONDS);
            
            // 验证结果
            assertEquals(0, errorCount.get(), "并发访问出现错误");
            assertTrue(successCount.get() > 0, "没有成功的并发操作");
            
            System.out.printf("并发测试完成：成功 %d 次，错误 %d 次%n", 
                    successCount.get(), errorCount.get());
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(10, TimeUnit.SECONDS);
        }
    }

    @Test
    @DisplayName("测试租户访问权限验证")
    void testTenantAccessValidation() {
        // 设置当前租户
        tenantContext.setCurrentTenantId(400L);
        
        // 测试访问自己租户的数据
        Boolean hasAccess = tenantContext.validateTenantAccess(400L, "read").await().indefinitely();
        assertTrue(hasAccess, "应该可以访问自己租户的数据");
        
        // 测试访问其他租户的数据
        Boolean noAccess = tenantContext.validateTenantAccess(500L, "read").await().indefinitely();
        assertFalse(noAccess, "不应该可以访问其他租户的数据");
        
        // 测试平台管理员访问
        tenantContext.setPlatformAdminContext();
        Boolean adminAccess = tenantContext.validateTenantAccess(500L, "read").await().indefinitely();
        assertTrue(adminAccess, "平台管理员应该可以访问所有租户数据");
    }

    @Test
    @DisplayName("测试跨租户操作权限验证")
    void testCrossTenantAccessValidation() {
        // 普通租户不应该有跨租户权限
        tenantContext.setCurrentTenantId(600L);
        Boolean noCrossAccess = tenantContext.validateCrossTenantAccess("export").await().indefinitely();
        assertFalse(noCrossAccess, "普通租户不应该有跨租户操作权限");
        
        // 平台管理员应该有跨租户权限
        tenantContext.setPlatformAdminContext();
        Boolean adminCrossAccess = tenantContext.validateCrossTenantAccess("export").await().indefinitely();
        assertTrue(adminCrossAccess, "平台管理员应该有跨租户操作权限");
    }

    @Test
    @DisplayName("测试在指定租户上下文中执行操作")
    void testRunInTenantContext() {
        // 设置初始租户
        tenantContext.setCurrentTenantId(700L);
        
        // 在另一个租户上下文中执行操作
        String result = tenantContext.runInTenantContext(800L, () -> {
            Long currentTenantId = tenantContext.getCurrentTenantIdSync();
            return Uni.createFrom().item("Tenant: " + currentTenantId);
        }).await().indefinitely();
        
        assertEquals("Tenant: 800", result);
        
        // 验证原始租户上下文已恢复
        Long restoredTenantId = tenantContext.getCurrentTenantIdSync();
        assertEquals(700L, restoredTenantId);
    }

    @Test
    @DisplayName("测试在平台管理员上下文中执行操作")
    void testRunAsPlatformAdmin() {
        // 设置普通租户
        tenantContext.setCurrentTenantId(900L);
        
        // 在平台管理员上下文中执行操作
        Boolean isPlatformAdmin = tenantContext.runAsPlatformAdmin(() -> {
            return Uni.createFrom().item(tenantContext.isPlatformAdmin());
        }).await().indefinitely();
        
        assertTrue(isPlatformAdmin);
        
        // 验证原始租户上下文已恢复
        Long restoredTenantId = tenantContext.getCurrentTenantIdSync();
        assertEquals(900L, restoredTenantId);
        assertFalse(tenantContext.isPlatformAdmin());
    }

    @Test
    @DisplayName("测试内存泄漏防护机制")
    void testMemoryLeakPrevention() {
        // 创建大量租户上下文
        for (int i = 0; i < 1000; i++) {
            tenantContext.setCurrentTenantId((long) i);
            tenantContext.setTenantInfo((long) i, "tenant-" + i, "租户" + i);
        }
        
        // 清理上下文
        tenantContext.clear();
        
        // 验证清理后的状态
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        assertEquals(TenantContext.getDefaultTenantId(), tenantId);
        
        // 手动触发垃圾回收
        System.gc();
        
        // 验证没有内存泄漏（这里主要是确保清理方法正常工作）
        assertDoesNotThrow(() -> {
            for (int i = 0; i < 100; i++) {
                tenantContext.setCurrentTenantId((long) i);
                tenantContext.clear();
            }
        });
    }

    @Test
    @DisplayName("测试TenantInfo复制构造函数的正确性")
    void testTenantInfoCopyConstructor() {
        // 创建原始租户信息
        TenantContext.TenantInfo original = new TenantContext.TenantInfo();
        original.setTenantId(1000L);
        original.setTenantCode("original-tenant");
        original.setTenantName("原始租户");
        original.setTenantType("enterprise");
        original.setActive(true);
        
        // 使用复制构造函数创建副本
        TenantContext.TenantInfo copy = new TenantContext.TenantInfo(original);
        
        // 验证副本内容正确
        assertEquals(original.getTenantId(), copy.getTenantId());
        assertEquals(original.getTenantCode(), copy.getTenantCode());
        assertEquals(original.getTenantName(), copy.getTenantName());
        assertEquals(original.getTenantType(), copy.getTenantType());
        assertEquals(original.getActive(), copy.getActive());
        
        // 验证是不同的对象实例
        assertNotSame(original, copy);
        
        // 修改原始对象不应影响副本
        original.setTenantId(2000L);
        assertEquals(1000L, copy.getTenantId());
    }

    @Test
    @DisplayName("测试空值处理")
    void testNullValueHandling() {
        // 测试设置null租户ID
        tenantContext.setCurrentTenantId(null);
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        assertEquals(TenantContext.getDefaultTenantId(), tenantId);
        
        // 测试空的租户信息
        tenantContext.setTenantInfo(null, null, null);
        TenantContext.TenantInfo tenantInfo = tenantContext.getCurrentTenantInfo();
        assertNotNull(tenantInfo);
        assertNull(tenantInfo.getTenantId());
    }

    @Test
    @DisplayName("测试租户上下文性能")
    void testTenantContextPerformance() {
        // 测试大量租户上下文操作的性能
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 10000; i++) {
            tenantContext.setCurrentTenantId((long) i);
            tenantContext.getCurrentTenantIdSync();
            tenantContext.getCurrentTenantId().await().indefinitely();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 10000次操作应该在合理时间内完成
        assertTrue(duration < 5000, 
                String.format("租户上下文性能测试失败，耗时: %d ms", duration));
        
        System.out.printf("租户上下文性能测试通过，10000次操作耗时: %d ms%n", duration);
    }
}
