package com.visthink.security.config;

import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.condition.DisabledIfSystemProperty;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全配置管理器测试类
 * 
 * 测试SecureConfigManager的功能：
 * 1. 环境变量读取功能
 * 2. 配置验证逻辑
 * 3. 默认值处理
 * 4. 安全检查
 * 5. 线程安全性
 * 6. 配置缓存机制
 * 
 * <AUTHOR>
 */
@QuarkusTest
@DisplayName("安全配置管理器测试")
class SecureConfigManagerTest {

    @Inject
    SecureConfigManager secureConfigManager;

    @Test
    @DisplayName("测试JWT配置获取")
    void testJwtConfiguration() {
        // 测试JWT过期时间
        Long jwtExpiration = secureConfigManager.getJwtExpiration();
        assertNotNull(jwtExpiration);
        assertTrue(jwtExpiration > 0, "JWT过期时间应该大于0");
        
        // 测试JWT签发者
        String jwtIssuer = secureConfigManager.getJwtIssuer();
        assertNotNull(jwtIssuer);
        assertFalse(jwtIssuer.trim().isEmpty(), "JWT签发者不能为空");
    }

    @Test
    @DisplayName("测试JWT密钥验证 - 使用测试配置")
    void testJwtSecretWithTestConfig() {
        // 在测试环境中，JWT密钥可能不存在，这是正常的
        // 我们测试方法的正确性而不是配置的存在性
        try {
            String jwtSecret = secureConfigManager.getJwtSecret();
            assertNotNull(jwtSecret);
            assertFalse(jwtSecret.trim().isEmpty());
            assertTrue(jwtSecret.length() >= 32, "JWT密钥长度应该至少32位");
        } catch (IllegalStateException e) {
            // 在测试环境中，JWT密钥未配置是正常的
            assertTrue(e.getMessage().contains("JWT密钥未配置"), "应该抛出JWT密钥未配置的异常");
        }
    }

    @Test
    @DisplayName("测试配置存在性检查")
    void testConfigExistence() {
        // 在测试环境中，某些配置可能不存在，这是正常的
        // 我们主要测试方法的正确性而不是配置的存在性

        // 测试不存在的配置
        assertFalse(secureConfigManager.hasConfig("non_existent_config"), "不存在的配置应该返回false");

        // 测试方法的正确性
        assertDoesNotThrow(() -> {
            secureConfigManager.hasConfig("jwt_secret");
            secureConfigManager.hasConfig("redis_url");
        }, "配置检查方法应该正常工作");
    }

    @Test
    @DisplayName("测试Redis配置获取")
    void testRedisConfiguration() {
        // 测试Redis URL（有默认值）
        String redisUrl = secureConfigManager.getRedisUrl();
        assertNotNull(redisUrl);
        assertFalse(redisUrl.trim().isEmpty());
        
        // 测试Redis密码（可选）
        assertDoesNotThrow(() -> {
            secureConfigManager.getRedisPassword();
        });
    }

    @Test
    @DisplayName("测试邮件配置获取")
    void testMailConfiguration() {
        // 邮件配置是可选的，不应该抛出异常
        assertDoesNotThrow(() -> {
            secureConfigManager.getMailUsername();
            secureConfigManager.getMailPassword();
            secureConfigManager.getMailSmtpHost();
        });
    }

    @Test
    @DisplayName("测试短信配置获取")
    void testSmsConfiguration() {
        // 短信配置是可选的，不应该抛出异常
        assertDoesNotThrow(() -> {
            secureConfigManager.getSmsAccessKey();
            secureConfigManager.getSmsSecretKey();
        });
    }

    @Test
    @DisplayName("测试配置摘要生成")
    void testConfigSummary() {
        String configSummary = secureConfigManager.getConfigSummary();
        assertNotNull(configSummary);
        assertFalse(configSummary.trim().isEmpty());
        
        // 配置摘要应该包含关键信息但不包含敏感信息
        assertTrue(configSummary.contains("配置摘要"));
        
        // 验证敏感信息被掩码
        if (configSummary.contains("password") || configSummary.contains("secret")) {
            assertTrue(configSummary.contains("****"), "敏感信息应该被掩码");
        }
    }

    @Test
    @DisplayName("测试默认值检查")
    void testDefaultValueCheck() {
        // 默认值检查不应该抛出异常，只是记录警告
        assertDoesNotThrow(() -> {
            secureConfigManager.checkDefaultValues();
        });
    }

    @Test
    @DisplayName("测试密码强度检查")
    void testPasswordStrengthCheck() {
        // 测试强密码
        assertTrue(secureConfigManager.isStrongPassword("StrongP@ssw0rd123"), 
                "包含大小写字母、数字和特殊字符的密码应该被认为是强密码");
        
        // 测试弱密码
        assertFalse(secureConfigManager.isStrongPassword("weak"), 
                "短密码应该被认为是弱密码");
        assertFalse(secureConfigManager.isStrongPassword("password123"), 
                "缺少大写字母和特殊字符的密码应该被认为是弱密码");
        assertFalse(secureConfigManager.isStrongPassword("PASSWORD123"), 
                "缺少小写字母和特殊字符的密码应该被认为是弱密码");
        assertFalse(secureConfigManager.isStrongPassword("Password"), 
                "缺少数字和特殊字符的密码应该被认为是弱密码");
        
        // 测试空密码
        assertFalse(secureConfigManager.isStrongPassword(null), 
                "空密码应该被认为是弱密码");
        assertFalse(secureConfigManager.isStrongPassword(""), 
                "空字符串密码应该被认为是弱密码");
    }

    @Test
    @DisplayName("测试配置的线程安全性")
    void testConfigThreadSafety() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(10);
        Exception[] exceptions = new Exception[10];
        
        try {
            // 测试多线程并发访问配置
            CompletableFuture<?>[] futures = new CompletableFuture[10];
            
            for (int i = 0; i < 10; i++) {
                final int index = i;
                futures[i] = CompletableFuture.runAsync(() -> {
                    try {
                        // 并发访问各种配置
                        secureConfigManager.getJwtExpiration();
                        secureConfigManager.getJwtIssuer();
                        secureConfigManager.getRedisUrl();
                        secureConfigManager.getConfigSummary();
                        secureConfigManager.hasConfig("jwt_secret");
                        secureConfigManager.checkDefaultValues();
                    } catch (Exception e) {
                        exceptions[index] = e;
                    }
                }, executor);
            }
            
            // 等待所有线程完成
            CompletableFuture.allOf(futures).get(10, TimeUnit.SECONDS);
            
            // 检查是否有异常
            for (int i = 0; i < 10; i++) {
                assertNull(exceptions[i], "线程 " + i + " 出现异常: " + 
                          (exceptions[i] != null ? exceptions[i].getMessage() : ""));
            }
            
        } finally {
            executor.shutdown();
            executor.awaitTermination(5, TimeUnit.SECONDS);
        }
    }

    @Test
    @DisplayName("测试配置性能")
    void testConfigPerformance() {
        // 测试配置获取的性能
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 1000; i++) {
            secureConfigManager.getJwtExpiration();
            secureConfigManager.getJwtIssuer();
            secureConfigManager.getRedisUrl();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 1000次配置获取应该在合理时间内完成
        assertTrue(duration < 1000, 
                String.format("配置获取性能测试失败，耗时: %d ms", duration));
        
        System.out.printf("配置获取性能测试通过，1000次操作耗时: %d ms%n", duration);
    }

    @Test
    @DisplayName("测试配置缓存机制")
    void testConfigCaching() {
        // 多次获取相同配置，应该返回相同的值
        Long expiration1 = secureConfigManager.getJwtExpiration();
        Long expiration2 = secureConfigManager.getJwtExpiration();
        assertEquals(expiration1, expiration2);
        
        String issuer1 = secureConfigManager.getJwtIssuer();
        String issuer2 = secureConfigManager.getJwtIssuer();
        assertEquals(issuer1, issuer2);
        
        String redisUrl1 = secureConfigManager.getRedisUrl();
        String redisUrl2 = secureConfigManager.getRedisUrl();
        assertEquals(redisUrl1, redisUrl2);
    }

    @Test
    @DisplayName("测试配置的不可变性")
    void testConfigImmutability() {
        // 获取配置值
        String originalIssuer = secureConfigManager.getJwtIssuer();
        String originalRedisUrl = secureConfigManager.getRedisUrl();
        
        // 尝试修改配置值（这里只是验证获取的是副本）
        assertDoesNotThrow(() -> {
            String modifiedIssuer = originalIssuer + "-modified";
            String modifiedRedisUrl = originalRedisUrl + "-modified";
            
            // 再次获取配置，应该还是原始值
            assertEquals(originalIssuer, secureConfigManager.getJwtIssuer());
            assertEquals(originalRedisUrl, secureConfigManager.getRedisUrl());
        });
    }

    @Test
    @DisplayName("测试配置验证方法")
    void testConfigValidation() {
        // 在测试环境中，某些配置可能不存在，这是正常的
        try {
            secureConfigManager.validateRequiredConfigs();
        } catch (IllegalStateException e) {
            // 在测试环境中，配置验证失败是正常的
            assertTrue(e.getMessage().contains("未配置"), "应该抛出配置未设置的异常");
        }
    }

    @Test
    @DisplayName("测试配置边界情况")
    void testConfigEdgeCases() {
        // 测试不存在的配置键
        assertFalse(secureConfigManager.hasConfig(""), "空配置键应该返回false");
        assertFalse(secureConfigManager.hasConfig("   "), "空白配置键应该返回false");
        assertFalse(secureConfigManager.hasConfig("INVALID_CONFIG_KEY"), "无效配置键应该返回false");

        // 测试配置键的大小写敏感性（不依赖具体配置是否存在）
        assertDoesNotThrow(() -> {
            secureConfigManager.hasConfig("jwt_secret");
            secureConfigManager.hasConfig("JWT_SECRET");
        }, "配置键检查应该正常工作");
    }

    @Test
    @DisplayName("测试模拟环境变量功能")
    void testMockEnvironmentVariable() {
        // 测试模拟环境变量设置（仅用于测试）
        assertDoesNotThrow(() -> {
            secureConfigManager.setMockEnvironmentVariable("TEST_KEY", "test_value");
        });
    }

    @Test
    @DisplayName("测试配置摘要的安全性")
    void testConfigSummarySecurity() {
        String configSummary = secureConfigManager.getConfigSummary();

        // 确保配置摘要不包含完整的敏感信息
        assertFalse(configSummary.contains("test-jwt-secret-key"),
                "配置摘要不应该包含完整的JWT密钥");

        // 配置摘要应该是安全的（不包含敏感信息）
        assertNotNull(configSummary);
        assertFalse(configSummary.trim().isEmpty());
    }

    @Test
    @DisplayName("测试大量配置操作的稳定性")
    void testConfigStability() {
        // 执行大量配置操作，测试稳定性
        assertDoesNotThrow(() -> {
            for (int i = 0; i < 10000; i++) {
                secureConfigManager.getJwtExpiration();
                secureConfigManager.hasConfig("jwt_secret");
                secureConfigManager.getConfigSummary();
                
                if (i % 1000 == 0) {
                    secureConfigManager.checkDefaultValues();
                }
            }
        }, "大量配置操作应该保持稳定");
    }
}
