# 安全功能测试项目配置文件
quarkus:
  application:
    name: security-test
    version: 2.0.0

  # 日志配置
  log:
    level: INFO
    category:
      "com.visthink.security": DEBUG
    console:
      enable: true
      format: "%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n"

  # 测试配置
  test:
    profile: test
    continuous-testing: disabled

# 环境变量配置（用于测试）
JWT_SECRET: "test-jwt-secret-key-for-security-testing-at-least-256-bits-long-and-secure"
JWT_EXPIRATION: "86400000"
JWT_ISSUER: "https://test.visthink.com"

DB_USERNAME: "test_user"
DB_PASSWORD: "test_password_123456"
DB_URL: "vertx-reactive:postgresql://localhost:5432/test_db"

REDIS_URL: "redis://localhost:6379"
REDIS_PASSWORD: "zylp"

# 测试专用配置
test:
  security:
    # 测试用的租户配置
    tenants:
      default: 1
      platform-admin: 0
      test-tenant: 100

    # 测试用的用户角色
    roles:
      system-admin: "SYSTEM_ADMIN"
      tenant-admin: "TENANT_ADMIN"
      user: "USER"

    # 性能测试配置
    performance:
      concurrent-threads: 1000
      test-iterations: 10000
      timeout-seconds: 30

# 模拟环境变量（用于测试）
mock:
  env:
    # 数据库配置
    DB_USERNAME: "test_user"
    DB_PASSWORD: "test_password_123456"
    DB_URL: "vertx-reactive:postgresql://localhost:5432/test_db"

    # JWT配置
    JWT_SECRET: "test-jwt-secret-key-for-security-testing-at-least-256-bits-long-and-secure"
    JWT_EXPIRATION: "86400000"
    JWT_ISSUER: "https://test.visthink.com"

    # Redis配置
    REDIS_URL: "redis://localhost:6379"
    REDIS_PASSWORD: "test_redis_password"

    # 邮件配置
    MAIL_USERNAME: "<EMAIL>"
    MAIL_PASSWORD: "test_mail_password"
    MAIL_SMTP_HOST: "smtp.test.com"

    # 短信配置
    SMS_ACCESS_KEY: "test_sms_access_key"
    SMS_SECRET_KEY: "test_sms_secret_key"
