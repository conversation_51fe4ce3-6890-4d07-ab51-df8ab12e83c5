package com.visthink.security.context;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.util.Optional;
import java.util.function.Supplier;

/**
 * 租户上下文管理器
 * 
 * 提供线程安全的租户信息管理，支持多租户数据隔离
 * 增强了线程安全性和内存泄漏防护机制
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class TenantContext {

    private static final Logger LOG = Logger.getLogger(TenantContext.class);

    /**
     * 默认租户ID
     */
    private static final Long DEFAULT_TENANT_ID = 1L;

    /**
     * 平台管理员租户ID
     */
    private static final Long PLATFORM_ADMIN_TENANT_ID = 0L;

    /**
     * 线程本地存储租户信息
     * 使用InheritableThreadLocal支持子线程继承，并添加内存泄漏防护
     */
    private static final ThreadLocal<TenantInfo> TENANT_HOLDER = new InheritableThreadLocal<TenantInfo>() {
        @Override
        protected TenantInfo initialValue() {
            return null;
        }
        
        @Override
        protected TenantInfo childValue(TenantInfo parentValue) {
            // 子线程继承父线程的租户信息
            return parentValue != null ? new TenantInfo(parentValue) : null;
        }
    };

    /**
     * 线程清理任务，防止内存泄漏
     */
    private static final ThreadLocal<Boolean> CLEANUP_MARKER = new ThreadLocal<>();

    /**
     * 设置当前租户ID
     * 
     * @param tenantId 租户ID
     */
    public void setCurrentTenantId(Long tenantId) {
        if (tenantId == null) {
            LOG.warn("尝试设置空的租户ID，使用默认租户ID");
            tenantId = DEFAULT_TENANT_ID;
        }
        
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId(tenantId);
        tenantInfo.setActive(true);
        
        TENANT_HOLDER.set(tenantInfo);
        CLEANUP_MARKER.set(true);
        
        LOG.debugf("设置当前租户ID: %d", tenantId);
    }

    /**
     * 设置租户信息
     * 
     * @param tenantId 租户ID
     * @param tenantCode 租户代码
     * @param tenantName 租户名称
     */
    public void setTenantInfo(Long tenantId, String tenantCode, String tenantName) {
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId(tenantId);
        tenantInfo.setTenantCode(tenantCode);
        tenantInfo.setTenantName(tenantName);
        tenantInfo.setActive(true);
        
        TENANT_HOLDER.set(tenantInfo);
        CLEANUP_MARKER.set(true);
        
        LOG.debugf("设置租户信息: ID=%d, Code=%s, Name=%s", tenantId, tenantCode, tenantName);
    }

    /**
     * 设置为平台管理员上下文
     */
    public void setPlatformAdminContext() {
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId(PLATFORM_ADMIN_TENANT_ID);
        tenantInfo.setTenantCode("platform");
        tenantInfo.setTenantName("平台管理员");
        tenantInfo.setTenantType("platform");
        tenantInfo.setActive(true);
        
        TENANT_HOLDER.set(tenantInfo);
        CLEANUP_MARKER.set(true);
        
        LOG.debug("设置为平台管理员上下文");
    }

    /**
     * 获取当前租户ID（响应式）
     * 
     * @return 租户ID
     */
    public Uni<Long> getCurrentTenantId() {
        return Uni.createFrom().item(() -> getCurrentTenantIdSync());
    }

    /**
     * 获取当前租户ID（同步）
     * 
     * @return 租户ID
     */
    public Long getCurrentTenantIdSync() {
        TenantInfo tenantInfo = TENANT_HOLDER.get();
        if (tenantInfo != null && tenantInfo.getTenantId() != null) {
            return tenantInfo.getTenantId();
        }
        
        LOG.debug("未找到租户信息，返回默认租户ID");
        return DEFAULT_TENANT_ID;
    }

    /**
     * 获取当前租户ID（Optional）
     * 
     * @return 租户ID的Optional包装
     */
    public Optional<Long> getCurrentTenantIdOptional() {
        TenantInfo tenantInfo = TENANT_HOLDER.get();
        if (tenantInfo != null && tenantInfo.getTenantId() != null) {
            return Optional.of(tenantInfo.getTenantId());
        }
        return Optional.empty();
    }

    /**
     * 获取当前租户信息
     * 
     * @return 租户信息
     */
    public TenantInfo getCurrentTenantInfo() {
        return TENANT_HOLDER.get();
    }

    /**
     * 清理当前租户上下文
     * 增强线程安全性，防止内存泄漏
     */
    public void clear() {
        try {
            TENANT_HOLDER.remove();
            CLEANUP_MARKER.remove();
            LOG.debug("清理租户上下文");
        } catch (Exception e) {
            LOG.warn("清理租户上下文时发生异常", e);
        }
    }

    /**
     * 验证租户访问权限
     * 
     * @param targetTenantId 目标租户ID
     * @param operation 操作类型
     * @return 验证结果
     */
    public Uni<Boolean> validateTenantAccess(Long targetTenantId, String operation) {
        return getCurrentTenantId()
                .map(currentTenantId -> {
                    // 平台管理员可以访问所有租户
                    if (PLATFORM_ADMIN_TENANT_ID.equals(currentTenantId)) {
                        LOG.debugf("平台管理员访问租户 %d，操作：%s", targetTenantId, operation);
                        return true;
                    }
                    
                    // 普通租户只能访问自己的数据
                    boolean hasAccess = currentTenantId.equals(targetTenantId);
                    if (!hasAccess) {
                        LOG.warnf("租户 %d 尝试访问租户 %d 的数据，操作：%s - 拒绝访问", 
                                currentTenantId, targetTenantId, operation);
                    }
                    return hasAccess;
                });
    }

    /**
     * 验证跨租户操作权限
     * 
     * @param operation 操作类型
     * @return 验证结果
     */
    public Uni<Boolean> validateCrossTenantAccess(String operation) {
        return getCurrentTenantId()
                .map(currentTenantId -> {
                    boolean isAllowed = PLATFORM_ADMIN_TENANT_ID.equals(currentTenantId);
                    if (!isAllowed) {
                        LOG.warnf("租户 %d 尝试执行跨租户操作：%s - 拒绝访问", currentTenantId, operation);
                    }
                    return isAllowed;
                });
    }

    /**
     * 在指定租户上下文中执行操作
     * 
     * @param tenantId 租户ID
     * @param supplier 操作供应商
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> Uni<T> runInTenantContext(Long tenantId, Supplier<Uni<T>> supplier) {
        // 保存当前租户信息
        TenantInfo originalTenant = TENANT_HOLDER.get();
        
        try {
            // 设置新的租户上下文
            setCurrentTenantId(tenantId);
            
            // 执行操作
            return supplier.get()
                    .onTermination().invoke(() -> {
                        // 恢复原始租户上下文
                        if (originalTenant != null) {
                            TENANT_HOLDER.set(originalTenant);
                        } else {
                            clear();
                        }
                    });
        } catch (Exception e) {
            // 确保在异常情况下也能恢复上下文
            if (originalTenant != null) {
                TENANT_HOLDER.set(originalTenant);
            } else {
                clear();
            }
            return Uni.createFrom().failure(e);
        }
    }

    /**
     * 在平台管理员上下文中执行操作
     * 
     * @param supplier 操作供应商
     * @param <T> 返回类型
     * @return 操作结果
     */
    public <T> Uni<T> runAsPlatformAdmin(Supplier<Uni<T>> supplier) {
        return runInTenantContext(PLATFORM_ADMIN_TENANT_ID, supplier);
    }

    /**
     * 判断当前是否为默认租户
     * 
     * @return 是否为默认租户
     */
    public boolean isDefaultTenant() {
        Long currentTenantId = getCurrentTenantIdSync();
        return DEFAULT_TENANT_ID.equals(currentTenantId);
    }

    /**
     * 判断当前是否为平台管理员
     * 
     * @return 是否为平台管理员
     */
    public boolean isPlatformAdmin() {
        Long currentTenantId = getCurrentTenantIdSync();
        return PLATFORM_ADMIN_TENANT_ID.equals(currentTenantId);
    }

    /**
     * 获取默认租户ID
     * 
     * @return 默认租户ID
     */
    public static Long getDefaultTenantId() {
        return DEFAULT_TENANT_ID;
    }

    /**
     * 获取平台管理员租户ID
     * 
     * @return 平台管理员租户ID
     */
    public static Long getPlatformAdminTenantId() {
        return PLATFORM_ADMIN_TENANT_ID;
    }

    /**
     * 租户信息内部类
     */
    public static class TenantInfo {
        private Long tenantId;
        private String tenantCode;
        private String tenantName;
        private String tenantType;
        private Boolean active;

        /**
         * 默认构造函数
         */
        public TenantInfo() {
        }

        /**
         * 复制构造函数，用于线程继承
         */
        public TenantInfo(TenantInfo other) {
            if (other != null) {
                this.tenantId = other.tenantId;
                this.tenantCode = other.tenantCode;
                this.tenantName = other.tenantName;
                this.tenantType = other.tenantType;
                this.active = other.active;
            }
        }

        // Getter和Setter方法
        public Long getTenantId() { return tenantId; }
        public void setTenantId(Long tenantId) { this.tenantId = tenantId; }

        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }

        public String getTenantName() { return tenantName; }
        public void setTenantName(String tenantName) { this.tenantName = tenantName; }

        public String getTenantType() { return tenantType; }
        public void setTenantType(String tenantType) { this.tenantType = tenantType; }

        public Boolean getActive() { return active; }
        public void setActive(Boolean active) { this.active = active; }

        @Override
        public String toString() {
            return "TenantInfo{" +
                    "tenantId=" + tenantId +
                    ", tenantCode='" + tenantCode + '\'' +
                    ", tenantName='" + tenantName + '\'' +
                    ", tenantType='" + tenantType + '\'' +
                    ", active=" + active +
                    '}';
        }
    }
}
