package com.visthink.security.service;

import com.visthink.security.context.TenantContext;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 权限验证服务
 * 
 * 提供细粒度的权限控制，支持操作级权限验证
 * 集成多租户上下文，确保数据安全隔离
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class PermissionService {

    private static final Logger LOG = Logger.getLogger(PermissionService.class);

    @Inject
    TenantContext tenantContext;

    @Inject
    JsonWebToken jwt;

    /**
     * 系统管理员角色
     */
    private static final String ROLE_SYSTEM_ADMIN = "SYSTEM_ADMIN";
    
    /**
     * 租户管理员角色
     */
    private static final String ROLE_TENANT_ADMIN = "TENANT_ADMIN";
    
    /**
     * 普通用户角色
     */
    private static final String ROLE_USER = "USER";

    /**
     * 验证用户是否有指定权限
     *
     * @param permission 权限代码，格式：resource:action（如 user:read, order:create）
     * @return 验证结果
     */
    public Uni<Boolean> hasPermission(String permission) {
        // 首先验证权限格式，无论什么角色都必须通过格式验证
        if (!isValidPermissionFormat(permission)) {
            LOG.warnf("权限格式无效：%s", permission);
            return Uni.createFrom().item(false);
        }

        return getCurrentUserRoles()
                .map(roles -> {
                    // 系统管理员拥有所有权限（但仍需通过格式验证）
                    if (roles.contains(ROLE_SYSTEM_ADMIN)) {
                        LOG.debugf("系统管理员访问权限：%s", permission);
                        return true;
                    }

                    // 检查具体权限
                    return checkSpecificPermission(permission, roles);
                })
                .onFailure().recoverWithItem(throwable -> {
                    LOG.errorf(throwable, "权限验证失败：%s", permission);
                    return false;
                });
    }

    /**
     * 验证用户是否有访问指定租户资源的权限
     * 
     * @param targetTenantId 目标租户ID
     * @param permission 权限代码
     * @return 验证结果
     */
    public Uni<Boolean> hasPermissionForTenant(Long targetTenantId, String permission) {
        return tenantContext.validateTenantAccess(targetTenantId, permission)
                .onItem().transformToUni(hasAccess -> {
                    if (!hasAccess) {
                        return Uni.createFrom().item(false);
                    }
                    return hasPermission(permission);
                });
    }

    /**
     * 验证批量操作权限
     *
     * @param permissions 权限列表
     * @return 验证结果，所有权限都通过才返回true
     */
    public Uni<Boolean> hasAllPermissions(String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return Uni.createFrom().item(true);
        }

        // 首先验证所有权限的格式
        for (String permission : permissions) {
            if (!isValidPermissionFormat(permission)) {
                LOG.warnf("权限格式无效：%s", permission);
                return Uni.createFrom().item(false);
            }
        }

        return getCurrentUserRoles()
                .map(roles -> {
                    // 系统管理员拥有所有权限（但仍需通过格式验证）
                    if (roles.contains(ROLE_SYSTEM_ADMIN)) {
                        return true;
                    }

                    // 检查所有权限
                    for (String permission : permissions) {
                        if (!checkSpecificPermission(permission, roles)) {
                            LOG.debugf("用户缺少权限：%s", permission);
                            return false;
                        }
                    }
                    return true;
                });
    }

    /**
     * 验证用户是否有任一权限
     *
     * @param permissions 权限列表
     * @return 验证结果，有任一权限即返回true
     */
    public Uni<Boolean> hasAnyPermission(String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return Uni.createFrom().item(false);
        }

        // 首先验证所有权限的格式
        for (String permission : permissions) {
            if (!isValidPermissionFormat(permission)) {
                LOG.warnf("权限格式无效：%s", permission);
                return Uni.createFrom().item(false);
            }
        }

        return getCurrentUserRoles()
                .map(roles -> {
                    // 系统管理员拥有所有权限（但仍需通过格式验证）
                    if (roles.contains(ROLE_SYSTEM_ADMIN)) {
                        return true;
                    }

                    // 检查是否有任一权限
                    for (String permission : permissions) {
                        if (checkSpecificPermission(permission, roles)) {
                            return true;
                        }
                    }
                    return false;
                });
    }

    /**
     * 获取当前用户角色
     *
     * @return 用户角色集合
     */
    protected Uni<Set<String>> getCurrentUserRoles() {
        try {
            if (jwt == null || jwt.getGroups() == null) {
                LOG.debug("JWT为空或无角色信息，返回默认用户角色");
                return Uni.createFrom().item(Set.of(ROLE_USER));
            }

            Set<String> roles = new HashSet<>(jwt.getGroups());
            LOG.debugf("当前用户角色：%s", roles);
            return Uni.createFrom().item(roles);
        } catch (Exception e) {
            LOG.warn("获取用户角色失败，返回默认角色", e);
            return Uni.createFrom().item(Set.of(ROLE_USER));
        }
    }

    /**
     * 检查具体权限
     *
     * @param permission 权限代码
     * @param roles 用户角色
     * @return 是否有权限
     */
    private boolean checkSpecificPermission(String permission, Set<String> roles) {
        // 首先验证权限格式，无论什么角色都必须通过格式验证
        if (!isValidPermissionFormat(permission)) {
            LOG.warnf("权限格式错误：%s，应为 resource:action", permission);
            return false;
        }

        // 解析权限格式：resource:action
        String[] parts = permission.split(":");
        String resource = parts[0];
        String action = parts[1];

        // 租户管理员权限检查
        if (roles.contains(ROLE_TENANT_ADMIN)) {
            return checkTenantAdminPermission(resource, action);
        }

        // 普通用户权限检查
        if (roles.contains(ROLE_USER)) {
            return checkUserPermission(resource, action);
        }

        return false;
    }

    /**
     * 检查租户管理员权限
     * 
     * @param resource 资源
     * @param action 操作
     * @return 是否有权限
     */
    private boolean checkTenantAdminPermission(String resource, String action) {
        // 租户管理员可以管理租户内的所有资源
        Set<String> allowedResources = Set.of("user", "role", "permission", "product", "inventory", "order", "customer");
        Set<String> allowedActions = Set.of("read", "create", "update", "delete", "export", "import");
        
        return allowedResources.contains(resource) && allowedActions.contains(action);
    }

    /**
     * 检查普通用户权限
     *
     * @param resource 资源
     * @param action 操作
     * @return 是否有权限
     */
    private boolean checkUserPermission(String resource, String action) {
        // 普通用户只有基本的读取权限
        Set<String> allowedResources = Set.of("product", "inventory", "order", "customer");
        Set<String> allowedActions = Set.of("read");

        // 用户可以管理自己的信息
        if ("user".equals(resource) && "update".equals(action)) {
            return true;
        }

        return allowedResources.contains(resource) && allowedActions.contains(action);
    }

    /**
     * 验证权限格式是否有效
     * 严格验证权限格式必须为"resource:action"格式
     *
     * @param permission 权限代码
     * @return 是否为有效格式
     */
    private boolean isValidPermissionFormat(String permission) {
        // 检查空值和空字符串
        if (permission == null || permission.trim().isEmpty()) {
            return false;
        }

        // 检查权限字符串的基本格式要求
        String trimmedPermission = permission.trim();

        // 不能以冒号开头或结尾
        if (trimmedPermission.startsWith(":") || trimmedPermission.endsWith(":")) {
            return false;
        }

        // 必须包含且仅包含一个冒号
        long colonCount = trimmedPermission.chars().filter(ch -> ch == ':').count();
        if (colonCount != 1) {
            return false;
        }

        // 分割权限字符串
        String[] parts = trimmedPermission.split(":");
        if (parts.length != 2) {
            return false;
        }

        // 检查资源部分和操作部分都不能为空
        String resource = parts[0].trim();
        String action = parts[1].trim();

        if (resource.isEmpty() || action.isEmpty()) {
            return false;
        }

        // 检查资源和操作部分只能包含字母、数字、下划线和连字符
        if (!isValidIdentifier(resource) || !isValidIdentifier(action)) {
            return false;
        }

        return true;
    }

    /**
     * 验证标识符是否有效（只包含字母、数字、下划线和连字符）
     *
     * @param identifier 标识符
     * @return 是否有效
     */
    private boolean isValidIdentifier(String identifier) {
        if (identifier == null || identifier.isEmpty()) {
            return false;
        }

        // 只允许字母、数字、下划线和连字符
        return identifier.matches("^[a-zA-Z0-9_-]+$");
    }

    /**
     * 记录权限验证日志
     * 
     * @param permission 权限
     * @param result 验证结果
     * @param userId 用户ID
     */
    private void logPermissionCheck(String permission, boolean result, String userId) {
        if (result) {
            LOG.debugf("权限验证通过 - 用户：%s，权限：%s", userId, permission);
        } else {
            LOG.warnf("权限验证失败 - 用户：%s，权限：%s", userId, permission);
        }
    }

    /**
     * 获取当前用户ID
     * 
     * @return 用户ID
     */
    public String getCurrentUserId() {
        try {
            if (jwt != null && jwt.getSubject() != null) {
                return jwt.getSubject();
            }
            return "anonymous";
        } catch (Exception e) {
            LOG.debug("获取用户ID失败", e);
            return "unknown";
        }
    }

    /**
     * 模拟设置用户角色（仅用于测试）
     * 
     * @param roles 角色集合
     */
    public void setMockUserRoles(Set<String> roles) {
        // 这个方法仅用于测试，生产环境不应该使用
        // 实际的角色应该从JWT token中获取
        LOG.debugf("设置模拟用户角色：%s", roles);
    }
}
