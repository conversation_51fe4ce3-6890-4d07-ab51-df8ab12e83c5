# 工作流引擎 API 接口设计

## 📋 接口概览

### 基础信息
- **服务名称**: workflow-service
- **服务端口**: 8088
- **API版本**: v1
- **基础路径**: `/api/v1/workflow`

### 认证方式
- **认证类型**: JWT Bearer Token
- **权限控制**: 基于RBAC + 租户隔离
- **请求头**: `Authorization: Bearer {token}`

## 🔄 流程定义管理 API

### 1. 创建流程定义
```http
POST /api/v1/workflow/process-definitions
```

**请求体**:
```json
{
  "processKey": "leave_approval",
  "processName": "请假审批流程",
  "category": "HR",
  "description": "员工请假审批流程",
  "designerType": "SIMPLE",
  "processDefinition": {
    "nodes": [
      {
        "id": "start",
        "type": "START",
        "name": "开始",
        "position": { "x": 100, "y": 100 }
      },
      {
        "id": "approve1",
        "type": "USER_TASK",
        "name": "直属领导审批",
        "position": { "x": 300, "y": 100 },
        "assignee": {
          "type": "ROLE",
          "value": "DIRECT_MANAGER"
        },
        "approvalType": "SINGLE"
      }
    ],
    "edges": [
      {
        "id": "edge1",
        "source": "start",
        "target": "approve1"
      }
    ]
  },
  "formDefinition": {
    "fields": [
      {
        "name": "leaveType",
        "label": "请假类型",
        "type": "SELECT",
        "required": true,
        "options": ["年假", "病假", "事假"]
      }
    ]
  }
}
```

### 2. 获取流程定义列表
```http
GET /api/v1/workflow/process-definitions?page=1&size=10&category=HR&status=ACTIVE
```

### 3. 获取流程定义详情
```http
GET /api/v1/workflow/process-definitions/{id}
```

### 4. 更新流程定义
```http
PUT /api/v1/workflow/process-definitions/{id}
```

### 5. 发布流程定义
```http
POST /api/v1/workflow/process-definitions/{id}/deploy
```

### 6. 删除流程定义
```http
DELETE /api/v1/workflow/process-definitions/{id}
```

## 🚀 流程实例管理 API

### 1. 启动流程实例
```http
POST /api/v1/workflow/process-instances
```

**请求体**:
```json
{
  "processKey": "leave_approval",
  "businessKey": "LEAVE_20240624_001",
  "title": "张三的年假申请",
  "formData": {
    "leaveType": "年假",
    "startDate": "2024-07-01",
    "endDate": "2024-07-03",
    "reason": "家庭旅游"
  },
  "variables": {
    "applicantId": 1001,
    "departmentId": 10
  }
}
```

### 2. 获取流程实例列表
```http
GET /api/v1/workflow/process-instances?page=1&size=10&status=RUNNING&initiatorId=1001
```

### 3. 获取流程实例详情
```http
GET /api/v1/workflow/process-instances/{id}
```

### 4. 终止流程实例
```http
POST /api/v1/workflow/process-instances/{id}/terminate
```

### 5. 撤销流程实例
```http
POST /api/v1/workflow/process-instances/{id}/cancel
```

## ✅ 任务管理 API

### 1. 获取待办任务列表
```http
GET /api/v1/workflow/tasks/todo?page=1&size=10&assigneeId=1001
```

### 2. 获取已办任务列表
```http
GET /api/v1/workflow/tasks/done?page=1&size=10&assigneeId=1001
```

### 3. 签收任务
```http
POST /api/v1/workflow/tasks/{taskId}/claim
```

### 4. 完成任务
```http
POST /api/v1/workflow/tasks/{taskId}/complete
```

**请求体**:
```json
{
  "action": "APPROVE",
  "result": "APPROVED",
  "comment": "同意申请",
  "formData": {
    "approverComment": "同意，注意安全"
  },
  "variables": {
    "approvalTime": "2024-06-24T10:30:00"
  }
}
```

### 5. 驳回任务
```http
POST /api/v1/workflow/tasks/{taskId}/reject
```

**请求体**:
```json
{
  "comment": "请假理由不充分",
  "targetNodeKey": "start",
  "rejectType": "TO_START"
}
```

### 6. 转办任务
```http
POST /api/v1/workflow/tasks/{taskId}/transfer
```

**请求体**:
```json
{
  "targetUserId": 1002,
  "comment": "转交给李四处理"
}
```

### 7. 委派任务
```http
POST /api/v1/workflow/tasks/{taskId}/delegate
```

**请求体**:
```json
{
  "delegateUserId": 1003,
  "comment": "委托王五代为处理"
}
```

### 8. 加签
```http
POST /api/v1/workflow/tasks/{taskId}/add-sign
```

**请求体**:
```json
{
  "userIds": [1004, 1005],
  "signType": "BEFORE",
  "comment": "需要部门经理和HR经理审批"
}
```

## 📊 流程监控 API

### 1. 获取流程统计数据
```http
GET /api/v1/workflow/statistics/overview
```

**响应**:
```json
{
  "code": 200,
  "data": {
    "totalProcesses": 156,
    "runningProcesses": 23,
    "completedProcesses": 128,
    "terminatedProcesses": 5,
    "avgProcessDuration": 86400000,
    "todayStarted": 8,
    "todayCompleted": 12
  }
}
```

### 2. 获取任务统计数据
```http
GET /api/v1/workflow/statistics/tasks
```

### 3. 获取流程性能分析
```http
GET /api/v1/workflow/statistics/performance?processKey=leave_approval&startDate=2024-06-01&endDate=2024-06-30
```

## 📝 流程模板 API

### 1. 获取流程模板列表
```http
GET /api/v1/workflow/templates?category=HR&designerType=SIMPLE
```

### 2. 创建流程模板
```http
POST /api/v1/workflow/templates
```

### 3. 基于模板创建流程
```http
POST /api/v1/workflow/templates/{templateId}/create-process
```

## 🔔 消息通知 API

### 1. 获取抄送消息列表
```http
GET /api/v1/workflow/notifications/copies?page=1&size=10&status=UNREAD
```

### 2. 标记抄送消息已读
```http
POST /api/v1/workflow/notifications/copies/{id}/read
```

### 3. 获取流程提醒设置
```http
GET /api/v1/workflow/notifications/reminders/{processKey}
```

## 📈 高级功能 API

### 1. 表单权限配置
```http
POST /api/v1/workflow/form-permissions
```

**请求体**:
```json
{
  "processKey": "leave_approval",
  "nodeKey": "approve1",
  "permissions": [
    {
      "fieldName": "leaveType",
      "permission": "READ_ONLY"
    },
    {
      "fieldName": "reason",
      "permission": "EDITABLE"
    }
  ]
}
```

### 2. 超时处理配置
```http
POST /api/v1/workflow/timeout-handlers
```

### 3. 子流程调用
```http
POST /api/v1/workflow/sub-processes
```

## 🔍 查询和搜索 API

### 1. 全文搜索流程
```http
GET /api/v1/workflow/search?q=请假&type=PROCESS&page=1&size=10
```

### 2. 高级查询
```http
POST /api/v1/workflow/query/advanced
```

**请求体**:
```json
{
  "conditions": [
    {
      "field": "processKey",
      "operator": "EQUALS",
      "value": "leave_approval"
    },
    {
      "field": "createTime",
      "operator": "BETWEEN",
      "value": ["2024-06-01", "2024-06-30"]
    }
  ],
  "orderBy": [
    {
      "field": "createTime",
      "direction": "DESC"
    }
  ],
  "page": 1,
  "size": 20
}
```

## 📋 响应格式规范

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1719216000000,
  "traceId": "workflow-abc123"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "INVALID_PARAMETER",
  "details": "processKey不能为空",
  "timestamp": 1719216000000,
  "traceId": "workflow-abc123"
}
```

## 🔐 权限控制

### 权限级别
- **WORKFLOW_ADMIN**: 工作流管理员
- **PROCESS_DESIGNER**: 流程设计师
- **PROCESS_INITIATOR**: 流程发起人
- **TASK_ASSIGNEE**: 任务处理人
- **PROCESS_VIEWER**: 流程查看者

### 权限矩阵
| 操作 | 管理员 | 设计师 | 发起人 | 处理人 | 查看者 |
|------|--------|--------|--------|--------|--------|
| 创建流程定义 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 启动流程实例 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 处理任务 | ✅ | ❌ | ❌ | ✅ | ❌ |
| 查看流程 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 终止流程 | ✅ | ❌ | ✅ | ❌ | ❌ |
