####
# VisThink ERP - Workflow Service Dockerfile
# 工作流引擎微服务容器化配置
####

# 构建阶段
FROM registry.access.redhat.com/ubi8/openjdk-17:1.18 AS builder

# 设置工作目录
WORKDIR /app

# 复制Maven配置文件
COPY pom.xml .
COPY ../shared-common/pom.xml ./shared-common/
COPY ../pom.xml ./

# 复制源代码
COPY src ./src
COPY ../shared-common/src ./shared-common/src

# 构建应用
RUN mvn clean package -DskipTests

# 运行阶段
FROM registry.access.redhat.com/ubi8/openjdk-17-runtime:1.18

# 设置环境变量
ENV LANGUAGE='en_US:en'
ENV JAVA_OPTS_APPEND="-Dquarkus.http.host=0.0.0.0 -Djava.util.logging.manager=org.jboss.logmanager.LogManager"

# 创建应用用户
RUN useradd -r -u 1001 -g root workflow && \
    mkdir -p /app/logs && \
    chown -R 1001:root /app && \
    chmod -R g+rwX /app

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder --chown=1001:root /app/target/quarkus-app/lib/ ./lib/
COPY --from=builder --chown=1001:root /app/target/quarkus-app/*.jar ./
COPY --from=builder --chown=1001:root /app/target/quarkus-app/app/ ./app/
COPY --from=builder --chown=1001:root /app/target/quarkus-app/quarkus/ ./quarkus/

# 切换到应用用户
USER 1001

# 暴露端口
EXPOSE 8088

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8088/health || exit 1

# 启动命令
ENTRYPOINT ["java", "-jar", "quarkus-run.jar"]
