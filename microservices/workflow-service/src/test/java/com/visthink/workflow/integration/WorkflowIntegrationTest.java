package com.visthink.workflow.integration;

import com.visthink.workflow.dto.ProcessDefinitionDTO;
import com.visthink.workflow.dto.ProcessInstanceDTO;
import com.visthink.workflow.dto.TaskInstanceDTO;
import com.visthink.workflow.entity.ProcessDefinition;
import com.visthink.workflow.entity.ProcessInstance;
import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.service.ProcessDefinitionService;
import com.visthink.workflow.service.ProcessInstanceService;
import com.visthink.workflow.service.TaskService;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.h2.H2DatabaseTestResource;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工作流引擎集成测试
 * 测试完整的工作流生命周期
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@QuarkusTest
@QuarkusTestResource(H2DatabaseTestResource.class)
@TestMethodOrder(OrderAnnotation.class)
class WorkflowIntegrationTest {

    @Inject
    ProcessDefinitionService processDefinitionService;

    @Inject
    ProcessInstanceService processInstanceService;

    @Inject
    TaskService taskService;

    private static Long processDefinitionId;
    private static Long processInstanceId;
    private static Long taskInstanceId;

    private static final String TEST_TENANT_ID = "test-tenant";
    private static final Long TEST_USER_ID = 1L;
    private static final String TEST_USER_NAME = "测试用户";

    @Test
    @Order(1)
    @DisplayName("1. 创建流程定义")
    @Transactional
    void testCreateProcessDefinition() {
        // 准备流程定义数据
        ProcessDefinitionDTO dto = new ProcessDefinitionDTO();
        dto.setTenantId(TEST_TENANT_ID);
        dto.setProcessKey("test-process");
        dto.setProcessName("测试流程");
        dto.setProcessVersion("1.0");
        dto.setDescription("集成测试流程");
        dto.setCategory("test");
        
        // 简单的BPMN XML
        String bpmnXml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
              <process id="test-process" name="测试流程">
                <startEvent id="start" name="开始"/>
                <userTask id="approve" name="审批"/>
                <endEvent id="end" name="结束"/>
                <sequenceFlow sourceRef="start" targetRef="approve"/>
                <sequenceFlow sourceRef="approve" targetRef="end"/>
              </process>
            </definitions>
            """;
        dto.setBpmnXml(bpmnXml);
        dto.setStatus(ProcessDefinition.ProcessStatus.ACTIVE);

        // 执行创建
        ProcessDefinitionDTO result = processDefinitionService.createProcessDefinition(dto)
                .await().indefinitely();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("test-process", result.getProcessKey());
        assertEquals("测试流程", result.getProcessName());
        assertEquals(ProcessDefinition.ProcessStatus.ACTIVE, result.getStatus());

        processDefinitionId = result.getId();
        System.out.println("创建流程定义成功，ID: " + processDefinitionId);
    }

    @Test
    @Order(2)
    @DisplayName("2. 启动流程实例")
    @Transactional
    void testStartProcessInstance() {
        assertNotNull(processDefinitionId, "流程定义ID不能为空");

        // 准备启动参数
        Map<String, Object> variables = new HashMap<>();
        variables.put("applicant", "张三");
        variables.put("amount", 5000.0);
        variables.put("reason", "差旅费报销");

        Map<String, Object> formData = new HashMap<>();
        formData.put("applicantName", "张三");
        formData.put("applyAmount", 5000.0);
        formData.put("applyReason", "差旅费报销");

        // 执行启动
        ProcessInstanceDTO result = processInstanceService.startProcess(
                processDefinitionId, TEST_USER_ID, TEST_USER_NAME, 
                "测试流程实例", variables, formData)
                .await().indefinitely();

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(processDefinitionId, result.getProcessDefinitionId());
        assertEquals(TEST_USER_ID, result.getInitiatorId());
        assertEquals(TEST_USER_NAME, result.getInitiatorName());
        assertEquals(ProcessInstance.ProcessStatus.RUNNING, result.getStatus());

        processInstanceId = result.getId();
        System.out.println("启动流程实例成功，ID: " + processInstanceId);
    }

    @Test
    @Order(3)
    @DisplayName("3. 查询待办任务")
    void testGetTodoTasks() {
        assertNotNull(processInstanceId, "流程实例ID不能为空");

        // 查询待办任务
        List<TaskInstanceDTO> todoTasks = taskService.getTodoTasks(TEST_USER_ID, TEST_TENANT_ID)
                .await().indefinitely();

        // 验证结果
        assertNotNull(todoTasks);
        assertFalse(todoTasks.isEmpty());
        
        // 找到我们创建的任务
        TaskInstanceDTO task = todoTasks.stream()
                .filter(t -> t.getProcessInstanceId().equals(processInstanceId))
                .findFirst()
                .orElse(null);
        
        assertNotNull(task, "应该有一个待办任务");
        assertEquals("approve", task.getNodeKey());
        assertEquals(TaskInstance.TaskStatus.CREATED, task.getStatus());

        taskInstanceId = task.getId();
        System.out.println("查询到待办任务，ID: " + taskInstanceId);
    }

    @Test
    @Order(4)
    @DisplayName("4. 签收任务")
    @Transactional
    void testClaimTask() {
        assertNotNull(taskInstanceId, "任务实例ID不能为空");

        // 执行签收
        var result = taskService.claimTask(taskInstanceId, TEST_USER_ID, TEST_USER_NAME)
                .await().indefinitely();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("任务签收成功", result.getMessage());

        // 验证任务状态
        TaskInstanceDTO task = taskService.findById(taskInstanceId).await().indefinitely();
        assertEquals(TaskInstance.TaskStatus.CLAIMED, task.getStatus());
        assertEquals(TEST_USER_ID, task.getAssigneeId());
        assertEquals(TEST_USER_NAME, task.getAssigneeName());
        assertNotNull(task.getClaimTime());

        System.out.println("签收任务成功");
    }

    @Test
    @Order(5)
    @DisplayName("5. 审批任务")
    @Transactional
    void testApproveTask() {
        assertNotNull(taskInstanceId, "任务实例ID不能为空");

        // 准备审批数据
        Map<String, Object> formData = new HashMap<>();
        formData.put("approveOpinion", "同意申请");
        formData.put("approveResult", "APPROVED");

        Map<String, Object> variables = new HashMap<>();
        variables.put("approved", true);
        variables.put("approver", TEST_USER_NAME);

        // 执行审批
        var result = taskService.approveTask(taskInstanceId, TEST_USER_ID, TEST_USER_NAME, 
                                           "同意申请", formData, variables)
                .await().indefinitely();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("任务审批完成", result.getMessage());

        // 验证任务状态
        TaskInstanceDTO task = taskService.findById(taskInstanceId).await().indefinitely();
        assertEquals(TaskInstance.TaskStatus.COMPLETED, task.getStatus());
        assertNotNull(task.getCompleteTime());

        System.out.println("审批任务成功");
    }

    @Test
    @Order(6)
    @DisplayName("6. 检查流程实例状态")
    void testCheckProcessInstanceStatus() {
        assertNotNull(processInstanceId, "流程实例ID不能为空");

        // 查询流程实例状态
        ProcessInstanceDTO processInstance = processInstanceService.findById(processInstanceId)
                .await().indefinitely();

        // 验证结果
        assertNotNull(processInstance);
        // 由于是简单的两节点流程，审批完成后流程应该结束
        assertEquals(ProcessInstance.ProcessStatus.COMPLETED, processInstance.getStatus());
        assertNotNull(processInstance.getEndTime());

        System.out.println("流程实例已完成");
    }

    @Test
    @Order(7)
    @DisplayName("7. 查询任务历史")
    void testGetTaskHistory() {
        assertNotNull(taskInstanceId, "任务实例ID不能为空");

        // 查询任务历史
        var history = taskService.getTaskHistory(taskInstanceId).await().indefinitely();

        // 验证结果
        assertNotNull(history);
        assertFalse(history.isEmpty());
        
        // 应该有签收和审批两条历史记录
        assertTrue(history.size() >= 2);
        
        // 验证历史记录内容
        boolean hasClaimRecord = history.stream()
                .anyMatch(h -> h.getAction() == TaskInstance.TaskAction.CLAIM);
        boolean hasApproveRecord = history.stream()
                .anyMatch(h -> h.getAction() == TaskInstance.TaskAction.APPROVE);
        
        assertTrue(hasClaimRecord, "应该有签收记录");
        assertTrue(hasApproveRecord, "应该有审批记录");

        System.out.println("查询任务历史成功，记录数: " + history.size());
    }

    @Test
    @Order(8)
    @DisplayName("8. 查询已办任务")
    void testGetDoneTasks() {
        // 查询已办任务
        List<TaskInstanceDTO> doneTasks = taskService.getDoneTasks(TEST_USER_ID, TEST_TENANT_ID)
                .await().indefinitely();

        // 验证结果
        assertNotNull(doneTasks);
        assertFalse(doneTasks.isEmpty());
        
        // 找到我们处理的任务
        TaskInstanceDTO task = doneTasks.stream()
                .filter(t -> t.getId().equals(taskInstanceId))
                .findFirst()
                .orElse(null);
        
        assertNotNull(task, "应该有一个已办任务");
        assertEquals(TaskInstance.TaskStatus.COMPLETED, task.getStatus());

        System.out.println("查询已办任务成功");
    }

    @Test
    @Order(9)
    @DisplayName("9. 测试驳回流程")
    @Transactional
    void testRejectWorkflow() {
        // 创建新的流程实例用于测试驳回
        Map<String, Object> variables = new HashMap<>();
        variables.put("applicant", "李四");
        variables.put("amount", 10000.0);

        ProcessInstanceDTO newProcess = processInstanceService.startProcess(
                processDefinitionId, TEST_USER_ID, TEST_USER_NAME, 
                "驳回测试流程", variables, new HashMap<>())
                .await().indefinitely();

        // 获取新任务
        List<TaskInstanceDTO> todoTasks = taskService.getTodoTasks(TEST_USER_ID, TEST_TENANT_ID)
                .await().indefinitely();
        
        TaskInstanceDTO newTask = todoTasks.stream()
                .filter(t -> t.getProcessInstanceId().equals(newProcess.getId()))
                .findFirst()
                .orElse(null);
        
        assertNotNull(newTask);

        // 签收任务
        taskService.claimTask(newTask.getId(), TEST_USER_ID, TEST_USER_NAME)
                .await().indefinitely();

        // 驳回任务
        var result = taskService.rejectTask(newTask.getId(), TEST_USER_ID, TEST_USER_NAME, 
                                          "材料不完整", "start")
                .await().indefinitely();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("任务驳回成功", result.getMessage());

        // 验证任务状态
        TaskInstanceDTO rejectedTask = taskService.findById(newTask.getId()).await().indefinitely();
        assertEquals(TaskInstance.TaskStatus.REJECTED, rejectedTask.getStatus());
        assertEquals("材料不完整", rejectedTask.getRejectReason());

        System.out.println("驳回流程测试成功");
    }

    @Test
    @Order(10)
    @DisplayName("10. 性能测试 - 批量创建流程")
    void testBatchProcessCreation() {
        int batchSize = 10;
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < batchSize; i++) {
            Map<String, Object> variables = new HashMap<>();
            variables.put("applicant", "批量用户" + i);
            variables.put("amount", 1000.0 + i * 100);

            try {
                ProcessInstanceDTO process = processInstanceService.startProcess(
                        processDefinitionId, TEST_USER_ID, TEST_USER_NAME, 
                        "批量测试流程" + i, variables, new HashMap<>())
                        .await().indefinitely();
                
                assertNotNull(process);
            } catch (Exception e) {
                fail("批量创建流程失败: " + e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println(String.format("批量创建 %d 个流程实例，耗时: %d ms，平均: %.2f ms/个", 
                                        batchSize, duration, (double) duration / batchSize));

        // 验证性能要求：平均每个流程创建时间应该小于500ms
        assertTrue(duration / batchSize < 500, "流程创建性能不达标");
    }

    @AfterAll
    static void cleanup() {
        System.out.println("集成测试完成");
        System.out.println("流程定义ID: " + processDefinitionId);
        System.out.println("流程实例ID: " + processInstanceId);
        System.out.println("任务实例ID: " + taskInstanceId);
    }
}
