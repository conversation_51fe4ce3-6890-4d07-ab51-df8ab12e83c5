package com.visthink.workflow.performance;

import com.visthink.workflow.dto.ProcessDefinitionDTO;
import com.visthink.workflow.dto.ProcessInstanceDTO;
import com.visthink.workflow.dto.TaskInstanceDTO;
import com.visthink.workflow.entity.ProcessDefinition;
import com.visthink.workflow.service.ProcessDefinitionService;
import com.visthink.workflow.service.ProcessInstanceService;
import com.visthink.workflow.service.TaskService;
import com.visthink.workflow.service.PerformanceMonitorService;
import io.quarkus.test.junit.QuarkusTest;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;

import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工作流引擎性能测试
 * 测试系统在高并发和大数据量下的性能表现
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@QuarkusTest
@EnabledIfSystemProperty(named = "performance.test.enabled", matches = "true")
class WorkflowPerformanceTest {

    @Inject
    ProcessDefinitionService processDefinitionService;

    @Inject
    ProcessInstanceService processInstanceService;

    @Inject
    TaskService taskService;

    @Inject
    PerformanceMonitorService performanceMonitorService;

    private static Long testProcessDefinitionId;
    private static final String TEST_TENANT_ID = "perf-test";
    private static final int THREAD_POOL_SIZE = 20;
    private static final int CONCURRENT_USERS = 100;

    @BeforeAll
    static void setupPerformanceTest() {
        System.out.println("=== 工作流引擎性能测试开始 ===");
        System.out.println("测试时间: " + LocalDateTime.now());
        System.out.println("并发用户数: " + CONCURRENT_USERS);
        System.out.println("线程池大小: " + THREAD_POOL_SIZE);
    }

    @BeforeEach
    void setUp() {
        if (testProcessDefinitionId == null) {
            createTestProcessDefinition();
        }
    }

    @Test
    @DisplayName("并发流程启动性能测试")
    void testConcurrentProcessStart() throws InterruptedException {
        int processCount = 1000;
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        CountDownLatch latch = new CountDownLatch(processCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 并发启动流程
        for (int i = 0; i < processCount; i++) {
            final int processIndex = i;
            executor.submit(() -> {
                try {
                    long taskStartTime = System.currentTimeMillis();
                    
                    Map<String, Object> variables = new HashMap<>();
                    variables.put("applicant", "用户" + processIndex);
                    variables.put("amount", 1000.0 + processIndex);

                    ProcessInstanceDTO process = processInstanceService.startProcess(
                            testProcessDefinitionId, 
                            (long) (processIndex % 100 + 1), 
                            "用户" + processIndex,
                            "性能测试流程" + processIndex, 
                            variables, 
                            new HashMap<>())
                            .await().atMost(Duration.ofSeconds(30));

                    long taskEndTime = System.currentTimeMillis();
                    totalTime.addAndGet(taskEndTime - taskStartTime);
                    
                    assertNotNull(process);
                    successCount.incrementAndGet();
                    
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    System.err.println("流程启动失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        boolean completed = latch.await(5, TimeUnit.MINUTES);
        long endTime = System.currentTimeMillis();
        
        executor.shutdown();

        // 性能统计
        long totalDuration = endTime - startTime;
        double avgTime = totalTime.get() / (double) successCount.get();
        double throughput = successCount.get() / (totalDuration / 1000.0);

        System.out.println("=== 并发流程启动性能测试结果 ===");
        System.out.println("总流程数: " + processCount);
        System.out.println("成功数: " + successCount.get());
        System.out.println("失败数: " + errorCount.get());
        System.out.println("总耗时: " + totalDuration + " ms");
        System.out.println("平均响应时间: " + String.format("%.2f", avgTime) + " ms");
        System.out.println("吞吐量: " + String.format("%.2f", throughput) + " 流程/秒");
        System.out.println("成功率: " + String.format("%.2f", (double) successCount.get() / processCount * 100) + "%");

        // 性能断言
        assertTrue(completed, "测试应该在5分钟内完成");
        assertTrue(successCount.get() > processCount * 0.95, "成功率应该大于95%");
        assertTrue(avgTime < 1000, "平均响应时间应该小于1秒");
        assertTrue(throughput > 10, "吞吐量应该大于10流程/秒");
    }

    @Test
    @DisplayName("并发任务处理性能测试")
    void testConcurrentTaskProcessing() throws InterruptedException {
        int taskCount = 500;
        
        // 先创建一批流程实例和任务
        List<Long> taskIds = new ArrayList<>();
        for (int i = 0; i < taskCount; i++) {
            Map<String, Object> variables = new HashMap<>();
            variables.put("applicant", "任务用户" + i);
            
            ProcessInstanceDTO process = processInstanceService.startProcess(
                    testProcessDefinitionId, 
                    (long) (i % 50 + 1), 
                    "任务用户" + i,
                    "任务性能测试" + i, 
                    variables, 
                    new HashMap<>())
                    .await().atMost(Duration.ofSeconds(10));

            // 获取创建的任务
            List<TaskInstanceDTO> tasks = taskService.getTodoTasks((long) (i % 50 + 1), TEST_TENANT_ID)
                    .await().atMost(Duration.ofSeconds(5));
            
            if (!tasks.isEmpty()) {
                TaskInstanceDTO task = tasks.stream()
                        .filter(t -> t.getProcessInstanceId().equals(process.getId()))
                        .findFirst()
                        .orElse(null);
                if (task != null) {
                    taskIds.add(task.getId());
                }
            }
        }

        System.out.println("创建了 " + taskIds.size() + " 个任务，开始并发处理测试");

        // 并发处理任务
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        CountDownLatch latch = new CountDownLatch(taskIds.size());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < taskIds.size(); i++) {
            final Long taskId = taskIds.get(i);
            final int userIndex = i % 50 + 1;
            
            executor.submit(() -> {
                try {
                    long taskStartTime = System.currentTimeMillis();
                    
                    // 签收任务
                    taskService.claimTask(taskId, (long) userIndex, "用户" + userIndex)
                            .await().atMost(Duration.ofSeconds(10));
                    
                    // 审批任务
                    Map<String, Object> formData = new HashMap<>();
                    formData.put("opinion", "性能测试审批");
                    
                    taskService.approveTask(taskId, (long) userIndex, "用户" + userIndex, 
                                          "性能测试审批", formData, new HashMap<>())
                            .await().atMost(Duration.ofSeconds(10));

                    long taskEndTime = System.currentTimeMillis();
                    totalTime.addAndGet(taskEndTime - taskStartTime);
                    
                    successCount.incrementAndGet();
                    
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    System.err.println("任务处理失败: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        boolean completed = latch.await(10, TimeUnit.MINUTES);
        long endTime = System.currentTimeMillis();
        
        executor.shutdown();

        // 性能统计
        long totalDuration = endTime - startTime;
        double avgTime = successCount.get() > 0 ? totalTime.get() / (double) successCount.get() : 0;
        double throughput = successCount.get() / (totalDuration / 1000.0);

        System.out.println("=== 并发任务处理性能测试结果 ===");
        System.out.println("总任务数: " + taskIds.size());
        System.out.println("成功数: " + successCount.get());
        System.out.println("失败数: " + errorCount.get());
        System.out.println("总耗时: " + totalDuration + " ms");
        System.out.println("平均响应时间: " + String.format("%.2f", avgTime) + " ms");
        System.out.println("吞吐量: " + String.format("%.2f", throughput) + " 任务/秒");
        System.out.println("成功率: " + String.format("%.2f", (double) successCount.get() / taskIds.size() * 100) + "%");

        // 性能断言
        assertTrue(completed, "测试应该在10分钟内完成");
        assertTrue(successCount.get() > taskIds.size() * 0.90, "成功率应该大于90%");
        assertTrue(avgTime < 2000, "平均响应时间应该小于2秒");
        assertTrue(throughput > 5, "吞吐量应该大于5任务/秒");
    }

    @Test
    @DisplayName("内存使用性能测试")
    void testMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        
        // 记录初始内存使用
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("初始内存使用: " + (initialMemory / 1024 / 1024) + " MB");

        // 创建大量流程实例
        int processCount = 1000;
        List<ProcessInstanceDTO> processes = new ArrayList<>();
        
        for (int i = 0; i < processCount; i++) {
            Map<String, Object> variables = new HashMap<>();
            variables.put("data", "测试数据" + i);
            
            ProcessInstanceDTO process = processInstanceService.startProcess(
                    testProcessDefinitionId, 
                    (long) (i % 10 + 1), 
                    "内存测试用户" + i,
                    "内存测试流程" + i, 
                    variables, 
                    new HashMap<>())
                    .await().atMost(Duration.ofSeconds(5));
            
            processes.add(process);
            
            // 每100个流程检查一次内存
            if ((i + 1) % 100 == 0) {
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                System.out.println("创建 " + (i + 1) + " 个流程后内存使用: " + 
                                 (currentMemory / 1024 / 1024) + " MB");
            }
        }

        // 记录峰值内存使用
        long peakMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = peakMemory - initialMemory;
        double avgMemoryPerProcess = memoryIncrease / (double) processCount;

        System.out.println("=== 内存使用性能测试结果 ===");
        System.out.println("创建流程数: " + processCount);
        System.out.println("初始内存: " + (initialMemory / 1024 / 1024) + " MB");
        System.out.println("峰值内存: " + (peakMemory / 1024 / 1024) + " MB");
        System.out.println("内存增长: " + (memoryIncrease / 1024 / 1024) + " MB");
        System.out.println("平均每流程内存: " + String.format("%.2f", avgMemoryPerProcess / 1024) + " KB");

        // 内存使用断言
        assertTrue(avgMemoryPerProcess < 50 * 1024, "平均每个流程内存使用应该小于50KB");
        assertTrue(memoryIncrease < 200 * 1024 * 1024, "总内存增长应该小于200MB");
    }

    @Test
    @DisplayName("数据库查询性能测试")
    void testDatabaseQueryPerformance() {
        int queryCount = 1000;
        long totalTime = 0;

        // 先创建一些测试数据
        for (int i = 0; i < 100; i++) {
            processInstanceService.startProcess(
                    testProcessDefinitionId, 
                    (long) (i % 10 + 1), 
                    "查询测试用户" + i,
                    "查询测试流程" + i, 
                    new HashMap<>(), 
                    new HashMap<>())
                    .await().atMost(Duration.ofSeconds(5));
        }

        // 测试查询性能
        for (int i = 0; i < queryCount; i++) {
            long startTime = System.currentTimeMillis();
            
            // 执行各种查询
            taskService.getTodoTasks((long) (i % 10 + 1), TEST_TENANT_ID)
                    .await().atMost(Duration.ofSeconds(5));
            
            long endTime = System.currentTimeMillis();
            totalTime += (endTime - startTime);
        }

        double avgQueryTime = totalTime / (double) queryCount;
        double queryThroughput = queryCount / (totalTime / 1000.0);

        System.out.println("=== 数据库查询性能测试结果 ===");
        System.out.println("查询次数: " + queryCount);
        System.out.println("总耗时: " + totalTime + " ms");
        System.out.println("平均查询时间: " + String.format("%.2f", avgQueryTime) + " ms");
        System.out.println("查询吞吐量: " + String.format("%.2f", queryThroughput) + " 查询/秒");

        // 查询性能断言
        assertTrue(avgQueryTime < 100, "平均查询时间应该小于100ms");
        assertTrue(queryThroughput > 50, "查询吞吐量应该大于50查询/秒");
    }

    @Test
    @DisplayName("系统整体性能报告")
    void testOverallPerformanceReport() {
        // 获取性能监控报告
        PerformanceMonitorService.PerformanceReport report = 
                performanceMonitorService.getPerformanceReport();

        System.out.println("=== 系统整体性能报告 ===");
        System.out.println("报告生成时间: " + report.getGeneratedTime());
        System.out.println("流程启动总数: " + report.getTotalProcessStarts());
        System.out.println("流程完成总数: " + report.getTotalProcessCompletes());
        System.out.println("任务创建总数: " + report.getTotalTaskCreates());
        System.out.println("任务完成总数: " + report.getTotalTaskCompletes());
        System.out.println("任务超时总数: " + report.getTotalTaskTimeouts());
        System.out.println("错误总数: " + report.getTotalErrors());
        System.out.println("平均流程执行时间: " + String.format("%.2f", report.getAvgProcessExecutionTime()) + " ms");
        System.out.println("平均任务执行时间: " + String.format("%.2f", report.getAvgTaskExecutionTime()) + " ms");
        System.out.println("平均数据库查询时间: " + String.format("%.2f", report.getAvgDatabaseQueryTime()) + " ms");
        System.out.println("当前活跃流程数: " + report.getCurrentActiveProcesses());
        System.out.println("当前活跃任务数: " + report.getCurrentActiveTasks());
        System.out.println("当前内存使用: " + (report.getCurrentMemoryUsage() / 1024 / 1024) + " MB");
        System.out.println("内存使用率: " + String.format("%.2f", report.getMemoryUsageRatio() * 100) + "%");

        // 整体性能断言
        assertTrue(report.getAvgProcessExecutionTime() < 5000, "平均流程执行时间应该小于5秒");
        assertTrue(report.getAvgTaskExecutionTime() < 2000, "平均任务执行时间应该小于2秒");
        assertTrue(report.getMemoryUsageRatio() < 0.8, "内存使用率应该小于80%");
    }

    private void createTestProcessDefinition() {
        ProcessDefinitionDTO dto = new ProcessDefinitionDTO();
        dto.setTenantId(TEST_TENANT_ID);
        dto.setProcessKey("perf-test-process");
        dto.setProcessName("性能测试流程");
        dto.setProcessVersion("1.0");
        dto.setDescription("用于性能测试的流程定义");
        dto.setCategory("performance");
        
        String bpmnXml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL">
              <process id="perf-test-process" name="性能测试流程">
                <startEvent id="start" name="开始"/>
                <userTask id="approve" name="审批"/>
                <endEvent id="end" name="结束"/>
                <sequenceFlow sourceRef="start" targetRef="approve"/>
                <sequenceFlow sourceRef="approve" targetRef="end"/>
              </process>
            </definitions>
            """;
        dto.setBpmnXml(bpmnXml);
        dto.setStatus(ProcessDefinition.ProcessStatus.ACTIVE);

        ProcessDefinitionDTO result = processDefinitionService.createProcessDefinition(dto)
                .await().indefinitely();
        
        testProcessDefinitionId = result.getId();
        System.out.println("创建性能测试流程定义，ID: " + testProcessDefinitionId);
    }

    @AfterAll
    static void tearDownPerformanceTest() {
        System.out.println("=== 工作流引擎性能测试结束 ===");
        System.out.println("测试完成时间: " + LocalDateTime.now());
    }
}
