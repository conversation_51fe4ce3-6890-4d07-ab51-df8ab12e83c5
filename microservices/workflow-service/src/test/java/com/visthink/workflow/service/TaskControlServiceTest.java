package com.visthink.workflow.service;

import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.entity.TaskHistory;
import com.visthink.workflow.repository.TaskInstanceRepository;
import com.visthink.workflow.repository.TaskHistoryRepository;
import com.visthink.workflow.exception.BusinessException;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectMock;
import io.smallrye.mutiny.Uni;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 任务控制服务单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@QuarkusTest
class TaskControlServiceTest {

    @Inject
    TaskControlService taskControlService;

    @InjectMock
    TaskInstanceRepository taskInstanceRepository;

    @InjectMock
    TaskHistoryRepository taskHistoryRepository;

    @InjectMock
    TaskAssignmentService taskAssignmentService;

    @InjectMock
    ProcessEngineService processEngineService;

    @InjectMock
    UserService userService;

    private TaskInstance testTask;
    private final Long TEST_TASK_ID = 1L;
    private final Long TEST_USER_ID = 100L;
    private final String TEST_USER_NAME = "测试用户";

    @BeforeEach
    void setUp() {
        // 创建测试任务实例
        testTask = new TaskInstance();
        testTask.setId(TEST_TASK_ID);
        testTask.setTenantId("test-tenant");
        testTask.setProcessInstanceId(1L);
        testTask.setProcessDefinitionId(1L);
        testTask.setNodeKey("approve");
        testTask.setNodeName("审批节点");
        testTask.setTaskKey("approve_task");
        testTask.setTaskName("审批任务");
        testTask.setStatus(TaskInstance.TaskStatus.CREATED);
        testTask.setApprovalType(TaskInstance.ApprovalType.SINGLE);
        testTask.setCandidateUsers(TEST_USER_ID.toString());
        testTask.setCreatedTime(LocalDateTime.now());
    }

    @Test
    @DisplayName("测试审批任务 - 成功场景")
    void testApproveTask_Success() {
        // 准备测试数据
        Map<String, Object> formData = new HashMap<>();
        formData.put("opinion", "同意申请");
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("approved", true);

        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(taskHistoryRepository.persist(any(TaskHistory.class)))
                .thenReturn(Uni.createFrom().item(new TaskHistory()));
        
        when(taskInstanceRepository.persist(any(TaskInstance.class)))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(processEngineService.completeTask(any(TaskInstance.class), any(), any()))
                .thenReturn(Uni.createFrom().item("success"));

        // 执行测试
        Uni<TaskControlService.TaskControlResult> result = taskControlService.approveTask(
                TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, "同意申请", formData, variables);

        // 验证结果
        TaskControlService.TaskControlResult controlResult = result.await().indefinitely();
        assertTrue(controlResult.isSuccess());
        assertEquals("任务审批完成", controlResult.getMessage());
        assertEquals(TEST_TASK_ID, controlResult.getTaskId());

        // 验证方法调用
        verify(taskInstanceRepository).findById(TEST_TASK_ID);
        verify(taskHistoryRepository).persist(any(TaskHistory.class));
        verify(processEngineService).completeTask(any(TaskInstance.class), eq(formData), eq(variables));
    }

    @Test
    @DisplayName("测试审批任务 - 任务不存在")
    void testApproveTask_TaskNotFound() {
        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item((TaskInstance) null));

        // 执行测试并验证异常
        assertThrows(BusinessException.class, () -> {
            taskControlService.approveTask(TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, 
                                         "同意申请", null, null)
                    .await().indefinitely();
        });
    }

    @Test
    @DisplayName("测试审批任务 - 无权限操作")
    void testApproveTask_NoPermission() {
        // 设置任务为已分配给其他用户
        testTask.setAssigneeId(999L);
        testTask.setAssigneeName("其他用户");
        testTask.setCandidateUsers("999");

        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));

        // 执行测试并验证异常
        assertThrows(BusinessException.class, () -> {
            taskControlService.approveTask(TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, 
                                         "同意申请", null, null)
                    .await().indefinitely();
        });
    }

    @Test
    @DisplayName("测试驳回任务 - 成功场景")
    void testRejectTask_Success() {
        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(taskHistoryRepository.persist(any(TaskHistory.class)))
                .thenReturn(Uni.createFrom().item(new TaskHistory()));
        
        when(taskInstanceRepository.persist(any(TaskInstance.class)))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(processEngineService.rejectTask(any(TaskInstance.class), anyString()))
                .thenReturn(Uni.createFrom().item("success"));

        // 执行测试
        Uni<TaskControlService.TaskControlResult> result = taskControlService.rejectTask(
                TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, "材料不完整", "start");

        // 验证结果
        TaskControlService.TaskControlResult controlResult = result.await().indefinitely();
        assertTrue(controlResult.isSuccess());
        assertEquals("任务驳回成功", controlResult.getMessage());

        // 验证任务状态
        assertEquals(TaskInstance.TaskStatus.REJECTED, testTask.getStatus());
        assertEquals("材料不完整", testTask.getRejectReason());
    }

    @Test
    @DisplayName("测试转办任务 - 成功场景")
    void testTransferTask_Success() {
        // 准备目标用户
        UserService.User targetUser = new UserService.User();
        targetUser.setId(200L);
        targetUser.setName("目标用户");

        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(userService.findById(200L))
                .thenReturn(Uni.createFrom().item(targetUser));
        
        when(taskHistoryRepository.persist(any(TaskHistory.class)))
                .thenReturn(Uni.createFrom().item(new TaskHistory()));
        
        when(taskInstanceRepository.persist(any(TaskInstance.class)))
                .thenReturn(Uni.createFrom().item(testTask));

        // 执行测试
        Uni<TaskControlService.TaskControlResult> result = taskControlService.transferTask(
                TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, 200L, "专业问题需要专家处理");

        // 验证结果
        TaskControlService.TaskControlResult controlResult = result.await().indefinitely();
        assertTrue(controlResult.isSuccess());
        assertEquals("任务转办成功", controlResult.getMessage());

        // 验证任务状态
        assertEquals(TaskInstance.TaskStatus.TRANSFERRED, testTask.getStatus());
        assertEquals(200L, testTask.getAssigneeId());
        assertEquals("目标用户", testTask.getAssigneeName());
    }

    @Test
    @DisplayName("测试委派任务 - 成功场景")
    void testDelegateTask_Success() {
        // 设置任务已分配
        testTask.setAssigneeId(TEST_USER_ID);
        testTask.setAssigneeName(TEST_USER_NAME);
        testTask.setStatus(TaskInstance.TaskStatus.CLAIMED);

        // 准备目标用户
        UserService.User targetUser = new UserService.User();
        targetUser.setId(200L);
        targetUser.setName("委派用户");

        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(userService.findById(200L))
                .thenReturn(Uni.createFrom().item(targetUser));
        
        when(taskHistoryRepository.persist(any(TaskHistory.class)))
                .thenReturn(Uni.createFrom().item(new TaskHistory()));
        
        when(taskInstanceRepository.persist(any(TaskInstance.class)))
                .thenReturn(Uni.createFrom().item(testTask));

        // 执行测试
        Uni<TaskControlService.TaskControlResult> result = taskControlService.delegateTask(
                TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, 200L, "临时委派处理");

        // 验证结果
        TaskControlService.TaskControlResult controlResult = result.await().indefinitely();
        assertTrue(controlResult.isSuccess());
        assertEquals("任务委派成功", controlResult.getMessage());

        // 验证任务状态
        assertEquals(TaskInstance.TaskStatus.DELEGATED, testTask.getStatus());
        assertEquals(200L, testTask.getAssigneeId());
        assertEquals("委派用户", testTask.getAssigneeName());
        assertEquals(TEST_USER_ID, testTask.getOriginalAssigneeId());
        assertEquals(TEST_USER_NAME, testTask.getOriginalAssigneeName());
    }

    @Test
    @DisplayName("测试签收任务 - 成功场景")
    void testClaimTask_Success() {
        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(taskHistoryRepository.persist(any(TaskHistory.class)))
                .thenReturn(Uni.createFrom().item(new TaskHistory()));
        
        when(taskInstanceRepository.persist(any(TaskInstance.class)))
                .thenReturn(Uni.createFrom().item(testTask));

        // 执行测试
        Uni<TaskControlService.TaskControlResult> result = taskControlService.claimTask(
                TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME);

        // 验证结果
        TaskControlService.TaskControlResult controlResult = result.await().indefinitely();
        assertTrue(controlResult.isSuccess());
        assertEquals("任务签收成功", controlResult.getMessage());

        // 验证任务状态
        assertEquals(TaskInstance.TaskStatus.CLAIMED, testTask.getStatus());
        assertEquals(TEST_USER_ID, testTask.getAssigneeId());
        assertEquals(TEST_USER_NAME, testTask.getAssigneeName());
        assertNotNull(testTask.getClaimTime());
    }

    @Test
    @DisplayName("测试签收任务 - 任务已被签收")
    void testClaimTask_AlreadyClaimed() {
        // 设置任务已被其他用户签收
        testTask.setStatus(TaskInstance.TaskStatus.CLAIMED);
        testTask.setAssigneeId(999L);
        testTask.setAssigneeName("其他用户");

        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));

        // 执行测试并验证异常
        assertThrows(BusinessException.class, () -> {
            taskControlService.claimTask(TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME)
                    .await().indefinitely();
        });
    }

    @Test
    @DisplayName("测试会签审批 - 部分审批")
    void testMultiAndApproval_Partial() {
        // 设置会签任务
        testTask.setApprovalType(TaskInstance.ApprovalType.MULTI_AND);
        testTask.setRequiredCount(3);
        testTask.setApprovalCount(0);

        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(taskHistoryRepository.persist(any(TaskHistory.class)))
                .thenReturn(Uni.createFrom().item(new TaskHistory()));
        
        when(taskInstanceRepository.persist(any(TaskInstance.class)))
                .thenReturn(Uni.createFrom().item(testTask));

        // 执行测试
        Uni<TaskControlService.TaskControlResult> result = taskControlService.approveTask(
                TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, "同意", null, null);

        // 验证结果
        TaskControlService.TaskControlResult controlResult = result.await().indefinitely();
        assertTrue(controlResult.isSuccess());
        assertEquals("审批成功，等待其他人审批", controlResult.getMessage());

        // 验证任务状态 - 应该还是CREATED状态，等待其他人审批
        assertEquals(TaskInstance.TaskStatus.CREATED, testTask.getStatus());
        assertEquals(1, testTask.getApprovalCount());
    }

    @Test
    @DisplayName("测试历史记录创建")
    void testTaskHistoryCreation() {
        // Mock依赖
        when(taskInstanceRepository.findById(TEST_TASK_ID))
                .thenReturn(Uni.createFrom().item(testTask));
        
        ArgumentCaptor<TaskHistory> historyCaptor = ArgumentCaptor.forClass(TaskHistory.class);
        when(taskHistoryRepository.persist(historyCaptor.capture()))
                .thenReturn(Uni.createFrom().item(new TaskHistory()));
        
        when(taskInstanceRepository.persist(any(TaskInstance.class)))
                .thenReturn(Uni.createFrom().item(testTask));
        
        when(processEngineService.completeTask(any(TaskInstance.class), any(), any()))
                .thenReturn(Uni.createFrom().item("success"));

        // 执行测试
        taskControlService.approveTask(TEST_TASK_ID, TEST_USER_ID, TEST_USER_NAME, 
                                     "审批意见", null, null)
                .await().indefinitely();

        // 验证历史记录
        TaskHistory capturedHistory = historyCaptor.getValue();
        assertNotNull(capturedHistory);
        assertEquals(TEST_TASK_ID, capturedHistory.getTaskInstanceId());
        assertEquals(TEST_USER_ID, capturedHistory.getOperatorId());
        assertEquals(TEST_USER_NAME, capturedHistory.getOperatorName());
        assertEquals(TaskInstance.TaskAction.APPROVE, capturedHistory.getAction());
        assertEquals(TaskHistory.ActionResult.APPROVED, capturedHistory.getResult());
        assertEquals("审批意见", capturedHistory.getComment());
    }
}
