# æµè¯ç¯å¢éç½®æä»¶
# application-test.properties

# æ°æ®åºéç½® - ä½¿ç¨H2åå­æ°æ®åº
quarkus.datasource.db-kind=h2
quarkus.datasource.username=sa
quarkus.datasource.password=
quarkus.datasource.jdbc.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
quarkus.datasource.jdbc.driver=org.h2.Driver

# Hibernateéç½®
quarkus.hibernate-orm.database.generation=drop-and-create
quarkus.hibernate-orm.log.sql=false
quarkus.hibernate-orm.sql-load-script=test-data.sql

# ç¼å­éç½®
quarkus.cache.caffeine.process-definition.initial-capacity=100
quarkus.cache.caffeine.process-definition.maximum-size=1000
quarkus.cache.caffeine.process-definition.expire-after-write=PT30M

quarkus.cache.caffeine.form-permission.initial-capacity=50
quarkus.cache.caffeine.form-permission.maximum-size=500
quarkus.cache.caffeine.form-permission.expire-after-write=PT15M

quarkus.cache.caffeine.timeout-config.initial-capacity=50
quarkus.cache.caffeine.timeout-config.maximum-size=500
quarkus.cache.caffeine.timeout-config.expire-after-write=PT15M

quarkus.cache.caffeine.notification-config.initial-capacity=50
quarkus.cache.caffeine.notification-config.maximum-size=500
quarkus.cache.caffeine.notification-config.expire-after-write=PT15M

quarkus.cache.caffeine.user-roles.initial-capacity=100
quarkus.cache.caffeine.user-roles.maximum-size=1000
quarkus.cache.caffeine.user-roles.expire-after-write=PT5M

quarkus.cache.caffeine.department-tree.initial-capacity=10
quarkus.cache.caffeine.department-tree.maximum-size=100
quarkus.cache.caffeine.department-tree.expire-after-write=PT30M

quarkus.cache.caffeine.process-variables.initial-capacity=200
quarkus.cache.caffeine.process-variables.maximum-size=2000
quarkus.cache.caffeine.process-variables.expire-after-write=PT5M

# æ¥å¿éç½®
quarkus.log.level=INFO
quarkus.log.category."com.visthink.workflow".level=DEBUG
quarkus.log.category."org.hibernate.SQL".level=DEBUG
quarkus.log.category."org.hibernate.type.descriptor.sql.BasicBinder".level=TRACE

# æ§è½çæ§éç½®
quarkus.micrometer.enabled=true
quarkus.micrometer.registry-enabled-default=true
quarkus.micrometer.binder.jvm=true
quarkus.micrometer.binder.system=true

# è°åº¦å¨éç½®
quarkus.scheduler.enabled=true
quarkus.scheduler.start-mode=forced

# äºå¡éç½®
quarkus.transaction-manager.enable-recovery=false

# æµè¯ä¸ç¨éç½®
# å¯ç¨æ§è½æµè¯
performance.test.enabled=true

# å¹¶åæ§å¶éç½®
workflow.concurrency.process-execution.max-permits=10
workflow.concurrency.task-execution.max-permits=50
workflow.concurrency.database-access.max-permits=25

# éæµéç½®
workflow.rate-limit.process-start.max-requests=5
workflow.rate-limit.process-start.time-window=PT1S
workflow.rate-limit.task-complete.max-requests=25
workflow.rate-limit.task-complete.time-window=PT1S

# è¶æ¶éç½®
workflow.timeout.default-task-timeout=PT30M
workflow.timeout.default-process-timeout=PT24H
workflow.timeout.check-interval=PT1M

# éç¥éç½®
workflow.notification.enabled=true
workflow.notification.email.enabled=false
workflow.notification.sms.enabled=false
workflow.notification.system.enabled=true

# è¡¨åæééç½®
workflow.form-permission.enabled=true
workflow.form-permission.cache-enabled=true
workflow.form-permission.default-permission=EDITABLE

# æµè¯æ°æ®éç½®
test.data.create-sample-processes=true
test.data.sample-process-count=10
test.data.create-sample-tasks=true
test.data.sample-task-count=50

# Mockæå¡éç½®
mock.user-service.enabled=true
mock.department-service.enabled=true
mock.role-service.enabled=true
mock.email-service.enabled=true
mock.sms-service.enabled=true

# æ§è½æµè¯éç½®
performance.test.concurrent-users=100
performance.test.thread-pool-size=20
performance.test.test-duration=PT5M
performance.test.ramp-up-time=PT30S

# åå­éç½®
quarkus.test.native-image-profile=test

# å¼åå·¥å·éç½®
quarkus.dev.instrumentation=true

# å¥åº·æ£æ¥éç½®
quarkus.health.enabled=true
quarkus.health.openapi.included=true

# OpenAPIéç½®
quarkus.swagger-ui.always-include=true
quarkus.swagger-ui.path=/test-api-docs

# å®å¨éç½®ï¼æµè¯ç¯å¢å³é­ï¼
quarkus.security.auth.enabled-in-dev-mode=false

# è·¨åéç½®
quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.methods=GET,PUT,POST,DELETE,OPTIONS
quarkus.http.cors.headers=accept,authorization,content-type,x-requested-with

# æµè¯ç«¯å£éç½®
quarkus.http.test-port=8081

# æ°æ®åºè¿æ¥æ± éç½®
quarkus.datasource.jdbc.min-size=1
quarkus.datasource.jdbc.max-size=10
quarkus.datasource.jdbc.acquisition-timeout=PT5S
quarkus.datasource.jdbc.leak-detection-interval=PT10M

# JVMéç½®
quarkus.test.arg-line=-Xmx1g -XX:+UseG1GC

# æµè¯æ¥åéç½®
quarkus.test.display-test-output=true
quarkus.test.include-tags=unit,integration
quarkus.test.exclude-tags=performance

# æ¨¡æå»¶è¿éç½®ï¼ç¨äºæµè¯ç½ç»å»¶è¿åºæ¯ï¼
test.simulation.network-delay.enabled=false
test.simulation.network-delay.min-ms=10
test.simulation.network-delay.max-ms=100

# éè¯¯æ³¨å¥éç½®ï¼ç¨äºæµè¯éè¯¯å¤çï¼
test.error-injection.enabled=false
test.error-injection.failure-rate=0.1

# æµè¯è¦ççéç½®
quarkus.jacoco.enabled=true
quarkus.jacoco.data-file=target/jacoco.exec
quarkus.jacoco.report-location=target/jacoco-report

# æµè¯æ°æ®æ¸çéç½®
test.cleanup.enabled=true
test.cleanup.retain-data=false
