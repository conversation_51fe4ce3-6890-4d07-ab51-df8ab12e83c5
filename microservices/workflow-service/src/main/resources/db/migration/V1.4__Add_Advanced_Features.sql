-- 添加高级功能相关表
-- V1.4__Add_Advanced_Features.sql

-- 创建表单权限表
CREATE TABLE IF NOT EXISTS wf_form_permission (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) COMMENT '租户ID',
    process_definition_id BIGINT NOT NULL COMMENT '流程定义ID',
    node_key VARCHAR(100) NOT NULL COMMENT '节点标识',
    node_name VARCHAR(200) COMMENT '节点名称',
    role_id BIGINT COMMENT '角色ID',
    role_name VARCHAR(100) COMMENT '角色名称',
    user_id BIGINT COMMENT '用户ID',
    user_name VARCHAR(100) COMMENT '用户名称',
    field_key VARCHAR(100) NOT NULL COMMENT '表单字段标识',
    field_name VARCHAR(200) COMMENT '表单字段名称',
    permission_type VARCHAR(20) NOT NULL COMMENT '权限类型：READONLY/EDITABLE/HIDDEN/REQUIRED/DISABLED',
    required BOOLEAN DEFAULT FALSE COMMENT '是否必填',
    default_value TEXT COMMENT '默认值',
    validation_rules TEXT COMMENT '验证规则JSON',
    condition_expression TEXT COMMENT '条件表达式',
    priority INTEGER DEFAULT 0 COMMENT '优先级',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 创建超时配置表
CREATE TABLE IF NOT EXISTS wf_timeout_config (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) COMMENT '租户ID',
    process_definition_id BIGINT NOT NULL COMMENT '流程定义ID',
    node_key VARCHAR(100) COMMENT '节点标识',
    node_name VARCHAR(200) COMMENT '节点名称',
    timeout_type VARCHAR(20) NOT NULL COMMENT '超时类型：TASK/PROCESS/STEP',
    timeout_duration INTEGER NOT NULL COMMENT '超时时长（分钟）',
    timeout_action VARCHAR(20) NOT NULL COMMENT '超时动作：AUTO_APPROVE/AUTO_REJECT/TRANSFER/DELEGATE/ESCALATE/TERMINATE/REMIND_ONLY',
    target_user_id BIGINT COMMENT '目标用户ID',
    target_user_name VARCHAR(100) COMMENT '目标用户名称',
    target_node_key VARCHAR(100) COMMENT '目标节点标识',
    reminder_times VARCHAR(200) COMMENT '提醒时间点（分钟）',
    reminder_methods VARCHAR(100) COMMENT '提醒方式',
    reminder_template TEXT COMMENT '提醒消息模板',
    timeout_template TEXT COMMENT '超时消息模板',
    condition_expression TEXT COMMENT '条件表达式',
    priority INTEGER DEFAULT 0 COMMENT '优先级',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 创建通知配置表
CREATE TABLE IF NOT EXISTS wf_notification_config (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) COMMENT '租户ID',
    process_definition_id BIGINT NOT NULL COMMENT '流程定义ID',
    node_key VARCHAR(100) COMMENT '节点标识',
    node_name VARCHAR(200) COMMENT '节点名称',
    event_type VARCHAR(30) NOT NULL COMMENT '事件类型',
    notification_method VARCHAR(20) NOT NULL COMMENT '通知方式：EMAIL/SMS/SYSTEM/WECHAT/DINGTALK/WEBHOOK',
    recipient_type VARCHAR(20) NOT NULL COMMENT '接收人类型：INITIATOR/ASSIGNEE/CANDIDATE/USER/ROLE/DEPARTMENT/EXPRESSION/SUPERIOR/SUBORDINATE',
    recipient_config TEXT COMMENT '接收人配置',
    title_template VARCHAR(500) COMMENT '消息标题模板',
    content_template TEXT COMMENT '消息内容模板',
    delay_minutes INTEGER DEFAULT 0 COMMENT '延迟发送时间（分钟）',
    repeat_send BOOLEAN DEFAULT FALSE COMMENT '是否重复发送',
    repeat_interval INTEGER COMMENT '重复间隔（分钟）',
    max_repeat_count INTEGER COMMENT '最大重复次数',
    condition_expression TEXT COMMENT '条件表达式',
    priority INTEGER DEFAULT 0 COMMENT '优先级',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 创建流程变量表
CREATE TABLE IF NOT EXISTS wf_process_variable (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) COMMENT '租户ID',
    process_instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    process_definition_id BIGINT NOT NULL COMMENT '流程定义ID',
    variable_name VARCHAR(100) NOT NULL COMMENT '变量名称',
    variable_type VARCHAR(50) NOT NULL COMMENT '变量类型',
    variable_value TEXT COMMENT '变量值',
    scope VARCHAR(20) DEFAULT 'PROCESS' COMMENT '变量作用域：PROCESS/TASK/GLOBAL',
    task_instance_id BIGINT COMMENT '任务实例ID（任务级变量）',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 创建子流程关系表
CREATE TABLE IF NOT EXISTS wf_subprocess_relation (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) COMMENT '租户ID',
    parent_process_instance_id BIGINT NOT NULL COMMENT '父流程实例ID',
    child_process_instance_id BIGINT NOT NULL COMMENT '子流程实例ID',
    parent_task_instance_id BIGINT COMMENT '父流程任务实例ID',
    subprocess_key VARCHAR(100) NOT NULL COMMENT '子流程标识',
    subprocess_name VARCHAR(200) COMMENT '子流程名称',
    relation_type VARCHAR(20) DEFAULT 'EMBEDDED' COMMENT '关系类型：EMBEDDED/CALL_ACTIVITY',
    input_variables TEXT COMMENT '输入变量JSON',
    output_variables TEXT COMMENT '输出变量JSON',
    status VARCHAR(20) DEFAULT 'RUNNING' COMMENT '状态：RUNNING/COMPLETED/CANCELLED/TERMINATED',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_time TIMESTAMP COMMENT '完成时间'
);

-- 创建表单权限表的索引
CREATE INDEX IF NOT EXISTS idx_form_permission_process_def ON wf_form_permission(process_definition_id);
CREATE INDEX IF NOT EXISTS idx_form_permission_node ON wf_form_permission(node_key);
CREATE INDEX IF NOT EXISTS idx_form_permission_role ON wf_form_permission(role_id);
CREATE INDEX IF NOT EXISTS idx_form_permission_field ON wf_form_permission(field_key);
CREATE INDEX IF NOT EXISTS idx_form_permission_enabled ON wf_form_permission(enabled);

-- 创建超时配置表的索引
CREATE INDEX IF NOT EXISTS idx_timeout_config_process_def ON wf_timeout_config(process_definition_id);
CREATE INDEX IF NOT EXISTS idx_timeout_config_node ON wf_timeout_config(node_key);
CREATE INDEX IF NOT EXISTS idx_timeout_config_type ON wf_timeout_config(timeout_type);
CREATE INDEX IF NOT EXISTS idx_timeout_config_enabled ON wf_timeout_config(enabled);

-- 创建通知配置表的索引
CREATE INDEX IF NOT EXISTS idx_notification_config_process_def ON wf_notification_config(process_definition_id);
CREATE INDEX IF NOT EXISTS idx_notification_config_event ON wf_notification_config(event_type);
CREATE INDEX IF NOT EXISTS idx_notification_config_method ON wf_notification_config(notification_method);
CREATE INDEX IF NOT EXISTS idx_notification_config_enabled ON wf_notification_config(enabled);

-- 创建流程变量表的索引
CREATE INDEX IF NOT EXISTS idx_process_variable_instance ON wf_process_variable(process_instance_id);
CREATE INDEX IF NOT EXISTS idx_process_variable_name ON wf_process_variable(variable_name);
CREATE INDEX IF NOT EXISTS idx_process_variable_scope ON wf_process_variable(scope);
CREATE INDEX IF NOT EXISTS idx_process_variable_task ON wf_process_variable(task_instance_id);

-- 创建子流程关系表的索引
CREATE INDEX IF NOT EXISTS idx_subprocess_parent ON wf_subprocess_relation(parent_process_instance_id);
CREATE INDEX IF NOT EXISTS idx_subprocess_child ON wf_subprocess_relation(child_process_instance_id);
CREATE INDEX IF NOT EXISTS idx_subprocess_parent_task ON wf_subprocess_relation(parent_task_instance_id);
CREATE INDEX IF NOT EXISTS idx_subprocess_status ON wf_subprocess_relation(status);

-- 为TaskInstance表添加审批类型字段（如果还没有）
ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS approval_type VARCHAR(20) DEFAULT 'SINGLE' COMMENT '审批类型：SINGLE/MULTI_AND/MULTI_OR/SEQUENTIAL';

-- 为TaskInstance表添加索引
CREATE INDEX IF NOT EXISTS idx_task_instance_approval_type ON wf_task_instance(approval_type);

-- 为TaskInstanceRepository添加查询活跃任务的方法需要的索引
CREATE INDEX IF NOT EXISTS idx_task_instance_status_created ON wf_task_instance(status, created_time);

-- 添加表注释
COMMENT ON TABLE wf_form_permission IS '表单权限配置表';
COMMENT ON TABLE wf_timeout_config IS '超时配置表';
COMMENT ON TABLE wf_notification_config IS '通知配置表';
COMMENT ON TABLE wf_process_variable IS '流程变量表';
COMMENT ON TABLE wf_subprocess_relation IS '子流程关系表';

-- 添加列注释
COMMENT ON COLUMN wf_form_permission.permission_type IS '权限类型：READONLY-只读，EDITABLE-可编辑，HIDDEN-隐藏，REQUIRED-必填，DISABLED-禁用';
COMMENT ON COLUMN wf_timeout_config.timeout_type IS '超时类型：TASK-任务超时，PROCESS-流程超时，STEP-步骤超时';
COMMENT ON COLUMN wf_timeout_config.timeout_action IS '超时动作：AUTO_APPROVE-自动同意，AUTO_REJECT-自动驳回，TRANSFER-转办，DELEGATE-委派，ESCALATE-升级，TERMINATE-终止，REMIND_ONLY-仅提醒';
COMMENT ON COLUMN wf_notification_config.event_type IS '事件类型：PROCESS_START-流程启动，PROCESS_END-流程结束，TASK_CREATE-任务创建，TASK_COMPLETE-任务完成等';
COMMENT ON COLUMN wf_notification_config.notification_method IS '通知方式：EMAIL-邮件，SMS-短信，SYSTEM-系统通知，WECHAT-微信，DINGTALK-钉钉，WEBHOOK-Webhook';
COMMENT ON COLUMN wf_notification_config.recipient_type IS '接收人类型：INITIATOR-发起人，ASSIGNEE-当前处理人，CANDIDATE-候选人，USER-指定用户，ROLE-指定角色，DEPARTMENT-指定部门，EXPRESSION-表达式，SUPERIOR-上级领导，SUBORDINATE-下级员工';
COMMENT ON COLUMN wf_process_variable.scope IS '变量作用域：PROCESS-流程级，TASK-任务级，GLOBAL-全局级';
COMMENT ON COLUMN wf_subprocess_relation.relation_type IS '关系类型：EMBEDDED-嵌入式子流程，CALL_ACTIVITY-调用活动';

-- 插入一些示例配置数据（可选）
-- 示例：表单权限配置
INSERT INTO wf_form_permission (
    process_definition_id, node_key, node_name, field_key, field_name, 
    permission_type, required, priority, enabled
) VALUES 
(1, 'start', '开始节点', 'applicant_name', '申请人姓名', 'EDITABLE', true, 10, true),
(1, 'start', '开始节点', 'apply_reason', '申请原因', 'REQUIRED', true, 10, true),
(1, 'approve', '审批节点', 'applicant_name', '申请人姓名', 'READONLY', false, 10, true),
(1, 'approve', '审批节点', 'approve_opinion', '审批意见', 'EDITABLE', true, 10, true)
ON CONFLICT DO NOTHING;

-- 示例：超时配置
INSERT INTO wf_timeout_config (
    process_definition_id, node_key, node_name, timeout_type, timeout_duration, 
    timeout_action, reminder_times, reminder_methods, priority, enabled
) VALUES 
(1, 'approve', '审批节点', 'TASK', 1440, 'REMIND_ONLY', '720,1200', 'EMAIL,SYSTEM', 10, true),
(1, NULL, '整个流程', 'PROCESS', 4320, 'TERMINATE', '2880,3600', 'EMAIL,SMS', 5, true)
ON CONFLICT DO NOTHING;

-- 示例：通知配置
INSERT INTO wf_notification_config (
    process_definition_id, node_key, event_type, notification_method, recipient_type,
    title_template, content_template, priority, enabled
) VALUES 
(1, NULL, 'PROCESS_START', 'SYSTEM', 'INITIATOR', 
 '流程启动通知', '您的${processName}流程已启动，流程实例ID：${processInstanceId}', 10, true),
(1, 'approve', 'TASK_CREATE', 'EMAIL', 'ASSIGNEE', 
 '待办任务通知', '您有一个新的待办任务：${taskName}，请及时处理。', 10, true),
(1, NULL, 'PROCESS_END', 'SYSTEM', 'INITIATOR', 
 '流程完成通知', '您的${processName}流程已完成。', 10, true)
ON CONFLICT DO NOTHING;
