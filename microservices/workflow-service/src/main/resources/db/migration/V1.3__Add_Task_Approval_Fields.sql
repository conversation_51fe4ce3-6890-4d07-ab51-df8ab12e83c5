-- 为任务实例表添加审批机制相关字段
-- V1.3__Add_Task_Approval_Fields.sql

-- 添加新的任务状态
ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS original_assignee_id BIGINT COMMENT '原始处理人ID（用于委派）';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS original_assignee_name VARCHAR(100) COMMENT '原始处理人姓名';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS delegator_id BIGINT COMMENT '委派人ID';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS delegator_name VARCHAR(100) COMMENT '委派人姓名';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS approval_count INTEGER DEFAULT 0 COMMENT '已审批人数';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS required_count INTEGER DEFAULT 1 COMMENT '需要审批人数';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS reject_reason TEXT COMMENT '驳回原因';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS transfer_reason TEXT COMMENT '转办原因';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS delegate_reason TEXT COMMENT '委派原因';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS approval_sequence INTEGER DEFAULT 0 COMMENT '审批顺序（用于依次审批）';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS current_sequence INTEGER DEFAULT 0 COMMENT '当前审批顺序';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS timeout_action VARCHAR(50) COMMENT '超时处理动作';

ALTER TABLE wf_task_instance 
ADD COLUMN IF NOT EXISTS auto_claim BOOLEAN DEFAULT FALSE COMMENT '是否自动签收';

-- 创建任务历史记录表
CREATE TABLE IF NOT EXISTS wf_task_history (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(50) COMMENT '租户ID',
    task_instance_id BIGINT NOT NULL COMMENT '任务实例ID',
    process_instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    process_definition_id BIGINT NOT NULL COMMENT '流程定义ID',
    node_key VARCHAR(100) NOT NULL COMMENT '节点标识',
    node_name VARCHAR(200) COMMENT '节点名称',
    task_key VARCHAR(100) COMMENT '任务标识',
    task_name VARCHAR(200) COMMENT '任务名称',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(100) COMMENT '操作人姓名',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    result VARCHAR(50) COMMENT '操作结果',
    comment TEXT COMMENT '审批意见/操作说明',
    form_data TEXT COMMENT '表单数据JSON',
    variables TEXT COMMENT '任务变量JSON',
    duration BIGINT COMMENT '处理时长（毫秒）',
    target_user_id BIGINT COMMENT '目标用户ID（转办、委派时使用）',
    target_user_name VARCHAR(100) COMMENT '目标用户姓名',
    target_node_key VARCHAR(100) COMMENT '目标节点标识（驳回时使用）',
    action_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 创建任务历史记录表的索引
CREATE INDEX IF NOT EXISTS idx_task_history_task_id ON wf_task_history(task_instance_id);
CREATE INDEX IF NOT EXISTS idx_task_history_process_instance ON wf_task_history(process_instance_id);
CREATE INDEX IF NOT EXISTS idx_task_history_operator ON wf_task_history(operator_id);
CREATE INDEX IF NOT EXISTS idx_task_history_action ON wf_task_history(action);
CREATE INDEX IF NOT EXISTS idx_task_history_time ON wf_task_history(action_time);

-- 为任务实例表添加新的索引
CREATE INDEX IF NOT EXISTS idx_task_instance_original_assignee ON wf_task_instance(original_assignee_id);
CREATE INDEX IF NOT EXISTS idx_task_instance_delegator ON wf_task_instance(delegator_id);
CREATE INDEX IF NOT EXISTS idx_task_instance_approval_count ON wf_task_instance(approval_count);
CREATE INDEX IF NOT EXISTS idx_task_instance_sequence ON wf_task_instance(approval_sequence, current_sequence);

-- 添加外键约束（如果需要）
-- ALTER TABLE wf_task_history 
-- ADD CONSTRAINT fk_task_history_task_instance 
-- FOREIGN KEY (task_instance_id) REFERENCES wf_task_instance(id) ON DELETE CASCADE;

-- ALTER TABLE wf_task_history 
-- ADD CONSTRAINT fk_task_history_process_instance 
-- FOREIGN KEY (process_instance_id) REFERENCES wf_process_instance(id) ON DELETE CASCADE;

-- 添加表注释
COMMENT ON TABLE wf_task_history IS '任务历史记录表';

-- 添加列注释
COMMENT ON COLUMN wf_task_history.id IS '主键ID';
COMMENT ON COLUMN wf_task_history.tenant_id IS '租户ID';
COMMENT ON COLUMN wf_task_history.task_instance_id IS '任务实例ID';
COMMENT ON COLUMN wf_task_history.process_instance_id IS '流程实例ID';
COMMENT ON COLUMN wf_task_history.process_definition_id IS '流程定义ID';
COMMENT ON COLUMN wf_task_history.node_key IS '节点标识';
COMMENT ON COLUMN wf_task_history.node_name IS '节点名称';
COMMENT ON COLUMN wf_task_history.task_key IS '任务标识';
COMMENT ON COLUMN wf_task_history.task_name IS '任务名称';
COMMENT ON COLUMN wf_task_history.operator_id IS '操作人ID';
COMMENT ON COLUMN wf_task_history.operator_name IS '操作人姓名';
COMMENT ON COLUMN wf_task_history.action IS '操作类型：APPROVE/REJECT/TRANSFER/DELEGATE/ADD_SIGN/REMOVE_SIGN/CLAIM/CANCEL/TERMINATE';
COMMENT ON COLUMN wf_task_history.result IS '操作结果：SUCCESS/FAILED/APPROVED/REJECTED/TRANSFERRED/DELEGATED/CANCELLED/TERMINATED';
COMMENT ON COLUMN wf_task_history.comment IS '审批意见/操作说明';
COMMENT ON COLUMN wf_task_history.form_data IS '表单数据JSON';
COMMENT ON COLUMN wf_task_history.variables IS '任务变量JSON';
COMMENT ON COLUMN wf_task_history.duration IS '处理时长（毫秒）';
COMMENT ON COLUMN wf_task_history.target_user_id IS '目标用户ID（转办、委派时使用）';
COMMENT ON COLUMN wf_task_history.target_user_name IS '目标用户姓名';
COMMENT ON COLUMN wf_task_history.target_node_key IS '目标节点标识（驳回时使用）';
COMMENT ON COLUMN wf_task_history.action_time IS '操作时间';
COMMENT ON COLUMN wf_task_history.created_time IS '创建时间';

-- 更新任务实例表的列注释
COMMENT ON COLUMN wf_task_instance.original_assignee_id IS '原始处理人ID（用于委派）';
COMMENT ON COLUMN wf_task_instance.original_assignee_name IS '原始处理人姓名';
COMMENT ON COLUMN wf_task_instance.delegator_id IS '委派人ID';
COMMENT ON COLUMN wf_task_instance.delegator_name IS '委派人姓名';
COMMENT ON COLUMN wf_task_instance.approval_count IS '已审批人数';
COMMENT ON COLUMN wf_task_instance.required_count IS '需要审批人数';
COMMENT ON COLUMN wf_task_instance.reject_reason IS '驳回原因';
COMMENT ON COLUMN wf_task_instance.transfer_reason IS '转办原因';
COMMENT ON COLUMN wf_task_instance.delegate_reason IS '委派原因';
COMMENT ON COLUMN wf_task_instance.approval_sequence IS '审批顺序（用于依次审批）';
COMMENT ON COLUMN wf_task_instance.current_sequence IS '当前审批顺序';
COMMENT ON COLUMN wf_task_instance.timeout_action IS '超时处理动作';
COMMENT ON COLUMN wf_task_instance.auto_claim IS '是否自动签收';

-- 创建任务历史记录的分区表（可选，用于大数据量场景）
-- CREATE TABLE wf_task_history_2024 PARTITION OF wf_task_history
-- FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- 插入一些示例数据（可选）
-- INSERT INTO wf_task_history (
--     task_instance_id, process_instance_id, process_definition_id,
--     node_key, node_name, task_key, task_name,
--     operator_id, operator_name, action, result, comment
-- ) VALUES (
--     1, 1, 1,
--     'start', '开始节点', 'start_task', '开始任务',
--     1, '系统管理员', 'APPROVE', 'SUCCESS', '流程启动'
-- );
