-- ===================================================================
-- VisThink ERP 工作流引擎数据库设计
-- 版本: V1.0.0
-- 创建时间: 2024-06-24
-- 描述: 工作流引擎核心数据表结构
-- ===================================================================

-- 流程定义表
CREATE TABLE wf_process_definition (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程标识',
    process_name VARCHAR(200) NOT NULL COMMENT '流程名称',
    process_version INTEGER NOT NULL DEFAULT 1 COMMENT '流程版本',
    category VARCHAR(100) COMMENT '流程分类',
    description TEXT COMMENT '流程描述',
    designer_type VARCHAR(20) NOT NULL DEFAULT 'SIMPLE' COMMENT '设计器类型：SIMPLE/BPMN',
    process_definition TEXT NOT NULL COMMENT '流程定义JSON',
    bpmn_xml TEXT COMMENT 'BPMN XML定义',
    form_definition TEXT COMMENT '表单定义JSON',
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT' COMMENT '状态：DRAFT/ACTIVE/SUSPENDED/DELETED',
    is_latest BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否最新版本',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time TIMESTAMP COMMENT '更新时间',
    UNIQUE(tenant_id, process_key, process_version)
);

-- 流程实例表
CREATE TABLE wf_process_instance (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_definition_id BIGINT NOT NULL COMMENT '流程定义ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程标识',
    process_name VARCHAR(200) NOT NULL COMMENT '流程名称',
    business_key VARCHAR(200) COMMENT '业务键',
    title VARCHAR(500) NOT NULL COMMENT '流程标题',
    initiator_id BIGINT NOT NULL COMMENT '发起人ID',
    initiator_name VARCHAR(100) NOT NULL COMMENT '发起人姓名',
    current_node_keys TEXT COMMENT '当前节点标识列表',
    status VARCHAR(20) NOT NULL DEFAULT 'RUNNING' COMMENT '状态：RUNNING/COMPLETED/TERMINATED/SUSPENDED',
    priority INTEGER NOT NULL DEFAULT 0 COMMENT '优先级',
    form_data TEXT COMMENT '表单数据JSON',
    variables TEXT COMMENT '流程变量JSON',
    start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    duration BIGINT COMMENT '持续时间(毫秒)',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 任务实例表
CREATE TABLE wf_task_instance (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    process_definition_id BIGINT NOT NULL COMMENT '流程定义ID',
    node_key VARCHAR(100) NOT NULL COMMENT '节点标识',
    node_name VARCHAR(200) NOT NULL COMMENT '节点名称',
    node_type VARCHAR(50) NOT NULL COMMENT '节点类型',
    task_key VARCHAR(200) NOT NULL COMMENT '任务标识',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    assignee_id BIGINT COMMENT '指定处理人ID',
    assignee_name VARCHAR(100) COMMENT '指定处理人姓名',
    candidate_users TEXT COMMENT '候选用户ID列表',
    candidate_groups TEXT COMMENT '候选用户组ID列表',
    approval_type VARCHAR(20) NOT NULL DEFAULT 'SINGLE' COMMENT '审批类型：SINGLE/MULTI_AND/MULTI_OR/SEQUENTIAL',
    status VARCHAR(20) NOT NULL DEFAULT 'CREATED' COMMENT '状态：CREATED/CLAIMED/COMPLETED/CANCELLED/SUSPENDED',
    priority INTEGER NOT NULL DEFAULT 0 COMMENT '优先级',
    due_date TIMESTAMP COMMENT '到期时间',
    claim_time TIMESTAMP COMMENT '签收时间',
    complete_time TIMESTAMP COMMENT '完成时间',
    form_data TEXT COMMENT '表单数据JSON',
    variables TEXT COMMENT '任务变量JSON',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 任务处理记录表
CREATE TABLE wf_task_history (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    task_instance_id BIGINT NOT NULL COMMENT '任务实例ID',
    process_instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    assignee_id BIGINT NOT NULL COMMENT '处理人ID',
    assignee_name VARCHAR(100) NOT NULL COMMENT '处理人姓名',
    action VARCHAR(50) NOT NULL COMMENT '操作类型：APPROVE/REJECT/TRANSFER/DELEGATE/ADD_SIGN/CANCEL',
    result VARCHAR(20) COMMENT '处理结果：APPROVED/REJECTED/TRANSFERRED/DELEGATED',
    comment TEXT COMMENT '处理意见',
    attachments TEXT COMMENT '附件信息JSON',
    duration BIGINT COMMENT '处理耗时(毫秒)',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 流程抄送表
CREATE TABLE wf_process_copy (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    task_instance_id BIGINT COMMENT '任务实例ID',
    copy_to_id BIGINT NOT NULL COMMENT '抄送人ID',
    copy_to_name VARCHAR(100) NOT NULL COMMENT '抄送人姓名',
    copy_type VARCHAR(20) NOT NULL DEFAULT 'PROCESS' COMMENT '抄送类型：PROCESS/TASK/RESULT',
    status VARCHAR(20) NOT NULL DEFAULT 'UNREAD' COMMENT '状态：UNREAD/READ',
    read_time TIMESTAMP COMMENT '阅读时间',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 流程变量表
CREATE TABLE wf_process_variable (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    task_instance_id BIGINT COMMENT '任务实例ID',
    variable_name VARCHAR(200) NOT NULL COMMENT '变量名',
    variable_type VARCHAR(50) NOT NULL COMMENT '变量类型',
    variable_value TEXT COMMENT '变量值',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 流程事件表
CREATE TABLE wf_process_event (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_instance_id BIGINT NOT NULL COMMENT '流程实例ID',
    task_instance_id BIGINT COMMENT '任务实例ID',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
    event_name VARCHAR(200) NOT NULL COMMENT '事件名称',
    event_data TEXT COMMENT '事件数据JSON',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING/PROCESSED/FAILED',
    retry_count INTEGER NOT NULL DEFAULT 0 COMMENT '重试次数',
    next_retry_time TIMESTAMP COMMENT '下次重试时间',
    error_message TEXT COMMENT '错误信息',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    processed_time TIMESTAMP COMMENT '处理时间'
);

-- 流程模板表
CREATE TABLE wf_process_template (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    template_key VARCHAR(100) NOT NULL COMMENT '模板标识',
    template_name VARCHAR(200) NOT NULL COMMENT '模板名称',
    category VARCHAR(100) COMMENT '模板分类',
    description TEXT COMMENT '模板描述',
    designer_type VARCHAR(20) NOT NULL DEFAULT 'SIMPLE' COMMENT '设计器类型',
    template_definition TEXT NOT NULL COMMENT '模板定义JSON',
    form_template TEXT COMMENT '表单模板JSON',
    is_public BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否公开模板',
    usage_count INTEGER NOT NULL DEFAULT 0 COMMENT '使用次数',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 创建索引
CREATE INDEX idx_process_definition_tenant_key ON wf_process_definition(tenant_id, process_key);
CREATE INDEX idx_process_definition_status ON wf_process_definition(status);
CREATE INDEX idx_process_instance_tenant ON wf_process_instance(tenant_id);
CREATE INDEX idx_process_instance_initiator ON wf_process_instance(initiator_id);
CREATE INDEX idx_process_instance_status ON wf_process_instance(status);
CREATE INDEX idx_task_instance_tenant ON wf_task_instance(tenant_id);
CREATE INDEX idx_task_instance_assignee ON wf_task_instance(assignee_id);
CREATE INDEX idx_task_instance_status ON wf_task_instance(status);
CREATE INDEX idx_task_instance_process ON wf_task_instance(process_instance_id);
CREATE INDEX idx_task_history_tenant ON wf_task_history(tenant_id);
CREATE INDEX idx_task_history_assignee ON wf_task_history(assignee_id);
CREATE INDEX idx_process_copy_tenant ON wf_process_copy(tenant_id);
CREATE INDEX idx_process_copy_user ON wf_process_copy(copy_to_id);
CREATE INDEX idx_process_variable_process ON wf_process_variable(process_instance_id);
CREATE INDEX idx_process_event_status ON wf_process_event(status);
CREATE INDEX idx_process_template_tenant ON wf_process_template(tenant_id);

-- 添加外键约束
ALTER TABLE wf_process_instance ADD CONSTRAINT fk_process_instance_definition 
    FOREIGN KEY (process_definition_id) REFERENCES wf_process_definition(id);
ALTER TABLE wf_task_instance ADD CONSTRAINT fk_task_instance_process 
    FOREIGN KEY (process_instance_id) REFERENCES wf_process_instance(id);
ALTER TABLE wf_task_history ADD CONSTRAINT fk_task_history_task 
    FOREIGN KEY (task_instance_id) REFERENCES wf_task_instance(id);
ALTER TABLE wf_process_copy ADD CONSTRAINT fk_process_copy_process 
    FOREIGN KEY (process_instance_id) REFERENCES wf_process_instance(id);
ALTER TABLE wf_process_variable ADD CONSTRAINT fk_process_variable_process 
    FOREIGN KEY (process_instance_id) REFERENCES wf_process_instance(id);
ALTER TABLE wf_process_event ADD CONSTRAINT fk_process_event_process 
    FOREIGN KEY (process_instance_id) REFERENCES wf_process_instance(id);

-- 流程表单权限表
CREATE TABLE wf_form_permission (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程标识',
    node_key VARCHAR(100) NOT NULL COMMENT '节点标识',
    field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
    permission VARCHAR(20) NOT NULL DEFAULT 'EDITABLE' COMMENT '权限类型：HIDDEN/READ_ONLY/EDITABLE',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间',
    UNIQUE(tenant_id, process_key, node_key, field_name)
);

-- 流程超时配置表
CREATE TABLE wf_timeout_config (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程标识',
    node_key VARCHAR(100) NOT NULL COMMENT '节点标识',
    timeout_duration BIGINT NOT NULL COMMENT '超时时长(毫秒)',
    timeout_action VARCHAR(50) NOT NULL COMMENT '超时动作：AUTO_APPROVE/AUTO_REJECT/ESCALATE/NOTIFY',
    escalate_to_user_id BIGINT COMMENT '升级处理人ID',
    notification_template TEXT COMMENT '通知模板',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 流程提醒配置表
CREATE TABLE wf_reminder_config (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程标识',
    node_key VARCHAR(100) COMMENT '节点标识',
    reminder_type VARCHAR(50) NOT NULL COMMENT '提醒类型：TASK_CREATED/TASK_OVERDUE/PROCESS_STARTED',
    reminder_time BIGINT NOT NULL COMMENT '提醒时间(相对时间，毫秒)',
    notification_channels TEXT NOT NULL COMMENT '通知渠道：EMAIL/SMS/SYSTEM',
    template_content TEXT COMMENT '模板内容',
    is_repeat BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否重复提醒',
    repeat_interval BIGINT COMMENT '重复间隔(毫秒)',
    max_repeat_count INTEGER COMMENT '最大重复次数',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 流程子流程关系表
CREATE TABLE wf_sub_process (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    parent_process_instance_id BIGINT NOT NULL COMMENT '父流程实例ID',
    parent_task_instance_id BIGINT COMMENT '父任务实例ID',
    sub_process_instance_id BIGINT NOT NULL COMMENT '子流程实例ID',
    sub_process_type VARCHAR(20) NOT NULL DEFAULT 'SYNC' COMMENT '子流程类型：SYNC/ASYNC',
    status VARCHAR(20) NOT NULL DEFAULT 'RUNNING' COMMENT '状态：RUNNING/COMPLETED/FAILED',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_time TIMESTAMP COMMENT '完成时间'
);

-- 流程分支条件表
CREATE TABLE wf_branch_condition (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程标识',
    edge_id VARCHAR(100) NOT NULL COMMENT '连线ID',
    condition_name VARCHAR(200) COMMENT '条件名称',
    condition_expression TEXT NOT NULL COMMENT '条件表达式',
    condition_type VARCHAR(50) NOT NULL DEFAULT 'SCRIPT' COMMENT '条件类型：SCRIPT/RULE/FORM_FIELD',
    priority INTEGER NOT NULL DEFAULT 0 COMMENT '优先级',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否默认分支',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 流程监听器配置表
CREATE TABLE wf_listener_config (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(64) NOT NULL COMMENT '租户ID',
    process_key VARCHAR(100) NOT NULL COMMENT '流程标识',
    node_key VARCHAR(100) COMMENT '节点标识',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型：PROCESS_START/PROCESS_END/TASK_CREATE/TASK_COMPLETE',
    listener_type VARCHAR(50) NOT NULL COMMENT '监听器类型：HTTP_REQUEST/EMAIL/SMS/SCRIPT',
    listener_config TEXT NOT NULL COMMENT '监听器配置JSON',
    execution_order INTEGER NOT NULL DEFAULT 0 COMMENT '执行顺序',
    is_async BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否异步执行',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP COMMENT '更新时间'
);

-- 创建额外索引
CREATE INDEX idx_form_permission_process_node ON wf_form_permission(process_key, node_key);
CREATE INDEX idx_timeout_config_process_node ON wf_timeout_config(process_key, node_key);
CREATE INDEX idx_reminder_config_process ON wf_reminder_config(process_key);
CREATE INDEX idx_sub_process_parent ON wf_sub_process(parent_process_instance_id);
CREATE INDEX idx_sub_process_child ON wf_sub_process(sub_process_instance_id);
CREATE INDEX idx_branch_condition_process ON wf_branch_condition(process_key);
CREATE INDEX idx_listener_config_process ON wf_listener_config(process_key);

-- 添加外键约束
ALTER TABLE wf_sub_process ADD CONSTRAINT fk_sub_process_parent
    FOREIGN KEY (parent_process_instance_id) REFERENCES wf_process_instance(id);
ALTER TABLE wf_sub_process ADD CONSTRAINT fk_sub_process_child
    FOREIGN KEY (sub_process_instance_id) REFERENCES wf_process_instance(id);

-- 添加表注释
COMMENT ON TABLE wf_process_definition IS '流程定义表';
COMMENT ON TABLE wf_process_instance IS '流程实例表';
COMMENT ON TABLE wf_task_instance IS '任务实例表';
COMMENT ON TABLE wf_task_history IS '任务处理记录表';
COMMENT ON TABLE wf_process_copy IS '流程抄送表';
COMMENT ON TABLE wf_process_variable IS '流程变量表';
COMMENT ON TABLE wf_process_event IS '流程事件表';
COMMENT ON TABLE wf_process_template IS '流程模板表';
COMMENT ON TABLE wf_form_permission IS '流程表单权限表';
COMMENT ON TABLE wf_timeout_config IS '流程超时配置表';
COMMENT ON TABLE wf_reminder_config IS '流程提醒配置表';
COMMENT ON TABLE wf_sub_process IS '流程子流程关系表';
COMMENT ON TABLE wf_branch_condition IS '流程分支条件表';
COMMENT ON TABLE wf_listener_config IS '流程监听器配置表';
