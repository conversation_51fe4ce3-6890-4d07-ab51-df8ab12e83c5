package com.visthink.workflow.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 表单权限实体
 * 定义不同节点、角色对表单字段的访问权限
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "wf_form_permission", indexes = {
    @Index(name = "idx_form_permission_process_def", columnList = "process_definition_id"),
    @Index(name = "idx_form_permission_node", columnList = "node_key"),
    @Index(name = "idx_form_permission_role", columnList = "role_id"),
    @Index(name = "idx_form_permission_field", columnList = "field_key")
})
@Comment("表单权限表")
public class FormPermission {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", length = 50)
    @Comment("租户ID")
    private String tenantId;

    /**
     * 流程定义ID
     */
    @Column(name = "process_definition_id", nullable = false)
    @Comment("流程定义ID")
    private Long processDefinitionId;

    /**
     * 节点标识
     */
    @Column(name = "node_key", length = 100, nullable = false)
    @Comment("节点标识")
    private String nodeKey;

    /**
     * 节点名称
     */
    @Column(name = "node_name", length = 200)
    @Comment("节点名称")
    private String nodeName;

    /**
     * 角色ID（可选，如果为空则对所有角色生效）
     */
    @Column(name = "role_id")
    @Comment("角色ID")
    private Long roleId;

    /**
     * 角色名称
     */
    @Column(name = "role_name", length = 100)
    @Comment("角色名称")
    private String roleName;

    /**
     * 用户ID（可选，如果为空则对所有用户生效）
     */
    @Column(name = "user_id")
    @Comment("用户ID")
    private Long userId;

    /**
     * 用户名称
     */
    @Column(name = "user_name", length = 100)
    @Comment("用户名称")
    private String userName;

    /**
     * 表单字段标识
     */
    @Column(name = "field_key", length = 100, nullable = false)
    @Comment("表单字段标识")
    private String fieldKey;

    /**
     * 表单字段名称
     */
    @Column(name = "field_name", length = 200)
    @Comment("表单字段名称")
    private String fieldName;

    /**
     * 权限类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "permission_type", length = 20, nullable = false)
    @Comment("权限类型")
    private PermissionType permissionType;

    /**
     * 是否必填
     */
    @Column(name = "required")
    @Comment("是否必填")
    private Boolean required = false;

    /**
     * 默认值
     */
    @Column(name = "default_value", columnDefinition = "TEXT")
    @Comment("默认值")
    private String defaultValue;

    /**
     * 验证规则JSON
     */
    @Column(name = "validation_rules", columnDefinition = "TEXT")
    @Comment("验证规则JSON")
    private String validationRules;

    /**
     * 条件表达式（满足条件时权限才生效）
     */
    @Column(name = "condition_expression", columnDefinition = "TEXT")
    @Comment("条件表达式")
    private String conditionExpression;

    /**
     * 优先级（数字越大优先级越高）
     */
    @Column(name = "priority")
    @Comment("优先级")
    private Integer priority = 0;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    @Comment("是否启用")
    private Boolean enabled = true;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @Comment("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @Comment("更新时间")
    private LocalDateTime updatedTime;

    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        READONLY("只读"),
        EDITABLE("可编辑"),
        HIDDEN("隐藏"),
        REQUIRED("必填"),
        DISABLED("禁用");

        private final String description;

        PermissionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public FormPermission() {
        this.createdTime = LocalDateTime.now();
    }

    public FormPermission(Long processDefinitionId, String nodeKey, String fieldKey, PermissionType permissionType) {
        this();
        this.processDefinitionId = processDefinitionId;
        this.nodeKey = nodeKey;
        this.fieldKey = fieldKey;
        this.permissionType = permissionType;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public void setNodeKey(String nodeKey) {
        this.nodeKey = nodeKey;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFieldKey() {
        return fieldKey;
    }

    public void setFieldKey(String fieldKey) {
        this.fieldKey = fieldKey;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public PermissionType getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(PermissionType permissionType) {
        this.permissionType = permissionType;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public String getValidationRules() {
        return validationRules;
    }

    public void setValidationRules(String validationRules) {
        this.validationRules = validationRules;
    }

    public String getConditionExpression() {
        return conditionExpression;
    }

    public void setConditionExpression(String conditionExpression) {
        this.conditionExpression = conditionExpression;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "FormPermission{" +
                "id=" + id +
                ", processDefinitionId=" + processDefinitionId +
                ", nodeKey='" + nodeKey + '\'' +
                ", fieldKey='" + fieldKey + '\'' +
                ", permissionType=" + permissionType +
                ", enabled=" + enabled +
                '}';
    }
}
