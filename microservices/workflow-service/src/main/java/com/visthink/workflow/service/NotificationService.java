package com.visthink.workflow.service;

import com.visthink.workflow.entity.NotificationConfig;
import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.entity.ProcessInstance;
import com.visthink.workflow.repository.NotificationConfigRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通知服务
 * 负责处理各种通知的发送逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Inject
    NotificationConfigRepository notificationConfigRepository;

    @Inject
    UserService userService;

    @Inject
    RoleService roleService;

    @Inject
    DepartmentService departmentService;

    @Inject
    EmailService emailService;

    @Inject
    SmsService smsService;

    @Inject
    SystemNotificationService systemNotificationService;

    @Inject
    ExpressionService expressionService;

    /**
     * 发送流程事件通知
     */
    public Uni<Boolean> sendProcessEventNotification(ProcessInstance processInstance, 
                                                    NotificationConfig.EventType eventType, 
                                                    String nodeKey, 
                                                    Map<String, Object> variables) {
        logger.debug("发送流程事件通知: processInstanceId={}, eventType={}, nodeKey={}", 
                    processInstance.getId(), eventType, nodeKey);

        return getNotificationConfigs(processInstance.getProcessDefinitionId(), eventType, nodeKey)
                .flatMap(configs -> {
                    List<Uni<Boolean>> notifications = new ArrayList<>();
                    
                    for (NotificationConfig config : configs) {
                        if (isConfigApplicable(config, processInstance, variables)) {
                            Uni<Boolean> notification = sendNotification(config, processInstance, null, variables);
                            notifications.add(notification);
                        }
                    }
                    
                    if (notifications.isEmpty()) {
                        return Uni.createFrom().item(true);
                    }
                    
                    return Uni.combine().all().unis(notifications)
                            .combinedWith(results -> {
                                return results.stream().allMatch(result -> (Boolean) result);
                            });
                });
    }

    /**
     * 发送任务事件通知
     */
    public Uni<Boolean> sendTaskEventNotification(TaskInstance task, 
                                                 NotificationConfig.EventType eventType, 
                                                 Map<String, Object> variables) {
        logger.debug("发送任务事件通知: taskId={}, eventType={}", task.getId(), eventType);

        return getNotificationConfigs(task.getProcessDefinitionId(), eventType, task.getNodeKey())
                .flatMap(configs -> {
                    List<Uni<Boolean>> notifications = new ArrayList<>();
                    
                    for (NotificationConfig config : configs) {
                        if (isConfigApplicable(config, null, variables)) {
                            Uni<Boolean> notification = sendNotification(config, null, task, variables);
                            notifications.add(notification);
                        }
                    }
                    
                    if (notifications.isEmpty()) {
                        return Uni.createFrom().item(true);
                    }
                    
                    return Uni.combine().all().unis(notifications)
                            .combinedWith(results -> {
                                return results.stream().allMatch(result -> (Boolean) result);
                            });
                });
    }

    /**
     * 发送任务提醒
     */
    public Uni<Boolean> sendTaskReminder(TaskInstance task, String title, String content, List<String> methods) {
        logger.debug("发送任务提醒: taskId={}, methods={}", task.getId(), methods);

        List<Uni<Boolean>> notifications = new ArrayList<>();
        
        // 获取提醒接收人
        List<Long> recipientIds = getTaskRecipients(task);
        
        for (String method : methods) {
            try {
                NotificationConfig.NotificationMethod notificationMethod = 
                        NotificationConfig.NotificationMethod.valueOf(method.trim().toUpperCase());
                
                Uni<Boolean> notification = sendDirectNotification(
                        notificationMethod, recipientIds, title, content, null);
                notifications.add(notification);
            } catch (IllegalArgumentException e) {
                logger.warn("不支持的通知方式: {}", method);
            }
        }
        
        if (notifications.isEmpty()) {
            return Uni.createFrom().item(true);
        }
        
        return Uni.combine().all().unis(notifications)
                .combinedWith(results -> {
                    return results.stream().allMatch(result -> (Boolean) result);
                });
    }

    /**
     * 发送通知
     */
    private Uni<Boolean> sendNotification(NotificationConfig config, 
                                         ProcessInstance processInstance, 
                                         TaskInstance task, 
                                         Map<String, Object> variables) {
        logger.debug("发送通知: configId={}, method={}", config.getId(), config.getNotificationMethod());

        // 获取接收人
        return getRecipients(config, processInstance, task)
                .flatMap(recipients -> {
                    if (recipients.isEmpty()) {
                        logger.warn("通知配置没有找到接收人: configId={}", config.getId());
                        return Uni.createFrom().item(true);
                    }
                    
                    // 构建消息内容
                    String title = buildMessageContent(config.getTitleTemplate(), processInstance, task, variables);
                    String content = buildMessageContent(config.getContentTemplate(), processInstance, task, variables);
                    
                    // 发送通知
                    return sendDirectNotification(config.getNotificationMethod(), recipients, title, content, config);
                });
    }

    /**
     * 直接发送通知
     */
    private Uni<Boolean> sendDirectNotification(NotificationConfig.NotificationMethod method, 
                                               List<Long> recipientIds, 
                                               String title, 
                                               String content, 
                                               NotificationConfig config) {
        logger.debug("直接发送通知: method={}, recipients={}", method, recipientIds.size());

        switch (method) {
            case EMAIL:
                return sendEmailNotification(recipientIds, title, content);
                
            case SMS:
                return sendSmsNotification(recipientIds, content);
                
            case SYSTEM:
                return sendSystemNotification(recipientIds, title, content);
                
            case WECHAT:
                return sendWechatNotification(recipientIds, title, content);
                
            case DINGTALK:
                return sendDingtalkNotification(recipientIds, title, content);
                
            case WEBHOOK:
                return sendWebhookNotification(title, content, config);
                
            default:
                logger.warn("不支持的通知方式: {}", method);
                return Uni.createFrom().item(false);
        }
    }

    /**
     * 发送邮件通知
     */
    private Uni<Boolean> sendEmailNotification(List<Long> recipientIds, String title, String content) {
        return userService.getUserEmailsByIds(recipientIds)
                .flatMap(emails -> {
                    if (emails.isEmpty()) {
                        return Uni.createFrom().item(true);
                    }
                    return emailService.sendEmail(emails, title, content);
                });
    }

    /**
     * 发送短信通知
     */
    private Uni<Boolean> sendSmsNotification(List<Long> recipientIds, String content) {
        return userService.getUserPhonesByIds(recipientIds)
                .flatMap(phones -> {
                    if (phones.isEmpty()) {
                        return Uni.createFrom().item(true);
                    }
                    return smsService.sendSms(phones, content);
                });
    }

    /**
     * 发送系统通知
     */
    private Uni<Boolean> sendSystemNotification(List<Long> recipientIds, String title, String content) {
        return systemNotificationService.sendNotification(recipientIds, title, content);
    }

    /**
     * 发送微信通知
     */
    private Uni<Boolean> sendWechatNotification(List<Long> recipientIds, String title, String content) {
        // TODO: 实现微信通知
        logger.info("微信通知功能待实现: recipients={}, title={}", recipientIds.size(), title);
        return Uni.createFrom().item(true);
    }

    /**
     * 发送钉钉通知
     */
    private Uni<Boolean> sendDingtalkNotification(List<Long> recipientIds, String title, String content) {
        // TODO: 实现钉钉通知
        logger.info("钉钉通知功能待实现: recipients={}, title={}", recipientIds.size(), title);
        return Uni.createFrom().item(true);
    }

    /**
     * 发送Webhook通知
     */
    private Uni<Boolean> sendWebhookNotification(String title, String content, NotificationConfig config) {
        // TODO: 实现Webhook通知
        logger.info("Webhook通知功能待实现: title={}", title);
        return Uni.createFrom().item(true);
    }

    // 私有辅助方法
    private Uni<List<NotificationConfig>> getNotificationConfigs(Long processDefinitionId, 
                                                                NotificationConfig.EventType eventType, 
                                                                String nodeKey) {
        return notificationConfigRepository.findByProcessDefinitionIdAndEventTypeAndNodeKey(
                processDefinitionId, eventType, nodeKey);
    }

    private boolean isConfigApplicable(NotificationConfig config, ProcessInstance processInstance, 
                                     Map<String, Object> variables) {
        if (!config.getEnabled()) {
            return false;
        }
        
        // 检查条件表达式
        if (config.getConditionExpression() != null && !config.getConditionExpression().trim().isEmpty()) {
            try {
                return expressionService.evaluateBoolean(config.getConditionExpression(), processInstance);
            } catch (Exception e) {
                logger.warn("通知配置条件表达式计算失败: {}", config.getConditionExpression(), e);
                return false;
            }
        }
        
        return true;
    }

    private Uni<List<Long>> getRecipients(NotificationConfig config, ProcessInstance processInstance, TaskInstance task) {
        switch (config.getRecipientType()) {
            case INITIATOR:
                if (processInstance != null) {
                    return Uni.createFrom().item(Arrays.asList(processInstance.getInitiatorId()));
                }
                break;
                
            case ASSIGNEE:
                if (task != null && task.getAssigneeId() != null) {
                    return Uni.createFrom().item(Arrays.asList(task.getAssigneeId()));
                }
                break;
                
            case CANDIDATE:
                if (task != null && task.getCandidateUsers() != null) {
                    List<Long> candidates = Arrays.stream(task.getCandidateUsers().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(java.util.stream.Collectors.toList());
                    return Uni.createFrom().item(candidates);
                }
                break;
                
            case USER:
                if (config.getRecipientConfig() != null) {
                    List<Long> userIds = Arrays.stream(config.getRecipientConfig().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(java.util.stream.Collectors.toList());
                    return Uni.createFrom().item(userIds);
                }
                break;
                
            case ROLE:
                if (config.getRecipientConfig() != null) {
                    List<Long> roleIds = Arrays.stream(config.getRecipientConfig().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(java.util.stream.Collectors.toList());
                    return roleService.findUsersByRoleIds(roleIds)
                            .map(users -> users.stream()
                                    .map(user -> user.getId())
                                    .collect(java.util.stream.Collectors.toList()));
                }
                break;
                
            case DEPARTMENT:
                if (config.getRecipientConfig() != null) {
                    List<Long> deptIds = Arrays.stream(config.getRecipientConfig().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .collect(java.util.stream.Collectors.toList());
                    return departmentService.findUsersByDepartmentIds(deptIds)
                            .map(users -> users.stream()
                                    .map(user -> user.getId())
                                    .collect(java.util.stream.Collectors.toList()));
                }
                break;
                
            case EXPRESSION:
                if (config.getRecipientConfig() != null) {
                    return expressionService.evaluateUserExpression(config.getRecipientConfig(), processInstance)
                            .map(userIds -> userIds.stream()
                                    .map(Long::parseLong)
                                    .collect(java.util.stream.Collectors.toList()));
                }
                break;
        }
        
        return Uni.createFrom().item(Collections.emptyList());
    }

    private List<Long> getTaskRecipients(TaskInstance task) {
        List<Long> recipients = new ArrayList<>();
        
        // 添加当前处理人
        if (task.getAssigneeId() != null) {
            recipients.add(task.getAssigneeId());
        }
        
        // 添加候选人
        if (task.getCandidateUsers() != null && !task.getCandidateUsers().trim().isEmpty()) {
            Arrays.stream(task.getCandidateUsers().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .forEach(userId -> {
                        try {
                            recipients.add(Long.parseLong(userId));
                        } catch (NumberFormatException e) {
                            logger.warn("无效的用户ID: {}", userId);
                        }
                    });
        }
        
        return recipients;
    }

    private String buildMessageContent(String template, ProcessInstance processInstance, 
                                     TaskInstance task, Map<String, Object> variables) {
        if (template == null || template.trim().isEmpty()) {
            return "";
        }
        
        String content = template;
        
        // 替换流程实例变量
        if (processInstance != null) {
            content = content.replace("${processName}", processInstance.getProcessName() != null ? processInstance.getProcessName() : "");
            content = content.replace("${initiatorName}", processInstance.getInitiatorName() != null ? processInstance.getInitiatorName() : "");
            content = content.replace("${processInstanceId}", String.valueOf(processInstance.getId()));
        }
        
        // 替换任务变量
        if (task != null) {
            content = content.replace("${taskName}", task.getTaskName() != null ? task.getTaskName() : "");
            content = content.replace("${assigneeName}", task.getAssigneeName() != null ? task.getAssigneeName() : "");
            content = content.replace("${taskId}", String.valueOf(task.getId()));
        }
        
        // 替换自定义变量
        if (variables != null) {
            for (Map.Entry<String, Object> entry : variables.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                String value = entry.getValue() != null ? String.valueOf(entry.getValue()) : "";
                content = content.replace(placeholder, value);
            }
        }
        
        // 替换系统变量
        content = content.replace("${currentTime}", java.time.LocalDateTime.now().toString());
        content = content.replace("${currentDate}", java.time.LocalDate.now().toString());
        
        return content;
    }
}
