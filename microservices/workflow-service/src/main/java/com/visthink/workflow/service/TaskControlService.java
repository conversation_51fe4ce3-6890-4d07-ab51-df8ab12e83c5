package com.visthink.workflow.service;

import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.entity.TaskHistory;
import com.visthink.workflow.entity.ProcessInstance;
import com.visthink.workflow.repository.TaskInstanceRepository;
import com.visthink.workflow.repository.TaskHistoryRepository;
import com.visthink.workflow.exception.BusinessException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务控制服务
 * 负责处理任务的各种控制操作：审批、驳回、转办、委派、加签、减签等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class TaskControlService {

    private static final Logger logger = LoggerFactory.getLogger(TaskControlService.class);

    @Inject
    TaskInstanceRepository taskInstanceRepository;

    @Inject
    TaskHistoryRepository taskHistoryRepository;

    @Inject
    TaskAssignmentService taskAssignmentService;

    @Inject
    ProcessEngineService processEngineService;

    @Inject
    UserService userService;

    /**
     * 审批任务（同意）
     */
    @Transactional
    public Uni<TaskControlResult> approveTask(Long taskId, Long operatorId, String operatorName, 
                                             String comment, Map<String, Object> formData, 
                                             Map<String, Object> variables) {
        logger.info("审批任务: taskId={}, operatorId={}, operatorName={}", taskId, operatorId, operatorName);

        return taskInstanceRepository.findById(taskId)
                .flatMap(task -> {
                    if (task == null) {
                        return Uni.createFrom().failure(new BusinessException("任务不存在"));
                    }

                    // 验证操作权限
                    validateTaskPermission(task, operatorId);

                    // 记录历史
                    TaskHistory history = createTaskHistory(task, TaskInstance.TaskAction.APPROVE, 
                                                          operatorId, operatorName, comment);
                    history.setResult(TaskHistory.ActionResult.APPROVED);
                    history.setFormData(formData != null ? formData.toString() : null);
                    history.setVariables(variables != null ? variables.toString() : null);

                    return taskHistoryRepository.persist(history)
                            .flatMap(savedHistory -> processApproval(task, operatorId, operatorName, formData, variables));
                });
    }

    /**
     * 驳回任务
     */
    @Transactional
    public Uni<TaskControlResult> rejectTask(Long taskId, Long operatorId, String operatorName, 
                                           String reason, String targetNodeKey) {
        logger.info("驳回任务: taskId={}, operatorId={}, targetNodeKey={}", taskId, operatorId, targetNodeKey);

        return taskInstanceRepository.findById(taskId)
                .flatMap(task -> {
                    if (task == null) {
                        return Uni.createFrom().failure(new BusinessException("任务不存在"));
                    }

                    // 验证操作权限
                    validateTaskPermission(task, operatorId);

                    // 驳回任务
                    task.reject(reason);

                    // 记录历史
                    TaskHistory history = createTaskHistory(task, TaskInstance.TaskAction.REJECT, 
                                                          operatorId, operatorName, reason);
                    history.setResult(TaskHistory.ActionResult.REJECTED);
                    history.setTargetNodeKey(targetNodeKey);

                    return taskHistoryRepository.persist(history)
                            .flatMap(savedHistory -> taskInstanceRepository.persist(task))
                            .flatMap(savedTask -> processRejection(savedTask, targetNodeKey))
                            .map(result -> {
                                result.setMessage("任务驳回成功");
                                return result;
                            });
                });
    }

    /**
     * 转办任务
     */
    @Transactional
    public Uni<TaskControlResult> transferTask(Long taskId, Long operatorId, String operatorName, 
                                             Long targetUserId, String reason) {
        logger.info("转办任务: taskId={}, operatorId={}, targetUserId={}", taskId, operatorId, targetUserId);

        return taskInstanceRepository.findById(taskId)
                .flatMap(task -> {
                    if (task == null) {
                        return Uni.createFrom().failure(new BusinessException("任务不存在"));
                    }

                    // 验证操作权限
                    validateTaskPermission(task, operatorId);

                    return userService.findById(targetUserId)
                            .flatMap(targetUser -> {
                                // 转办任务
                                task.transfer(targetUserId, targetUser.getName(), reason);

                                // 记录历史
                                TaskHistory history = createTaskHistory(task, TaskInstance.TaskAction.TRANSFER, 
                                                                      operatorId, operatorName, reason);
                                history.setResult(TaskHistory.ActionResult.TRANSFERRED);
                                history.setTargetUserId(targetUserId);
                                history.setTargetUserName(targetUser.getName());

                                return taskHistoryRepository.persist(history)
                                        .flatMap(savedHistory -> taskInstanceRepository.persist(task))
                                        .map(savedTask -> {
                                            TaskControlResult result = new TaskControlResult();
                                            result.setSuccess(true);
                                            result.setMessage("任务转办成功");
                                            result.setTaskId(savedTask.getId());
                                            return result;
                                        });
                            });
                });
    }

    /**
     * 委派任务
     */
    @Transactional
    public Uni<TaskControlResult> delegateTask(Long taskId, Long operatorId, String operatorName, 
                                             Long targetUserId, String reason) {
        logger.info("委派任务: taskId={}, operatorId={}, targetUserId={}", taskId, operatorId, targetUserId);

        return taskInstanceRepository.findById(taskId)
                .flatMap(task -> {
                    if (task == null) {
                        return Uni.createFrom().failure(new BusinessException("任务不存在"));
                    }

                    // 验证操作权限
                    validateTaskPermission(task, operatorId);

                    return userService.findById(targetUserId)
                            .flatMap(targetUser -> {
                                // 委派任务
                                task.delegate(targetUserId, targetUser.getName(), reason);

                                // 记录历史
                                TaskHistory history = createTaskHistory(task, TaskInstance.TaskAction.DELEGATE, 
                                                                      operatorId, operatorName, reason);
                                history.setResult(TaskHistory.ActionResult.DELEGATED);
                                history.setTargetUserId(targetUserId);
                                history.setTargetUserName(targetUser.getName());

                                return taskHistoryRepository.persist(history)
                                        .flatMap(savedHistory -> taskInstanceRepository.persist(task))
                                        .map(savedTask -> {
                                            TaskControlResult result = new TaskControlResult();
                                            result.setSuccess(true);
                                            result.setMessage("任务委派成功");
                                            result.setTaskId(savedTask.getId());
                                            return result;
                                        });
                            });
                });
    }

    /**
     * 加签
     */
    @Transactional
    public Uni<TaskControlResult> addSign(Long taskId, Long operatorId, String operatorName, 
                                        List<Long> userIds, String reason) {
        logger.info("加签: taskId={}, operatorId={}, userIds={}", taskId, operatorId, userIds);

        return taskInstanceRepository.findById(taskId)
                .flatMap(task -> {
                    if (task == null) {
                        return Uni.createFrom().failure(new BusinessException("任务不存在"));
                    }

                    // 验证操作权限
                    validateTaskPermission(task, operatorId);

                    return userService.findUsersByIds(userIds)
                            .flatMap(users -> {
                                if (users.isEmpty()) {
                                    return Uni.createFrom().failure(new BusinessException("未找到指定的用户"));
                                }

                                // 添加候选用户
                                String existingUsers = task.getCandidateUsers() != null ? task.getCandidateUsers() : "";
                                String newUsers = users.stream()
                                        .map(u -> u.getId().toString())
                                        .reduce(existingUsers, (a, b) -> a.isEmpty() ? b : a + "," + b);
                                task.setCandidateUsers(newUsers);

                                // 更新需要审批人数
                                if (task.getApprovalType() == TaskInstance.ApprovalType.MULTI_AND) {
                                    task.setRequiredCount(task.getRequiredCount() + users.size());
                                }

                                // 记录历史
                                TaskHistory history = createTaskHistory(task, TaskInstance.TaskAction.ADD_SIGN, 
                                                                      operatorId, operatorName, reason);
                                history.setResult(TaskHistory.ActionResult.SUCCESS);

                                return taskHistoryRepository.persist(history)
                                        .flatMap(savedHistory -> taskInstanceRepository.persist(task))
                                        .map(savedTask -> {
                                            TaskControlResult result = new TaskControlResult();
                                            result.setSuccess(true);
                                            result.setMessage("加签成功");
                                            result.setTaskId(savedTask.getId());
                                            return result;
                                        });
                            });
                });
    }

    /**
     * 减签
     */
    @Transactional
    public Uni<TaskControlResult> removeSign(Long taskId, Long operatorId, String operatorName, 
                                           List<Long> userIds, String reason) {
        logger.info("减签: taskId={}, operatorId={}, userIds={}", taskId, operatorId, userIds);

        return taskInstanceRepository.findById(taskId)
                .flatMap(task -> {
                    if (task == null) {
                        return Uni.createFrom().failure(new BusinessException("任务不存在"));
                    }

                    // 验证操作权限
                    validateTaskPermission(task, operatorId);

                    // 移除候选用户
                    String candidateUsers = task.getCandidateUsers();
                    if (candidateUsers != null && !candidateUsers.isEmpty()) {
                        String[] users = candidateUsers.split(",");
                        StringBuilder newUsers = new StringBuilder();
                        
                        for (String user : users) {
                            if (!userIds.contains(Long.parseLong(user.trim()))) {
                                if (newUsers.length() > 0) {
                                    newUsers.append(",");
                                }
                                newUsers.append(user);
                            }
                        }
                        
                        task.setCandidateUsers(newUsers.toString());
                        
                        // 更新需要审批人数
                        if (task.getApprovalType() == TaskInstance.ApprovalType.MULTI_AND) {
                            task.setRequiredCount(Math.max(1, task.getRequiredCount() - userIds.size()));
                        }
                    }

                    // 记录历史
                    TaskHistory history = createTaskHistory(task, TaskInstance.TaskAction.REMOVE_SIGN, 
                                                          operatorId, operatorName, reason);
                    history.setResult(TaskHistory.ActionResult.SUCCESS);

                    return taskHistoryRepository.persist(history)
                            .flatMap(savedHistory -> taskInstanceRepository.persist(task))
                            .map(savedTask -> {
                                TaskControlResult result = new TaskControlResult();
                                result.setSuccess(true);
                                result.setMessage("减签成功");
                                result.setTaskId(savedTask.getId());
                                return result;
                            });
                });
    }

    /**
     * 签收任务
     */
    @Transactional
    public Uni<TaskControlResult> claimTask(Long taskId, Long operatorId, String operatorName) {
        logger.info("签收任务: taskId={}, operatorId={}", taskId, operatorId);

        return taskInstanceRepository.findById(taskId)
                .flatMap(task -> {
                    if (task == null) {
                        return Uni.createFrom().failure(new BusinessException("任务不存在"));
                    }

                    // 验证是否可以签收
                    if (!canClaimTask(task, operatorId)) {
                        return Uni.createFrom().failure(new BusinessException("无权签收此任务"));
                    }

                    // 签收任务
                    task.claim(operatorId, operatorName);

                    // 记录历史
                    TaskHistory history = createTaskHistory(task, TaskInstance.TaskAction.CLAIM, 
                                                          operatorId, operatorName, "签收任务");
                    history.setResult(TaskHistory.ActionResult.SUCCESS);

                    return taskHistoryRepository.persist(history)
                            .flatMap(savedHistory -> taskInstanceRepository.persist(task))
                            .map(savedTask -> {
                                TaskControlResult result = new TaskControlResult();
                                result.setSuccess(true);
                                result.setMessage("任务签收成功");
                                result.setTaskId(savedTask.getId());
                                return result;
                            });
                });
    }

    // 私有辅助方法
    private void validateTaskPermission(TaskInstance task, Long operatorId) {
        if (task.getStatus() != TaskInstance.TaskStatus.CREATED && 
            task.getStatus() != TaskInstance.TaskStatus.CLAIMED) {
            throw new BusinessException("任务状态不允许此操作");
        }

        // 检查操作权限
        if (task.getAssigneeId() != null && !task.getAssigneeId().equals(operatorId)) {
            // 检查是否在候选人列表中
            if (task.getCandidateUsers() == null || 
                !task.getCandidateUsers().contains(operatorId.toString())) {
                throw new BusinessException("无权操作此任务");
            }
        }
    }

    private boolean canClaimTask(TaskInstance task, Long operatorId) {
        // 任务必须是未签收状态
        if (task.getStatus() != TaskInstance.TaskStatus.CREATED) {
            return false;
        }

        // 如果已指定处理人，只有指定人可以签收
        if (task.getAssigneeId() != null) {
            return task.getAssigneeId().equals(operatorId);
        }

        // 检查是否在候选人列表中
        if (task.getCandidateUsers() != null) {
            return task.getCandidateUsers().contains(operatorId.toString());
        }

        return false;
    }

    private TaskHistory createTaskHistory(TaskInstance task, TaskInstance.TaskAction action, 
                                        Long operatorId, String operatorName, String comment) {
        TaskHistory history = new TaskHistory();
        history.setTenantId(task.getTenantId());
        history.setTaskInstanceId(task.getId());
        history.setProcessInstanceId(task.getProcessInstanceId());
        history.setProcessDefinitionId(task.getProcessDefinitionId());
        history.setNodeKey(task.getNodeKey());
        history.setNodeName(task.getNodeName());
        history.setTaskKey(task.getTaskKey());
        history.setTaskName(task.getTaskName());
        history.setOperatorId(operatorId);
        history.setOperatorName(operatorName);
        history.setAction(action);
        history.setComment(comment);
        return history;
    }

    private Uni<TaskControlResult> processApproval(TaskInstance task, Long operatorId, String operatorName, 
                                                  Map<String, Object> formData, Map<String, Object> variables) {
        // 增加审批计数
        task.incrementApprovalCount();

        // 检查是否满足审批条件
        if (task.isApprovalComplete()) {
            // 完成任务
            task.complete(operatorId, operatorName);
            
            return taskInstanceRepository.persist(task)
                    .flatMap(savedTask -> {
                        // 触发流程引擎继续执行
                        return processEngineService.completeTask(savedTask, formData, variables);
                    })
                    .map(result -> {
                        TaskControlResult controlResult = new TaskControlResult();
                        controlResult.setSuccess(true);
                        controlResult.setMessage("任务审批完成");
                        controlResult.setTaskId(task.getId());
                        return controlResult;
                    });
        } else {
            // 部分审批完成，等待其他人审批
            return taskInstanceRepository.persist(task)
                    .map(savedTask -> {
                        TaskControlResult result = new TaskControlResult();
                        result.setSuccess(true);
                        result.setMessage("审批成功，等待其他人审批");
                        result.setTaskId(savedTask.getId());
                        return result;
                    });
        }
    }

    private Uni<TaskControlResult> processRejection(TaskInstance task, String targetNodeKey) {
        // TODO: 实现驳回逻辑，根据targetNodeKey决定驳回到哪个节点
        return processEngineService.rejectTask(task, targetNodeKey)
                .map(result -> {
                    TaskControlResult controlResult = new TaskControlResult();
                    controlResult.setSuccess(true);
                    controlResult.setMessage("任务驳回成功");
                    controlResult.setTaskId(task.getId());
                    return controlResult;
                });
    }

    /**
     * 任务控制结果
     */
    public static class TaskControlResult {
        private boolean success;
        private String message;
        private Long taskId;
        private Object data;

        // Getter和Setter方法
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Long getTaskId() {
            return taskId;
        }

        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }
    }
}
