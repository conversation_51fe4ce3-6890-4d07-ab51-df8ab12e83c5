package com.visthink.workflow.repository;

import com.visthink.workflow.entity.NotificationConfig;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 通知配置Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class NotificationConfigRepository implements PanacheRepository<NotificationConfig> {

    /**
     * 根据流程定义ID、事件类型和节点标识查询通知配置
     */
    public Uni<List<NotificationConfig>> findByProcessDefinitionIdAndEventTypeAndNodeKey(
            Long processDefinitionId, NotificationConfig.EventType eventType, String nodeKey) {
        return find("processDefinitionId = ?1 and eventType = ?2 and (nodeKey = ?3 or nodeKey is null) and enabled = true order by priority desc", 
                   processDefinitionId, eventType, nodeKey).list();
    }

    /**
     * 根据流程定义ID查询所有通知配置
     */
    public Uni<List<NotificationConfig>> findByProcessDefinitionId(Long processDefinitionId) {
        return find("processDefinitionId = ?1 order by eventType, nodeKey, priority desc", processDefinitionId).list();
    }

    /**
     * 根据事件类型查询通知配置
     */
    public Uni<List<NotificationConfig>> findByEventType(NotificationConfig.EventType eventType) {
        return find("eventType = ?1 and enabled = true order by priority desc", eventType).list();
    }

    /**
     * 根据通知方式查询配置
     */
    public Uni<List<NotificationConfig>> findByNotificationMethod(NotificationConfig.NotificationMethod method) {
        return find("notificationMethod = ?1 and enabled = true order by priority desc", method).list();
    }

    /**
     * 根据接收人类型查询配置
     */
    public Uni<List<NotificationConfig>> findByRecipientType(NotificationConfig.RecipientType recipientType) {
        return find("recipientType = ?1 and enabled = true order by priority desc", recipientType).list();
    }

    /**
     * 根据租户ID查询通知配置
     */
    public Uni<List<NotificationConfig>> findByTenantId(String tenantId) {
        return find("tenantId = ?1 order by processDefinitionId, eventType, nodeKey", tenantId).list();
    }

    /**
     * 查询启用的通知配置
     */
    public Uni<List<NotificationConfig>> findEnabledConfigs() {
        return find("enabled = true order by processDefinitionId, eventType, nodeKey, priority desc").list();
    }

    /**
     * 根据流程定义ID和事件类型查询配置
     */
    public Uni<List<NotificationConfig>> findByProcessDefinitionIdAndEventType(
            Long processDefinitionId, NotificationConfig.EventType eventType) {
        return find("processDefinitionId = ?1 and eventType = ?2 and enabled = true order by priority desc", 
                   processDefinitionId, eventType).list();
    }

    /**
     * 检查是否存在重复的通知配置
     */
    public Uni<Boolean> existsDuplicate(Long processDefinitionId, String nodeKey, 
                                       NotificationConfig.EventType eventType,
                                       NotificationConfig.NotificationMethod notificationMethod,
                                       NotificationConfig.RecipientType recipientType,
                                       Long excludeId) {
        String query = "processDefinitionId = ?1 and nodeKey = ?2 and eventType = ?3 and notificationMethod = ?4 and recipientType = ?5";
        
        if (excludeId != null) {
            query += " and id != ?6";
            return count(query, processDefinitionId, nodeKey, eventType, notificationMethod, recipientType, excludeId)
                    .map(count -> count > 0);
        } else {
            return count(query, processDefinitionId, nodeKey, eventType, notificationMethod, recipientType)
                    .map(count -> count > 0);
        }
    }

    /**
     * 批量删除流程定义的通知配置
     */
    public Uni<Long> deleteByProcessDefinitionId(Long processDefinitionId) {
        return delete("processDefinitionId = ?1", processDefinitionId);
    }

    /**
     * 批量删除节点的通知配置
     */
    public Uni<Long> deleteByProcessDefinitionIdAndNodeKey(Long processDefinitionId, String nodeKey) {
        return delete("processDefinitionId = ?1 and nodeKey = ?2", processDefinitionId, nodeKey);
    }

    /**
     * 批量删除事件类型的通知配置
     */
    public Uni<Long> deleteByEventType(NotificationConfig.EventType eventType) {
        return delete("eventType = ?1", eventType);
    }

    /**
     * 启用或禁用通知配置
     */
    public Uni<Integer> updateEnabled(Long id, Boolean enabled) {
        return update("enabled = ?1 where id = ?2", enabled, id);
    }

    /**
     * 批量启用或禁用通知配置
     */
    public Uni<Integer> batchUpdateEnabled(List<Long> ids, Boolean enabled) {
        return update("enabled = ?1 where id in ?2", enabled, ids);
    }

    /**
     * 统计流程定义的通知配置数量
     */
    public Uni<Long> countByProcessDefinitionId(Long processDefinitionId) {
        return count("processDefinitionId = ?1", processDefinitionId);
    }

    /**
     * 统计启用的通知配置数量
     */
    public Uni<Long> countEnabledConfigs() {
        return count("enabled = true");
    }

    /**
     * 获取流程定义的所有事件类型（有通知配置的）
     */
    public Uni<List<NotificationConfig.EventType>> getEventTypesByProcessDefinitionId(Long processDefinitionId) {
        return getEntityManager()
                .createQuery("SELECT DISTINCT nc.eventType FROM NotificationConfig nc WHERE nc.processDefinitionId = :processDefinitionId ORDER BY nc.eventType", NotificationConfig.EventType.class)
                .setParameter("processDefinitionId", processDefinitionId)
                .getResultList();
    }

    /**
     * 获取流程定义的所有节点（有通知配置的）
     */
    public Uni<List<String>> getNodeKeysByProcessDefinitionId(Long processDefinitionId) {
        return getEntityManager()
                .createQuery("SELECT DISTINCT nc.nodeKey FROM NotificationConfig nc WHERE nc.processDefinitionId = :processDefinitionId AND nc.nodeKey IS NOT NULL ORDER BY nc.nodeKey", String.class)
                .setParameter("processDefinitionId", processDefinitionId)
                .getResultList();
    }

    /**
     * 获取通知配置统计信息
     */
    public Uni<List<Object[]>> getNotificationConfigStatistics() {
        return getEntityManager()
                .createQuery("SELECT nc.eventType, nc.notificationMethod, nc.recipientType, COUNT(nc) FROM NotificationConfig nc WHERE nc.enabled = true GROUP BY nc.eventType, nc.notificationMethod, nc.recipientType", Object[].class)
                .getResultList();
    }

    /**
     * 查询需要延迟发送的通知配置
     */
    public Uni<List<NotificationConfig>> findDelayedNotificationConfigs() {
        return find("enabled = true and delayMinutes > 0 order by delayMinutes").list();
    }

    /**
     * 查询需要重复发送的通知配置
     */
    public Uni<List<NotificationConfig>> findRepeatNotificationConfigs() {
        return find("enabled = true and repeatSend = true order by repeatInterval").list();
    }
}
