package com.visthink.workflow.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Gauge;
import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 性能监控服务
 * 负责收集和监控工作流引擎的性能指标
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class PerformanceMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceMonitorService.class);

    @Inject
    MeterRegistry meterRegistry;

    // 性能计数器
    private final Counter processStartCounter;
    private final Counter processCompleteCounter;
    private final Counter taskCreateCounter;
    private final Counter taskCompleteCounter;
    private final Counter taskTimeoutCounter;
    private final Counter errorCounter;

    // 性能计时器
    private final Timer processExecutionTimer;
    private final Timer taskExecutionTimer;
    private final Timer databaseQueryTimer;
    private final Timer cacheAccessTimer;

    // 性能指标
    private final AtomicLong activeProcessCount = new AtomicLong(0);
    private final AtomicLong activeTaskCount = new AtomicLong(0);
    private final AtomicLong totalMemoryUsage = new AtomicLong(0);
    private final AtomicLong usedMemoryUsage = new AtomicLong(0);

    // 性能统计
    private final ConcurrentMap<String, PerformanceMetric> performanceMetrics = new ConcurrentHashMap<>();
    private final AtomicReference<PerformanceSnapshot> lastSnapshot = new AtomicReference<>();

    public PerformanceMonitorService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        
        // 初始化计数器
        this.processStartCounter = Counter.builder("workflow.process.start")
                .description("流程启动次数")
                .register(meterRegistry);
        
        this.processCompleteCounter = Counter.builder("workflow.process.complete")
                .description("流程完成次数")
                .register(meterRegistry);
        
        this.taskCreateCounter = Counter.builder("workflow.task.create")
                .description("任务创建次数")
                .register(meterRegistry);
        
        this.taskCompleteCounter = Counter.builder("workflow.task.complete")
                .description("任务完成次数")
                .register(meterRegistry);
        
        this.taskTimeoutCounter = Counter.builder("workflow.task.timeout")
                .description("任务超时次数")
                .register(meterRegistry);
        
        this.errorCounter = Counter.builder("workflow.error")
                .description("错误次数")
                .register(meterRegistry);

        // 初始化计时器
        this.processExecutionTimer = Timer.builder("workflow.process.execution")
                .description("流程执行时间")
                .register(meterRegistry);
        
        this.taskExecutionTimer = Timer.builder("workflow.task.execution")
                .description("任务执行时间")
                .register(meterRegistry);
        
        this.databaseQueryTimer = Timer.builder("workflow.database.query")
                .description("数据库查询时间")
                .register(meterRegistry);
        
        this.cacheAccessTimer = Timer.builder("workflow.cache.access")
                .description("缓存访问时间")
                .register(meterRegistry);

        // 注册Gauge指标
        Gauge.builder("workflow.process.active")
                .description("活跃流程数量")
                .register(meterRegistry, this, service -> service.activeProcessCount.get());
        
        Gauge.builder("workflow.task.active")
                .description("活跃任务数量")
                .register(meterRegistry, this, service -> service.activeTaskCount.get());
        
        Gauge.builder("workflow.memory.total")
                .description("总内存使用量")
                .register(meterRegistry, this, service -> service.totalMemoryUsage.get());
        
        Gauge.builder("workflow.memory.used")
                .description("已用内存使用量")
                .register(meterRegistry, this, service -> service.usedMemoryUsage.get());
    }

    /**
     * 记录流程启动
     */
    public void recordProcessStart(String processDefinitionKey) {
        processStartCounter.increment();
        activeProcessCount.incrementAndGet();
        
        recordMetric("process.start." + processDefinitionKey, 1);
        logger.debug("记录流程启动: {}", processDefinitionKey);
    }

    /**
     * 记录流程完成
     */
    public void recordProcessComplete(String processDefinitionKey, long duration) {
        processCompleteCounter.increment();
        activeProcessCount.decrementAndGet();
        processExecutionTimer.record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        recordMetric("process.complete." + processDefinitionKey, 1);
        recordMetric("process.duration." + processDefinitionKey, duration);
        logger.debug("记录流程完成: {}, 耗时: {}ms", processDefinitionKey, duration);
    }

    /**
     * 记录任务创建
     */
    public void recordTaskCreate(String taskDefinitionKey) {
        taskCreateCounter.increment();
        activeTaskCount.incrementAndGet();
        
        recordMetric("task.create." + taskDefinitionKey, 1);
        logger.debug("记录任务创建: {}", taskDefinitionKey);
    }

    /**
     * 记录任务完成
     */
    public void recordTaskComplete(String taskDefinitionKey, long duration) {
        taskCompleteCounter.increment();
        activeTaskCount.decrementAndGet();
        taskExecutionTimer.record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        recordMetric("task.complete." + taskDefinitionKey, 1);
        recordMetric("task.duration." + taskDefinitionKey, duration);
        logger.debug("记录任务完成: {}, 耗时: {}ms", taskDefinitionKey, duration);
    }

    /**
     * 记录任务超时
     */
    public void recordTaskTimeout(String taskDefinitionKey) {
        taskTimeoutCounter.increment();
        
        recordMetric("task.timeout." + taskDefinitionKey, 1);
        logger.debug("记录任务超时: {}", taskDefinitionKey);
    }

    /**
     * 记录错误
     */
    public void recordError(String errorType, String errorMessage) {
        errorCounter.increment();
        
        recordMetric("error." + errorType, 1);
        logger.debug("记录错误: {}, 消息: {}", errorType, errorMessage);
    }

    /**
     * 记录数据库查询时间
     */
    public void recordDatabaseQuery(String queryType, long duration) {
        databaseQueryTimer.record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        recordMetric("database.query." + queryType, duration);
        logger.debug("记录数据库查询: {}, 耗时: {}ms", queryType, duration);
    }

    /**
     * 记录缓存访问时间
     */
    public void recordCacheAccess(String cacheType, long duration, boolean hit) {
        cacheAccessTimer.record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        recordMetric("cache.access." + cacheType, duration);
        recordMetric("cache." + (hit ? "hit" : "miss") + "." + cacheType, 1);
        logger.debug("记录缓存访问: {}, 耗时: {}ms, 命中: {}", cacheType, duration, hit);
    }

    /**
     * 获取性能快照
     */
    public PerformanceSnapshot getPerformanceSnapshot() {
        PerformanceSnapshot snapshot = new PerformanceSnapshot();
        snapshot.setTimestamp(LocalDateTime.now());
        snapshot.setActiveProcessCount(activeProcessCount.get());
        snapshot.setActiveTaskCount(activeTaskCount.get());
        snapshot.setTotalMemoryUsage(totalMemoryUsage.get());
        snapshot.setUsedMemoryUsage(usedMemoryUsage.get());
        
        // 复制性能指标
        snapshot.setMetrics(new ConcurrentHashMap<>(performanceMetrics));
        
        return snapshot;
    }

    /**
     * 获取性能统计报告
     */
    public PerformanceReport getPerformanceReport() {
        PerformanceReport report = new PerformanceReport();
        report.setGeneratedTime(LocalDateTime.now());
        
        // 基础统计
        report.setTotalProcessStarts(processStartCounter.count());
        report.setTotalProcessCompletes(processCompleteCounter.count());
        report.setTotalTaskCreates(taskCreateCounter.count());
        report.setTotalTaskCompletes(taskCompleteCounter.count());
        report.setTotalTaskTimeouts(taskTimeoutCounter.count());
        report.setTotalErrors(errorCounter.count());
        
        // 平均执行时间
        report.setAvgProcessExecutionTime(processExecutionTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
        report.setAvgTaskExecutionTime(taskExecutionTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
        report.setAvgDatabaseQueryTime(databaseQueryTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
        report.setAvgCacheAccessTime(cacheAccessTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS));
        
        // 当前状态
        report.setCurrentActiveProcesses(activeProcessCount.get());
        report.setCurrentActiveTasks(activeTaskCount.get());
        report.setCurrentMemoryUsage(usedMemoryUsage.get());
        report.setMemoryUsageRatio((double) usedMemoryUsage.get() / totalMemoryUsage.get());
        
        return report;
    }

    /**
     * 定时收集系统指标（每分钟执行一次）
     */
    @Scheduled(every = "60s")
    public void collectSystemMetrics() {
        try {
            // 收集内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long usedMemory = totalMemory - runtime.freeMemory();
            
            totalMemoryUsage.set(totalMemory);
            usedMemoryUsage.set(usedMemory);
            
            // 记录性能快照
            PerformanceSnapshot snapshot = getPerformanceSnapshot();
            lastSnapshot.set(snapshot);
            
            logger.debug("收集系统指标完成 - 内存使用: {}/{} MB", 
                        usedMemory / 1024 / 1024, totalMemory / 1024 / 1024);
            
        } catch (Exception e) {
            logger.error("收集系统指标失败", e);
        }
    }

    /**
     * 清理过期的性能指标（每小时执行一次）
     */
    @Scheduled(every = "3600s")
    public void cleanupExpiredMetrics() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
            
            performanceMetrics.entrySet().removeIf(entry -> 
                entry.getValue().getLastUpdated().isBefore(cutoffTime));
            
            logger.debug("清理过期性能指标完成，当前指标数量: {}", performanceMetrics.size());
            
        } catch (Exception e) {
            logger.error("清理过期性能指标失败", e);
        }
    }

    // 私有辅助方法
    private void recordMetric(String metricName, double value) {
        performanceMetrics.compute(metricName, (key, existing) -> {
            if (existing == null) {
                return new PerformanceMetric(metricName, value);
            } else {
                existing.addValue(value);
                return existing;
            }
        });
    }

    /**
     * 性能指标类
     */
    public static class PerformanceMetric {
        private final String name;
        private double totalValue;
        private long count;
        private double minValue;
        private double maxValue;
        private LocalDateTime lastUpdated;

        public PerformanceMetric(String name, double initialValue) {
            this.name = name;
            this.totalValue = initialValue;
            this.count = 1;
            this.minValue = initialValue;
            this.maxValue = initialValue;
            this.lastUpdated = LocalDateTime.now();
        }

        public void addValue(double value) {
            this.totalValue += value;
            this.count++;
            this.minValue = Math.min(this.minValue, value);
            this.maxValue = Math.max(this.maxValue, value);
            this.lastUpdated = LocalDateTime.now();
        }

        public double getAverageValue() {
            return count > 0 ? totalValue / count : 0;
        }

        // Getter方法
        public String getName() { return name; }
        public double getTotalValue() { return totalValue; }
        public long getCount() { return count; }
        public double getMinValue() { return minValue; }
        public double getMaxValue() { return maxValue; }
        public LocalDateTime getLastUpdated() { return lastUpdated; }
    }

    /**
     * 性能快照类
     */
    public static class PerformanceSnapshot {
        private LocalDateTime timestamp;
        private long activeProcessCount;
        private long activeTaskCount;
        private long totalMemoryUsage;
        private long usedMemoryUsage;
        private ConcurrentMap<String, PerformanceMetric> metrics;

        // Getter和Setter方法
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        public long getActiveProcessCount() { return activeProcessCount; }
        public void setActiveProcessCount(long activeProcessCount) { this.activeProcessCount = activeProcessCount; }
        public long getActiveTaskCount() { return activeTaskCount; }
        public void setActiveTaskCount(long activeTaskCount) { this.activeTaskCount = activeTaskCount; }
        public long getTotalMemoryUsage() { return totalMemoryUsage; }
        public void setTotalMemoryUsage(long totalMemoryUsage) { this.totalMemoryUsage = totalMemoryUsage; }
        public long getUsedMemoryUsage() { return usedMemoryUsage; }
        public void setUsedMemoryUsage(long usedMemoryUsage) { this.usedMemoryUsage = usedMemoryUsage; }
        public ConcurrentMap<String, PerformanceMetric> getMetrics() { return metrics; }
        public void setMetrics(ConcurrentMap<String, PerformanceMetric> metrics) { this.metrics = metrics; }
    }

    /**
     * 性能报告类
     */
    public static class PerformanceReport {
        private LocalDateTime generatedTime;
        private double totalProcessStarts;
        private double totalProcessCompletes;
        private double totalTaskCreates;
        private double totalTaskCompletes;
        private double totalTaskTimeouts;
        private double totalErrors;
        private double avgProcessExecutionTime;
        private double avgTaskExecutionTime;
        private double avgDatabaseQueryTime;
        private double avgCacheAccessTime;
        private long currentActiveProcesses;
        private long currentActiveTasks;
        private long currentMemoryUsage;
        private double memoryUsageRatio;

        // Getter和Setter方法
        public LocalDateTime getGeneratedTime() { return generatedTime; }
        public void setGeneratedTime(LocalDateTime generatedTime) { this.generatedTime = generatedTime; }
        public double getTotalProcessStarts() { return totalProcessStarts; }
        public void setTotalProcessStarts(double totalProcessStarts) { this.totalProcessStarts = totalProcessStarts; }
        public double getTotalProcessCompletes() { return totalProcessCompletes; }
        public void setTotalProcessCompletes(double totalProcessCompletes) { this.totalProcessCompletes = totalProcessCompletes; }
        public double getTotalTaskCreates() { return totalTaskCreates; }
        public void setTotalTaskCreates(double totalTaskCreates) { this.totalTaskCreates = totalTaskCreates; }
        public double getTotalTaskCompletes() { return totalTaskCompletes; }
        public void setTotalTaskCompletes(double totalTaskCompletes) { this.totalTaskCompletes = totalTaskCompletes; }
        public double getTotalTaskTimeouts() { return totalTaskTimeouts; }
        public void setTotalTaskTimeouts(double totalTaskTimeouts) { this.totalTaskTimeouts = totalTaskTimeouts; }
        public double getTotalErrors() { return totalErrors; }
        public void setTotalErrors(double totalErrors) { this.totalErrors = totalErrors; }
        public double getAvgProcessExecutionTime() { return avgProcessExecutionTime; }
        public void setAvgProcessExecutionTime(double avgProcessExecutionTime) { this.avgProcessExecutionTime = avgProcessExecutionTime; }
        public double getAvgTaskExecutionTime() { return avgTaskExecutionTime; }
        public void setAvgTaskExecutionTime(double avgTaskExecutionTime) { this.avgTaskExecutionTime = avgTaskExecutionTime; }
        public double getAvgDatabaseQueryTime() { return avgDatabaseQueryTime; }
        public void setAvgDatabaseQueryTime(double avgDatabaseQueryTime) { this.avgDatabaseQueryTime = avgDatabaseQueryTime; }
        public double getAvgCacheAccessTime() { return avgCacheAccessTime; }
        public void setAvgCacheAccessTime(double avgCacheAccessTime) { this.avgCacheAccessTime = avgCacheAccessTime; }
        public long getCurrentActiveProcesses() { return currentActiveProcesses; }
        public void setCurrentActiveProcesses(long currentActiveProcesses) { this.currentActiveProcesses = currentActiveProcesses; }
        public long getCurrentActiveTasks() { return currentActiveTasks; }
        public void setCurrentActiveTasks(long currentActiveTasks) { this.currentActiveTasks = currentActiveTasks; }
        public long getCurrentMemoryUsage() { return currentMemoryUsage; }
        public void setCurrentMemoryUsage(long currentMemoryUsage) { this.currentMemoryUsage = currentMemoryUsage; }
        public double getMemoryUsageRatio() { return memoryUsageRatio; }
        public void setMemoryUsageRatio(double memoryUsageRatio) { this.memoryUsageRatio = memoryUsageRatio; }
    }
}
