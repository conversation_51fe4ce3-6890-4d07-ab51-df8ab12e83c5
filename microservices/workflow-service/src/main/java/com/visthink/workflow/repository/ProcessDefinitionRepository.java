package com.visthink.workflow.repository;

import com.visthink.workflow.entity.ProcessDefinition;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 流程定义数据访问层
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class ProcessDefinitionRepository implements PanacheRepository<ProcessDefinition> {

    /**
     * 根据租户ID和流程标识查找最新版本的流程定义
     * 
     * @param tenantId 租户ID
     * @param processKey 流程标识
     * @return 流程定义
     */
    public Uni<ProcessDefinition> findLatestByTenantAndKey(String tenantId, String processKey) {
        return find("tenantId = ?1 and processKey = ?2 and isLatest = true and status != ?3", 
                   tenantId, processKey, ProcessDefinition.ProcessStatus.DELETED)
                .firstResult();
    }

    /**
     * 根据租户ID和流程标识查找指定版本的流程定义
     * 
     * @param tenantId 租户ID
     * @param processKey 流程标识
     * @param version 版本号
     * @return 流程定义
     */
    public Uni<ProcessDefinition> findByTenantKeyAndVersion(String tenantId, String processKey, Integer version) {
        return find("tenantId = ?1 and processKey = ?2 and processVersion = ?3 and status != ?4", 
                   tenantId, processKey, version, ProcessDefinition.ProcessStatus.DELETED)
                .firstResult();
    }

    /**
     * 根据租户ID查找所有激活状态的流程定义
     * 
     * @param tenantId 租户ID
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> findActiveByTenant(String tenantId) {
        return list("tenantId = ?1 and status = ?2 and isLatest = true", 
                   tenantId, ProcessDefinition.ProcessStatus.ACTIVE);
    }

    /**
     * 根据租户ID和分类查找流程定义
     * 
     * @param tenantId 租户ID
     * @param category 分类
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> findByTenantAndCategory(String tenantId, String category) {
        return list("tenantId = ?1 and category = ?2 and status != ?3 and isLatest = true", 
                   tenantId, category, ProcessDefinition.ProcessStatus.DELETED);
    }

    /**
     * 根据租户ID和设计器类型查找流程定义
     * 
     * @param tenantId 租户ID
     * @param designerType 设计器类型
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> findByTenantAndDesignerType(String tenantId, 
                                                                   ProcessDefinition.DesignerType designerType) {
        return list("tenantId = ?1 and designerType = ?2 and status != ?3 and isLatest = true", 
                   tenantId, designerType, ProcessDefinition.ProcessStatus.DELETED);
    }

    /**
     * 根据租户ID和状态查找流程定义
     * 
     * @param tenantId 租户ID
     * @param status 状态
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> findByTenantAndStatus(String tenantId, 
                                                             ProcessDefinition.ProcessStatus status) {
        return list("tenantId = ?1 and status = ?2 and isLatest = true", tenantId, status);
    }

    /**
     * 根据租户ID和创建人查找流程定义
     * 
     * @param tenantId 租户ID
     * @param createdBy 创建人ID
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> findByTenantAndCreatedBy(String tenantId, Long createdBy) {
        return list("tenantId = ?1 and createdBy = ?2 and status != ?3 and isLatest = true", 
                   tenantId, createdBy, ProcessDefinition.ProcessStatus.DELETED);
    }

    /**
     * 检查流程标识是否已存在
     * 
     * @param tenantId 租户ID
     * @param processKey 流程标识
     * @return 是否存在
     */
    public Uni<Boolean> existsByTenantAndKey(String tenantId, String processKey) {
        return count("tenantId = ?1 and processKey = ?2 and status != ?3", 
                    tenantId, processKey, ProcessDefinition.ProcessStatus.DELETED)
                .map(count -> count > 0);
    }

    /**
     * 获取流程定义的下一个版本号
     * 
     * @param tenantId 租户ID
     * @param processKey 流程标识
     * @return 下一个版本号
     */
    public Uni<Integer> getNextVersion(String tenantId, String processKey) {
        return find("select max(processVersion) from ProcessDefinition where tenantId = ?1 and processKey = ?2", 
                   tenantId, processKey)
                .firstResult()
                .map(maxVersion -> maxVersion != null ? (Integer) maxVersion + 1 : 1);
    }

    /**
     * 将指定流程标识的所有版本设置为非最新版本
     * 
     * @param tenantId 租户ID
     * @param processKey 流程标识
     * @return 更新结果
     */
    public Uni<Integer> updateAllVersionsToNotLatest(String tenantId, String processKey) {
        return update("isLatest = false where tenantId = ?1 and processKey = ?2", tenantId, processKey);
    }

    /**
     * 分页查询流程定义
     * 
     * @param tenantId 租户ID
     * @param page 页码（从0开始）
     * @param size 页大小
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> findByTenantWithPaging(String tenantId, int page, int size) {
        return find("tenantId = ?1 and status != ?2 and isLatest = true order by createdTime desc", 
                   tenantId, ProcessDefinition.ProcessStatus.DELETED)
                .page(page, size)
                .list();
    }

    /**
     * 统计租户下的流程定义数量
     * 
     * @param tenantId 租户ID
     * @return 数量
     */
    public Uni<Long> countByTenant(String tenantId) {
        return count("tenantId = ?1 and status != ?2 and isLatest = true", 
                    tenantId, ProcessDefinition.ProcessStatus.DELETED);
    }

    /**
     * 根据关键词搜索流程定义
     * 
     * @param tenantId 租户ID
     * @param keyword 关键词
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> searchByKeyword(String tenantId, String keyword) {
        String likeKeyword = "%" + keyword + "%";
        return list("tenantId = ?1 and (processName like ?2 or processKey like ?2 or description like ?2) " +
                   "and status != ?3 and isLatest = true order by createdTime desc", 
                   tenantId, likeKeyword, ProcessDefinition.ProcessStatus.DELETED);
    }

    /**
     * 根据多个条件查询流程定义
     * 
     * @param tenantId 租户ID
     * @param category 分类（可为空）
     * @param designerType 设计器类型（可为空）
     * @param status 状态（可为空）
     * @param keyword 关键词（可为空）
     * @param page 页码
     * @param size 页大小
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinition>> findByConditions(String tenantId, String category, 
                                                        ProcessDefinition.DesignerType designerType,
                                                        ProcessDefinition.ProcessStatus status,
                                                        String keyword, int page, int size) {
        StringBuilder query = new StringBuilder("tenantId = ?1 and isLatest = true");
        
        if (category != null && !category.trim().isEmpty()) {
            query.append(" and category = ?2");
        }
        if (designerType != null) {
            query.append(" and designerType = ?3");
        }
        if (status != null) {
            query.append(" and status = ?4");
        } else {
            query.append(" and status != ?5");
        }
        if (keyword != null && !keyword.trim().isEmpty()) {
            query.append(" and (processName like ?6 or processKey like ?6 or description like ?6)");
        }
        
        query.append(" order by createdTime desc");
        
        // 构建参数数组
        Object[] params = new Object[6];
        params[0] = tenantId;
        params[1] = category;
        params[2] = designerType;
        params[3] = status;
        params[4] = ProcessDefinition.ProcessStatus.DELETED;
        params[5] = keyword != null ? "%" + keyword + "%" : null;
        
        return find(query.toString(), params)
                .page(page, size)
                .list();
    }

    /**
     * 统计符合条件的流程定义数量
     * 
     * @param tenantId 租户ID
     * @param category 分类（可为空）
     * @param designerType 设计器类型（可为空）
     * @param status 状态（可为空）
     * @param keyword 关键词（可为空）
     * @return 数量
     */
    public Uni<Long> countByConditions(String tenantId, String category, 
                                      ProcessDefinition.DesignerType designerType,
                                      ProcessDefinition.ProcessStatus status,
                                      String keyword) {
        StringBuilder query = new StringBuilder("tenantId = ?1 and isLatest = true");
        
        if (category != null && !category.trim().isEmpty()) {
            query.append(" and category = ?2");
        }
        if (designerType != null) {
            query.append(" and designerType = ?3");
        }
        if (status != null) {
            query.append(" and status = ?4");
        } else {
            query.append(" and status != ?5");
        }
        if (keyword != null && !keyword.trim().isEmpty()) {
            query.append(" and (processName like ?6 or processKey like ?6 or description like ?6)");
        }
        
        // 构建参数数组
        Object[] params = new Object[6];
        params[0] = tenantId;
        params[1] = category;
        params[2] = designerType;
        params[3] = status;
        params[4] = ProcessDefinition.ProcessStatus.DELETED;
        params[5] = keyword != null ? "%" + keyword + "%" : null;
        
        return count(query.toString(), params);
    }
}
