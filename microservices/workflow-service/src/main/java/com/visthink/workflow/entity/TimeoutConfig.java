package com.visthink.workflow.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 超时配置实体
 * 定义任务和流程的超时处理规则
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "wf_timeout_config", indexes = {
    @Index(name = "idx_timeout_config_process_def", columnList = "process_definition_id"),
    @Index(name = "idx_timeout_config_node", columnList = "node_key"),
    @Index(name = "idx_timeout_config_type", columnList = "timeout_type"),
    @Index(name = "idx_timeout_config_enabled", columnList = "enabled")
})
@Comment("超时配置表")
public class TimeoutConfig {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", length = 50)
    @Comment("租户ID")
    private String tenantId;

    /**
     * 流程定义ID
     */
    @Column(name = "process_definition_id", nullable = false)
    @Comment("流程定义ID")
    private Long processDefinitionId;

    /**
     * 节点标识（如果为空则对整个流程生效）
     */
    @Column(name = "node_key", length = 100)
    @Comment("节点标识")
    private String nodeKey;

    /**
     * 节点名称
     */
    @Column(name = "node_name", length = 200)
    @Comment("节点名称")
    private String nodeName;

    /**
     * 超时类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "timeout_type", length = 20, nullable = false)
    @Comment("超时类型")
    private TimeoutType timeoutType;

    /**
     * 超时时长（分钟）
     */
    @Column(name = "timeout_duration", nullable = false)
    @Comment("超时时长（分钟）")
    private Integer timeoutDuration;

    /**
     * 超时动作
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "timeout_action", length = 20, nullable = false)
    @Comment("超时动作")
    private TimeoutAction timeoutAction;

    /**
     * 目标用户ID（转办时使用）
     */
    @Column(name = "target_user_id")
    @Comment("目标用户ID")
    private Long targetUserId;

    /**
     * 目标用户名称
     */
    @Column(name = "target_user_name", length = 100)
    @Comment("目标用户名称")
    private String targetUserName;

    /**
     * 目标节点标识（驳回时使用）
     */
    @Column(name = "target_node_key", length = 100)
    @Comment("目标节点标识")
    private String targetNodeKey;

    /**
     * 提醒时间点（分钟，多个用逗号分隔）
     */
    @Column(name = "reminder_times", length = 200)
    @Comment("提醒时间点（分钟）")
    private String reminderTimes;

    /**
     * 提醒方式（多个用逗号分隔）
     */
    @Column(name = "reminder_methods", length = 100)
    @Comment("提醒方式")
    private String reminderMethods;

    /**
     * 提醒消息模板
     */
    @Column(name = "reminder_template", columnDefinition = "TEXT")
    @Comment("提醒消息模板")
    private String reminderTemplate;

    /**
     * 超时消息模板
     */
    @Column(name = "timeout_template", columnDefinition = "TEXT")
    @Comment("超时消息模板")
    private String timeoutTemplate;

    /**
     * 条件表达式（满足条件时配置才生效）
     */
    @Column(name = "condition_expression", columnDefinition = "TEXT")
    @Comment("条件表达式")
    private String conditionExpression;

    /**
     * 优先级（数字越大优先级越高）
     */
    @Column(name = "priority")
    @Comment("优先级")
    private Integer priority = 0;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    @Comment("是否启用")
    private Boolean enabled = true;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @Comment("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @Comment("更新时间")
    private LocalDateTime updatedTime;

    /**
     * 超时类型枚举
     */
    public enum TimeoutType {
        TASK("任务超时"),
        PROCESS("流程超时"),
        STEP("步骤超时");

        private final String description;

        TimeoutType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 超时动作枚举
     */
    public enum TimeoutAction {
        AUTO_APPROVE("自动同意"),
        AUTO_REJECT("自动驳回"),
        TRANSFER("转办"),
        DELEGATE("委派"),
        ESCALATE("升级"),
        TERMINATE("终止"),
        REMIND_ONLY("仅提醒");

        private final String description;

        TimeoutAction(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 提醒方式枚举
     */
    public enum ReminderMethod {
        EMAIL("邮件"),
        SMS("短信"),
        SYSTEM("系统通知"),
        WECHAT("微信"),
        DINGTALK("钉钉");

        private final String description;

        ReminderMethod(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public TimeoutConfig() {
        this.createdTime = LocalDateTime.now();
    }

    public TimeoutConfig(Long processDefinitionId, String nodeKey, TimeoutType timeoutType, 
                        Integer timeoutDuration, TimeoutAction timeoutAction) {
        this();
        this.processDefinitionId = processDefinitionId;
        this.nodeKey = nodeKey;
        this.timeoutType = timeoutType;
        this.timeoutDuration = timeoutDuration;
        this.timeoutAction = timeoutAction;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public void setNodeKey(String nodeKey) {
        this.nodeKey = nodeKey;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public TimeoutType getTimeoutType() {
        return timeoutType;
    }

    public void setTimeoutType(TimeoutType timeoutType) {
        this.timeoutType = timeoutType;
    }

    public Integer getTimeoutDuration() {
        return timeoutDuration;
    }

    public void setTimeoutDuration(Integer timeoutDuration) {
        this.timeoutDuration = timeoutDuration;
    }

    public TimeoutAction getTimeoutAction() {
        return timeoutAction;
    }

    public void setTimeoutAction(TimeoutAction timeoutAction) {
        this.timeoutAction = timeoutAction;
    }

    public Long getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(Long targetUserId) {
        this.targetUserId = targetUserId;
    }

    public String getTargetUserName() {
        return targetUserName;
    }

    public void setTargetUserName(String targetUserName) {
        this.targetUserName = targetUserName;
    }

    public String getTargetNodeKey() {
        return targetNodeKey;
    }

    public void setTargetNodeKey(String targetNodeKey) {
        this.targetNodeKey = targetNodeKey;
    }

    public String getReminderTimes() {
        return reminderTimes;
    }

    public void setReminderTimes(String reminderTimes) {
        this.reminderTimes = reminderTimes;
    }

    public String getReminderMethods() {
        return reminderMethods;
    }

    public void setReminderMethods(String reminderMethods) {
        this.reminderMethods = reminderMethods;
    }

    public String getReminderTemplate() {
        return reminderTemplate;
    }

    public void setReminderTemplate(String reminderTemplate) {
        this.reminderTemplate = reminderTemplate;
    }

    public String getTimeoutTemplate() {
        return timeoutTemplate;
    }

    public void setTimeoutTemplate(String timeoutTemplate) {
        this.timeoutTemplate = timeoutTemplate;
    }

    public String getConditionExpression() {
        return conditionExpression;
    }

    public void setConditionExpression(String conditionExpression) {
        this.conditionExpression = conditionExpression;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "TimeoutConfig{" +
                "id=" + id +
                ", processDefinitionId=" + processDefinitionId +
                ", nodeKey='" + nodeKey + '\'' +
                ", timeoutType=" + timeoutType +
                ", timeoutDuration=" + timeoutDuration +
                ", timeoutAction=" + timeoutAction +
                ", enabled=" + enabled +
                '}';
    }
}
