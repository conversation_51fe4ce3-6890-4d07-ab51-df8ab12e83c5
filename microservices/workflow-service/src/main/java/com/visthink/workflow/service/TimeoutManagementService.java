package com.visthink.workflow.service;

import com.visthink.workflow.entity.TimeoutConfig;
import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.entity.ProcessInstance;
import com.visthink.workflow.repository.TimeoutConfigRepository;
import com.visthink.workflow.repository.TaskInstanceRepository;
import com.visthink.workflow.exception.BusinessException;
import io.quarkus.scheduler.Scheduled;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 超时管理服务
 * 负责处理任务和流程的超时检测、提醒和自动处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class TimeoutManagementService {

    private static final Logger logger = LoggerFactory.getLogger(TimeoutManagementService.class);

    @Inject
    TimeoutConfigRepository timeoutConfigRepository;

    @Inject
    TaskInstanceRepository taskInstanceRepository;

    @Inject
    TaskControlService taskControlService;

    @Inject
    NotificationService notificationService;

    @Inject
    ExpressionService expressionService;

    // 缓存超时配置，避免频繁查询数据库
    private final Map<String, List<TimeoutConfig>> timeoutConfigCache = new ConcurrentHashMap<>();

    /**
     * 定时检查超时任务（每分钟执行一次）
     */
    @Scheduled(every = "60s")
    @Transactional
    public void checkTimeoutTasks() {
        logger.debug("开始检查超时任务");
        
        try {
            // 查询所有活跃的任务
            taskInstanceRepository.findActiveTasks()
                    .subscribe().with(
                            tasks -> {
                                for (TaskInstance task : tasks) {
                                    checkTaskTimeout(task).subscribe().with(
                                            result -> logger.debug("任务超时检查完成: taskId={}", task.getId()),
                                            failure -> logger.error("任务超时检查失败: taskId=" + task.getId(), failure)
                                    );
                                }
                            },
                            failure -> logger.error("查询活跃任务失败", failure)
                    );
        } catch (Exception e) {
            logger.error("超时检查任务执行失败", e);
        }
    }

    /**
     * 检查单个任务是否超时
     */
    public Uni<TimeoutCheckResult> checkTaskTimeout(TaskInstance task) {
        logger.debug("检查任务超时: taskId={}", task.getId());
        
        return getTimeoutConfigs(task.getProcessDefinitionId(), task.getNodeKey())
                .map(configs -> {
                    TimeoutCheckResult result = new TimeoutCheckResult();
                    result.setTaskId(task.getId());
                    
                    for (TimeoutConfig config : configs) {
                        if (isConfigApplicable(config, task)) {
                            TimeoutCheckResult.ConfigResult configResult = checkConfigTimeout(task, config);
                            result.addConfigResult(configResult);
                            
                            // 如果需要执行超时动作
                            if (configResult.isTimeout() && configResult.shouldExecuteAction()) {
                                executeTimeoutAction(task, config).subscribe().with(
                                        actionResult -> logger.info("超时动作执行成功: taskId={}, action={}", 
                                                                   task.getId(), config.getTimeoutAction()),
                                        failure -> logger.error("超时动作执行失败: taskId=" + task.getId(), failure)
                                );
                            }
                            
                            // 如果需要发送提醒
                            if (configResult.shouldSendReminder()) {
                                sendTimeoutReminder(task, config).subscribe().with(
                                        reminderResult -> logger.info("超时提醒发送成功: taskId={}", task.getId()),
                                        failure -> logger.error("超时提醒发送失败: taskId=" + task.getId(), failure)
                                );
                            }
                        }
                    }
                    
                    return result;
                });
    }

    /**
     * 执行超时动作
     */
    public Uni<TimeoutActionResult> executeTimeoutAction(TaskInstance task, TimeoutConfig config) {
        logger.info("执行超时动作: taskId={}, action={}", task.getId(), config.getTimeoutAction());
        
        TimeoutActionResult result = new TimeoutActionResult();
        result.setTaskId(task.getId());
        result.setAction(config.getTimeoutAction());
        
        switch (config.getTimeoutAction()) {
            case AUTO_APPROVE:
                return taskControlService.approveTask(
                        task.getId(), 
                        0L, // 系统用户
                        "系统", 
                        "任务超时自动同意", 
                        null, 
                        null
                ).map(controlResult -> {
                    result.setSuccess(controlResult.isSuccess());
                    result.setMessage(controlResult.getMessage());
                    return result;
                });
                
            case AUTO_REJECT:
                return taskControlService.rejectTask(
                        task.getId(), 
                        0L, // 系统用户
                        "系统", 
                        "任务超时自动驳回", 
                        config.getTargetNodeKey()
                ).map(controlResult -> {
                    result.setSuccess(controlResult.isSuccess());
                    result.setMessage(controlResult.getMessage());
                    return result;
                });
                
            case TRANSFER:
                if (config.getTargetUserId() != null) {
                    return taskControlService.transferTask(
                            task.getId(), 
                            task.getAssigneeId() != null ? task.getAssigneeId() : 0L, 
                            task.getAssigneeName() != null ? task.getAssigneeName() : "系统", 
                            config.getTargetUserId(), 
                            "任务超时自动转办"
                    ).map(controlResult -> {
                        result.setSuccess(controlResult.isSuccess());
                        result.setMessage(controlResult.getMessage());
                        return result;
                    });
                }
                break;
                
            case DELEGATE:
                if (config.getTargetUserId() != null) {
                    return taskControlService.delegateTask(
                            task.getId(), 
                            task.getAssigneeId() != null ? task.getAssigneeId() : 0L, 
                            task.getAssigneeName() != null ? task.getAssigneeName() : "系统", 
                            config.getTargetUserId(), 
                            "任务超时自动委派"
                    ).map(controlResult -> {
                        result.setSuccess(controlResult.isSuccess());
                        result.setMessage(controlResult.getMessage());
                        return result;
                    });
                }
                break;
                
            case TERMINATE:
                // 标记任务为超时状态
                task.timeout();
                return taskInstanceRepository.persist(task).map(savedTask -> {
                    result.setSuccess(true);
                    result.setMessage("任务已标记为超时");
                    return result;
                });
                
            case REMIND_ONLY:
                // 仅提醒，不执行其他动作
                result.setSuccess(true);
                result.setMessage("仅发送提醒");
                return Uni.createFrom().item(result);
                
            default:
                result.setSuccess(false);
                result.setMessage("不支持的超时动作: " + config.getTimeoutAction());
                return Uni.createFrom().item(result);
        }
        
        result.setSuccess(false);
        result.setMessage("超时动作执行失败");
        return Uni.createFrom().item(result);
    }

    /**
     * 发送超时提醒
     */
    public Uni<Boolean> sendTimeoutReminder(TaskInstance task, TimeoutConfig config) {
        logger.debug("发送超时提醒: taskId={}", task.getId());
        
        // 构建提醒消息
        String title = "任务超时提醒";
        String content = config.getReminderTemplate() != null ? 
                        config.getReminderTemplate() : 
                        String.format("您有一个任务即将超时：%s", task.getTaskName());
        
        // 解析提醒方式
        List<String> methods = config.getReminderMethods() != null ? 
                              Arrays.asList(config.getReminderMethods().split(",")) : 
                              Arrays.asList("SYSTEM");
        
        return notificationService.sendTaskReminder(task, title, content, methods);
    }

    /**
     * 创建或更新超时配置
     */
    public Uni<TimeoutConfig> saveTimeoutConfig(TimeoutConfig config) {
        logger.debug("保存超时配置: {}", config);
        
        // 清除缓存
        clearTimeoutConfigCache(config.getProcessDefinitionId());
        
        if (config.getId() == null) {
            return timeoutConfigRepository.persist(config);
        } else {
            return timeoutConfigRepository.findById(config.getId())
                    .flatMap(existing -> {
                        if (existing == null) {
                            return Uni.createFrom().failure(new BusinessException("超时配置不存在"));
                        }
                        
                        updateTimeoutConfigFields(existing, config);
                        return timeoutConfigRepository.persist(existing);
                    });
        }
    }

    /**
     * 删除超时配置
     */
    public Uni<Boolean> deleteTimeoutConfig(Long id) {
        logger.debug("删除超时配置: {}", id);
        
        return timeoutConfigRepository.findById(id)
                .flatMap(config -> {
                    if (config != null) {
                        clearTimeoutConfigCache(config.getProcessDefinitionId());
                    }
                    return timeoutConfigRepository.deleteById(id);
                });
    }

    // 私有辅助方法
    private Uni<List<TimeoutConfig>> getTimeoutConfigs(Long processDefinitionId, String nodeKey) {
        String cacheKey = processDefinitionId + ":" + nodeKey;
        
        if (timeoutConfigCache.containsKey(cacheKey)) {
            return Uni.createFrom().item(timeoutConfigCache.get(cacheKey));
        }
        
        return timeoutConfigRepository.findByProcessDefinitionIdAndNodeKey(processDefinitionId, nodeKey)
                .map(configs -> {
                    timeoutConfigCache.put(cacheKey, configs);
                    return configs;
                });
    }

    private boolean isConfigApplicable(TimeoutConfig config, TaskInstance task) {
        if (!config.getEnabled()) {
            return false;
        }
        
        // 检查条件表达式
        if (config.getConditionExpression() != null && !config.getConditionExpression().trim().isEmpty()) {
            try {
                // 这里需要构建流程实例对象用于表达式计算
                // 简化处理，实际应该查询完整的流程实例
                return expressionService.evaluateBoolean(config.getConditionExpression(), null);
            } catch (Exception e) {
                logger.warn("超时配置条件表达式计算失败: {}", config.getConditionExpression(), e);
                return false;
            }
        }
        
        return true;
    }

    private TimeoutCheckResult.ConfigResult checkConfigTimeout(TaskInstance task, TimeoutConfig config) {
        TimeoutCheckResult.ConfigResult result = new TimeoutCheckResult.ConfigResult();
        result.setConfigId(config.getId());
        result.setTimeoutDuration(config.getTimeoutDuration());
        result.setTimeoutAction(config.getTimeoutAction());
        
        // 计算任务已运行时间
        LocalDateTime startTime = task.getCreatedTime();
        if (task.getClaimTime() != null) {
            startTime = task.getClaimTime(); // 从签收时间开始计算
        }
        
        long runningMinutes = java.time.Duration.between(startTime, LocalDateTime.now()).toMinutes();
        result.setRunningMinutes(runningMinutes);
        
        // 判断是否超时
        boolean isTimeout = runningMinutes >= config.getTimeoutDuration();
        result.setTimeout(isTimeout);
        
        // 判断是否需要发送提醒
        if (config.getReminderTimes() != null && !config.getReminderTimes().trim().isEmpty()) {
            String[] reminderTimes = config.getReminderTimes().split(",");
            for (String timeStr : reminderTimes) {
                try {
                    int reminderTime = Integer.parseInt(timeStr.trim());
                    if (runningMinutes >= reminderTime && runningMinutes < reminderTime + 1) {
                        result.setShouldSendReminder(true);
                        break;
                    }
                } catch (NumberFormatException e) {
                    logger.warn("无效的提醒时间配置: {}", timeStr);
                }
            }
        }
        
        return result;
    }

    private void clearTimeoutConfigCache(Long processDefinitionId) {
        timeoutConfigCache.entrySet().removeIf(entry -> 
                entry.getKey().startsWith(processDefinitionId + ":"));
    }

    private void updateTimeoutConfigFields(TimeoutConfig existing, TimeoutConfig updated) {
        existing.setNodeKey(updated.getNodeKey());
        existing.setNodeName(updated.getNodeName());
        existing.setTimeoutType(updated.getTimeoutType());
        existing.setTimeoutDuration(updated.getTimeoutDuration());
        existing.setTimeoutAction(updated.getTimeoutAction());
        existing.setTargetUserId(updated.getTargetUserId());
        existing.setTargetUserName(updated.getTargetUserName());
        existing.setTargetNodeKey(updated.getTargetNodeKey());
        existing.setReminderTimes(updated.getReminderTimes());
        existing.setReminderMethods(updated.getReminderMethods());
        existing.setReminderTemplate(updated.getReminderTemplate());
        existing.setTimeoutTemplate(updated.getTimeoutTemplate());
        existing.setConditionExpression(updated.getConditionExpression());
        existing.setPriority(updated.getPriority());
        existing.setEnabled(updated.getEnabled());
    }

    /**
     * 超时检查结果类
     */
    public static class TimeoutCheckResult {
        private Long taskId;
        private List<ConfigResult> configResults = new java.util.ArrayList<>();

        public void addConfigResult(ConfigResult result) {
            configResults.add(result);
        }

        // Getter和Setter方法
        public Long getTaskId() {
            return taskId;
        }

        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }

        public List<ConfigResult> getConfigResults() {
            return configResults;
        }

        public void setConfigResults(List<ConfigResult> configResults) {
            this.configResults = configResults;
        }

        public static class ConfigResult {
            private Long configId;
            private Integer timeoutDuration;
            private TimeoutConfig.TimeoutAction timeoutAction;
            private long runningMinutes;
            private boolean timeout;
            private boolean shouldSendReminder;
            private boolean shouldExecuteAction;

            // Getter和Setter方法
            public Long getConfigId() {
                return configId;
            }

            public void setConfigId(Long configId) {
                this.configId = configId;
            }

            public Integer getTimeoutDuration() {
                return timeoutDuration;
            }

            public void setTimeoutDuration(Integer timeoutDuration) {
                this.timeoutDuration = timeoutDuration;
            }

            public TimeoutConfig.TimeoutAction getTimeoutAction() {
                return timeoutAction;
            }

            public void setTimeoutAction(TimeoutConfig.TimeoutAction timeoutAction) {
                this.timeoutAction = timeoutAction;
            }

            public long getRunningMinutes() {
                return runningMinutes;
            }

            public void setRunningMinutes(long runningMinutes) {
                this.runningMinutes = runningMinutes;
            }

            public boolean isTimeout() {
                return timeout;
            }

            public void setTimeout(boolean timeout) {
                this.timeout = timeout;
                this.shouldExecuteAction = timeout; // 超时时执行动作
            }

            public boolean shouldSendReminder() {
                return shouldSendReminder;
            }

            public void setShouldSendReminder(boolean shouldSendReminder) {
                this.shouldSendReminder = shouldSendReminder;
            }

            public boolean shouldExecuteAction() {
                return shouldExecuteAction;
            }

            public void setShouldExecuteAction(boolean shouldExecuteAction) {
                this.shouldExecuteAction = shouldExecuteAction;
            }
        }
    }

    /**
     * 超时动作执行结果类
     */
    public static class TimeoutActionResult {
        private Long taskId;
        private TimeoutConfig.TimeoutAction action;
        private boolean success;
        private String message;

        // Getter和Setter方法
        public Long getTaskId() {
            return taskId;
        }

        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }

        public TimeoutConfig.TimeoutAction getAction() {
            return action;
        }

        public void setAction(TimeoutConfig.TimeoutAction action) {
            this.action = action;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
