package com.visthink.workflow.repository;

import com.visthink.workflow.entity.TimeoutConfig;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 超时配置Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class TimeoutConfigRepository implements PanacheRepository<TimeoutConfig> {

    /**
     * 根据流程定义ID和节点标识查询超时配置
     */
    public Uni<List<TimeoutConfig>> findByProcessDefinitionIdAndNodeKey(Long processDefinitionId, String nodeKey) {
        return find("processDefinitionId = ?1 and (nodeKey = ?2 or nodeKey is null) and enabled = true order by priority desc", 
                   processDefinitionId, nodeKey).list();
    }

    /**
     * 根据流程定义ID查询所有超时配置
     */
    public Uni<List<TimeoutConfig>> findByProcessDefinitionId(Long processDefinitionId) {
        return find("processDefinitionId = ?1 order by nodeKey, priority desc", processDefinitionId).list();
    }

    /**
     * 根据超时类型查询配置
     */
    public Uni<List<TimeoutConfig>> findByTimeoutType(TimeoutConfig.TimeoutType timeoutType) {
        return find("timeoutType = ?1 and enabled = true order by priority desc", timeoutType).list();
    }

    /**
     * 根据超时动作查询配置
     */
    public Uni<List<TimeoutConfig>> findByTimeoutAction(TimeoutConfig.TimeoutAction timeoutAction) {
        return find("timeoutAction = ?1 and enabled = true order by priority desc", timeoutAction).list();
    }

    /**
     * 根据租户ID查询超时配置
     */
    public Uni<List<TimeoutConfig>> findByTenantId(String tenantId) {
        return find("tenantId = ?1 order by processDefinitionId, nodeKey", tenantId).list();
    }

    /**
     * 查询启用的超时配置
     */
    public Uni<List<TimeoutConfig>> findEnabledConfigs() {
        return find("enabled = true order by processDefinitionId, nodeKey, priority desc").list();
    }

    /**
     * 根据目标用户ID查询超时配置
     */
    public Uni<List<TimeoutConfig>> findByTargetUserId(Long targetUserId) {
        return find("targetUserId = ?1 and enabled = true", targetUserId).list();
    }

    /**
     * 检查是否存在重复的超时配置
     */
    public Uni<Boolean> existsDuplicate(Long processDefinitionId, String nodeKey, 
                                       TimeoutConfig.TimeoutType timeoutType, Long excludeId) {
        String query = "processDefinitionId = ?1 and nodeKey = ?2 and timeoutType = ?3";
        
        if (excludeId != null) {
            query += " and id != ?4";
            return count(query, processDefinitionId, nodeKey, timeoutType, excludeId)
                    .map(count -> count > 0);
        } else {
            return count(query, processDefinitionId, nodeKey, timeoutType)
                    .map(count -> count > 0);
        }
    }

    /**
     * 批量删除流程定义的超时配置
     */
    public Uni<Long> deleteByProcessDefinitionId(Long processDefinitionId) {
        return delete("processDefinitionId = ?1", processDefinitionId);
    }

    /**
     * 批量删除节点的超时配置
     */
    public Uni<Long> deleteByProcessDefinitionIdAndNodeKey(Long processDefinitionId, String nodeKey) {
        return delete("processDefinitionId = ?1 and nodeKey = ?2", processDefinitionId, nodeKey);
    }

    /**
     * 启用或禁用超时配置
     */
    public Uni<Integer> updateEnabled(Long id, Boolean enabled) {
        return update("enabled = ?1 where id = ?2", enabled, id);
    }

    /**
     * 批量启用或禁用超时配置
     */
    public Uni<Integer> batchUpdateEnabled(List<Long> ids, Boolean enabled) {
        return update("enabled = ?1 where id in ?2", enabled, ids);
    }

    /**
     * 统计流程定义的超时配置数量
     */
    public Uni<Long> countByProcessDefinitionId(Long processDefinitionId) {
        return count("processDefinitionId = ?1", processDefinitionId);
    }

    /**
     * 统计启用的超时配置数量
     */
    public Uni<Long> countEnabledConfigs() {
        return count("enabled = true");
    }

    /**
     * 获取流程定义的所有节点（有超时配置的）
     */
    public Uni<List<String>> getNodeKeysByProcessDefinitionId(Long processDefinitionId) {
        return getEntityManager()
                .createQuery("SELECT DISTINCT tc.nodeKey FROM TimeoutConfig tc WHERE tc.processDefinitionId = :processDefinitionId AND tc.nodeKey IS NOT NULL ORDER BY tc.nodeKey", String.class)
                .setParameter("processDefinitionId", processDefinitionId)
                .getResultList();
    }

    /**
     * 获取超时配置统计信息
     */
    public Uni<List<Object[]>> getTimeoutConfigStatistics() {
        return getEntityManager()
                .createQuery("SELECT tc.timeoutType, tc.timeoutAction, COUNT(tc) FROM TimeoutConfig tc WHERE tc.enabled = true GROUP BY tc.timeoutType, tc.timeoutAction", Object[].class)
                .getResultList();
    }

    /**
     * 查询即将超时的配置（用于提前提醒）
     */
    public Uni<List<TimeoutConfig>> findUpcomingTimeoutConfigs(Integer reminderMinutes) {
        return find("enabled = true and reminderTimes like ?1", "%" + reminderMinutes + "%").list();
    }
}
