package com.visthink.workflow.service;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Supplier;

/**
 * 并发控制服务
 * 负责管理工作流引擎的并发访问和资源控制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class ConcurrencyControlService {

    private static final Logger logger = LoggerFactory.getLogger(ConcurrencyControlService.class);

    @Inject
    PerformanceMonitorService performanceMonitorService;

    // 线程池配置
    private static final int CORE_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = 50;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static final int QUEUE_CAPACITY = 1000;

    // 执行器
    private final ExecutorService taskExecutor;
    private final ScheduledExecutorService scheduledExecutor;
    
    // 信号量控制并发数
    private final Semaphore processExecutionSemaphore;
    private final Semaphore taskExecutionSemaphore;
    private final Semaphore databaseAccessSemaphore;
    
    // 分布式锁管理
    private final ConcurrentHashMap<String, ReentrantLock> localLocks;
    private final ConcurrentHashMap<String, LockInfo> lockRegistry;
    
    // 限流器
    private final RateLimiter processStartLimiter;
    private final RateLimiter taskCompleteLimiter;
    
    // 统计信息
    private final AtomicInteger activeThreadCount = new AtomicInteger(0);
    private final AtomicInteger queuedTaskCount = new AtomicInteger(0);

    public ConcurrencyControlService() {
        // 初始化线程池
        this.taskExecutor = new ThreadPoolExecutor(
                CORE_POOL_SIZE,
                MAX_POOL_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(QUEUE_CAPACITY),
                new WorkflowThreadFactory("workflow-task"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        
        this.scheduledExecutor = Executors.newScheduledThreadPool(5, 
                new WorkflowThreadFactory("workflow-scheduled"));
        
        // 初始化信号量
        this.processExecutionSemaphore = new Semaphore(20); // 最多20个并发流程
        this.taskExecutionSemaphore = new Semaphore(100);   // 最多100个并发任务
        this.databaseAccessSemaphore = new Semaphore(50);   // 最多50个并发数据库访问
        
        // 初始化锁管理
        this.localLocks = new ConcurrentHashMap<>();
        this.lockRegistry = new ConcurrentHashMap<>();
        
        // 初始化限流器
        this.processStartLimiter = new RateLimiter(10, Duration.ofSeconds(1)); // 每秒最多10个流程启动
        this.taskCompleteLimiter = new RateLimiter(50, Duration.ofSeconds(1)); // 每秒最多50个任务完成
    }

    /**
     * 执行流程操作（带并发控制）
     */
    public <T> Uni<T> executeProcessOperation(String processInstanceId, Supplier<Uni<T>> operation) {
        return Uni.createFrom().item(() -> {
            try {
                // 获取流程执行许可
                processExecutionSemaphore.acquire();
                
                // 检查限流
                if (!processStartLimiter.tryAcquire()) {
                    throw new RuntimeException("流程启动频率过高，请稍后重试");
                }
                
                logger.debug("开始执行流程操作: {}", processInstanceId);
                long startTime = System.currentTimeMillis();
                
                return operation.get()
                        .onTermination().invoke(() -> {
                            processExecutionSemaphore.release();
                            long duration = System.currentTimeMillis() - startTime;
                            performanceMonitorService.recordProcessComplete(processInstanceId, duration);
                            logger.debug("流程操作完成: {}, 耗时: {}ms", processInstanceId, duration);
                        });
                        
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("流程操作被中断", e);
            }
        }).flatMap(uni -> uni);
    }

    /**
     * 执行任务操作（带并发控制）
     */
    public <T> Uni<T> executeTaskOperation(String taskInstanceId, Supplier<Uni<T>> operation) {
        return Uni.createFrom().item(() -> {
            try {
                // 获取任务执行许可
                taskExecutionSemaphore.acquire();
                
                // 检查限流
                if (!taskCompleteLimiter.tryAcquire()) {
                    throw new RuntimeException("任务处理频率过高，请稍后重试");
                }
                
                logger.debug("开始执行任务操作: {}", taskInstanceId);
                long startTime = System.currentTimeMillis();
                
                return operation.get()
                        .onTermination().invoke(() -> {
                            taskExecutionSemaphore.release();
                            long duration = System.currentTimeMillis() - startTime;
                            performanceMonitorService.recordTaskComplete(taskInstanceId, duration);
                            logger.debug("任务操作完成: {}, 耗时: {}ms", taskInstanceId, duration);
                        });
                        
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("任务操作被中断", e);
            }
        }).flatMap(uni -> uni);
    }

    /**
     * 执行数据库操作（带并发控制）
     */
    public <T> Uni<T> executeDatabaseOperation(String operationType, Supplier<Uni<T>> operation) {
        return Uni.createFrom().item(() -> {
            try {
                // 获取数据库访问许可
                databaseAccessSemaphore.acquire();
                
                logger.debug("开始执行数据库操作: {}", operationType);
                long startTime = System.currentTimeMillis();
                
                return operation.get()
                        .onTermination().invoke(() -> {
                            databaseAccessSemaphore.release();
                            long duration = System.currentTimeMillis() - startTime;
                            performanceMonitorService.recordDatabaseQuery(operationType, duration);
                            logger.debug("数据库操作完成: {}, 耗时: {}ms", operationType, duration);
                        });
                        
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("数据库操作被中断", e);
            }
        }).flatMap(uni -> uni);
    }

    /**
     * 获取分布式锁
     */
    public boolean acquireLock(String lockKey, long timeoutMs) {
        try {
            ReentrantLock lock = localLocks.computeIfAbsent(lockKey, k -> new ReentrantLock());
            
            if (lock.tryLock(timeoutMs, TimeUnit.MILLISECONDS)) {
                // 记录锁信息
                lockRegistry.put(lockKey, new LockInfo(lockKey, Thread.currentThread().getName(), LocalDateTime.now()));
                logger.debug("获取锁成功: {}", lockKey);
                return true;
            } else {
                logger.warn("获取锁超时: {}", lockKey);
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("获取锁被中断: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     */
    public void releaseLock(String lockKey) {
        ReentrantLock lock = localLocks.get(lockKey);
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
            lockRegistry.remove(lockKey);
            logger.debug("释放锁成功: {}", lockKey);
        } else {
            logger.warn("尝试释放未持有的锁: {}", lockKey);
        }
    }

    /**
     * 异步执行任务
     */
    public <T> CompletableFuture<T> executeAsync(Supplier<T> task) {
        queuedTaskCount.incrementAndGet();
        
        return CompletableFuture.supplyAsync(() -> {
            activeThreadCount.incrementAndGet();
            queuedTaskCount.decrementAndGet();
            
            try {
                return task.get();
            } finally {
                activeThreadCount.decrementAndGet();
            }
        }, taskExecutor);
    }

    /**
     * 延迟执行任务
     */
    public ScheduledFuture<?> scheduleTask(Runnable task, long delay, TimeUnit unit) {
        return scheduledExecutor.schedule(() -> {
            activeThreadCount.incrementAndGet();
            try {
                task.run();
            } finally {
                activeThreadCount.decrementAndGet();
            }
        }, delay, unit);
    }

    /**
     * 定期执行任务
     */
    public ScheduledFuture<?> scheduleAtFixedRate(Runnable task, long initialDelay, long period, TimeUnit unit) {
        return scheduledExecutor.scheduleAtFixedRate(() -> {
            activeThreadCount.incrementAndGet();
            try {
                task.run();
            } finally {
                activeThreadCount.decrementAndGet();
            }
        }, initialDelay, period, unit);
    }

    /**
     * 获取并发统计信息
     */
    public ConcurrencyStatistics getConcurrencyStatistics() {
        ConcurrencyStatistics stats = new ConcurrencyStatistics();
        
        // 线程池统计
        if (taskExecutor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) taskExecutor;
            stats.setCorePoolSize(tpe.getCorePoolSize());
            stats.setMaxPoolSize(tpe.getMaximumPoolSize());
            stats.setActiveThreadCount(tpe.getActiveCount());
            stats.setCompletedTaskCount(tpe.getCompletedTaskCount());
            stats.setQueueSize(tpe.getQueue().size());
        }
        
        // 信号量统计
        stats.setAvailableProcessPermits(processExecutionSemaphore.availablePermits());
        stats.setAvailableTaskPermits(taskExecutionSemaphore.availablePermits());
        stats.setAvailableDatabasePermits(databaseAccessSemaphore.availablePermits());
        
        // 锁统计
        stats.setActiveLockCount(lockRegistry.size());
        
        // 限流器统计
        stats.setProcessStartRate(processStartLimiter.getCurrentRate());
        stats.setTaskCompleteRate(taskCompleteLimiter.getCurrentRate());
        
        return stats;
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        logger.info("关闭并发控制服务");
        
        taskExecutor.shutdown();
        scheduledExecutor.shutdown();
        
        try {
            if (!taskExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                taskExecutor.shutdownNow();
            }
            if (!scheduledExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                scheduledExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            taskExecutor.shutdownNow();
            scheduledExecutor.shutdownNow();
        }
    }

    // 内部类
    private static class WorkflowThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        WorkflowThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix + "-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }

    private static class LockInfo {
        private final String lockKey;
        private final String threadName;
        private final LocalDateTime acquiredTime;

        public LockInfo(String lockKey, String threadName, LocalDateTime acquiredTime) {
            this.lockKey = lockKey;
            this.threadName = threadName;
            this.acquiredTime = acquiredTime;
        }

        // Getter方法
        public String getLockKey() { return lockKey; }
        public String getThreadName() { return threadName; }
        public LocalDateTime getAcquiredTime() { return acquiredTime; }
    }

    private static class RateLimiter {
        private final int maxRequests;
        private final Duration timeWindow;
        private final ConcurrentLinkedQueue<LocalDateTime> requests = new ConcurrentLinkedQueue<>();

        public RateLimiter(int maxRequests, Duration timeWindow) {
            this.maxRequests = maxRequests;
            this.timeWindow = timeWindow;
        }

        public boolean tryAcquire() {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime cutoff = now.minus(timeWindow);
            
            // 清理过期请求
            requests.removeIf(time -> time.isBefore(cutoff));
            
            if (requests.size() < maxRequests) {
                requests.offer(now);
                return true;
            }
            
            return false;
        }

        public double getCurrentRate() {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime cutoff = now.minus(timeWindow);
            
            // 清理过期请求
            requests.removeIf(time -> time.isBefore(cutoff));
            
            return (double) requests.size() / timeWindow.getSeconds();
        }
    }

    public static class ConcurrencyStatistics {
        private int corePoolSize;
        private int maxPoolSize;
        private int activeThreadCount;
        private long completedTaskCount;
        private int queueSize;
        private int availableProcessPermits;
        private int availableTaskPermits;
        private int availableDatabasePermits;
        private int activeLockCount;
        private double processStartRate;
        private double taskCompleteRate;

        // Getter和Setter方法
        public int getCorePoolSize() { return corePoolSize; }
        public void setCorePoolSize(int corePoolSize) { this.corePoolSize = corePoolSize; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public void setMaxPoolSize(int maxPoolSize) { this.maxPoolSize = maxPoolSize; }
        public int getActiveThreadCount() { return activeThreadCount; }
        public void setActiveThreadCount(int activeThreadCount) { this.activeThreadCount = activeThreadCount; }
        public long getCompletedTaskCount() { return completedTaskCount; }
        public void setCompletedTaskCount(long completedTaskCount) { this.completedTaskCount = completedTaskCount; }
        public int getQueueSize() { return queueSize; }
        public void setQueueSize(int queueSize) { this.queueSize = queueSize; }
        public int getAvailableProcessPermits() { return availableProcessPermits; }
        public void setAvailableProcessPermits(int availableProcessPermits) { this.availableProcessPermits = availableProcessPermits; }
        public int getAvailableTaskPermits() { return availableTaskPermits; }
        public void setAvailableTaskPermits(int availableTaskPermits) { this.availableTaskPermits = availableTaskPermits; }
        public int getAvailableDatabasePermits() { return availableDatabasePermits; }
        public void setAvailableDatabasePermits(int availableDatabasePermits) { this.availableDatabasePermits = availableDatabasePermits; }
        public int getActiveLockCount() { return activeLockCount; }
        public void setActiveLockCount(int activeLockCount) { this.activeLockCount = activeLockCount; }
        public double getProcessStartRate() { return processStartRate; }
        public void setProcessStartRate(double processStartRate) { this.processStartRate = processStartRate; }
        public double getTaskCompleteRate() { return taskCompleteRate; }
        public void setTaskCompleteRate(double taskCompleteRate) { this.taskCompleteRate = taskCompleteRate; }
    }
}
