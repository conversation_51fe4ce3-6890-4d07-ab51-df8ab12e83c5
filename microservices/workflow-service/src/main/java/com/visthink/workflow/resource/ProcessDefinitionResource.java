package com.visthink.workflow.resource;

import com.visthink.shared.dto.ApiResponse;
import com.visthink.shared.dto.PageResult;
import com.visthink.workflow.dto.ProcessDefinitionDTO;
import com.visthink.workflow.entity.ProcessDefinition;
import com.visthink.workflow.service.ProcessDefinitionService;
import io.smallrye.mutiny.Uni;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 流程定义REST API控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/v1/workflow/process-definitions")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "流程定义管理", description = "流程定义的增删改查和状态管理")
public class ProcessDefinitionResource {

    private static final Logger logger = LoggerFactory.getLogger(ProcessDefinitionResource.class);

    @Inject
    ProcessDefinitionService processDefinitionService;

    /**
     * 创建流程定义
     */
    @POST
    @Operation(summary = "创建流程定义", description = "创建新的流程定义")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "创建成功",
            content = @Content(schema = @Schema(implementation = ProcessDefinitionDTO.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "请求参数错误"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "409", 
            description = "流程标识已存在"
        )
    })
    public Uni<ApiResponse<ProcessDefinitionDTO>> create(@Valid ProcessDefinitionDTO dto) {
        logger.info("创建流程定义请求: {}", dto.getProcessKey());
        
        return processDefinitionService.create(dto)
                .map(result -> ApiResponse.success(result, "流程定义创建成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("创建流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 更新流程定义
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新流程定义", description = "更新指定ID的流程定义")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "更新成功"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "流程定义不存在"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "请求参数错误"
        )
    })
    public Uni<ApiResponse<ProcessDefinitionDTO>> update(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id,
            @Valid ProcessDefinitionDTO dto) {
        logger.info("更新流程定义请求: {}", id);
        
        return processDefinitionService.update(id, dto)
                .map(result -> ApiResponse.success(result, "流程定义更新成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("更新流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 发布流程定义
     */
    @POST
    @Path("/{id}/deploy")
    @Operation(summary = "发布流程定义", description = "发布指定ID的流程定义，使其生效")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200", 
            description = "发布成功"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "404", 
            description = "流程定义不存在"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400", 
            description = "流程定义状态不允许发布"
        )
    })
    public Uni<ApiResponse<ProcessDefinitionDTO>> deploy(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id) {
        logger.info("发布流程定义请求: {}", id);
        
        return processDefinitionService.deploy(id)
                .map(result -> ApiResponse.success(result, "流程定义发布成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("发布流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 挂起流程定义
     */
    @POST
    @Path("/{id}/suspend")
    @Operation(summary = "挂起流程定义", description = "挂起指定ID的流程定义，暂停新实例创建")
    public Uni<ApiResponse<ProcessDefinitionDTO>> suspend(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id) {
        logger.info("挂起流程定义请求: {}", id);
        
        return processDefinitionService.suspend(id)
                .map(result -> ApiResponse.success(result, "流程定义挂起成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("挂起流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 激活流程定义
     */
    @POST
    @Path("/{id}/activate")
    @Operation(summary = "激活流程定义", description = "激活指定ID的流程定义")
    public Uni<ApiResponse<ProcessDefinitionDTO>> activate(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id) {
        logger.info("激活流程定义请求: {}", id);
        
        return processDefinitionService.activate(id)
                .map(result -> ApiResponse.success(result, "流程定义激活成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("激活流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 删除流程定义
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除流程定义", description = "删除指定ID的流程定义（逻辑删除）")
    public Uni<ApiResponse<Boolean>> delete(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id) {
        logger.info("删除流程定义请求: {}", id);
        
        return processDefinitionService.delete(id)
                .map(result -> ApiResponse.success(result, "流程定义删除成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("删除流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 根据ID查询流程定义
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询流程定义详情", description = "根据ID查询流程定义的详细信息")
    public Uni<ApiResponse<ProcessDefinitionDTO>> findById(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id) {
        logger.debug("查询流程定义详情: {}", id);
        
        return processDefinitionService.findById(id)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("查询流程定义详情失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 根据流程标识查询最新版本
     */
    @GET
    @Path("/key/{processKey}")
    @Operation(summary = "根据流程标识查询最新版本", description = "根据流程标识查询最新版本的流程定义")
    public Uni<ApiResponse<ProcessDefinitionDTO>> findLatestByKey(
            @Parameter(description = "流程标识", required = true) @PathParam("processKey") String processKey) {
        logger.debug("根据流程标识查询最新版本: {}", processKey);
        
        return processDefinitionService.findLatestByKey(processKey)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("根据流程标识查询最新版本失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 查询所有激活状态的流程定义
     */
    @GET
    @Path("/active")
    @Operation(summary = "查询激活状态的流程定义", description = "查询所有激活状态的流程定义列表")
    public Uni<ApiResponse<List<ProcessDefinitionDTO>>> findAllActive() {
        logger.debug("查询激活状态的流程定义");
        
        return processDefinitionService.findAllActive()
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("查询激活状态的流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 分页查询流程定义
     */
    @GET
    @Operation(summary = "分页查询流程定义", description = "分页查询流程定义列表")
    public Uni<ApiResponse<PageResult<ProcessDefinitionDTO>>> findWithPaging(
            @Parameter(description = "页码，从1开始", example = "1") @QueryParam("page") @DefaultValue("1") int page,
            @Parameter(description = "页大小", example = "10") @QueryParam("size") @DefaultValue("10") int size,
            @Parameter(description = "分类") @QueryParam("category") String category,
            @Parameter(description = "设计器类型") @QueryParam("designerType") ProcessDefinition.DesignerType designerType,
            @Parameter(description = "状态") @QueryParam("status") ProcessDefinition.ProcessStatus status,
            @Parameter(description = "关键词搜索") @QueryParam("keyword") String keyword) {
        
        logger.debug("分页查询流程定义: page={}, size={}", page, size);
        
        // 页码从1开始，转换为从0开始
        int pageIndex = Math.max(0, page - 1);
        int pageSize = Math.max(1, Math.min(size, 100)); // 限制最大页大小为100
        
        return processDefinitionService.findWithPaging(pageIndex, pageSize)
                .flatMap(records -> 
                    processDefinitionService.count()
                        .map(total -> {
                            PageResult<ProcessDefinitionDTO> pageResult = new PageResult<>();
                            pageResult.setRecords(records);
                            pageResult.setTotal(total);
                            pageResult.setCurrent(page);
                            pageResult.setSize(pageSize);
                            pageResult.setPages((int) Math.ceil((double) total / pageSize));
                            return ApiResponse.success(pageResult);
                        })
                )
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("分页查询流程定义失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 获取流程定义统计信息
     */
    @GET
    @Path("/statistics")
    @Operation(summary = "获取流程定义统计信息", description = "获取流程定义的统计信息")
    public Uni<ApiResponse<Object>> getStatistics() {
        logger.debug("获取流程定义统计信息");

        return processDefinitionService.count()
                .map(total -> {
                    // TODO: 实现更详细的统计信息
                    return ApiResponse.success(java.util.Map.of(
                        "total", total,
                        "timestamp", System.currentTimeMillis()
                    ));
                })
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("获取流程定义统计信息失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 导出流程定义为BPMN XML
     */
    @GET
    @Path("/{id}/export/bpmn")
    @Produces(MediaType.APPLICATION_XML)
    @Operation(summary = "导出BPMN XML", description = "导出指定流程定义的BPMN XML")
    public Uni<jakarta.ws.rs.core.Response> exportBpmn(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id) {
        logger.info("导出流程定义BPMN: {}", id);

        return processDefinitionService.findById(id)
                .map(dto -> {
                    String bpmnXml = dto.getBpmnXml();
                    if (bpmnXml == null || bpmnXml.trim().isEmpty()) {
                        throw new BusinessException("该流程定义没有BPMN XML内容");
                    }

                    String filename = dto.getProcessKey() + "_v" + dto.getProcessVersion() + ".bpmn";
                    return jakarta.ws.rs.core.Response.ok(bpmnXml)
                            .header("Content-Disposition", "attachment; filename=\"" + filename + "\"")
                            .build();
                })
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("导出BPMN失败", throwable);
                    return jakarta.ws.rs.core.Response.status(jakarta.ws.rs.core.Response.Status.INTERNAL_SERVER_ERROR)
                            .entity(ApiResponse.error(throwable.getMessage()))
                            .build();
                });
    }

    /**
     * 导出流程定义为JSON
     */
    @GET
    @Path("/{id}/export/json")
    @Produces(MediaType.APPLICATION_JSON)
    @Operation(summary = "导出流程定义JSON", description = "导出指定流程定义的JSON格式")
    public Uni<jakarta.ws.rs.core.Response> exportJson(
            @Parameter(description = "流程定义ID", required = true) @PathParam("id") Long id) {
        logger.info("导出流程定义JSON: {}", id);

        return processDefinitionService.findById(id)
                .map(dto -> {
                    String filename = dto.getProcessKey() + "_v" + dto.getProcessVersion() + ".json";
                    return jakarta.ws.rs.core.Response.ok(dto)
                            .header("Content-Disposition", "attachment; filename=\"" + filename + "\"")
                            .build();
                })
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("导出JSON失败", throwable);
                    return jakarta.ws.rs.core.Response.status(jakarta.ws.rs.core.Response.Status.INTERNAL_SERVER_ERROR)
                            .entity(ApiResponse.error(throwable.getMessage()))
                            .build();
                });
    }

    /**
     * 导入BPMN XML创建流程定义
     */
    @POST
    @Path("/import/bpmn")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Operation(summary = "导入BPMN XML", description = "通过上传BPMN XML文件创建流程定义")
    public Uni<ApiResponse<ProcessDefinitionDTO>> importBpmn(
            @org.jboss.resteasy.reactive.multipart.FileUpload("file") java.io.File file,
            @org.jboss.resteasy.reactive.RestForm("processName") String processName,
            @org.jboss.resteasy.reactive.RestForm("category") String category) {
        logger.info("导入BPMN文件: {}", file.getName());

        return Uni.createFrom().item(() -> {
            try {
                // 读取文件内容
                String bpmnXml = java.nio.file.Files.readString(file.toPath());

                // 创建流程定义DTO
                ProcessDefinitionDTO dto = new ProcessDefinitionDTO();
                dto.setProcessName(processName != null ? processName : "导入的流程");
                dto.setCategory(category);
                dto.setDesignerType(ProcessDefinition.DesignerType.BPMN);
                dto.setBpmnXml(bpmnXml);

                // 从BPMN XML中提取流程标识
                String processKey = extractProcessKeyFromBpmn(bpmnXml);
                dto.setProcessKey(processKey != null ? processKey : generateProcessKey(dto.getProcessName()));

                // 转换BPMN为通用格式（如果需要）
                // TODO: 实现BPMN到通用格式的转换
                dto.setProcessDefinitionJson("{}");

                return dto;
            } catch (Exception e) {
                throw new BusinessException("读取BPMN文件失败: " + e.getMessage());
            }
        })
        .flatMap(dto -> processDefinitionService.create(dto))
        .map(result -> ApiResponse.success(result, "BPMN导入成功"))
        .onFailure().recoverWithItem(throwable -> {
            logger.error("导入BPMN失败", throwable);
            return ApiResponse.error(throwable.getMessage());
        });
    }

    /**
     * 从BPMN XML中提取流程标识
     */
    private String extractProcessKeyFromBpmn(String bpmnXml) {
        try {
            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            org.w3c.dom.Document doc = builder.parse(new java.io.ByteArrayInputStream(bpmnXml.getBytes()));

            org.w3c.dom.NodeList processes = doc.getElementsByTagName("process");
            if (processes.getLength() > 0) {
                org.w3c.dom.Element process = (org.w3c.dom.Element) processes.item(0);
                return process.getAttribute("id");
            }
        } catch (Exception e) {
            logger.warn("提取流程标识失败", e);
        }
        return null;
    }

    /**
     * 生成流程标识
     */
    private String generateProcessKey(String processName) {
        return processName.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "_")
                .toLowerCase() + "_" + System.currentTimeMillis();
    }
}
