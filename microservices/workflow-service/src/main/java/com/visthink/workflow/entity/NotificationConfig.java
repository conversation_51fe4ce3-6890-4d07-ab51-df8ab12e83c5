package com.visthink.workflow.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 通知配置实体
 * 定义流程各个环节的通知规则
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "wf_notification_config", indexes = {
    @Index(name = "idx_notification_config_process_def", columnList = "process_definition_id"),
    @Index(name = "idx_notification_config_event", columnList = "event_type"),
    @Index(name = "idx_notification_config_method", columnList = "notification_method"),
    @Index(name = "idx_notification_config_enabled", columnList = "enabled")
})
@Comment("通知配置表")
public class NotificationConfig {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", length = 50)
    @Comment("租户ID")
    private String tenantId;

    /**
     * 流程定义ID
     */
    @Column(name = "process_definition_id", nullable = false)
    @Comment("流程定义ID")
    private Long processDefinitionId;

    /**
     * 节点标识（如果为空则对整个流程生效）
     */
    @Column(name = "node_key", length = 100)
    @Comment("节点标识")
    private String nodeKey;

    /**
     * 节点名称
     */
    @Column(name = "node_name", length = 200)
    @Comment("节点名称")
    private String nodeName;

    /**
     * 事件类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", length = 30, nullable = false)
    @Comment("事件类型")
    private EventType eventType;

    /**
     * 通知方式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_method", length = 20, nullable = false)
    @Comment("通知方式")
    private NotificationMethod notificationMethod;

    /**
     * 接收人类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "recipient_type", length = 20, nullable = false)
    @Comment("接收人类型")
    private RecipientType recipientType;

    /**
     * 接收人配置（用户ID、角色ID、表达式等）
     */
    @Column(name = "recipient_config", columnDefinition = "TEXT")
    @Comment("接收人配置")
    private String recipientConfig;

    /**
     * 消息标题模板
     */
    @Column(name = "title_template", length = 500)
    @Comment("消息标题模板")
    private String titleTemplate;

    /**
     * 消息内容模板
     */
    @Column(name = "content_template", columnDefinition = "TEXT")
    @Comment("消息内容模板")
    private String contentTemplate;

    /**
     * 延迟发送时间（分钟）
     */
    @Column(name = "delay_minutes")
    @Comment("延迟发送时间（分钟）")
    private Integer delayMinutes = 0;

    /**
     * 是否重复发送
     */
    @Column(name = "repeat_send")
    @Comment("是否重复发送")
    private Boolean repeatSend = false;

    /**
     * 重复间隔（分钟）
     */
    @Column(name = "repeat_interval")
    @Comment("重复间隔（分钟）")
    private Integer repeatInterval;

    /**
     * 最大重复次数
     */
    @Column(name = "max_repeat_count")
    @Comment("最大重复次数")
    private Integer maxRepeatCount;

    /**
     * 条件表达式（满足条件时才发送通知）
     */
    @Column(name = "condition_expression", columnDefinition = "TEXT")
    @Comment("条件表达式")
    private String conditionExpression;

    /**
     * 优先级（数字越大优先级越高）
     */
    @Column(name = "priority")
    @Comment("优先级")
    private Integer priority = 0;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    @Comment("是否启用")
    private Boolean enabled = true;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @Comment("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @Comment("更新时间")
    private LocalDateTime updatedTime;

    /**
     * 事件类型枚举
     */
    public enum EventType {
        PROCESS_START("流程启动"),
        PROCESS_END("流程结束"),
        PROCESS_CANCEL("流程取消"),
        PROCESS_TERMINATE("流程终止"),
        TASK_CREATE("任务创建"),
        TASK_ASSIGN("任务分配"),
        TASK_COMPLETE("任务完成"),
        TASK_REJECT("任务驳回"),
        TASK_TRANSFER("任务转办"),
        TASK_DELEGATE("任务委派"),
        TASK_TIMEOUT("任务超时"),
        TASK_REMINDER("任务提醒"),
        NODE_ENTER("节点进入"),
        NODE_EXIT("节点退出");

        private final String description;

        EventType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 通知方式枚举
     */
    public enum NotificationMethod {
        EMAIL("邮件"),
        SMS("短信"),
        SYSTEM("系统通知"),
        WECHAT("微信"),
        DINGTALK("钉钉"),
        WEBHOOK("Webhook");

        private final String description;

        NotificationMethod(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 接收人类型枚举
     */
    public enum RecipientType {
        INITIATOR("发起人"),
        ASSIGNEE("当前处理人"),
        CANDIDATE("候选人"),
        USER("指定用户"),
        ROLE("指定角色"),
        DEPARTMENT("指定部门"),
        EXPRESSION("表达式"),
        SUPERIOR("上级领导"),
        SUBORDINATE("下级员工");

        private final String description;

        RecipientType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public NotificationConfig() {
        this.createdTime = LocalDateTime.now();
    }

    public NotificationConfig(Long processDefinitionId, EventType eventType, 
                            NotificationMethod notificationMethod, RecipientType recipientType) {
        this();
        this.processDefinitionId = processDefinitionId;
        this.eventType = eventType;
        this.notificationMethod = notificationMethod;
        this.recipientType = recipientType;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public void setNodeKey(String nodeKey) {
        this.nodeKey = nodeKey;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public EventType getEventType() {
        return eventType;
    }

    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }

    public NotificationMethod getNotificationMethod() {
        return notificationMethod;
    }

    public void setNotificationMethod(NotificationMethod notificationMethod) {
        this.notificationMethod = notificationMethod;
    }

    public RecipientType getRecipientType() {
        return recipientType;
    }

    public void setRecipientType(RecipientType recipientType) {
        this.recipientType = recipientType;
    }

    public String getRecipientConfig() {
        return recipientConfig;
    }

    public void setRecipientConfig(String recipientConfig) {
        this.recipientConfig = recipientConfig;
    }

    public String getTitleTemplate() {
        return titleTemplate;
    }

    public void setTitleTemplate(String titleTemplate) {
        this.titleTemplate = titleTemplate;
    }

    public String getContentTemplate() {
        return contentTemplate;
    }

    public void setContentTemplate(String contentTemplate) {
        this.contentTemplate = contentTemplate;
    }

    public Integer getDelayMinutes() {
        return delayMinutes;
    }

    public void setDelayMinutes(Integer delayMinutes) {
        this.delayMinutes = delayMinutes;
    }

    public Boolean getRepeatSend() {
        return repeatSend;
    }

    public void setRepeatSend(Boolean repeatSend) {
        this.repeatSend = repeatSend;
    }

    public Integer getRepeatInterval() {
        return repeatInterval;
    }

    public void setRepeatInterval(Integer repeatInterval) {
        this.repeatInterval = repeatInterval;
    }

    public Integer getMaxRepeatCount() {
        return maxRepeatCount;
    }

    public void setMaxRepeatCount(Integer maxRepeatCount) {
        this.maxRepeatCount = maxRepeatCount;
    }

    public String getConditionExpression() {
        return conditionExpression;
    }

    public void setConditionExpression(String conditionExpression) {
        this.conditionExpression = conditionExpression;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "NotificationConfig{" +
                "id=" + id +
                ", processDefinitionId=" + processDefinitionId +
                ", eventType=" + eventType +
                ", notificationMethod=" + notificationMethod +
                ", recipientType=" + recipientType +
                ", enabled=" + enabled +
                '}';
    }
}
