package com.visthink.workflow.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.visthink.shared.entity.BaseEntity;
import io.quarkus.hibernate.reactive.panache.PanacheEntityBase;
import jakarta.persistence.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 流程定义实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "wf_process_definition", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"tenant_id", "process_key", "process_version"}))
@Comment("流程定义表")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class ProcessDefinition extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", nullable = false, length = 64)
    @Comment("租户ID")
    private String tenantId;

    /**
     * 流程标识
     */
    @Column(name = "process_key", nullable = false, length = 100)
    @Comment("流程标识")
    private String processKey;

    /**
     * 流程名称
     */
    @Column(name = "process_name", nullable = false, length = 200)
    @Comment("流程名称")
    private String processName;

    /**
     * 流程版本
     */
    @Column(name = "process_version", nullable = false)
    @Comment("流程版本")
    private Integer processVersion = 1;

    /**
     * 流程分类
     */
    @Column(name = "category", length = 100)
    @Comment("流程分类")
    private String category;

    /**
     * 流程描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    @Comment("流程描述")
    private String description;

    /**
     * 设计器类型：SIMPLE/BPMN
     */
    @Column(name = "designer_type", nullable = false, length = 20)
    @Comment("设计器类型：SIMPLE/BPMN")
    @Enumerated(EnumType.STRING)
    private DesignerType designerType = DesignerType.SIMPLE;

    /**
     * 流程定义JSON
     */
    @Column(name = "process_definition", nullable = false, columnDefinition = "TEXT")
    @Comment("流程定义JSON")
    private String processDefinitionJson;

    /**
     * BPMN XML定义
     */
    @Column(name = "bpmn_xml", columnDefinition = "TEXT")
    @Comment("BPMN XML定义")
    private String bpmnXml;

    /**
     * 表单定义JSON
     */
    @Column(name = "form_definition", columnDefinition = "TEXT")
    @Comment("表单定义JSON")
    private String formDefinition;

    /**
     * 状态：DRAFT/ACTIVE/SUSPENDED/DELETED
     */
    @Column(name = "status", nullable = false, length = 20)
    @Comment("状态：DRAFT/ACTIVE/SUSPENDED/DELETED")
    @Enumerated(EnumType.STRING)
    private ProcessStatus status = ProcessStatus.DRAFT;

    /**
     * 是否最新版本
     */
    @Column(name = "is_latest", nullable = false)
    @Comment("是否最新版本")
    private Boolean isLatest = true;

    /**
     * 创建人
     */
    @Column(name = "created_by", nullable = false)
    @Comment("创建人")
    private Long createdBy;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @Comment("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @Column(name = "updated_by")
    @Comment("更新人")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @Comment("更新时间")
    private LocalDateTime updatedTime;

    // 设计器类型枚举
    public enum DesignerType {
        SIMPLE("简单设计器"),
        BPMN("BPMN设计器");

        private final String description;

        DesignerType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 流程状态枚举
    public enum ProcessStatus {
        DRAFT("草稿"),
        ACTIVE("激活"),
        SUSPENDED("挂起"),
        DELETED("已删除");

        private final String description;

        ProcessStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public ProcessDefinition() {
        this.createdTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public Integer getProcessVersion() {
        return processVersion;
    }

    public void setProcessVersion(Integer processVersion) {
        this.processVersion = processVersion;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public DesignerType getDesignerType() {
        return designerType;
    }

    public void setDesignerType(DesignerType designerType) {
        this.designerType = designerType;
    }

    public String getProcessDefinitionJson() {
        return processDefinitionJson;
    }

    public void setProcessDefinitionJson(String processDefinitionJson) {
        this.processDefinitionJson = processDefinitionJson;
    }

    public String getBpmnXml() {
        return bpmnXml;
    }

    public void setBpmnXml(String bpmnXml) {
        this.bpmnXml = bpmnXml;
    }

    public String getFormDefinition() {
        return formDefinition;
    }

    public void setFormDefinition(String formDefinition) {
        this.formDefinition = formDefinition;
    }

    public ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessStatus status) {
        this.status = status;
    }

    public Boolean getIsLatest() {
        return isLatest;
    }

    public void setIsLatest(Boolean isLatest) {
        this.isLatest = isLatest;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "ProcessDefinition{" +
                "id=" + id +
                ", tenantId='" + tenantId + '\'' +
                ", processKey='" + processKey + '\'' +
                ", processName='" + processName + '\'' +
                ", processVersion=" + processVersion +
                ", category='" + category + '\'' +
                ", designerType=" + designerType +
                ", status=" + status +
                ", isLatest=" + isLatest +
                ", createdTime=" + createdTime +
                '}';
    }
}
