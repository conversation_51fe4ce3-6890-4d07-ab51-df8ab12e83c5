package com.visthink.workflow.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 任务历史记录实体
 * 记录任务的所有操作历史，包括审批、驳回、转办、委派等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "wf_task_history", indexes = {
    @Index(name = "idx_task_history_task_id", columnList = "task_instance_id"),
    @Index(name = "idx_task_history_process_instance", columnList = "process_instance_id"),
    @Index(name = "idx_task_history_operator", columnList = "operator_id"),
    @Index(name = "idx_task_history_action", columnList = "action"),
    @Index(name = "idx_task_history_time", columnList = "action_time")
})
@Comment("任务历史记录表")
public class TaskHistory {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", length = 50)
    @Comment("租户ID")
    private String tenantId;

    /**
     * 任务实例ID
     */
    @Column(name = "task_instance_id", nullable = false)
    @Comment("任务实例ID")
    private Long taskInstanceId;

    /**
     * 流程实例ID
     */
    @Column(name = "process_instance_id", nullable = false)
    @Comment("流程实例ID")
    private Long processInstanceId;

    /**
     * 流程定义ID
     */
    @Column(name = "process_definition_id", nullable = false)
    @Comment("流程定义ID")
    private Long processDefinitionId;

    /**
     * 节点标识
     */
    @Column(name = "node_key", length = 100, nullable = false)
    @Comment("节点标识")
    private String nodeKey;

    /**
     * 节点名称
     */
    @Column(name = "node_name", length = 200)
    @Comment("节点名称")
    private String nodeName;

    /**
     * 任务标识
     */
    @Column(name = "task_key", length = 100)
    @Comment("任务标识")
    private String taskKey;

    /**
     * 任务名称
     */
    @Column(name = "task_name", length = 200)
    @Comment("任务名称")
    private String taskName;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    @Comment("操作人ID")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name", length = 100)
    @Comment("操作人姓名")
    private String operatorName;

    /**
     * 操作类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "action", length = 50, nullable = false)
    @Comment("操作类型")
    private TaskInstance.TaskAction action;

    /**
     * 操作结果
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "result", length = 50)
    @Comment("操作结果")
    private ActionResult result;

    /**
     * 审批意见/操作说明
     */
    @Column(name = "comment", columnDefinition = "TEXT")
    @Comment("审批意见/操作说明")
    private String comment;

    /**
     * 表单数据JSON
     */
    @Column(name = "form_data", columnDefinition = "TEXT")
    @Comment("表单数据JSON")
    private String formData;

    /**
     * 任务变量JSON
     */
    @Column(name = "variables", columnDefinition = "TEXT")
    @Comment("任务变量JSON")
    private String variables;

    /**
     * 处理时长（毫秒）
     */
    @Column(name = "duration")
    @Comment("处理时长（毫秒）")
    private Long duration;

    /**
     * 目标用户ID（转办、委派时使用）
     */
    @Column(name = "target_user_id")
    @Comment("目标用户ID（转办、委派时使用）")
    private Long targetUserId;

    /**
     * 目标用户姓名
     */
    @Column(name = "target_user_name", length = 100)
    @Comment("目标用户姓名")
    private String targetUserName;

    /**
     * 目标节点标识（驳回时使用）
     */
    @Column(name = "target_node_key", length = 100)
    @Comment("目标节点标识（驳回时使用）")
    private String targetNodeKey;

    /**
     * 操作时间
     */
    @Column(name = "action_time", nullable = false)
    @Comment("操作时间")
    private LocalDateTime actionTime;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @Comment("创建时间")
    private LocalDateTime createdTime;

    /**
     * 操作结果枚举
     */
    public enum ActionResult {
        SUCCESS("成功"),
        FAILED("失败"),
        APPROVED("同意"),
        REJECTED("驳回"),
        TRANSFERRED("已转办"),
        DELEGATED("已委派"),
        CANCELLED("已撤销"),
        TERMINATED("已终止");

        private final String description;

        ActionResult(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public TaskHistory() {
        this.actionTime = LocalDateTime.now();
        this.createdTime = LocalDateTime.now();
    }

    public TaskHistory(Long taskInstanceId, Long processInstanceId, TaskInstance.TaskAction action, 
                      Long operatorId, String operatorName) {
        this();
        this.taskInstanceId = taskInstanceId;
        this.processInstanceId = processInstanceId;
        this.action = action;
        this.operatorId = operatorId;
        this.operatorName = operatorName;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getTaskInstanceId() {
        return taskInstanceId;
    }

    public void setTaskInstanceId(Long taskInstanceId) {
        this.taskInstanceId = taskInstanceId;
    }

    public Long getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(Long processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public Long getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public void setNodeKey(String nodeKey) {
        this.nodeKey = nodeKey;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public TaskInstance.TaskAction getAction() {
        return action;
    }

    public void setAction(TaskInstance.TaskAction action) {
        this.action = action;
    }

    public ActionResult getResult() {
        return result;
    }

    public void setResult(ActionResult result) {
        this.result = result;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getFormData() {
        return formData;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Long getTargetUserId() {
        return targetUserId;
    }

    public void setTargetUserId(Long targetUserId) {
        this.targetUserId = targetUserId;
    }

    public String getTargetUserName() {
        return targetUserName;
    }

    public void setTargetUserName(String targetUserName) {
        this.targetUserName = targetUserName;
    }

    public String getTargetNodeKey() {
        return targetNodeKey;
    }

    public void setTargetNodeKey(String targetNodeKey) {
        this.targetNodeKey = targetNodeKey;
    }

    public LocalDateTime getActionTime() {
        return actionTime;
    }

    public void setActionTime(LocalDateTime actionTime) {
        this.actionTime = actionTime;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
        if (actionTime == null) {
            actionTime = LocalDateTime.now();
        }
    }

    @Override
    public String toString() {
        return "TaskHistory{" +
                "id=" + id +
                ", taskInstanceId=" + taskInstanceId +
                ", action=" + action +
                ", operatorName='" + operatorName + '\'' +
                ", result=" + result +
                ", actionTime=" + actionTime +
                '}';
    }
}
