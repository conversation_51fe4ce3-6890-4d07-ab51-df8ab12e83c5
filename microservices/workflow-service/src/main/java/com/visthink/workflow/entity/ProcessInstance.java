package com.visthink.workflow.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.visthink.shared.entity.BaseEntity;
import jakarta.persistence.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 流程实例实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "wf_process_instance")
@Comment("流程实例表")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class ProcessInstance extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", nullable = false, length = 64)
    @Comment("租户ID")
    private String tenantId;

    /**
     * 流程定义ID
     */
    @Column(name = "process_definition_id", nullable = false)
    @Comment("流程定义ID")
    private Long processDefinitionId;

    /**
     * 流程标识
     */
    @Column(name = "process_key", nullable = false, length = 100)
    @Comment("流程标识")
    private String processKey;

    /**
     * 流程名称
     */
    @Column(name = "process_name", nullable = false, length = 200)
    @Comment("流程名称")
    private String processName;

    /**
     * 业务键
     */
    @Column(name = "business_key", length = 200)
    @Comment("业务键")
    private String businessKey;

    /**
     * 流程标题
     */
    @Column(name = "title", nullable = false, length = 500)
    @Comment("流程标题")
    private String title;

    /**
     * 发起人ID
     */
    @Column(name = "initiator_id", nullable = false)
    @Comment("发起人ID")
    private Long initiatorId;

    /**
     * 发起人姓名
     */
    @Column(name = "initiator_name", nullable = false, length = 100)
    @Comment("发起人姓名")
    private String initiatorName;

    /**
     * 当前节点标识列表
     */
    @Column(name = "current_node_keys", columnDefinition = "TEXT")
    @Comment("当前节点标识列表")
    private String currentNodeKeys;

    /**
     * 状态：RUNNING/COMPLETED/TERMINATED/SUSPENDED
     */
    @Column(name = "status", nullable = false, length = 20)
    @Comment("状态：RUNNING/COMPLETED/TERMINATED/SUSPENDED")
    @Enumerated(EnumType.STRING)
    private ProcessInstanceStatus status = ProcessInstanceStatus.RUNNING;

    /**
     * 优先级
     */
    @Column(name = "priority", nullable = false)
    @Comment("优先级")
    private Integer priority = 0;

    /**
     * 表单数据JSON
     */
    @Column(name = "form_data", columnDefinition = "TEXT")
    @Comment("表单数据JSON")
    private String formData;

    /**
     * 流程变量JSON
     */
    @Column(name = "variables", columnDefinition = "TEXT")
    @Comment("流程变量JSON")
    private String variables;

    /**
     * 开始时间
     */
    @Column(name = "start_time", nullable = false)
    @Comment("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    @Comment("结束时间")
    private LocalDateTime endTime;

    /**
     * 持续时间(毫秒)
     */
    @Column(name = "duration")
    @Comment("持续时间(毫秒)")
    private Long duration;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @Comment("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @Comment("更新时间")
    private LocalDateTime updatedTime;

    // 流程实例状态枚举
    public enum ProcessInstanceStatus {
        RUNNING("运行中"),
        COMPLETED("已完成"),
        TERMINATED("已终止"),
        SUSPENDED("已挂起");

        private final String description;

        ProcessInstanceStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public ProcessInstance() {
        this.startTime = LocalDateTime.now();
        this.createdTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getInitiatorId() {
        return initiatorId;
    }

    public void setInitiatorId(Long initiatorId) {
        this.initiatorId = initiatorId;
    }

    public String getInitiatorName() {
        return initiatorName;
    }

    public void setInitiatorName(String initiatorName) {
        this.initiatorName = initiatorName;
    }

    public String getCurrentNodeKeys() {
        return currentNodeKeys;
    }

    public void setCurrentNodeKeys(String currentNodeKeys) {
        this.currentNodeKeys = currentNodeKeys;
    }

    public ProcessInstanceStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessInstanceStatus status) {
        this.status = status;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getFormData() {
        return formData;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
        if (startTime == null) {
            startTime = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
        
        // 如果流程结束，计算持续时间
        if ((status == ProcessInstanceStatus.COMPLETED || status == ProcessInstanceStatus.TERMINATED) 
            && endTime != null && startTime != null) {
            duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 完成流程实例
     */
    public void complete() {
        this.status = ProcessInstanceStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        if (startTime != null) {
            this.duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 终止流程实例
     */
    public void terminate() {
        this.status = ProcessInstanceStatus.TERMINATED;
        this.endTime = LocalDateTime.now();
        if (startTime != null) {
            this.duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 挂起流程实例
     */
    public void suspend() {
        this.status = ProcessInstanceStatus.SUSPENDED;
    }

    /**
     * 激活流程实例
     */
    public void activate() {
        this.status = ProcessInstanceStatus.RUNNING;
    }

    @Override
    public String toString() {
        return "ProcessInstance{" +
                "id=" + id +
                ", tenantId='" + tenantId + '\'' +
                ", processKey='" + processKey + '\'' +
                ", title='" + title + '\'' +
                ", initiatorName='" + initiatorName + '\'' +
                ", status=" + status +
                ", startTime=" + startTime +
                '}';
    }
}
