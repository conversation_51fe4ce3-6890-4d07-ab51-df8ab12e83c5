package com.visthink.workflow.service;

import com.visthink.workflow.dto.TaskInstanceDTO;
import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.entity.TaskHistory;
import com.visthink.workflow.repository.TaskInstanceRepository;
import com.visthink.workflow.repository.TaskHistoryRepository;
import com.visthink.workflow.exception.BusinessException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务服务
 * 提供任务的CRUD操作和业务逻辑处理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class TaskService {

    private static final Logger logger = LoggerFactory.getLogger(TaskService.class);

    @Inject
    TaskInstanceRepository taskInstanceRepository;

    @Inject
    TaskControlService taskControlService;

    @Inject
    TaskAssignmentService taskAssignmentService;

    @Inject
    TaskHistoryRepository taskHistoryRepository;

    /**
     * 创建任务实例
     */
    @Transactional
    public Uni<TaskInstanceDTO> createTask(TaskInstanceDTO dto) {
        logger.debug("创建任务实例: {}", dto.getTaskName());
        
        TaskInstance entity = convertToEntity(dto);
        entity.setCreatedTime(LocalDateTime.now());
        entity.setStatus(TaskInstance.TaskStatus.CREATED);
        
        return taskInstanceRepository.persist(entity)
                .map(this::convertToDTO);
    }

    /**
     * 根据ID查询任务实例
     */
    public Uni<TaskInstanceDTO> findById(Long id) {
        logger.debug("查询任务实例: {}", id);
        
        return taskInstanceRepository.findById(id)
                .map(entity -> {
                    if (entity == null) {
                        throw new BusinessException("任务实例不存在: " + id);
                    }
                    return convertToDTO(entity);
                });
    }

    /**
     * 更新任务实例
     */
    @Transactional
    public Uni<TaskInstanceDTO> updateTask(Long id, TaskInstanceDTO dto) {
        logger.debug("更新任务实例: {}", id);
        
        return taskInstanceRepository.findById(id)
                .flatMap(entity -> {
                    if (entity == null) {
                        return Uni.createFrom().failure(new BusinessException("任务实例不存在: " + id));
                    }
                    
                    updateEntityFromDTO(entity, dto);
                    entity.setUpdatedTime(LocalDateTime.now());
                    
                    return taskInstanceRepository.persist(entity);
                })
                .map(this::convertToDTO);
    }

    /**
     * 删除任务实例
     */
    @Transactional
    public Uni<Boolean> deleteTask(Long id) {
        logger.debug("删除任务实例: {}", id);
        
        return taskInstanceRepository.deleteById(id)
                .map(deleted -> {
                    if (!deleted) {
                        throw new BusinessException("任务实例不存在: " + id);
                    }
                    return true;
                });
    }

    /**
     * 分页查询任务实例
     */
    public Uni<List<TaskInstanceDTO>> findTasks(int page, int size, Map<String, Object> filters) {
        logger.debug("分页查询任务实例: page={}, size={}", page, size);
        
        // 构建查询条件
        StringBuilder query = new StringBuilder("1=1");
        
        if (filters.containsKey("assigneeId")) {
            query.append(" AND assigneeId = :assigneeId");
        }
        if (filters.containsKey("status")) {
            query.append(" AND status = :status");
        }
        if (filters.containsKey("processInstanceId")) {
            query.append(" AND processInstanceId = :processInstanceId");
        }
        if (filters.containsKey("tenantId")) {
            query.append(" AND tenantId = :tenantId");
        }
        
        query.append(" ORDER BY createdTime DESC");
        
        return taskInstanceRepository.find(query.toString(), filters)
                .page(page, size)
                .list()
                .map(entities -> entities.stream()
                        .map(this::convertToDTO)
                        .collect(Collectors.toList()));
    }

    /**
     * 获取用户的待办任务
     */
    public Uni<List<TaskInstanceDTO>> getTodoTasks(Long userId, String tenantId) {
        logger.debug("获取用户待办任务: userId={}", userId);
        
        return taskInstanceRepository.find(
                "(assigneeId = ?1 OR candidateUsers LIKE ?2) AND status IN (?3, ?4) AND tenantId = ?5 ORDER BY createdTime DESC",
                userId, "%" + userId + "%", 
                TaskInstance.TaskStatus.CREATED, TaskInstance.TaskStatus.CLAIMED, tenantId)
                .list()
                .map(entities -> entities.stream()
                        .map(this::convertToDTO)
                        .collect(Collectors.toList()));
    }

    /**
     * 获取用户的已办任务
     */
    public Uni<List<TaskInstanceDTO>> getDoneTasks(Long userId, String tenantId) {
        logger.debug("获取用户已办任务: userId={}", userId);
        
        return taskInstanceRepository.find(
                "assigneeId = ?1 AND status IN (?2, ?3, ?4, ?5) AND tenantId = ?6 ORDER BY completeTime DESC",
                userId, 
                TaskInstance.TaskStatus.COMPLETED, TaskInstance.TaskStatus.REJECTED,
                TaskInstance.TaskStatus.TRANSFERRED, TaskInstance.TaskStatus.DELEGATED, 
                tenantId)
                .list()
                .map(entities -> entities.stream()
                        .map(this::convertToDTO)
                        .collect(Collectors.toList()));
    }

    /**
     * 签收任务
     */
    @Transactional
    public Uni<TaskControlService.TaskControlResult> claimTask(Long taskId, Long userId, String userName) {
        logger.info("签收任务: taskId={}, userId={}", taskId, userId);
        
        return taskControlService.claimTask(taskId, userId, userName);
    }

    /**
     * 审批任务
     */
    @Transactional
    public Uni<TaskControlService.TaskControlResult> approveTask(Long taskId, Long userId, String userName, 
                                                                String comment, Map<String, Object> formData, 
                                                                Map<String, Object> variables) {
        logger.info("审批任务: taskId={}, userId={}", taskId, userId);
        
        return taskControlService.approveTask(taskId, userId, userName, comment, formData, variables);
    }

    /**
     * 驳回任务
     */
    @Transactional
    public Uni<TaskControlService.TaskControlResult> rejectTask(Long taskId, Long userId, String userName, 
                                                               String reason, String targetNodeKey) {
        logger.info("驳回任务: taskId={}, userId={}", taskId, userId);
        
        return taskControlService.rejectTask(taskId, userId, userName, reason, targetNodeKey);
    }

    /**
     * 转办任务
     */
    @Transactional
    public Uni<TaskControlService.TaskControlResult> transferTask(Long taskId, Long userId, String userName, 
                                                                 Long targetUserId, String reason) {
        logger.info("转办任务: taskId={}, userId={}, targetUserId={}", taskId, userId, targetUserId);
        
        return taskControlService.transferTask(taskId, userId, userName, targetUserId, reason);
    }

    /**
     * 委派任务
     */
    @Transactional
    public Uni<TaskControlService.TaskControlResult> delegateTask(Long taskId, Long userId, String userName, 
                                                                 Long targetUserId, String reason) {
        logger.info("委派任务: taskId={}, userId={}, targetUserId={}", taskId, userId, targetUserId);
        
        return taskControlService.delegateTask(taskId, userId, userName, targetUserId, reason);
    }

    /**
     * 加签
     */
    @Transactional
    public Uni<TaskControlService.TaskControlResult> addSign(Long taskId, Long userId, String userName, 
                                                            List<Long> userIds, String reason) {
        logger.info("加签: taskId={}, userId={}, userIds={}", taskId, userId, userIds);
        
        return taskControlService.addSign(taskId, userId, userName, userIds, reason);
    }

    /**
     * 减签
     */
    @Transactional
    public Uni<TaskControlService.TaskControlResult> removeSign(Long taskId, Long userId, String userName, 
                                                               List<Long> userIds, String reason) {
        logger.info("减签: taskId={}, userId={}, userIds={}", taskId, userId, userIds);
        
        return taskControlService.removeSign(taskId, userId, userName, userIds, reason);
    }

    /**
     * 获取任务历史记录
     */
    public Uni<List<TaskHistory>> getTaskHistory(Long taskId) {
        logger.debug("获取任务历史记录: taskId={}", taskId);
        
        return taskHistoryRepository.findByTaskInstanceId(taskId);
    }

    /**
     * 统计任务数量
     */
    public Uni<Long> countTasks(Map<String, Object> filters) {
        logger.debug("统计任务数量");
        
        StringBuilder query = new StringBuilder("1=1");
        
        if (filters.containsKey("assigneeId")) {
            query.append(" AND assigneeId = :assigneeId");
        }
        if (filters.containsKey("status")) {
            query.append(" AND status = :status");
        }
        if (filters.containsKey("processInstanceId")) {
            query.append(" AND processInstanceId = :processInstanceId");
        }
        if (filters.containsKey("tenantId")) {
            query.append(" AND tenantId = :tenantId");
        }
        
        return taskInstanceRepository.count(query.toString(), filters);
    }

    // 私有辅助方法
    private TaskInstance convertToEntity(TaskInstanceDTO dto) {
        TaskInstance entity = new TaskInstance();
        updateEntityFromDTO(entity, dto);
        return entity;
    }

    private void updateEntityFromDTO(TaskInstance entity, TaskInstanceDTO dto) {
        entity.setTenantId(dto.getTenantId());
        entity.setProcessInstanceId(dto.getProcessInstanceId());
        entity.setProcessDefinitionId(dto.getProcessDefinitionId());
        entity.setNodeKey(dto.getNodeKey());
        entity.setNodeName(dto.getNodeName());
        entity.setTaskKey(dto.getTaskKey());
        entity.setTaskName(dto.getTaskName());
        entity.setAssigneeId(dto.getAssigneeId());
        entity.setAssigneeName(dto.getAssigneeName());
        entity.setCandidateUsers(dto.getCandidateUsers());
        entity.setCandidateGroups(dto.getCandidateGroups());
        entity.setApprovalType(dto.getApprovalType());
        entity.setPriority(dto.getPriority());
        entity.setDueDate(dto.getDueDate());
        entity.setDescription(dto.getDescription());
        entity.setFormKey(dto.getFormKey());
        entity.setFormData(dto.getFormData());
        entity.setVariables(dto.getVariables());
    }

    private TaskInstanceDTO convertToDTO(TaskInstance entity) {
        TaskInstanceDTO dto = new TaskInstanceDTO();
        dto.setId(entity.getId());
        dto.setTenantId(entity.getTenantId());
        dto.setProcessInstanceId(entity.getProcessInstanceId());
        dto.setProcessDefinitionId(entity.getProcessDefinitionId());
        dto.setNodeKey(entity.getNodeKey());
        dto.setNodeName(entity.getNodeName());
        dto.setTaskKey(entity.getTaskKey());
        dto.setTaskName(entity.getTaskName());
        dto.setAssigneeId(entity.getAssigneeId());
        dto.setAssigneeName(entity.getAssigneeName());
        dto.setCandidateUsers(entity.getCandidateUsers());
        dto.setCandidateGroups(entity.getCandidateGroups());
        dto.setApprovalType(entity.getApprovalType());
        dto.setStatus(entity.getStatus());
        dto.setPriority(entity.getPriority());
        dto.setDueDate(entity.getDueDate());
        dto.setDescription(entity.getDescription());
        dto.setFormKey(entity.getFormKey());
        dto.setFormData(entity.getFormData());
        dto.setVariables(entity.getVariables());
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setClaimTime(entity.getClaimTime());
        dto.setCompleteTime(entity.getCompleteTime());
        dto.setUpdatedTime(entity.getUpdatedTime());
        
        // 新增字段
        dto.setOriginalAssigneeId(entity.getOriginalAssigneeId());
        dto.setOriginalAssigneeName(entity.getOriginalAssigneeName());
        dto.setDelegatorId(entity.getDelegatorId());
        dto.setDelegatorName(entity.getDelegatorName());
        dto.setApprovalCount(entity.getApprovalCount());
        dto.setRequiredCount(entity.getRequiredCount());
        dto.setRejectReason(entity.getRejectReason());
        dto.setTransferReason(entity.getTransferReason());
        dto.setDelegateReason(entity.getDelegateReason());
        dto.setApprovalSequence(entity.getApprovalSequence());
        dto.setCurrentSequence(entity.getCurrentSequence());
        dto.setTimeoutAction(entity.getTimeoutAction());
        dto.setAutoClaim(entity.getAutoClaim());
        
        return dto;
    }
}
