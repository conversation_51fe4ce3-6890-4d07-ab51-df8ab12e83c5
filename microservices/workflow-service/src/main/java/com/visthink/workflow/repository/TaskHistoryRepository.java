package com.visthink.workflow.repository;

import com.visthink.workflow.entity.TaskHistory;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务历史记录Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class TaskHistoryRepository implements PanacheRepository<TaskHistory> {

    /**
     * 根据任务实例ID查询历史记录
     */
    public Uni<List<TaskHistory>> findByTaskInstanceId(Long taskInstanceId) {
        return find("taskInstanceId = ?1 order by actionTime desc", taskInstanceId).list();
    }

    /**
     * 根据流程实例ID查询历史记录
     */
    public Uni<List<TaskHistory>> findByProcessInstanceId(Long processInstanceId) {
        return find("processInstanceId = ?1 order by actionTime desc", processInstanceId).list();
    }

    /**
     * 根据操作人ID查询历史记录
     */
    public Uni<List<TaskHistory>> findByOperatorId(Long operatorId) {
        return find("operatorId = ?1 order by actionTime desc", operatorId).list();
    }

    /**
     * 根据操作类型查询历史记录
     */
    public Uni<List<TaskHistory>> findByAction(TaskHistory.ActionResult action) {
        return find("action = ?1 order by actionTime desc", action).list();
    }

    /**
     * 根据时间范围查询历史记录
     */
    public Uni<List<TaskHistory>> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return find("actionTime >= ?1 and actionTime <= ?2 order by actionTime desc", 
                   startTime, endTime).list();
    }

    /**
     * 根据租户ID查询历史记录
     */
    public Uni<List<TaskHistory>> findByTenantId(String tenantId) {
        return find("tenantId = ?1 order by actionTime desc", tenantId).list();
    }

    /**
     * 分页查询任务历史记录
     */
    public Uni<List<TaskHistory>> findByTaskInstanceIdWithPaging(Long taskInstanceId, int page, int size) {
        return find("taskInstanceId = ?1 order by actionTime desc", taskInstanceId)
                .page(page, size)
                .list();
    }

    /**
     * 统计任务历史记录数量
     */
    public Uni<Long> countByTaskInstanceId(Long taskInstanceId) {
        return count("taskInstanceId = ?1", taskInstanceId);
    }

    /**
     * 统计流程实例历史记录数量
     */
    public Uni<Long> countByProcessInstanceId(Long processInstanceId) {
        return count("processInstanceId = ?1", processInstanceId);
    }

    /**
     * 删除指定时间之前的历史记录
     */
    public Uni<Long> deleteOldRecords(LocalDateTime beforeTime) {
        return delete("actionTime < ?1", beforeTime);
    }

    /**
     * 根据任务实例ID和操作类型查询最新记录
     */
    public Uni<TaskHistory> findLatestByTaskInstanceIdAndAction(Long taskInstanceId, 
                                                               TaskHistory.ActionResult action) {
        return find("taskInstanceId = ?1 and action = ?2 order by actionTime desc", 
                   taskInstanceId, action)
                .firstResult();
    }

    /**
     * 查询用户的审批统计
     */
    public Uni<List<Object[]>> getApprovalStatistics(Long operatorId, LocalDateTime startTime, LocalDateTime endTime) {
        return getEntityManager()
                .createQuery("SELECT h.result, COUNT(h) FROM TaskHistory h " +
                           "WHERE h.operatorId = :operatorId " +
                           "AND h.actionTime >= :startTime " +
                           "AND h.actionTime <= :endTime " +
                           "GROUP BY h.result", Object[].class)
                .setParameter("operatorId", operatorId)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .getResultList();
    }

    /**
     * 查询流程的处理时长统计
     */
    public Uni<List<Object[]>> getProcessDurationStatistics(Long processDefinitionId, 
                                                           LocalDateTime startTime, 
                                                           LocalDateTime endTime) {
        return getEntityManager()
                .createQuery("SELECT h.nodeKey, AVG(h.duration), MIN(h.duration), MAX(h.duration) " +
                           "FROM TaskHistory h " +
                           "WHERE h.processDefinitionId = :processDefinitionId " +
                           "AND h.actionTime >= :startTime " +
                           "AND h.actionTime <= :endTime " +
                           "AND h.duration IS NOT NULL " +
                           "GROUP BY h.nodeKey", Object[].class)
                .setParameter("processDefinitionId", processDefinitionId)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .getResultList();
    }
}
