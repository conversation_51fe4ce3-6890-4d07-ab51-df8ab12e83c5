package com.visthink.workflow.resource;

import com.visthink.workflow.dto.TaskInstanceDTO;
import com.visthink.workflow.entity.TaskHistory;
import com.visthink.workflow.service.TaskService;
import com.visthink.workflow.service.TaskControlService;
import com.visthink.workflow.common.ApiResponse;
import com.visthink.workflow.exception.BusinessException;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务管理REST接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Path("/api/v1/tasks")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "任务管理", description = "任务实例的CRUD操作和审批控制")
public class TaskResource {

    private static final Logger logger = LoggerFactory.getLogger(TaskResource.class);

    @Inject
    TaskService taskService;

    /**
     * 创建任务实例
     */
    @POST
    @Operation(summary = "创建任务实例", description = "创建新的任务实例")
    public Uni<ApiResponse<TaskInstanceDTO>> createTask(TaskInstanceDTO dto) {
        logger.info("创建任务实例: {}", dto.getTaskName());
        
        return taskService.createTask(dto)
                .map(result -> ApiResponse.success(result, "任务创建成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("创建任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 根据ID查询任务实例
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询任务实例", description = "根据ID查询任务实例详情")
    public Uni<ApiResponse<TaskInstanceDTO>> getTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long id) {
        logger.info("查询任务实例: {}", id);
        
        return taskService.findById(id)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("查询任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 更新任务实例
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新任务实例", description = "更新指定的任务实例")
    public Uni<ApiResponse<TaskInstanceDTO>> updateTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long id,
            TaskInstanceDTO dto) {
        logger.info("更新任务实例: {}", id);
        
        return taskService.updateTask(id, dto)
                .map(result -> ApiResponse.success(result, "任务更新成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("更新任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 删除任务实例
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除任务实例", description = "删除指定的任务实例")
    public Uni<ApiResponse<Boolean>> deleteTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long id) {
        logger.info("删除任务实例: {}", id);
        
        return taskService.deleteTask(id)
                .map(result -> ApiResponse.success(result, "任务删除成功"))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("删除任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 分页查询任务实例
     */
    @GET
    @Operation(summary = "分页查询任务", description = "分页查询任务实例列表")
    public Uni<ApiResponse<List<TaskInstanceDTO>>> getTasks(
            @Parameter(description = "页码", required = false) @QueryParam("page") @DefaultValue("0") int page,
            @Parameter(description = "页大小", required = false) @QueryParam("size") @DefaultValue("20") int size,
            @Parameter(description = "处理人ID", required = false) @QueryParam("assigneeId") Long assigneeId,
            @Parameter(description = "任务状态", required = false) @QueryParam("status") String status,
            @Parameter(description = "流程实例ID", required = false) @QueryParam("processInstanceId") Long processInstanceId,
            @Parameter(description = "租户ID", required = false) @QueryParam("tenantId") String tenantId) {
        logger.info("分页查询任务: page={}, size={}", page, size);
        
        Map<String, Object> filters = new HashMap<>();
        if (assigneeId != null) filters.put("assigneeId", assigneeId);
        if (status != null) filters.put("status", status);
        if (processInstanceId != null) filters.put("processInstanceId", processInstanceId);
        if (tenantId != null) filters.put("tenantId", tenantId);
        
        return taskService.findTasks(page, size, filters)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("查询任务列表失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 获取用户待办任务
     */
    @GET
    @Path("/todo")
    @Operation(summary = "获取待办任务", description = "获取指定用户的待办任务列表")
    public Uni<ApiResponse<List<TaskInstanceDTO>>> getTodoTasks(
            @Parameter(description = "用户ID", required = true) @QueryParam("userId") Long userId,
            @Parameter(description = "租户ID", required = false) @QueryParam("tenantId") String tenantId) {
        logger.info("获取用户待办任务: userId={}", userId);
        
        if (userId == null) {
            return Uni.createFrom().item(ApiResponse.error("用户ID不能为空"));
        }
        
        return taskService.getTodoTasks(userId, tenantId)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("获取待办任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 获取用户已办任务
     */
    @GET
    @Path("/done")
    @Operation(summary = "获取已办任务", description = "获取指定用户的已办任务列表")
    public Uni<ApiResponse<List<TaskInstanceDTO>>> getDoneTasks(
            @Parameter(description = "用户ID", required = true) @QueryParam("userId") Long userId,
            @Parameter(description = "租户ID", required = false) @QueryParam("tenantId") String tenantId) {
        logger.info("获取用户已办任务: userId={}", userId);
        
        if (userId == null) {
            return Uni.createFrom().item(ApiResponse.error("用户ID不能为空"));
        }
        
        return taskService.getDoneTasks(userId, tenantId)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("获取已办任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 签收任务
     */
    @POST
    @Path("/{id}/claim")
    @Operation(summary = "签收任务", description = "用户签收指定的任务")
    public Uni<ApiResponse<TaskControlService.TaskControlResult>> claimTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId,
            @Parameter(description = "用户ID", required = true) @QueryParam("userId") Long userId,
            @Parameter(description = "用户姓名", required = true) @QueryParam("userName") String userName) {
        logger.info("签收任务: taskId={}, userId={}", taskId, userId);
        
        if (userId == null || userName == null) {
            return Uni.createFrom().item(ApiResponse.error("用户信息不能为空"));
        }
        
        return taskService.claimTask(taskId, userId, userName)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("签收任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 审批任务（同意）
     */
    @POST
    @Path("/{id}/approve")
    @Operation(summary = "审批任务", description = "用户审批（同意）指定的任务")
    public Uni<ApiResponse<TaskControlService.TaskControlResult>> approveTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId,
            ApprovalRequest request) {
        logger.info("审批任务: taskId={}, userId={}", taskId, request.getUserId());
        
        if (request.getUserId() == null || request.getUserName() == null) {
            return Uni.createFrom().item(ApiResponse.error("用户信息不能为空"));
        }
        
        return taskService.approveTask(taskId, request.getUserId(), request.getUserName(), 
                                     request.getComment(), request.getFormData(), request.getVariables())
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("审批任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 驳回任务
     */
    @POST
    @Path("/{id}/reject")
    @Operation(summary = "驳回任务", description = "用户驳回指定的任务")
    public Uni<ApiResponse<TaskControlService.TaskControlResult>> rejectTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId,
            RejectRequest request) {
        logger.info("驳回任务: taskId={}, userId={}", taskId, request.getUserId());
        
        if (request.getUserId() == null || request.getUserName() == null) {
            return Uni.createFrom().item(ApiResponse.error("用户信息不能为空"));
        }
        
        return taskService.rejectTask(taskId, request.getUserId(), request.getUserName(), 
                                    request.getReason(), request.getTargetNodeKey())
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("驳回任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 转办任务
     */
    @POST
    @Path("/{id}/transfer")
    @Operation(summary = "转办任务", description = "将任务转办给其他用户")
    public Uni<ApiResponse<TaskControlService.TaskControlResult>> transferTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId,
            TransferRequest request) {
        logger.info("转办任务: taskId={}, userId={}, targetUserId={}", 
                   taskId, request.getUserId(), request.getTargetUserId());
        
        if (request.getUserId() == null || request.getTargetUserId() == null) {
            return Uni.createFrom().item(ApiResponse.error("用户信息不能为空"));
        }
        
        return taskService.transferTask(taskId, request.getUserId(), request.getUserName(), 
                                      request.getTargetUserId(), request.getReason())
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("转办任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 委派任务
     */
    @POST
    @Path("/{id}/delegate")
    @Operation(summary = "委派任务", description = "将任务委派给其他用户")
    public Uni<ApiResponse<TaskControlService.TaskControlResult>> delegateTask(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId,
            DelegateRequest request) {
        logger.info("委派任务: taskId={}, userId={}, targetUserId={}", 
                   taskId, request.getUserId(), request.getTargetUserId());
        
        if (request.getUserId() == null || request.getTargetUserId() == null) {
            return Uni.createFrom().item(ApiResponse.error("用户信息不能为空"));
        }
        
        return taskService.delegateTask(taskId, request.getUserId(), request.getUserName(), 
                                      request.getTargetUserId(), request.getReason())
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("委派任务失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 加签
     */
    @POST
    @Path("/{id}/add-sign")
    @Operation(summary = "加签", description = "为任务添加审批人")
    public Uni<ApiResponse<TaskControlService.TaskControlResult>> addSign(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId,
            AddSignRequest request) {
        logger.info("加签: taskId={}, userId={}, userIds={}", 
                   taskId, request.getUserId(), request.getUserIds());
        
        if (request.getUserId() == null || request.getUserIds() == null || request.getUserIds().isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("用户信息不能为空"));
        }
        
        return taskService.addSign(taskId, request.getUserId(), request.getUserName(), 
                                 request.getUserIds(), request.getReason())
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("加签失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 减签
     */
    @POST
    @Path("/{id}/remove-sign")
    @Operation(summary = "减签", description = "为任务移除审批人")
    public Uni<ApiResponse<TaskControlService.TaskControlResult>> removeSign(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId,
            RemoveSignRequest request) {
        logger.info("减签: taskId={}, userId={}, userIds={}", 
                   taskId, request.getUserId(), request.getUserIds());
        
        if (request.getUserId() == null || request.getUserIds() == null || request.getUserIds().isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("用户信息不能为空"));
        }
        
        return taskService.removeSign(taskId, request.getUserId(), request.getUserName(), 
                                    request.getUserIds(), request.getReason())
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("减签失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    /**
     * 获取任务历史记录
     */
    @GET
    @Path("/{id}/history")
    @Operation(summary = "获取任务历史", description = "获取指定任务的历史记录")
    public Uni<ApiResponse<List<TaskHistory>>> getTaskHistory(
            @Parameter(description = "任务ID", required = true) @PathParam("id") Long taskId) {
        logger.info("获取任务历史记录: taskId={}", taskId);
        
        return taskService.getTaskHistory(taskId)
                .map(result -> ApiResponse.success(result))
                .onFailure().recoverWithItem(throwable -> {
                    logger.error("获取任务历史失败", throwable);
                    return ApiResponse.error(throwable.getMessage());
                });
    }

    // 请求对象定义
    public static class ApprovalRequest {
        private Long userId;
        private String userName;
        private String comment;
        private Map<String, Object> formData;
        private Map<String, Object> variables;

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        public Map<String, Object> getFormData() { return formData; }
        public void setFormData(Map<String, Object> formData) { this.formData = formData; }
        public Map<String, Object> getVariables() { return variables; }
        public void setVariables(Map<String, Object> variables) { this.variables = variables; }
    }

    public static class RejectRequest {
        private Long userId;
        private String userName;
        private String reason;
        private String targetNodeKey;

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        public String getTargetNodeKey() { return targetNodeKey; }
        public void setTargetNodeKey(String targetNodeKey) { this.targetNodeKey = targetNodeKey; }
    }

    public static class TransferRequest {
        private Long userId;
        private String userName;
        private Long targetUserId;
        private String reason;

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public Long getTargetUserId() { return targetUserId; }
        public void setTargetUserId(Long targetUserId) { this.targetUserId = targetUserId; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class DelegateRequest {
        private Long userId;
        private String userName;
        private Long targetUserId;
        private String reason;

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public Long getTargetUserId() { return targetUserId; }
        public void setTargetUserId(Long targetUserId) { this.targetUserId = targetUserId; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class AddSignRequest {
        private Long userId;
        private String userName;
        private List<Long> userIds;
        private String reason;

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public List<Long> getUserIds() { return userIds; }
        public void setUserIds(List<Long> userIds) { this.userIds = userIds; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class RemoveSignRequest {
        private Long userId;
        private String userName;
        private List<Long> userIds;
        private String reason;

        // Getter和Setter方法
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public List<Long> getUserIds() { return userIds; }
        public void setUserIds(List<Long> userIds) { this.userIds = userIds; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
