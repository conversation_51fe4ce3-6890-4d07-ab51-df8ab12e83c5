package com.visthink.workflow.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.visthink.shared.entity.BaseEntity;
import jakarta.persistence.*;
import org.hibernate.annotations.Comment;

import java.time.LocalDateTime;

/**
 * 任务实例实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "wf_task_instance")
@Comment("任务实例表")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class TaskInstance extends BaseEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Comment("主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", nullable = false, length = 64)
    @Comment("租户ID")
    private String tenantId;

    /**
     * 流程实例ID
     */
    @Column(name = "process_instance_id", nullable = false)
    @Comment("流程实例ID")
    private Long processInstanceId;

    /**
     * 流程定义ID
     */
    @Column(name = "process_definition_id", nullable = false)
    @Comment("流程定义ID")
    private Long processDefinitionId;

    /**
     * 节点标识
     */
    @Column(name = "node_key", nullable = false, length = 100)
    @Comment("节点标识")
    private String nodeKey;

    /**
     * 节点名称
     */
    @Column(name = "node_name", nullable = false, length = 200)
    @Comment("节点名称")
    private String nodeName;

    /**
     * 节点类型
     */
    @Column(name = "node_type", nullable = false, length = 50)
    @Comment("节点类型")
    @Enumerated(EnumType.STRING)
    private NodeType nodeType;

    /**
     * 任务标识
     */
    @Column(name = "task_key", nullable = false, length = 200)
    @Comment("任务标识")
    private String taskKey;

    /**
     * 任务名称
     */
    @Column(name = "task_name", nullable = false, length = 200)
    @Comment("任务名称")
    private String taskName;

    /**
     * 指定处理人ID
     */
    @Column(name = "assignee_id")
    @Comment("指定处理人ID")
    private Long assigneeId;

    /**
     * 指定处理人姓名
     */
    @Column(name = "assignee_name", length = 100)
    @Comment("指定处理人姓名")
    private String assigneeName;

    /**
     * 候选用户ID列表
     */
    @Column(name = "candidate_users", columnDefinition = "TEXT")
    @Comment("候选用户ID列表")
    private String candidateUsers;

    /**
     * 候选用户组ID列表
     */
    @Column(name = "candidate_groups", columnDefinition = "TEXT")
    @Comment("候选用户组ID列表")
    private String candidateGroups;

    /**
     * 审批类型：SINGLE/MULTI_AND/MULTI_OR/SEQUENTIAL
     */
    @Column(name = "approval_type", nullable = false, length = 20)
    @Comment("审批类型：SINGLE/MULTI_AND/MULTI_OR/SEQUENTIAL")
    @Enumerated(EnumType.STRING)
    private ApprovalType approvalType = ApprovalType.SINGLE;

    /**
     * 状态：CREATED/CLAIMED/COMPLETED/CANCELLED/SUSPENDED
     */
    @Column(name = "status", nullable = false, length = 20)
    @Comment("状态：CREATED/CLAIMED/COMPLETED/CANCELLED/SUSPENDED")
    @Enumerated(EnumType.STRING)
    private TaskStatus status = TaskStatus.CREATED;

    /**
     * 优先级
     */
    @Column(name = "priority", nullable = false)
    @Comment("优先级")
    private Integer priority = 0;

    /**
     * 到期时间
     */
    @Column(name = "due_date")
    @Comment("到期时间")
    private LocalDateTime dueDate;

    /**
     * 签收时间
     */
    @Column(name = "claim_time")
    @Comment("签收时间")
    private LocalDateTime claimTime;

    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    @Comment("完成时间")
    private LocalDateTime completeTime;

    /**
     * 表单数据JSON
     */
    @Column(name = "form_data", columnDefinition = "TEXT")
    @Comment("表单数据JSON")
    private String formData;

    /**
     * 任务变量JSON
     */
    @Column(name = "variables", columnDefinition = "TEXT")
    @Comment("任务变量JSON")
    private String variables;

    /**
     * 创建时间
     */
    @Column(name = "created_time", nullable = false)
    @Comment("创建时间")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @Column(name = "updated_time")
    @Comment("更新时间")
    private LocalDateTime updatedTime;

    // 新增字段用于审批机制
    /**
     * 原始处理人ID（用于委派）
     */
    @Column(name = "original_assignee_id")
    @Comment("原始处理人ID（用于委派）")
    private Long originalAssigneeId;

    /**
     * 原始处理人姓名
     */
    @Column(name = "original_assignee_name", length = 100)
    @Comment("原始处理人姓名")
    private String originalAssigneeName;

    /**
     * 委派人ID
     */
    @Column(name = "delegator_id")
    @Comment("委派人ID")
    private Long delegatorId;

    /**
     * 委派人姓名
     */
    @Column(name = "delegator_name", length = 100)
    @Comment("委派人姓名")
    private String delegatorName;

    /**
     * 已审批人数
     */
    @Column(name = "approval_count")
    @Comment("已审批人数")
    private Integer approvalCount = 0;

    /**
     * 需要审批人数
     */
    @Column(name = "required_count")
    @Comment("需要审批人数")
    private Integer requiredCount = 1;

    /**
     * 驳回原因
     */
    @Column(name = "reject_reason", columnDefinition = "TEXT")
    @Comment("驳回原因")
    private String rejectReason;

    /**
     * 转办原因
     */
    @Column(name = "transfer_reason", columnDefinition = "TEXT")
    @Comment("转办原因")
    private String transferReason;

    /**
     * 委派原因
     */
    @Column(name = "delegate_reason", columnDefinition = "TEXT")
    @Comment("委派原因")
    private String delegateReason;

    /**
     * 审批顺序（用于依次审批）
     */
    @Column(name = "approval_sequence")
    @Comment("审批顺序（用于依次审批）")
    private Integer approvalSequence = 0;

    /**
     * 当前审批顺序
     */
    @Column(name = "current_sequence")
    @Comment("当前审批顺序")
    private Integer currentSequence = 0;

    /**
     * 超时处理动作
     */
    @Column(name = "timeout_action", length = 50)
    @Comment("超时处理动作")
    private String timeoutAction;

    /**
     * 是否自动签收
     */
    @Column(name = "auto_claim")
    @Comment("是否自动签收")
    private Boolean autoClaim = false;

    // 节点类型枚举
    public enum NodeType {
        START("开始节点"),
        USER_TASK("用户任务"),
        SERVICE_TASK("服务任务"),
        SCRIPT_TASK("脚本任务"),
        GATEWAY("网关"),
        END("结束节点");

        private final String description;

        NodeType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 审批类型枚举
    public enum ApprovalType {
        SINGLE("单人审批"),
        MULTI_AND("会签（全部同意）"),
        MULTI_OR("或签（任一同意）"),
        SEQUENTIAL("依次审批");

        private final String description;

        ApprovalType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 任务状态枚举
    public enum TaskStatus {
        CREATED("已创建"),
        CLAIMED("已签收"),
        IN_PROGRESS("处理中"),
        COMPLETED("已完成"),
        REJECTED("已驳回"),
        TRANSFERRED("已转办"),
        DELEGATED("已委派"),
        CANCELLED("已取消"),
        SUSPENDED("已挂起"),
        TIMEOUT("已超时"),
        TERMINATED("已终止");

        private final String description;

        TaskStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 任务操作类型枚举
    public enum TaskAction {
        APPROVE("同意"),
        REJECT("驳回"),
        TRANSFER("转办"),
        DELEGATE("委派"),
        ADD_SIGN("加签"),
        REMOVE_SIGN("减签"),
        CLAIM("签收"),
        UNCLAIM("取消签收"),
        CANCEL("撤销"),
        TERMINATE("终止");

        private final String description;

        TaskAction(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 构造函数
    public TaskInstance() {
        this.createdTime = LocalDateTime.now();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(Long processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public Long getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(Long processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public String getNodeKey() {
        return nodeKey;
    }

    public void setNodeKey(String nodeKey) {
        this.nodeKey = nodeKey;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public NodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(NodeType nodeType) {
        this.nodeType = nodeType;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Long getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(Long assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getCandidateUsers() {
        return candidateUsers;
    }

    public void setCandidateUsers(String candidateUsers) {
        this.candidateUsers = candidateUsers;
    }

    public String getCandidateGroups() {
        return candidateGroups;
    }

    public void setCandidateGroups(String candidateGroups) {
        this.candidateGroups = candidateGroups;
    }

    public ApprovalType getApprovalType() {
        return approvalType;
    }

    public void setApprovalType(ApprovalType approvalType) {
        this.approvalType = approvalType;
    }

    public TaskStatus getStatus() {
        return status;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getClaimTime() {
        return claimTime;
    }

    public void setClaimTime(LocalDateTime claimTime) {
        this.claimTime = claimTime;
    }

    public LocalDateTime getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(LocalDateTime completeTime) {
        this.completeTime = completeTime;
    }

    public String getFormData() {
        return formData;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    // 新增字段的getter和setter方法
    public Long getOriginalAssigneeId() {
        return originalAssigneeId;
    }

    public void setOriginalAssigneeId(Long originalAssigneeId) {
        this.originalAssigneeId = originalAssigneeId;
    }

    public String getOriginalAssigneeName() {
        return originalAssigneeName;
    }

    public void setOriginalAssigneeName(String originalAssigneeName) {
        this.originalAssigneeName = originalAssigneeName;
    }

    public Long getDelegatorId() {
        return delegatorId;
    }

    public void setDelegatorId(Long delegatorId) {
        this.delegatorId = delegatorId;
    }

    public String getDelegatorName() {
        return delegatorName;
    }

    public void setDelegatorName(String delegatorName) {
        this.delegatorName = delegatorName;
    }

    public Integer getApprovalCount() {
        return approvalCount;
    }

    public void setApprovalCount(Integer approvalCount) {
        this.approvalCount = approvalCount;
    }

    public Integer getRequiredCount() {
        return requiredCount;
    }

    public void setRequiredCount(Integer requiredCount) {
        this.requiredCount = requiredCount;
    }

    public String getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getTransferReason() {
        return transferReason;
    }

    public void setTransferReason(String transferReason) {
        this.transferReason = transferReason;
    }

    public String getDelegateReason() {
        return delegateReason;
    }

    public void setDelegateReason(String delegateReason) {
        this.delegateReason = delegateReason;
    }

    public Integer getApprovalSequence() {
        return approvalSequence;
    }

    public void setApprovalSequence(Integer approvalSequence) {
        this.approvalSequence = approvalSequence;
    }

    public Integer getCurrentSequence() {
        return currentSequence;
    }

    public void setCurrentSequence(Integer currentSequence) {
        this.currentSequence = currentSequence;
    }

    public String getTimeoutAction() {
        return timeoutAction;
    }

    public void setTimeoutAction(String timeoutAction) {
        this.timeoutAction = timeoutAction;
    }

    public Boolean getAutoClaim() {
        return autoClaim;
    }

    public void setAutoClaim(Boolean autoClaim) {
        this.autoClaim = autoClaim;
    }

    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }

    /**
     * 签收任务
     */
    public void claim(Long userId, String userName) {
        this.assigneeId = userId;
        this.assigneeName = userName;
        this.status = TaskStatus.CLAIMED;
        this.claimTime = LocalDateTime.now();
    }

    /**
     * 完成任务
     */
    public void complete() {
        this.status = TaskStatus.COMPLETED;
        this.completeTime = LocalDateTime.now();
    }

    /**
     * 完成任务（带操作人信息）
     */
    public void complete(Long operatorId, String operatorName) {
        this.status = TaskStatus.COMPLETED;
        this.completeTime = LocalDateTime.now();
        // 如果需要记录最后操作人，可以在这里设置
    }

    /**
     * 取消任务
     */
    public void cancel() {
        this.status = TaskStatus.CANCELLED;
    }

    /**
     * 挂起任务
     */
    public void suspend() {
        this.status = TaskStatus.SUSPENDED;
    }

    /**
     * 激活任务
     */
    public void activate() {
        this.status = TaskStatus.CREATED;
    }

    /**
     * 驳回任务
     */
    public void reject(String reason) {
        this.status = TaskStatus.REJECTED;
        this.rejectReason = reason;
        this.completeTime = LocalDateTime.now();
    }

    /**
     * 转办任务
     */
    public void transfer(Long newAssigneeId, String newAssigneeName, String reason) {
        this.assigneeId = newAssigneeId;
        this.assigneeName = newAssigneeName;
        this.transferReason = reason;
        this.status = TaskStatus.TRANSFERRED;
        this.claimTime = null; // 清除签收时间，需要重新签收
    }

    /**
     * 委派任务
     */
    public void delegate(Long delegateToId, String delegateToName, String reason) {
        // 保存原始处理人信息
        this.originalAssigneeId = this.assigneeId;
        this.originalAssigneeName = this.assigneeName;

        // 设置委派信息
        this.delegatorId = this.assigneeId;
        this.delegatorName = this.assigneeName;
        this.assigneeId = delegateToId;
        this.assigneeName = delegateToName;
        this.delegateReason = reason;
        this.status = TaskStatus.DELEGATED;
        this.claimTime = null; // 清除签收时间，需要重新签收
    }

    /**
     * 委派完成，返回原处理人
     */
    public void completeDelegation() {
        if (this.originalAssigneeId != null) {
            this.assigneeId = this.originalAssigneeId;
            this.assigneeName = this.originalAssigneeName;
            this.originalAssigneeId = null;
            this.originalAssigneeName = null;
            this.delegatorId = null;
            this.delegatorName = null;
            this.delegateReason = null;
            this.status = TaskStatus.COMPLETED;
            this.completeTime = LocalDateTime.now();
        }
    }

    /**
     * 终止任务
     */
    public void terminate() {
        this.status = TaskStatus.TERMINATED;
        this.completeTime = LocalDateTime.now();
    }

    /**
     * 超时处理
     */
    public void timeout() {
        this.status = TaskStatus.TIMEOUT;
        this.completeTime = LocalDateTime.now();
    }

    /**
     * 增加审批计数
     */
    public void incrementApprovalCount() {
        this.approvalCount = (this.approvalCount == null ? 0 : this.approvalCount) + 1;
    }

    /**
     * 检查是否满足审批条件
     */
    public boolean isApprovalComplete() {
        if (this.approvalType == ApprovalType.SINGLE) {
            return this.approvalCount >= 1;
        } else if (this.approvalType == ApprovalType.MULTI_AND) {
            return this.approvalCount >= this.requiredCount;
        } else if (this.approvalType == ApprovalType.MULTI_OR) {
            return this.approvalCount >= 1;
        } else if (this.approvalType == ApprovalType.SEQUENTIAL) {
            return this.currentSequence >= this.requiredCount;
        }
        return false;
    }

    /**
     * 检查是否为委派任务
     */
    public boolean isDelegated() {
        return this.status == TaskStatus.DELEGATED || this.originalAssigneeId != null;
    }

    /**
     * 检查任务是否已完成（包括各种完成状态）
     */
    public boolean isFinished() {
        return this.status == TaskStatus.COMPLETED ||
               this.status == TaskStatus.REJECTED ||
               this.status == TaskStatus.CANCELLED ||
               this.status == TaskStatus.TERMINATED ||
               this.status == TaskStatus.TIMEOUT;
    }

    @Override
    public String toString() {
        return "TaskInstance{" +
                "id=" + id +
                ", tenantId='" + tenantId + '\'' +
                ", nodeKey='" + nodeKey + '\'' +
                ", taskName='" + taskName + '\'' +
                ", assigneeName='" + assigneeName + '\'' +
                ", status=" + status +
                ", createdTime=" + createdTime +
                '}';
    }
}
