package com.visthink.workflow.service;

import com.visthink.shared.context.TenantContext;
import com.visthink.shared.exception.BusinessException;
import com.visthink.workflow.dto.ProcessDefinitionDTO;
import com.visthink.workflow.entity.ProcessDefinition;
import com.visthink.workflow.repository.ProcessDefinitionRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程定义服务类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class ProcessDefinitionService {

    private static final Logger logger = LoggerFactory.getLogger(ProcessDefinitionService.class);

    @Inject
    ProcessDefinitionRepository processDefinitionRepository;

    @Inject
    TenantContext tenantContext;

    /**
     * 创建流程定义
     * 
     * @param dto 流程定义DTO
     * @return 创建的流程定义
     */
    @Transactional
    public Uni<ProcessDefinitionDTO> create(ProcessDefinitionDTO dto) {
        logger.info("创建流程定义: {}", dto.getProcessKey());
        
        String tenantId = tenantContext.getCurrentTenantId();
        dto.setTenantId(tenantId);
        
        // 检查流程标识是否已存在
        return processDefinitionRepository.existsByTenantAndKey(tenantId, dto.getProcessKey())
                .flatMap(exists -> {
                    if (exists) {
                        return Uni.createFrom().failure(
                            new BusinessException("流程标识已存在: " + dto.getProcessKey()));
                    }
                    
                    // 设置版本号和状态
                    dto.setProcessVersion(1);
                    dto.setStatus(ProcessDefinition.ProcessStatus.DRAFT);
                    dto.setIsLatest(true);
                    dto.setCreatedBy(tenantContext.getCurrentUserId());
                    dto.setCreatedTime(LocalDateTime.now());
                    
                    ProcessDefinition entity = dto.toEntity();
                    return processDefinitionRepository.persist(entity);
                })
                .map(ProcessDefinitionDTO::new)
                .invoke(result -> logger.info("流程定义创建成功: {}", result.getId()));
    }

    /**
     * 更新流程定义
     * 
     * @param id 流程定义ID
     * @param dto 流程定义DTO
     * @return 更新的流程定义
     */
    @Transactional
    public Uni<ProcessDefinitionDTO> update(Long id, ProcessDefinitionDTO dto) {
        logger.info("更新流程定义: {}", id);
        
        String tenantId = tenantContext.getCurrentTenantId();
        
        return processDefinitionRepository.findById(id)
                .onItem().ifNull().failWith(new BusinessException("流程定义不存在: " + id))
                .flatMap(entity -> {
                    // 检查租户权限
                    if (!tenantId.equals(entity.getTenantId())) {
                        return Uni.createFrom().failure(new BusinessException("无权限访问该流程定义"));
                    }
                    
                    // 检查是否可以修改
                    if (entity.getStatus() == ProcessDefinition.ProcessStatus.ACTIVE) {
                        return Uni.createFrom().failure(new BusinessException("激活状态的流程定义不能修改"));
                    }
                    
                    // 更新实体
                    entity.setProcessName(dto.getProcessName());
                    entity.setCategory(dto.getCategory());
                    entity.setDescription(dto.getDescription());
                    entity.setProcessDefinitionJson(dto.getProcessDefinitionJson());
                    entity.setBpmnXml(dto.getBpmnXml());
                    entity.setFormDefinition(dto.getFormDefinition());
                    entity.setUpdatedBy(tenantContext.getCurrentUserId());
                    entity.setUpdatedTime(LocalDateTime.now());
                    
                    return processDefinitionRepository.persist(entity);
                })
                .map(ProcessDefinitionDTO::new)
                .invoke(result -> logger.info("流程定义更新成功: {}", result.getId()));
    }

    /**
     * 发布流程定义
     * 
     * @param id 流程定义ID
     * @return 发布的流程定义
     */
    @Transactional
    public Uni<ProcessDefinitionDTO> deploy(Long id) {
        logger.info("发布流程定义: {}", id);
        
        String tenantId = tenantContext.getCurrentTenantId();
        
        return processDefinitionRepository.findById(id)
                .onItem().ifNull().failWith(new BusinessException("流程定义不存在: " + id))
                .flatMap(entity -> {
                    // 检查租户权限
                    if (!tenantId.equals(entity.getTenantId())) {
                        return Uni.createFrom().failure(new BusinessException("无权限访问该流程定义"));
                    }
                    
                    // 检查状态
                    if (entity.getStatus() == ProcessDefinition.ProcessStatus.ACTIVE) {
                        return Uni.createFrom().failure(new BusinessException("流程定义已经是激活状态"));
                    }
                    
                    // 验证流程定义
                    validateProcessDefinition(entity);
                    
                    // 如果是新版本发布，需要创建新版本
                    if (entity.getStatus() == ProcessDefinition.ProcessStatus.DRAFT && entity.getProcessVersion() == 1) {
                        // 第一次发布，直接激活
                        entity.setStatus(ProcessDefinition.ProcessStatus.ACTIVE);
                        entity.setUpdatedBy(tenantContext.getCurrentUserId());
                        entity.setUpdatedTime(LocalDateTime.now());
                        return processDefinitionRepository.persist(entity);
                    } else {
                        // 创建新版本
                        return createNewVersion(entity);
                    }
                })
                .map(ProcessDefinitionDTO::new)
                .invoke(result -> logger.info("流程定义发布成功: {}", result.getId()));
    }

    /**
     * 创建新版本
     */
    private Uni<ProcessDefinition> createNewVersion(ProcessDefinition originalEntity) {
        return processDefinitionRepository.getNextVersion(originalEntity.getTenantId(), originalEntity.getProcessKey())
                .flatMap(nextVersion -> {
                    // 将所有旧版本设置为非最新版本
                    return processDefinitionRepository.updateAllVersionsToNotLatest(
                            originalEntity.getTenantId(), originalEntity.getProcessKey())
                            .flatMap(updated -> {
                                // 创建新版本
                                ProcessDefinition newEntity = new ProcessDefinition();
                                newEntity.setTenantId(originalEntity.getTenantId());
                                newEntity.setProcessKey(originalEntity.getProcessKey());
                                newEntity.setProcessName(originalEntity.getProcessName());
                                newEntity.setProcessVersion(nextVersion);
                                newEntity.setCategory(originalEntity.getCategory());
                                newEntity.setDescription(originalEntity.getDescription());
                                newEntity.setDesignerType(originalEntity.getDesignerType());
                                newEntity.setProcessDefinitionJson(originalEntity.getProcessDefinitionJson());
                                newEntity.setBpmnXml(originalEntity.getBpmnXml());
                                newEntity.setFormDefinition(originalEntity.getFormDefinition());
                                newEntity.setStatus(ProcessDefinition.ProcessStatus.ACTIVE);
                                newEntity.setIsLatest(true);
                                newEntity.setCreatedBy(tenantContext.getCurrentUserId());
                                newEntity.setCreatedTime(LocalDateTime.now());
                                
                                return processDefinitionRepository.persist(newEntity);
                            });
                });
    }

    /**
     * 验证流程定义
     */
    private void validateProcessDefinition(ProcessDefinition entity) {
        logger.debug("验证流程定义: {}", entity.getProcessKey());

        // 验证流程定义JSON
        if (entity.getProcessDefinitionJson() != null && !entity.getProcessDefinitionJson().trim().isEmpty()) {
            validateProcessDefinitionJson(entity.getProcessDefinitionJson());
        }

        // 验证BPMN XML（如果是BPMN设计器）
        if (entity.getDesignerType() == ProcessDefinition.DesignerType.BPMN &&
            entity.getBpmnXml() != null && !entity.getBpmnXml().trim().isEmpty()) {
            validateBpmnXml(entity.getBpmnXml());
        }

        // 验证表单定义
        if (entity.getFormDefinition() != null && !entity.getFormDefinition().trim().isEmpty()) {
            validateFormDefinition(entity.getFormDefinition());
        }
    }

    /**
     * 验证流程定义JSON
     */
    private void validateProcessDefinitionJson(String processDefinitionJson) {
        try {
            // 简单的JSON格式验证
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            mapper.readTree(processDefinitionJson);

            // TODO: 实现更详细的流程定义结构验证
            // 1. 检查是否有开始节点和结束节点
            // 2. 检查节点连接是否正确
            // 3. 检查节点属性是否完整

        } catch (Exception e) {
            throw new BusinessException("流程定义JSON格式错误: " + e.getMessage());
        }
    }

    /**
     * 验证BPMN XML
     */
    private void validateBpmnXml(String bpmnXml) {
        try {
            // 简单的XML格式验证
            javax.xml.parsers.DocumentBuilderFactory factory = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            javax.xml.parsers.DocumentBuilder builder = factory.newDocumentBuilder();
            builder.parse(new java.io.ByteArrayInputStream(bpmnXml.getBytes()));

            // TODO: 实现BPMN 2.0标准验证
            // 1. 检查BPMN命名空间
            // 2. 检查必需的元素
            // 3. 检查元素关系

        } catch (Exception e) {
            throw new BusinessException("BPMN XML格式错误: " + e.getMessage());
        }
    }

    /**
     * 验证表单定义
     */
    private void validateFormDefinition(String formDefinition) {
        try {
            // 简单的JSON格式验证
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            mapper.readTree(formDefinition);

            // TODO: 实现表单定义结构验证

        } catch (Exception e) {
            throw new BusinessException("表单定义JSON格式错误: " + e.getMessage());
        }
    }

    /**
     * 挂起流程定义
     * 
     * @param id 流程定义ID
     * @return 挂起的流程定义
     */
    @Transactional
    public Uni<ProcessDefinitionDTO> suspend(Long id) {
        logger.info("挂起流程定义: {}", id);
        
        return updateStatus(id, ProcessDefinition.ProcessStatus.SUSPENDED);
    }

    /**
     * 激活流程定义
     * 
     * @param id 流程定义ID
     * @return 激活的流程定义
     */
    @Transactional
    public Uni<ProcessDefinitionDTO> activate(Long id) {
        logger.info("激活流程定义: {}", id);
        
        return updateStatus(id, ProcessDefinition.ProcessStatus.ACTIVE);
    }

    /**
     * 删除流程定义
     * 
     * @param id 流程定义ID
     * @return 删除结果
     */
    @Transactional
    public Uni<Boolean> delete(Long id) {
        logger.info("删除流程定义: {}", id);
        
        return updateStatus(id, ProcessDefinition.ProcessStatus.DELETED)
                .map(dto -> true);
    }

    /**
     * 更新流程定义状态
     */
    private Uni<ProcessDefinitionDTO> updateStatus(Long id, ProcessDefinition.ProcessStatus status) {
        String tenantId = tenantContext.getCurrentTenantId();
        
        return processDefinitionRepository.findById(id)
                .onItem().ifNull().failWith(new BusinessException("流程定义不存在: " + id))
                .flatMap(entity -> {
                    // 检查租户权限
                    if (!tenantId.equals(entity.getTenantId())) {
                        return Uni.createFrom().failure(new BusinessException("无权限访问该流程定义"));
                    }
                    
                    entity.setStatus(status);
                    entity.setUpdatedBy(tenantContext.getCurrentUserId());
                    entity.setUpdatedTime(LocalDateTime.now());
                    
                    return processDefinitionRepository.persist(entity);
                })
                .map(ProcessDefinitionDTO::new);
    }

    /**
     * 根据ID查找流程定义
     * 
     * @param id 流程定义ID
     * @return 流程定义
     */
    public Uni<ProcessDefinitionDTO> findById(Long id) {
        String tenantId = tenantContext.getCurrentTenantId();
        
        return processDefinitionRepository.findById(id)
                .onItem().ifNull().failWith(new BusinessException("流程定义不存在: " + id))
                .map(entity -> {
                    // 检查租户权限
                    if (!tenantId.equals(entity.getTenantId())) {
                        throw new BusinessException("无权限访问该流程定义");
                    }
                    return new ProcessDefinitionDTO(entity);
                });
    }

    /**
     * 根据流程标识查找最新版本的流程定义
     * 
     * @param processKey 流程标识
     * @return 流程定义
     */
    public Uni<ProcessDefinitionDTO> findLatestByKey(String processKey) {
        String tenantId = tenantContext.getCurrentTenantId();
        
        return processDefinitionRepository.findLatestByTenantAndKey(tenantId, processKey)
                .onItem().ifNull().failWith(new BusinessException("流程定义不存在: " + processKey))
                .map(ProcessDefinitionDTO::new);
    }

    /**
     * 查找所有激活状态的流程定义
     * 
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinitionDTO>> findAllActive() {
        String tenantId = tenantContext.getCurrentTenantId();
        
        return processDefinitionRepository.findActiveByTenant(tenantId)
                .map(entities -> entities.stream()
                        .map(ProcessDefinitionDTO::new)
                        .collect(Collectors.toList()));
    }

    /**
     * 分页查询流程定义
     * 
     * @param page 页码（从0开始）
     * @param size 页大小
     * @return 流程定义列表
     */
    public Uni<List<ProcessDefinitionDTO>> findWithPaging(int page, int size) {
        String tenantId = tenantContext.getCurrentTenantId();
        
        return processDefinitionRepository.findByTenantWithPaging(tenantId, page, size)
                .map(entities -> entities.stream()
                        .map(ProcessDefinitionDTO::new)
                        .collect(Collectors.toList()));
    }

    /**
     * 统计流程定义数量
     * 
     * @return 数量
     */
    public Uni<Long> count() {
        String tenantId = tenantContext.getCurrentTenantId();
        return processDefinitionRepository.countByTenant(tenantId);
    }
}
