package com.visthink.workflow.repository;

import com.visthink.workflow.entity.FormPermission;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 表单权限Repository
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class FormPermissionRepository implements PanacheRepository<FormPermission> {

    /**
     * 根据流程定义ID和节点标识查询表单权限
     */
    public Uni<List<FormPermission>> findByProcessDefinitionIdAndNodeKey(Long processDefinitionId, String nodeKey) {
        return find("processDefinitionId = ?1 and (nodeKey = ?2 or nodeKey is null) and enabled = true order by priority desc", 
                   processDefinitionId, nodeKey).list();
    }

    /**
     * 根据流程定义ID查询所有表单权限
     */
    public Uni<List<FormPermission>> findByProcessDefinitionId(Long processDefinitionId) {
        return find("processDefinitionId = ?1 order by nodeKey, fieldKey, priority desc", processDefinitionId).list();
    }

    /**
     * 根据流程定义ID和字段标识查询表单权限
     */
    public Uni<List<FormPermission>> findByProcessDefinitionIdAndFieldKey(Long processDefinitionId, String fieldKey) {
        return find("processDefinitionId = ?1 and fieldKey = ?2 and enabled = true order by priority desc", 
                   processDefinitionId, fieldKey).list();
    }

    /**
     * 根据用户ID查询表单权限
     */
    public Uni<List<FormPermission>> findByUserId(Long userId) {
        return find("userId = ?1 and enabled = true order by priority desc", userId).list();
    }

    /**
     * 根据角色ID查询表单权限
     */
    public Uni<List<FormPermission>> findByRoleId(Long roleId) {
        return find("roleId = ?1 and enabled = true order by priority desc", roleId).list();
    }

    /**
     * 根据租户ID查询表单权限
     */
    public Uni<List<FormPermission>> findByTenantId(String tenantId) {
        return find("tenantId = ?1 order by processDefinitionId, nodeKey, fieldKey", tenantId).list();
    }

    /**
     * 检查是否存在重复的权限配置
     */
    public Uni<Boolean> existsDuplicate(Long processDefinitionId, String nodeKey, String fieldKey, 
                                       Long userId, Long roleId, Long excludeId) {
        StringBuilder query = new StringBuilder("processDefinitionId = ?1 and nodeKey = ?2 and fieldKey = ?3");
        
        if (userId != null) {
            query.append(" and userId = ?4");
        } else {
            query.append(" and userId is null");
        }
        
        if (roleId != null) {
            query.append(" and roleId = ?5");
        } else {
            query.append(" and roleId is null");
        }
        
        if (excludeId != null) {
            query.append(" and id != ?6");
        }
        
        if (userId != null && roleId != null && excludeId != null) {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey, userId, roleId, excludeId)
                    .map(count -> count > 0);
        } else if (userId != null && roleId != null) {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey, userId, roleId)
                    .map(count -> count > 0);
        } else if (userId != null && excludeId != null) {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey, userId, excludeId)
                    .map(count -> count > 0);
        } else if (roleId != null && excludeId != null) {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey, roleId, excludeId)
                    .map(count -> count > 0);
        } else if (userId != null) {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey, userId)
                    .map(count -> count > 0);
        } else if (roleId != null) {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey, roleId)
                    .map(count -> count > 0);
        } else if (excludeId != null) {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey, excludeId)
                    .map(count -> count > 0);
        } else {
            return count(query.toString(), processDefinitionId, nodeKey, fieldKey)
                    .map(count -> count > 0);
        }
    }

    /**
     * 批量删除流程定义的表单权限
     */
    public Uni<Long> deleteByProcessDefinitionId(Long processDefinitionId) {
        return delete("processDefinitionId = ?1", processDefinitionId);
    }

    /**
     * 批量删除节点的表单权限
     */
    public Uni<Long> deleteByProcessDefinitionIdAndNodeKey(Long processDefinitionId, String nodeKey) {
        return delete("processDefinitionId = ?1 and nodeKey = ?2", processDefinitionId, nodeKey);
    }

    /**
     * 启用或禁用表单权限
     */
    public Uni<Integer> updateEnabled(Long id, Boolean enabled) {
        return update("enabled = ?1 where id = ?2", enabled, id);
    }

    /**
     * 批量启用或禁用表单权限
     */
    public Uni<Integer> batchUpdateEnabled(List<Long> ids, Boolean enabled) {
        return update("enabled = ?1 where id in ?2", enabled, ids);
    }

    /**
     * 统计流程定义的表单权限数量
     */
    public Uni<Long> countByProcessDefinitionId(Long processDefinitionId) {
        return count("processDefinitionId = ?1", processDefinitionId);
    }

    /**
     * 获取流程定义的所有字段
     */
    public Uni<List<String>> getFieldKeysByProcessDefinitionId(Long processDefinitionId) {
        return getEntityManager()
                .createQuery("SELECT DISTINCT fp.fieldKey FROM FormPermission fp WHERE fp.processDefinitionId = :processDefinitionId ORDER BY fp.fieldKey", String.class)
                .setParameter("processDefinitionId", processDefinitionId)
                .getResultList();
    }

    /**
     * 获取流程定义的所有节点
     */
    public Uni<List<String>> getNodeKeysByProcessDefinitionId(Long processDefinitionId) {
        return getEntityManager()
                .createQuery("SELECT DISTINCT fp.nodeKey FROM FormPermission fp WHERE fp.processDefinitionId = :processDefinitionId AND fp.nodeKey IS NOT NULL ORDER BY fp.nodeKey", String.class)
                .setParameter("processDefinitionId", processDefinitionId)
                .getResultList();
    }
}
