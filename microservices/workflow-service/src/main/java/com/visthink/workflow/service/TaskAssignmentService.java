package com.visthink.workflow.service;

import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.entity.ProcessInstance;
import com.visthink.workflow.exception.BusinessException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务分配服务
 * 负责处理任务的分配逻辑，包括用户、角色、部门、表达式等方式的任务分配
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class TaskAssignmentService {

    private static final Logger logger = LoggerFactory.getLogger(TaskAssignmentService.class);

    @Inject
    UserService userService;

    @Inject
    RoleService roleService;

    @Inject
    DepartmentService departmentService;

    @Inject
    ExpressionService expressionService;

    /**
     * 分配任务给指定用户
     * 
     * @param taskInstance 任务实例
     * @param assigneeType 分配类型
     * @param assigneeValue 分配值
     * @param processInstance 流程实例
     * @return 分配结果
     */
    public Uni<TaskAssignmentResult> assignTask(TaskInstance taskInstance, String assigneeType, 
                                               String assigneeValue, ProcessInstance processInstance) {
        logger.debug("分配任务: taskId={}, assigneeType={}, assigneeValue={}", 
                    taskInstance.getId(), assigneeType, assigneeValue);

        return switch (assigneeType.toUpperCase()) {
            case "USER" -> assignToUser(taskInstance, assigneeValue);
            case "ROLE" -> assignToRole(taskInstance, assigneeValue);
            case "DEPARTMENT" -> assignToDepartment(taskInstance, assigneeValue);
            case "EXPRESSION" -> assignByExpression(taskInstance, assigneeValue, processInstance);
            case "INITIATOR" -> assignToInitiator(taskInstance, processInstance);
            case "PREVIOUS_ASSIGNEE" -> assignToPreviousAssignee(taskInstance, processInstance);
            default -> Uni.createFrom().failure(new BusinessException("不支持的分配类型: " + assigneeType));
        };
    }

    /**
     * 分配给指定用户
     */
    private Uni<TaskAssignmentResult> assignToUser(TaskInstance taskInstance, String userIds) {
        return userService.findUsersByIds(parseIds(userIds))
                .map(users -> {
                    if (users.isEmpty()) {
                        throw new BusinessException("未找到指定的用户: " + userIds);
                    }
                    
                    TaskAssignmentResult result = new TaskAssignmentResult();
                    
                    if (users.size() == 1) {
                        // 单人分配
                        var user = users.get(0);
                        taskInstance.setAssigneeId(user.getId());
                        taskInstance.setAssigneeName(user.getName());
                        taskInstance.setApprovalType(TaskInstance.ApprovalType.SINGLE);
                        taskInstance.setRequiredCount(1);
                        result.setAssigneeId(user.getId());
                        result.setAssigneeName(user.getName());
                    } else {
                        // 多人分配，默认为或签
                        String candidateUsers = users.stream()
                                .map(u -> u.getId().toString())
                                .collect(Collectors.joining(","));
                        String candidateNames = users.stream()
                                .map(u -> u.getName())
                                .collect(Collectors.joining(","));
                        
                        taskInstance.setCandidateUsers(candidateUsers);
                        taskInstance.setCandidateGroups(candidateNames);
                        taskInstance.setApprovalType(TaskInstance.ApprovalType.MULTI_OR);
                        taskInstance.setRequiredCount(users.size());
                        result.setCandidateUsers(candidateUsers);
                    }
                    
                    result.setSuccess(true);
                    return result;
                });
    }

    /**
     * 分配给角色
     */
    private Uni<TaskAssignmentResult> assignToRole(TaskInstance taskInstance, String roleIds) {
        return roleService.findUsersByRoleIds(parseIds(roleIds))
                .map(users -> {
                    if (users.isEmpty()) {
                        throw new BusinessException("角色下没有用户: " + roleIds);
                    }
                    
                    String candidateUsers = users.stream()
                            .map(u -> u.getId().toString())
                            .collect(Collectors.joining(","));
                    String candidateNames = users.stream()
                            .map(u -> u.getName())
                            .collect(Collectors.joining(","));
                    
                    taskInstance.setCandidateUsers(candidateUsers);
                    taskInstance.setCandidateGroups(candidateNames);
                    taskInstance.setApprovalType(TaskInstance.ApprovalType.MULTI_OR);
                    taskInstance.setRequiredCount(users.size());
                    
                    TaskAssignmentResult result = new TaskAssignmentResult();
                    result.setCandidateUsers(candidateUsers);
                    result.setSuccess(true);
                    return result;
                });
    }

    /**
     * 分配给部门
     */
    private Uni<TaskAssignmentResult> assignToDepartment(TaskInstance taskInstance, String departmentIds) {
        return departmentService.findUsersByDepartmentIds(parseIds(departmentIds))
                .map(users -> {
                    if (users.isEmpty()) {
                        throw new BusinessException("部门下没有用户: " + departmentIds);
                    }
                    
                    String candidateUsers = users.stream()
                            .map(u -> u.getId().toString())
                            .collect(Collectors.joining(","));
                    String candidateNames = users.stream()
                            .map(u -> u.getName())
                            .collect(Collectors.joining(","));
                    
                    taskInstance.setCandidateUsers(candidateUsers);
                    taskInstance.setCandidateGroups(candidateNames);
                    taskInstance.setApprovalType(TaskInstance.ApprovalType.MULTI_OR);
                    taskInstance.setRequiredCount(users.size());
                    
                    TaskAssignmentResult result = new TaskAssignmentResult();
                    result.setCandidateUsers(candidateUsers);
                    result.setSuccess(true);
                    return result;
                });
    }

    /**
     * 通过表达式分配
     */
    private Uni<TaskAssignmentResult> assignByExpression(TaskInstance taskInstance, String expression, 
                                                        ProcessInstance processInstance) {
        return expressionService.evaluateUserExpression(expression, processInstance)
                .flatMap(userIds -> {
                    if (userIds.isEmpty()) {
                        return Uni.createFrom().failure(new BusinessException("表达式未返回有效用户: " + expression));
                    }
                    return assignToUser(taskInstance, String.join(",", userIds));
                });
    }

    /**
     * 分配给流程发起人
     */
    private Uni<TaskAssignmentResult> assignToInitiator(TaskInstance taskInstance, ProcessInstance processInstance) {
        return userService.findById(processInstance.getInitiatorId())
                .map(user -> {
                    taskInstance.setAssigneeId(user.getId());
                    taskInstance.setAssigneeName(user.getName());
                    taskInstance.setApprovalType(TaskInstance.ApprovalType.SINGLE);
                    taskInstance.setRequiredCount(1);
                    
                    TaskAssignmentResult result = new TaskAssignmentResult();
                    result.setAssigneeId(user.getId());
                    result.setAssigneeName(user.getName());
                    result.setSuccess(true);
                    return result;
                });
    }

    /**
     * 分配给上一个处理人
     */
    private Uni<TaskAssignmentResult> assignToPreviousAssignee(TaskInstance taskInstance, ProcessInstance processInstance) {
        // TODO: 实现查找上一个处理人的逻辑
        return Uni.createFrom().failure(new BusinessException("暂不支持分配给上一个处理人"));
    }

    /**
     * 设置审批类型
     */
    public void setApprovalType(TaskInstance taskInstance, TaskInstance.ApprovalType approvalType) {
        taskInstance.setApprovalType(approvalType);
        
        // 根据审批类型设置相关参数
        switch (approvalType) {
            case SINGLE -> {
                taskInstance.setRequiredCount(1);
                taskInstance.setCurrentSequence(0);
            }
            case MULTI_AND -> {
                // 会签需要所有人同意
                int candidateCount = getCandidateCount(taskInstance);
                taskInstance.setRequiredCount(candidateCount);
            }
            case MULTI_OR -> {
                // 或签只需要一人同意
                taskInstance.setRequiredCount(1);
            }
            case SEQUENTIAL -> {
                // 依次审批需要设置审批顺序
                int candidateCount = getCandidateCount(taskInstance);
                taskInstance.setRequiredCount(candidateCount);
                taskInstance.setCurrentSequence(1); // 从第一个开始
            }
        }
    }

    /**
     * 获取候选人数量
     */
    private int getCandidateCount(TaskInstance taskInstance) {
        if (taskInstance.getCandidateUsers() != null && !taskInstance.getCandidateUsers().isEmpty()) {
            return taskInstance.getCandidateUsers().split(",").length;
        }
        return 1;
    }

    /**
     * 解析ID字符串
     */
    private List<Long> parseIds(String ids) {
        if (ids == null || ids.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        return Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    /**
     * 任务分配结果
     */
    public static class TaskAssignmentResult {
        private boolean success;
        private String message;
        private Long assigneeId;
        private String assigneeName;
        private String candidateUsers;
        private String candidateGroups;

        // Getter和Setter方法
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Long getAssigneeId() {
            return assigneeId;
        }

        public void setAssigneeId(Long assigneeId) {
            this.assigneeId = assigneeId;
        }

        public String getAssigneeName() {
            return assigneeName;
        }

        public void setAssigneeName(String assigneeName) {
            this.assigneeName = assigneeName;
        }

        public String getCandidateUsers() {
            return candidateUsers;
        }

        public void setCandidateUsers(String candidateUsers) {
            this.candidateUsers = candidateUsers;
        }

        public String getCandidateGroups() {
            return candidateGroups;
        }

        public void setCandidateGroups(String candidateGroups) {
            this.candidateGroups = candidateGroups;
        }
    }
}
