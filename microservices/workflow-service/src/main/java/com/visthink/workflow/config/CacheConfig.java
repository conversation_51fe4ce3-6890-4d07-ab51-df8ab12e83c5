package com.visthink.workflow.config;

import io.quarkus.cache.CacheManager;
import io.quarkus.cache.CacheResult;
import io.quarkus.cache.CacheInvalidate;
import io.quarkus.cache.CacheInvalidateAll;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 缓存配置类
 * 定义工作流引擎的缓存策略和管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class CacheConfig {

    private static final Logger logger = LoggerFactory.getLogger(CacheConfig.class);

    @Inject
    CacheManager cacheManager;

    // 缓存名称常量
    public static final String PROCESS_DEFINITION_CACHE = "process-definition";
    public static final String FORM_PERMISSION_CACHE = "form-permission";
    public static final String TIMEOUT_CONFIG_CACHE = "timeout-config";
    public static final String NOTIFICATION_CONFIG_CACHE = "notification-config";
    public static final String USER_ROLES_CACHE = "user-roles";
    public static final String DEPARTMENT_TREE_CACHE = "department-tree";
    public static final String PROCESS_VARIABLES_CACHE = "process-variables";

    // 本地缓存，用于高频访问的小数据
    private final ConcurrentMap<String, Object> localCache = new ConcurrentHashMap<>();

    /**
     * 缓存配置信息
     */
    public static class CacheInfo {
        public static final Duration DEFAULT_TTL = Duration.ofMinutes(30);
        public static final Duration LONG_TTL = Duration.ofHours(2);
        public static final Duration SHORT_TTL = Duration.ofMinutes(5);
        
        // 各缓存的TTL配置
        public static final Duration PROCESS_DEFINITION_TTL = LONG_TTL;
        public static final Duration FORM_PERMISSION_TTL = DEFAULT_TTL;
        public static final Duration TIMEOUT_CONFIG_TTL = DEFAULT_TTL;
        public static final Duration NOTIFICATION_CONFIG_TTL = DEFAULT_TTL;
        public static final Duration USER_ROLES_TTL = SHORT_TTL;
        public static final Duration DEPARTMENT_TREE_TTL = DEFAULT_TTL;
        public static final Duration PROCESS_VARIABLES_TTL = SHORT_TTL;
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getCacheStatistics() {
        CacheStatistics stats = new CacheStatistics();
        
        try {
            // 获取各个缓存的统计信息
            stats.addCacheInfo(PROCESS_DEFINITION_CACHE, getCacheSize(PROCESS_DEFINITION_CACHE));
            stats.addCacheInfo(FORM_PERMISSION_CACHE, getCacheSize(FORM_PERMISSION_CACHE));
            stats.addCacheInfo(TIMEOUT_CONFIG_CACHE, getCacheSize(TIMEOUT_CONFIG_CACHE));
            stats.addCacheInfo(NOTIFICATION_CONFIG_CACHE, getCacheSize(NOTIFICATION_CONFIG_CACHE));
            stats.addCacheInfo(USER_ROLES_CACHE, getCacheSize(USER_ROLES_CACHE));
            stats.addCacheInfo(DEPARTMENT_TREE_CACHE, getCacheSize(DEPARTMENT_TREE_CACHE));
            stats.addCacheInfo(PROCESS_VARIABLES_CACHE, getCacheSize(PROCESS_VARIABLES_CACHE));
            
            // 本地缓存统计
            stats.setLocalCacheSize(localCache.size());
            
        } catch (Exception e) {
            logger.error("获取缓存统计信息失败", e);
        }
        
        return stats;
    }

    /**
     * 清除所有缓存
     */
    @CacheInvalidateAll(cacheName = PROCESS_DEFINITION_CACHE)
    @CacheInvalidateAll(cacheName = FORM_PERMISSION_CACHE)
    @CacheInvalidateAll(cacheName = TIMEOUT_CONFIG_CACHE)
    @CacheInvalidateAll(cacheName = NOTIFICATION_CONFIG_CACHE)
    @CacheInvalidateAll(cacheName = USER_ROLES_CACHE)
    @CacheInvalidateAll(cacheName = DEPARTMENT_TREE_CACHE)
    @CacheInvalidateAll(cacheName = PROCESS_VARIABLES_CACHE)
    public void clearAllCaches() {
        logger.info("清除所有缓存");
        localCache.clear();
    }

    /**
     * 清除指定缓存
     */
    public void clearCache(String cacheName) {
        logger.info("清除缓存: {}", cacheName);
        
        try {
            switch (cacheName) {
                case PROCESS_DEFINITION_CACHE:
                    clearProcessDefinitionCache();
                    break;
                case FORM_PERMISSION_CACHE:
                    clearFormPermissionCache();
                    break;
                case TIMEOUT_CONFIG_CACHE:
                    clearTimeoutConfigCache();
                    break;
                case NOTIFICATION_CONFIG_CACHE:
                    clearNotificationConfigCache();
                    break;
                case USER_ROLES_CACHE:
                    clearUserRolesCache();
                    break;
                case DEPARTMENT_TREE_CACHE:
                    clearDepartmentTreeCache();
                    break;
                case PROCESS_VARIABLES_CACHE:
                    clearProcessVariablesCache();
                    break;
                default:
                    logger.warn("未知的缓存名称: {}", cacheName);
            }
        } catch (Exception e) {
            logger.error("清除缓存失败: {}", cacheName, e);
        }
    }

    /**
     * 预热缓存
     */
    public void warmupCaches() {
        logger.info("开始预热缓存");
        
        // 这里可以预加载一些常用数据到缓存中
        // 例如：活跃的流程定义、常用的权限配置等
        
        logger.info("缓存预热完成");
    }

    /**
     * 本地缓存操作
     */
    public void putLocalCache(String key, Object value) {
        localCache.put(key, value);
    }

    public Object getLocalCache(String key) {
        return localCache.get(key);
    }

    public void removeLocalCache(String key) {
        localCache.remove(key);
    }

    public void clearLocalCache() {
        localCache.clear();
    }

    // 私有方法
    @CacheInvalidateAll(cacheName = PROCESS_DEFINITION_CACHE)
    private void clearProcessDefinitionCache() {
        // 清除流程定义缓存
    }

    @CacheInvalidateAll(cacheName = FORM_PERMISSION_CACHE)
    private void clearFormPermissionCache() {
        // 清除表单权限缓存
    }

    @CacheInvalidateAll(cacheName = TIMEOUT_CONFIG_CACHE)
    private void clearTimeoutConfigCache() {
        // 清除超时配置缓存
    }

    @CacheInvalidateAll(cacheName = NOTIFICATION_CONFIG_CACHE)
    private void clearNotificationConfigCache() {
        // 清除通知配置缓存
    }

    @CacheInvalidateAll(cacheName = USER_ROLES_CACHE)
    private void clearUserRolesCache() {
        // 清除用户角色缓存
    }

    @CacheInvalidateAll(cacheName = DEPARTMENT_TREE_CACHE)
    private void clearDepartmentTreeCache() {
        // 清除部门树缓存
    }

    @CacheInvalidateAll(cacheName = PROCESS_VARIABLES_CACHE)
    private void clearProcessVariablesCache() {
        // 清除流程变量缓存
    }

    private long getCacheSize(String cacheName) {
        try {
            // 这里需要根据实际的缓存实现来获取大小
            // Quarkus默认使用Caffeine缓存
            return 0; // 简化实现
        } catch (Exception e) {
            logger.warn("获取缓存大小失败: {}", cacheName, e);
            return 0;
        }
    }

    /**
     * 缓存统计信息类
     */
    public static class CacheStatistics {
        private final ConcurrentMap<String, Long> cacheSizes = new ConcurrentHashMap<>();
        private long localCacheSize;
        private long totalHits;
        private long totalMisses;
        private double hitRatio;

        public void addCacheInfo(String cacheName, long size) {
            cacheSizes.put(cacheName, size);
        }

        public ConcurrentMap<String, Long> getCacheSizes() {
            return cacheSizes;
        }

        public long getLocalCacheSize() {
            return localCacheSize;
        }

        public void setLocalCacheSize(long localCacheSize) {
            this.localCacheSize = localCacheSize;
        }

        public long getTotalHits() {
            return totalHits;
        }

        public void setTotalHits(long totalHits) {
            this.totalHits = totalHits;
        }

        public long getTotalMisses() {
            return totalMisses;
        }

        public void setTotalMisses(long totalMisses) {
            this.totalMisses = totalMisses;
        }

        public double getHitRatio() {
            return hitRatio;
        }

        public void setHitRatio(double hitRatio) {
            this.hitRatio = hitRatio;
        }

        public long getTotalCacheSize() {
            return cacheSizes.values().stream().mapToLong(Long::longValue).sum() + localCacheSize;
        }
    }
}
