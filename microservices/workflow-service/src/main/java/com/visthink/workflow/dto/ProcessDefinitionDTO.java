package com.visthink.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.visthink.workflow.entity.ProcessDefinition;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 流程定义DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "流程定义数据传输对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessDefinitionDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "租户ID")
    private String tenantId;

    @Schema(description = "流程标识", required = true, example = "leave_approval")
    @NotBlank(message = "流程标识不能为空")
    @Size(max = 100, message = "流程标识长度不能超过100个字符")
    private String processKey;

    @Schema(description = "流程名称", required = true, example = "请假审批流程")
    @NotBlank(message = "流程名称不能为空")
    @Size(max = 200, message = "流程名称长度不能超过200个字符")
    private String processName;

    @Schema(description = "流程版本", example = "1")
    private Integer processVersion;

    @Schema(description = "流程分类", example = "HR")
    @Size(max = 100, message = "流程分类长度不能超过100个字符")
    private String category;

    @Schema(description = "流程描述", example = "员工请假审批流程")
    private String description;

    @Schema(description = "设计器类型", required = true, allowableValues = {"SIMPLE", "BPMN"})
    @NotNull(message = "设计器类型不能为空")
    private ProcessDefinition.DesignerType designerType;

    @Schema(description = "流程定义JSON", required = true)
    @NotBlank(message = "流程定义不能为空")
    private String processDefinitionJson;

    @Schema(description = "BPMN XML定义")
    private String bpmnXml;

    @Schema(description = "表单定义JSON")
    private String formDefinition;

    @Schema(description = "状态", allowableValues = {"DRAFT", "ACTIVE", "SUSPENDED", "DELETED"})
    private ProcessDefinition.ProcessStatus status;

    @Schema(description = "是否最新版本")
    private Boolean isLatest;

    @Schema(description = "创建人ID")
    private Long createdBy;

    @Schema(description = "创建人姓名")
    private String createdByName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @Schema(description = "更新人ID")
    private Long updatedBy;

    @Schema(description = "更新人姓名")
    private String updatedByName;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 构造函数
    public ProcessDefinitionDTO() {
    }

    public ProcessDefinitionDTO(ProcessDefinition entity) {
        this.id = entity.getId();
        this.tenantId = entity.getTenantId();
        this.processKey = entity.getProcessKey();
        this.processName = entity.getProcessName();
        this.processVersion = entity.getProcessVersion();
        this.category = entity.getCategory();
        this.description = entity.getDescription();
        this.designerType = entity.getDesignerType();
        this.processDefinitionJson = entity.getProcessDefinitionJson();
        this.bpmnXml = entity.getBpmnXml();
        this.formDefinition = entity.getFormDefinition();
        this.status = entity.getStatus();
        this.isLatest = entity.getIsLatest();
        this.createdBy = entity.getCreatedBy();
        this.createdTime = entity.getCreatedTime();
        this.updatedBy = entity.getUpdatedBy();
        this.updatedTime = entity.getUpdatedTime();
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public Integer getProcessVersion() {
        return processVersion;
    }

    public void setProcessVersion(Integer processVersion) {
        this.processVersion = processVersion;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ProcessDefinition.DesignerType getDesignerType() {
        return designerType;
    }

    public void setDesignerType(ProcessDefinition.DesignerType designerType) {
        this.designerType = designerType;
    }

    public String getProcessDefinitionJson() {
        return processDefinitionJson;
    }

    public void setProcessDefinitionJson(String processDefinitionJson) {
        this.processDefinitionJson = processDefinitionJson;
    }

    public String getBpmnXml() {
        return bpmnXml;
    }

    public void setBpmnXml(String bpmnXml) {
        this.bpmnXml = bpmnXml;
    }

    public String getFormDefinition() {
        return formDefinition;
    }

    public void setFormDefinition(String formDefinition) {
        this.formDefinition = formDefinition;
    }

    public ProcessDefinition.ProcessStatus getStatus() {
        return status;
    }

    public void setStatus(ProcessDefinition.ProcessStatus status) {
        this.status = status;
    }

    public Boolean getIsLatest() {
        return isLatest;
    }

    public void setIsLatest(Boolean isLatest) {
        this.isLatest = isLatest;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedByName() {
        return createdByName;
    }

    public void setCreatedByName(String createdByName) {
        this.createdByName = createdByName;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedByName() {
        return updatedByName;
    }

    public void setUpdatedByName(String updatedByName) {
        this.updatedByName = updatedByName;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * 转换为实体对象
     */
    public ProcessDefinition toEntity() {
        ProcessDefinition entity = new ProcessDefinition();
        entity.setId(this.id);
        entity.setTenantId(this.tenantId);
        entity.setProcessKey(this.processKey);
        entity.setProcessName(this.processName);
        entity.setProcessVersion(this.processVersion);
        entity.setCategory(this.category);
        entity.setDescription(this.description);
        entity.setDesignerType(this.designerType);
        entity.setProcessDefinitionJson(this.processDefinitionJson);
        entity.setBpmnXml(this.bpmnXml);
        entity.setFormDefinition(this.formDefinition);
        entity.setStatus(this.status);
        entity.setIsLatest(this.isLatest);
        entity.setCreatedBy(this.createdBy);
        entity.setCreatedTime(this.createdTime);
        entity.setUpdatedBy(this.updatedBy);
        entity.setUpdatedTime(this.updatedTime);
        return entity;
    }

    @Override
    public String toString() {
        return "ProcessDefinitionDTO{" +
                "id=" + id +
                ", processKey='" + processKey + '\'' +
                ", processName='" + processName + '\'' +
                ", processVersion=" + processVersion +
                ", category='" + category + '\'' +
                ", designerType=" + designerType +
                ", status=" + status +
                ", isLatest=" + isLatest +
                '}';
    }
}
