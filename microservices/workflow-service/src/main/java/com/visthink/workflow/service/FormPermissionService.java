package com.visthink.workflow.service;

import com.visthink.workflow.entity.FormPermission;
import com.visthink.workflow.entity.TaskInstance;
import com.visthink.workflow.entity.ProcessInstance;
import com.visthink.workflow.repository.FormPermissionRepository;
import com.visthink.workflow.exception.BusinessException;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单权限服务
 * 负责处理表单字段的权限控制逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class FormPermissionService {

    private static final Logger logger = LoggerFactory.getLogger(FormPermissionService.class);

    @Inject
    FormPermissionRepository formPermissionRepository;

    @Inject
    UserService userService;

    @Inject
    RoleService roleService;

    @Inject
    ExpressionService expressionService;

    /**
     * 获取用户在指定节点的表单权限
     * 
     * @param processDefinitionId 流程定义ID
     * @param nodeKey 节点标识
     * @param userId 用户ID
     * @param processInstance 流程实例（用于表达式计算）
     * @return 表单权限映射
     */
    public Uni<Map<String, FieldPermission>> getUserFormPermissions(Long processDefinitionId, 
                                                                   String nodeKey, 
                                                                   Long userId, 
                                                                   ProcessInstance processInstance) {
        logger.debug("获取用户表单权限: processDefinitionId={}, nodeKey={}, userId={}", 
                    processDefinitionId, nodeKey, userId);

        return formPermissionRepository.findByProcessDefinitionIdAndNodeKey(processDefinitionId, nodeKey)
                .flatMap(permissions -> {
                    return userService.getUserRoles(userId)
                            .map(userRoles -> {
                                Map<String, FieldPermission> fieldPermissions = new HashMap<>();
                                
                                // 按优先级排序权限配置
                                List<FormPermission> sortedPermissions = permissions.stream()
                                        .filter(p -> p.getEnabled())
                                        .sorted((a, b) -> Integer.compare(b.getPriority(), a.getPriority()))
                                        .collect(Collectors.toList());
                                
                                // 处理每个字段的权限
                                for (FormPermission permission : sortedPermissions) {
                                    if (isPermissionApplicable(permission, userId, userRoles, processInstance)) {
                                        String fieldKey = permission.getFieldKey();
                                        
                                        // 如果字段权限还未设置，或当前权限优先级更高
                                        if (!fieldPermissions.containsKey(fieldKey)) {
                                            FieldPermission fieldPermission = createFieldPermission(permission);
                                            fieldPermissions.put(fieldKey, fieldPermission);
                                        }
                                    }
                                }
                                
                                return fieldPermissions;
                            });
                });
    }

    /**
     * 验证表单数据权限
     * 
     * @param formData 表单数据
     * @param fieldPermissions 字段权限
     * @return 验证结果
     */
    public FormValidationResult validateFormData(Map<String, Object> formData, 
                                                Map<String, FieldPermission> fieldPermissions) {
        logger.debug("验证表单数据权限");
        
        FormValidationResult result = new FormValidationResult();
        List<String> errors = new ArrayList<>();
        
        // 检查必填字段
        for (Map.Entry<String, FieldPermission> entry : fieldPermissions.entrySet()) {
            String fieldKey = entry.getKey();
            FieldPermission permission = entry.getValue();
            
            if (permission.isRequired()) {
                Object value = formData.get(fieldKey);
                if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                    errors.add(String.format("字段 %s 为必填项", permission.getFieldName() != null ? permission.getFieldName() : fieldKey));
                }
            }
        }
        
        // 检查只读字段是否被修改
        for (Map.Entry<String, Object> entry : formData.entrySet()) {
            String fieldKey = entry.getKey();
            FieldPermission permission = fieldPermissions.get(fieldKey);
            
            if (permission != null && permission.getPermissionType() == FormPermission.PermissionType.READONLY) {
                // 如果有默认值，检查是否与默认值一致
                if (permission.getDefaultValue() != null && 
                    !permission.getDefaultValue().equals(String.valueOf(entry.getValue()))) {
                    errors.add(String.format("字段 %s 为只读字段，不能修改", permission.getFieldName() != null ? permission.getFieldName() : fieldKey));
                }
            }
        }
        
        result.setValid(errors.isEmpty());
        result.setErrors(errors);
        
        return result;
    }

    /**
     * 应用表单权限到表单数据
     * 
     * @param formData 原始表单数据
     * @param fieldPermissions 字段权限
     * @return 处理后的表单数据
     */
    public Map<String, Object> applyFormPermissions(Map<String, Object> formData, 
                                                   Map<String, FieldPermission> fieldPermissions) {
        logger.debug("应用表单权限");
        
        Map<String, Object> processedData = new HashMap<>(formData);
        
        for (Map.Entry<String, FieldPermission> entry : fieldPermissions.entrySet()) {
            String fieldKey = entry.getKey();
            FieldPermission permission = entry.getValue();
            
            // 隐藏字段直接移除
            if (permission.getPermissionType() == FormPermission.PermissionType.HIDDEN) {
                processedData.remove(fieldKey);
                continue;
            }
            
            // 设置默认值
            if (permission.getDefaultValue() != null && !processedData.containsKey(fieldKey)) {
                processedData.put(fieldKey, parseDefaultValue(permission.getDefaultValue()));
            }
            
            // 只读字段使用默认值或原值
            if (permission.getPermissionType() == FormPermission.PermissionType.READONLY) {
                if (permission.getDefaultValue() != null) {
                    processedData.put(fieldKey, parseDefaultValue(permission.getDefaultValue()));
                }
            }
        }
        
        return processedData;
    }

    /**
     * 创建或更新表单权限配置
     */
    public Uni<FormPermission> saveFormPermission(FormPermission permission) {
        logger.debug("保存表单权限配置: {}", permission);
        
        if (permission.getId() == null) {
            return formPermissionRepository.persist(permission);
        } else {
            return formPermissionRepository.findById(permission.getId())
                    .flatMap(existing -> {
                        if (existing == null) {
                            return Uni.createFrom().failure(new BusinessException("表单权限配置不存在"));
                        }
                        
                        // 更新字段
                        updateFormPermissionFields(existing, permission);
                        
                        return formPermissionRepository.persist(existing);
                    });
        }
    }

    /**
     * 批量保存表单权限配置
     */
    public Uni<List<FormPermission>> batchSaveFormPermissions(List<FormPermission> permissions) {
        logger.debug("批量保存表单权限配置: {} 条", permissions.size());
        
        return formPermissionRepository.persist(permissions);
    }

    /**
     * 删除表单权限配置
     */
    public Uni<Boolean> deleteFormPermission(Long id) {
        logger.debug("删除表单权限配置: {}", id);
        
        return formPermissionRepository.deleteById(id);
    }

    /**
     * 根据流程定义ID删除所有权限配置
     */
    public Uni<Long> deleteByProcessDefinitionId(Long processDefinitionId) {
        logger.debug("删除流程定义的所有表单权限配置: {}", processDefinitionId);
        
        return formPermissionRepository.delete("processDefinitionId = ?1", processDefinitionId);
    }

    // 私有辅助方法
    private boolean isPermissionApplicable(FormPermission permission, Long userId, 
                                         List<Long> userRoles, ProcessInstance processInstance) {
        // 检查用户匹配
        if (permission.getUserId() != null && !permission.getUserId().equals(userId)) {
            return false;
        }
        
        // 检查角色匹配
        if (permission.getRoleId() != null && !userRoles.contains(permission.getRoleId())) {
            return false;
        }
        
        // 检查条件表达式
        if (permission.getConditionExpression() != null && !permission.getConditionExpression().trim().isEmpty()) {
            try {
                return expressionService.evaluateBoolean(permission.getConditionExpression(), processInstance);
            } catch (Exception e) {
                logger.warn("表达式计算失败: {}", permission.getConditionExpression(), e);
                return false;
            }
        }
        
        return true;
    }

    private FieldPermission createFieldPermission(FormPermission permission) {
        FieldPermission fieldPermission = new FieldPermission();
        fieldPermission.setFieldKey(permission.getFieldKey());
        fieldPermission.setFieldName(permission.getFieldName());
        fieldPermission.setPermissionType(permission.getPermissionType());
        fieldPermission.setRequired(permission.getRequired());
        fieldPermission.setDefaultValue(permission.getDefaultValue());
        fieldPermission.setValidationRules(permission.getValidationRules());
        return fieldPermission;
    }

    private Object parseDefaultValue(String defaultValue) {
        if (defaultValue == null) {
            return null;
        }
        
        // 尝试解析为不同类型
        if ("true".equalsIgnoreCase(defaultValue) || "false".equalsIgnoreCase(defaultValue)) {
            return Boolean.parseBoolean(defaultValue);
        }
        
        try {
            return Integer.parseInt(defaultValue);
        } catch (NumberFormatException e) {
            // 不是整数，继续尝试其他类型
        }
        
        try {
            return Double.parseDouble(defaultValue);
        } catch (NumberFormatException e) {
            // 不是数字，返回字符串
        }
        
        return defaultValue;
    }

    private void updateFormPermissionFields(FormPermission existing, FormPermission updated) {
        existing.setNodeKey(updated.getNodeKey());
        existing.setNodeName(updated.getNodeName());
        existing.setRoleId(updated.getRoleId());
        existing.setRoleName(updated.getRoleName());
        existing.setUserId(updated.getUserId());
        existing.setUserName(updated.getUserName());
        existing.setFieldKey(updated.getFieldKey());
        existing.setFieldName(updated.getFieldName());
        existing.setPermissionType(updated.getPermissionType());
        existing.setRequired(updated.getRequired());
        existing.setDefaultValue(updated.getDefaultValue());
        existing.setValidationRules(updated.getValidationRules());
        existing.setConditionExpression(updated.getConditionExpression());
        existing.setPriority(updated.getPriority());
        existing.setEnabled(updated.getEnabled());
    }

    /**
     * 字段权限类
     */
    public static class FieldPermission {
        private String fieldKey;
        private String fieldName;
        private FormPermission.PermissionType permissionType;
        private Boolean required = false;
        private String defaultValue;
        private String validationRules;

        // Getter和Setter方法
        public String getFieldKey() {
            return fieldKey;
        }

        public void setFieldKey(String fieldKey) {
            this.fieldKey = fieldKey;
        }

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public FormPermission.PermissionType getPermissionType() {
            return permissionType;
        }

        public void setPermissionType(FormPermission.PermissionType permissionType) {
            this.permissionType = permissionType;
        }

        public Boolean isRequired() {
            return required;
        }

        public void setRequired(Boolean required) {
            this.required = required;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }

        public String getValidationRules() {
            return validationRules;
        }

        public void setValidationRules(String validationRules) {
            this.validationRules = validationRules;
        }
    }

    /**
     * 表单验证结果类
     */
    public static class FormValidationResult {
        private boolean valid;
        private List<String> errors = new ArrayList<>();

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public void setErrors(List<String> errors) {
            this.errors = errors;
        }
    }
}
