# VisThink ERP - Workflow Service

工作流引擎微服务，提供双设计器工作流解决方案，支持SIMPLE和BPMN两种设计器模式。

## 🚀 功能特性

### 双设计器架构
- **SIMPLE设计器**: 仿钉钉/飞书风格的可视化流程设计器，适用于简单审批流程
- **BPMN设计器**: 基于BPMN 2.0标准的专业流程设计器，支持复杂业务场景

### 核心审批机制
- **会签**: 多人同时审批，需全部同意才能进入下一节点
- **或签**: 多人中任意一人审批即可进入下一节点
- **依次审批**: 多人按指定顺序依次审批
- **抄送**: 审批结果自动通知相关人员，支持去重

### 流程控制功能
- **驳回**: 支持驳回至发起人/上一节点/任意指定节点
- **转办**: 审批权限完全转移给他人
- **委派**: 临时委托他人审批，审批后返回原审批人
- **加签/减签**: 动态增减当前节点审批人
- **撤销**: 发起人可撤销流程
- **终止**: 管理员可强制终止流程

### 高级功能
- **表单权限**: 每个节点可配置字段的只读/编辑/隐藏权限
- **超时审批**: 自动触发超时处理机制
- **自动提醒**: 支持多种通知方式和重复提醒
- **父子流程**: 支持同步/异步子流程调用
- **分支控制**: 条件分支、并行分支、包容分支、路由分支
- **特殊节点**: 触发节点（HTTP请求/数据操作）、延迟节点

## 📋 技术架构

### 技术栈
- **框架**: Quarkus 3.22.2
- **数据库**: PostgreSQL 15
- **缓存**: Redis 7
- **任务调度**: Quartz Scheduler
- **表达式引擎**: Spring Expression Language
- **API文档**: OpenAPI 3.0

### 服务信息
- **服务名称**: workflow-service
- **服务端口**: 8088
- **数据库**: workflow_db
- **Redis DB**: 2

## 🛠️ 快速开始

### 环境要求
- JDK 17+
- Maven 3.8+
- PostgreSQL 15+
- Redis 7+

### 本地开发

1. **启动依赖服务**
```bash
# 启动PostgreSQL和Redis
docker-compose up -d postgres redis
```

2. **创建数据库**
```sql
CREATE DATABASE workflow_db;
```

3. **启动服务**
```bash
# 开发模式启动
mvn quarkus:dev

# 或者编译后启动
mvn clean package
java -jar target/quarkus-app/quarkus-run.jar
```

4. **访问服务**
- API文档: http://localhost:8088/q/swagger-ui
- 健康检查: http://localhost:8088/health
- 指标监控: http://localhost:8088/metrics

### Docker部署

1. **构建镜像**
```bash
docker build -t visthink/workflow-service:latest .
```

2. **运行容器**
```bash
docker run -d \
  --name workflow-service \
  -p 8088:8088 \
  -e DB_HOST=postgres \
  -e DB_PORT=5432 \
  -e DB_NAME=workflow_db \
  -e DB_USERNAME=visthink \
  -e DB_PASSWORD=visthink123 \
  -e REDIS_HOST=redis \
  -e REDIS_PORT=6379 \
  visthink/workflow-service:latest
```

## 📚 API文档

### 流程定义管理
- `POST /api/v1/workflow/process-definitions` - 创建流程定义
- `PUT /api/v1/workflow/process-definitions/{id}` - 更新流程定义
- `POST /api/v1/workflow/process-definitions/{id}/deploy` - 发布流程定义
- `GET /api/v1/workflow/process-definitions` - 查询流程定义列表
- `GET /api/v1/workflow/process-definitions/{id}` - 查询流程定义详情

### 流程实例管理
- `POST /api/v1/workflow/process-instances` - 启动流程实例
- `GET /api/v1/workflow/process-instances` - 查询流程实例列表
- `GET /api/v1/workflow/process-instances/{id}` - 查询流程实例详情
- `POST /api/v1/workflow/process-instances/{id}/terminate` - 终止流程实例

### 任务管理
- `GET /api/v1/workflow/tasks/todo` - 获取待办任务列表
- `GET /api/v1/workflow/tasks/done` - 获取已办任务列表
- `POST /api/v1/workflow/tasks/{taskId}/claim` - 签收任务
- `POST /api/v1/workflow/tasks/{taskId}/complete` - 完成任务
- `POST /api/v1/workflow/tasks/{taskId}/reject` - 驳回任务

## 🗄️ 数据库设计

### 核心表结构
- `wf_process_definition` - 流程定义表
- `wf_process_instance` - 流程实例表
- `wf_task_instance` - 任务实例表
- `wf_task_history` - 任务处理记录表
- `wf_process_copy` - 流程抄送表
- `wf_process_variable` - 流程变量表
- `wf_process_event` - 流程事件表

### 高级功能表
- `wf_form_permission` - 流程表单权限表
- `wf_timeout_config` - 流程超时配置表
- `wf_reminder_config` - 流程提醒配置表
- `wf_sub_process` - 流程子流程关系表
- `wf_branch_condition` - 流程分支条件表
- `wf_listener_config` - 流程监听器配置表

## ⚙️ 配置说明

### 环境变量
| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 35432 |
| DB_NAME | 数据库名称 | workflow_db |
| DB_USERNAME | 数据库用户名 | visthink |
| DB_PASSWORD | 数据库密码 | visthink123 |
| REDIS_HOST | Redis主机 | localhost |
| REDIS_PORT | Redis端口 | 6379 |
| REDIS_DB | Redis数据库 | 2 |

### 工作流配置
```yaml
workflow:
  engine:
    executor:
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 1000
  scheduler:
    timeout-check:
      enabled: true
      cron: "0 */5 * * * ?"
    reminder:
      enabled: true
      cron: "0 */10 * * * ?"
```

## 🔧 开发指南

### 添加新的节点类型
1. 在`NodeType`枚举中添加新类型
2. 实现对应的节点处理器
3. 更新前端设计器组件
4. 添加相应的测试用例

### 扩展审批机制
1. 在`ApprovalType`枚举中添加新类型
2. 实现对应的审批逻辑
3. 更新任务处理服务
4. 添加相应的API接口

### 自定义监听器
1. 实现`WorkflowListener`接口
2. 在`wf_listener_config`表中配置
3. 注册到监听器管理器
4. 测试监听器功能

## 🧪 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn verify
```

### API测试
```bash
# 使用curl测试API
curl -X GET http://localhost:8088/health

# 使用Postman导入API文档
# 访问 http://localhost:8088/q/openapi 获取OpenAPI规范
```

## 📊 监控和日志

### 健康检查
- 数据库连接检查
- Redis连接检查
- 内存使用检查
- 磁盘空间检查

### 指标监控
- 流程实例数量
- 任务处理时长
- 系统资源使用
- API调用统计

### 日志配置
- 应用日志: `/app/logs/workflow-service.log`
- 访问日志: 通过Nginx记录
- 错误日志: 自动记录到日志文件

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 📞 联系我们

- **邮箱**: <EMAIL>
- **官网**: https://www.visthink.com
- **文档**: https://docs.visthink.com
