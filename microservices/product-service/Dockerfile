####
# 商品服务 Docker 镜像构建文件
# 基于 Quarkus 多阶段构建
####

# 第一阶段：构建阶段
FROM registry.access.redhat.com/ubi8/openjdk-17:1.18 AS build

# 设置工作目录
WORKDIR /app

# 复制 Maven 配置文件
COPY pom.xml .
COPY ../shared-common/pom.xml ../shared-common/
COPY ../pom.xml ../

# 下载依赖（利用 Docker 缓存）
USER root
RUN microdnf install findutils
USER 185

# 复制源代码
COPY src ./src
COPY ../shared-common/src ../shared-common/src

# 构建应用
RUN ./mvnw clean package -DskipTests -Dquarkus.package.type=uber-jar

# 第二阶段：运行阶段
FROM registry.access.redhat.com/ubi8/openjdk-17-runtime:1.18

# 设置环境变量
ENV LANGUAGE='en_US:en'
ENV JAVA_OPTS_APPEND="-Dquarkus.http.host=0.0.0.0 -Djava.util.logging.manager=org.jboss.logmanager.LogManager"

# 创建应用目录
WORKDIR /app

# 复制构建产物
COPY --from=build /app/target/*-runner.jar app.jar

# 创建非 root 用户
USER 185

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8082/health/ready || exit 1

# 暴露端口
EXPOSE 8082

# 启动应用
ENTRYPOINT ["java", "-jar", "app.jar"]
