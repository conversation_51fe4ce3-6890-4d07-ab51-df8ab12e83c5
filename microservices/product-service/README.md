# 🛍️ 商品管理服务 (Product Service)

## 📋 服务概述

商品管理服务是Visthink ERP微服务架构中的核心业务服务，负责管理商品信息、SKU、分类、品牌等相关功能。

## ✨ 主要功能

### 🎯 核心功能
- **商品管理**: 商品的创建、更新、删除、查询
- **SKU管理**: 商品规格单元的完整生命周期管理
- **分类管理**: 商品分类的层级管理
- **品牌管理**: 商品品牌信息管理
- **状态控制**: 商品上架、下架、删除状态管理

### 🔍 高级功能
- **分页查询**: 支持灵活的分页和排序
- **搜索功能**: 基于关键词的模糊搜索
- **批量操作**: 批量上架、下架、删除
- **统计分析**: 商品数量统计和分析
- **多租户**: 完整的租户数据隔离

## 🏗️ 技术架构

### 技术栈
- **框架**: Quarkus 3.22.2
- **编程语言**: Java 17
- **数据库**: PostgreSQL (响应式)
- **ORM**: Hibernate Reactive Panache
- **API**: RESTful + OpenAPI 3.0
- **缓存**: Redis
- **监控**: Prometheus + Micrometer

### 架构模式
- **响应式编程**: 基于Uni<T>的非阻塞编程
- **分层架构**: Entity -> Repository -> Service -> Resource
- **多租户**: 基于租户ID的数据隔离
- **容器化**: Docker + Docker Compose

## 📊 数据模型

### 核心实体

#### Product (商品)
```java
- id: 主键ID
- tenantId: 租户ID
- productCode: 商品编码
- productName: 商品名称
- categoryId: 分类ID
- brandId: 品牌ID
- status: 商品状态
- salePrice: 销售价
- marketPrice: 市场价
- costPrice: 成本价
- multiSpec: 是否多规格
- isRecommend: 是否推荐
- isHot: 是否热销
- isNew: 是否新品
```

#### ProductSku (商品SKU)
```java
- id: 主键ID
- tenantId: 租户ID
- productId: 商品ID
- skuCode: SKU编码
- skuName: SKU名称
- salePrice: 销售价
- stockQuantity: 库存数量
- warningStock: 预警库存
- status: SKU状态
```

## 🚀 快速开始

### 环境要求
- JDK 17+
- Maven 3.8+
- Docker 20.10+
- PostgreSQL 13+

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd microservices/product-service
```

2. **编译项目**
```bash
mvn clean compile
```

3. **运行服务**
```bash
mvn quarkus:dev
```

4. **访问服务**
- 服务地址: http://localhost:8082
- API文档: http://localhost:8082/q/swagger-ui
- 健康检查: http://localhost:8082/health

### Docker部署

1. **构建镜像**
```bash
mvn clean package
docker build -t product-service .
```

2. **运行容器**
```bash
docker run -p 8082:8082 product-service
```

## 📚 API文档

### 主要接口

#### 商品管理
- `POST /api/products` - 创建商品
- `GET /api/products/{id}` - 获取商品详情
- `PUT /api/products/{id}` - 更新商品
- `DELETE /api/products/{id}` - 删除商品
- `GET /api/products` - 分页查询商品

#### 商品操作
- `PUT /api/products/{id}/publish` - 上架商品
- `PUT /api/products/{id}/unpublish` - 下架商品
- `PUT /api/products/batch/publish` - 批量上架
- `PUT /api/products/batch/unpublish` - 批量下架

#### 查询功能
- `GET /api/products/search` - 搜索商品
- `GET /api/products/hot` - 热销商品
- `GET /api/products/recommend` - 推荐商品
- `GET /api/products/new` - 新品列表
- `GET /api/products/statistics` - 商品统计

### 请求示例

#### 创建商品
```bash
curl -X POST http://localhost:8082/api/products \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: 1" \
  -d '{
    "productCode": "P001",
    "productName": "测试商品",
    "salePrice": 99.99,
    "status": 1
  }'
```

#### 查询商品列表
```bash
curl "http://localhost:8082/api/products?page=1&size=10" \
  -H "X-Tenant-Id: 1"
```

## ⚙️ 配置说明

### 应用配置 (application.yml)
```yaml
quarkus:
  application:
    name: product-service
  http:
    port: 8082
  datasource:
    reactive:
      url: postgresql://localhost:35432/visthink_product
```

### 环境变量
- `DB_URL`: 数据库连接地址
- `DB_USERNAME`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `REDIS_HOSTS`: Redis连接地址

## 🔧 开发指南

### 添加新功能

1. **创建实体类**
```java
@Entity
@Table(name = "new_entity")
public class NewEntity extends BaseEntity {
    // 字段定义
}
```

2. **创建Repository**
```java
@ApplicationScoped
public class NewEntityRepository implements PanacheRepository<NewEntity> {
    // 数据访问方法
}
```

3. **创建Service**
```java
@ApplicationScoped
public class NewEntityService {
    // 业务逻辑
}
```

4. **创建Resource**
```java
@Path("/api/new-entities")
public class NewEntityResource {
    // REST接口
}
```

### 数据库迁移

在 `src/main/resources/db/migration/` 目录下创建新的SQL文件：
```sql
-- V1.1__Add_New_Table.sql
CREATE TABLE new_table (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    -- 其他字段
);
```

## 🧪 测试

### 运行测试
```bash
mvn test
```

### 集成测试
```bash
mvn verify
```

## 📈 监控与运维

### 健康检查
- `/health/live` - 存活检查
- `/health/ready` - 就绪检查

### 指标监控
- `/metrics` - Prometheus指标

### 日志配置
```yaml
quarkus:
  log:
    level: INFO
    category:
      "com.visthink": DEBUG
```

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接配置是否正确

2. **编译错误**
   - 确保JDK版本为17+
   - 清理Maven缓存: `mvn clean`

3. **端口冲突**
   - 修改application.yml中的端口配置
   - 或使用环境变量: `QUARKUS_HTTP_PORT=8083`

## 📞 支持与反馈

如有问题或建议，请联系开发团队或提交Issue。

---

**🎉 感谢使用Visthink ERP商品管理服务！**
