package com.visthink.product.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 商品SKU实体
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "product_sku")
@EqualsAndHashCode(callSuper = true)
public class ProductSku extends BaseEntity {

    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;

    /**
     * 商品编码（冗余字段，便于查询）
     */
    @Column(name = "product_code", length = 100)
    private String productCode;

    /**
     * SKU编码（系统内唯一）
     */
    @Column(name = "sku_code", unique = true, nullable = false, length = 100)
    private String skuCode;

    /**
     * SKU名称
     */
    @Column(name = "sku_name", nullable = false, length = 500)
    private String skuName;

    /**
     * 规格值（JSON格式）
     */
    @Column(name = "spec_values", columnDefinition = "TEXT")
    private String specValues;

    /**
     * SKU图片URL
     */
    @Column(name = "image_url", length = 500)
    private String imageUrl;

    /**
     * 市场价
     */
    @Column(name = "market_price", precision = 10, scale = 2)
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    @Column(name = "sale_price", precision = 10, scale = 2, nullable = false)
    private BigDecimal salePrice;

    /**
     * 成本价
     */
    @Column(name = "cost_price", precision = 10, scale = 2)
    private BigDecimal costPrice;

    /**
     * 重量（克）
     */
    @Column(name = "weight")
    private Integer weight;

    /**
     * 库存数量
     */
    @Column(name = "stock_quantity", nullable = false)
    private Integer stockQuantity = 0;

    /**
     * 预警库存
     */
    @Column(name = "warning_stock")
    private Integer warningStock = 0;

    /**
     * 销量
     */
    @Column(name = "sales_count")
    private Integer salesCount = 0;

    /**
     * SKU状态：1-正常，2-停售，3-删除
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;

    /**
     * 条形码
     */
    @Column(name = "barcode", length = 100)
    private String barcode;

    /**
     * 排序权重
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 商品关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;

    /**
     * SKU状态常量
     */
    public static class Status {
        public static final int NORMAL = 1;      // 正常
        public static final int DISABLED = 2;    // 停售
        public static final int DELETED = 3;     // 删除
    }
}
