package com.visthink.product.dto.query;

import lombok.Data;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.DefaultValue;
import java.math.BigDecimal;

/**
 * 商品查询请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class ProductQueryRequest {

    /**
     * 页码（从1开始）
     */
    @QueryParam("page")
    @DefaultValue("1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @QueryParam("size")
    @DefaultValue("20")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 20;

    /**
     * 商品编码
     */
    @QueryParam("productCode")
    private String productCode;

    /**
     * 商品名称（模糊查询）
     */
    @QueryParam("productName")
    private String productName;

    /**
     * 商品分类ID
     */
    @QueryParam("categoryId")
    private Long categoryId;

    /**
     * 品牌ID
     */
    @QueryParam("brandId")
    private Long brandId;

    /**
     * 商品类型：1-实物商品，2-虚拟商品，3-服务商品
     */
    @QueryParam("productType")
    private Integer productType;

    /**
     * 商品状态：1-正常，2-停售，3-删除
     */
    @QueryParam("status")
    private Integer status;

    /**
     * 关键词（模糊查询商品名称、编码、关键词）
     */
    @QueryParam("keyword")
    private String keyword;

    /**
     * 最低价格
     */
    @QueryParam("minPrice")
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    @QueryParam("maxPrice")
    private BigDecimal maxPrice;

    /**
     * 是否推荐
     */
    @QueryParam("isRecommend")
    private Boolean isRecommend;

    /**
     * 是否热销
     */
    @QueryParam("isHot")
    private Boolean isHot;

    /**
     * 是否新品
     */
    @QueryParam("isNew")
    private Boolean isNew;

    /**
     * 是否支持多规格
     */
    @QueryParam("multiSpec")
    private Boolean multiSpec;

    /**
     * 排序字段
     * 可选值：id, productName, salePrice, salesCount, viewCount, createdAt, updatedAt
     */
    @QueryParam("sortField")
    @DefaultValue("id")
    private String sortField = "id";

    /**
     * 排序方向：asc-升序，desc-降序
     */
    @QueryParam("sortDirection")
    @DefaultValue("desc")
    private String sortDirection = "desc";

    /**
     * 创建时间开始
     */
    @QueryParam("createdAtStart")
    private String createdAtStart;

    /**
     * 创建时间结束
     */
    @QueryParam("createdAtEnd")
    private String createdAtEnd;

    /**
     * 更新时间开始
     */
    @QueryParam("updatedAtStart")
    private String updatedAtStart;

    /**
     * 更新时间结束
     */
    @QueryParam("updatedAtEnd")
    private String updatedAtEnd;

    /**
     * 获取排序字符串
     */
    public String getSortString() {
        if (sortField == null || sortField.trim().isEmpty()) {
            return "id desc";
        }
        
        String direction = "desc";
        if ("asc".equalsIgnoreCase(sortDirection)) {
            direction = "asc";
        }
        
        return sortField + " " + direction;
    }

    /**
     * 获取分页偏移量
     */
    public int getOffset() {
        return (page - 1) * size;
    }
}
