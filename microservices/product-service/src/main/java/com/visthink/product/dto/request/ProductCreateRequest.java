package com.visthink.product.dto.request;

import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品创建请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class ProductCreateRequest {

    /**
     * 商品编码（系统内唯一）
     */
    @NotBlank(message = "商品编码不能为空")
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    private String productCode;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 500, message = "商品名称长度不能超过500个字符")
    private String productName;

    /**
     * 商品简称
     */
    @Size(max = 200, message = "商品简称长度不能超过200个字符")
    private String shortName;

    /**
     * 商品分类ID
     */
    private Long categoryId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 商品类型：1-实物商品，2-虚拟商品，3-服务商品
     */
    @NotNull(message = "商品类型不能为空")
    @Min(value = 1, message = "商品类型值无效")
    @Max(value = 3, message = "商品类型值无效")
    private Integer productType = 1;

    /**
     * 商品状态：1-正常，2-停售，3-删除
     */
    @NotNull(message = "商品状态不能为空")
    @Min(value = 1, message = "商品状态值无效")
    @Max(value = 3, message = "商品状态值无效")
    private Integer status = 1;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品详情
     */
    private String detailContent;

    /**
     * 主图URL
     */
    @Size(max = 500, message = "主图URL长度不能超过500个字符")
    private String mainImageUrl;

    /**
     * 商品图片URLs（逗号分隔）
     */
    private String imageUrls;

    /**
     * 商品视频URL
     */
    @Size(max = 500, message = "视频URL长度不能超过500个字符")
    private String videoUrl;

    /**
     * 市场价
     */
    @DecimalMin(value = "0.00", message = "市场价不能小于0")
    @Digits(integer = 8, fraction = 2, message = "市场价格式不正确")
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    @DecimalMin(value = "0.00", message = "销售价不能小于0")
    @Digits(integer = 8, fraction = 2, message = "销售价格式不正确")
    private BigDecimal salePrice;

    /**
     * 成本价
     */
    @DecimalMin(value = "0.00", message = "成本价不能小于0")
    @Digits(integer = 8, fraction = 2, message = "成本价格式不正确")
    private BigDecimal costPrice;

    /**
     * 重量（克）
     */
    @Min(value = 0, message = "重量不能小于0")
    private Integer weight;

    /**
     * 长度（毫米）
     */
    @Min(value = 0, message = "长度不能小于0")
    private Integer length;

    /**
     * 宽度（毫米）
     */
    @Min(value = 0, message = "宽度不能小于0")
    private Integer width;

    /**
     * 高度（毫米）
     */
    @Min(value = 0, message = "高度不能小于0")
    private Integer height;

    /**
     * 是否支持多规格
     */
    private Boolean multiSpec = false;

    /**
     * 规格参数（JSON格式）
     */
    private String specParams;

    /**
     * 商品属性（JSON格式）
     */
    private String attributes;

    /**
     * 关键词
     */
    @Size(max = 500, message = "关键词长度不能超过500个字符")
    private String keywords;

    /**
     * 排序权重
     */
    private Integer sortOrder = 0;

    /**
     * 是否推荐
     */
    private Boolean isRecommend = false;

    /**
     * 是否热销
     */
    private Boolean isHot = false;

    /**
     * 是否新品
     */
    private Boolean isNew = false;

    /**
     * SKU列表
     */
    private List<ProductSkuCreateRequest> skuList;

    /**
     * 商品SKU创建请求
     */
    @Data
    public static class ProductSkuCreateRequest {

        /**
         * SKU编码（系统内唯一）
         */
        @NotBlank(message = "SKU编码不能为空")
        @Size(max = 100, message = "SKU编码长度不能超过100个字符")
        private String skuCode;

        /**
         * SKU名称
         */
        @NotBlank(message = "SKU名称不能为空")
        @Size(max = 500, message = "SKU名称长度不能超过500个字符")
        private String skuName;

        /**
         * 规格值（JSON格式）
         */
        private String specValues;

        /**
         * SKU图片URL
         */
        @Size(max = 500, message = "SKU图片URL长度不能超过500个字符")
        private String imageUrl;

        /**
         * 市场价
         */
        @DecimalMin(value = "0.00", message = "市场价不能小于0")
        @Digits(integer = 8, fraction = 2, message = "市场价格式不正确")
        private BigDecimal marketPrice;

        /**
         * 销售价
         */
        @NotNull(message = "销售价不能为空")
        @DecimalMin(value = "0.00", message = "销售价不能小于0")
        @Digits(integer = 8, fraction = 2, message = "销售价格式不正确")
        private BigDecimal salePrice;

        /**
         * 成本价
         */
        @DecimalMin(value = "0.00", message = "成本价不能小于0")
        @Digits(integer = 8, fraction = 2, message = "成本价格式不正确")
        private BigDecimal costPrice;

        /**
         * 重量（克）
         */
        @Min(value = 0, message = "重量不能小于0")
        private Integer weight;

        /**
         * 库存数量
         */
        @NotNull(message = "库存数量不能为空")
        @Min(value = 0, message = "库存数量不能小于0")
        private Integer stockQuantity = 0;

        /**
         * 预警库存
         */
        @Min(value = 0, message = "预警库存不能小于0")
        private Integer warningStock = 0;

        /**
         * SKU状态：1-正常，2-停售，3-删除
         */
        @NotNull(message = "SKU状态不能为空")
        @Min(value = 1, message = "SKU状态值无效")
        @Max(value = 3, message = "SKU状态值无效")
        private Integer status = 1;

        /**
         * 条形码
         */
        @Size(max = 100, message = "条形码长度不能超过100个字符")
        private String barcode;

        /**
         * 排序权重
         */
        private Integer sortOrder = 0;
    }
}
