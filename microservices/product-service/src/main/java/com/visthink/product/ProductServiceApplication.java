package com.visthink.product;

import io.quarkus.runtime.Quarkus;
import io.quarkus.runtime.QuarkusApplication;
import io.quarkus.runtime.annotations.QuarkusMain;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品服务应用程序入口
 * 
 * <AUTHOR>
 */
@Slf4j
@QuarkusMain
public class ProductServiceApplication implements QuarkusApplication {

    @Override
    public int run(String... args) throws Exception {
        log.info("🚀 商品服务启动中...");
        log.info("📦 服务名称: Product Service");
        log.info("🌐 服务端口: 8082");
        log.info("📚 API文档: http://localhost:8082/q/swagger-ui");
        log.info("❤️ 健康检查: http://localhost:8082/health");
        log.info("📊 指标监控: http://localhost:8082/metrics");
        
        Quarkus.waitForExit();
        return 0;
    }

    public static void main(String[] args) {
        Quarkus.run(ProductServiceApplication.class, args);
    }
}
