package com.visthink.product.service;

import com.visthink.common.dto.PageResult;
import com.visthink.product.dto.request.ProductCreateRequest;
import com.visthink.product.dto.request.ProductUpdateRequest;
import com.visthink.product.dto.query.ProductQueryRequest;
import com.visthink.product.dto.response.ProductResponse;
import io.smallrye.mutiny.Uni;

import java.util.List;
import java.util.Map;

/**
 * 商品管理服务接口
 *
 * <AUTHOR>
 */
public interface ProductService {

    /**
     * 创建商品
     */
    Uni<ProductResponse> createProduct(Long tenantId, ProductCreateRequest request);

    /**
     * 更新商品
     */
    Uni<ProductResponse> updateProduct(Long tenantId, Long productId, ProductUpdateRequest request);

    /**
     * 根据ID获取商品详情
     */
    Uni<ProductResponse> getProductById(Long tenantId, Long productId);

    /**
     * 根据商品编码获取商品详情
     */
    Uni<ProductResponse> getProductByCode(Long tenantId, String productCode);

    /**
     * 分页查询商品列表
     */
    Uni<PageResult<ProductResponse>> getProductList(Long tenantId, ProductQueryRequest request);

    /**
     * 搜索商品
     */
    Uni<PageResult<ProductResponse>> searchProducts(Long tenantId, String keyword, int page, int size);

    /**
     * 删除商品（软删除）
     */
    Uni<Boolean> deleteProduct(Long tenantId, Long productId);

    /**
     * 批量删除商品
     */
    Uni<Boolean> batchDeleteProducts(Long tenantId, List<Long> productIds);

    /**
     * 上架商品
     */
    Uni<Boolean> publishProduct(Long tenantId, Long productId);

    /**
     * 下架商品
     */
    Uni<Boolean> unpublishProduct(Long tenantId, Long productId);

    /**
     * 批量上架商品
     */
    Uni<Boolean> batchPublishProducts(Long tenantId, List<Long> productIds);

    /**
     * 批量下架商品
     */
    Uni<Boolean> batchUnpublishProducts(Long tenantId, List<Long> productIds);

    /**
     * 一键上架到所有平台
     */
    Uni<Boolean> publishToAllPlatforms(Long tenantId, Long productId);

    /**
     * 上架到指定平台
     */
    Uni<Boolean> publishToPlatform(Long tenantId, Long productId, String platformCode);

    /**
     * 同步商品到平台
     */
    Uni<Boolean> syncProductToPlatform(Long tenantId, Long productId, String platformCode);

    /**
     * 批量同步商品到平台
     */
    Uni<Boolean> batchSyncProductsToPlatform(Long tenantId, List<Long> productIds, String platformCode);

    /**
     * 获取商品统计信息
     */
    Uni<Map<String, Object>> getProductStatistics(Long tenantId);

    /**
     * 获取热销商品
     */
    Uni<PageResult<ProductResponse>> getHotProducts(Long tenantId, int page, int size);

    /**
     * 获取推荐商品
     */
    Uni<PageResult<ProductResponse>> getRecommendProducts(Long tenantId, int page, int size);

    /**
     * 获取新品
     */
    Uni<PageResult<ProductResponse>> getNewProducts(Long tenantId, int page, int size);

    /**
     * 根据价格区间查询商品
     */
    Uni<PageResult<ProductResponse>> getProductsByPriceRange(Long tenantId, Double minPrice, Double maxPrice, int page, int size);

    /**
     * 增加商品浏览量
     */
    Uni<Boolean> incrementViewCount(Long productId);

    /**
     * 增加商品销量
     */
    Uni<Boolean> incrementSalesCount(Long productId, Integer quantity);

    /**
     * 检查商品编码是否存在
     */
    Uni<Boolean> checkProductCodeExists(Long tenantId, String productCode);

    /**
     * 根据分类获取商品
     */
    Uni<PageResult<ProductResponse>> getProductsByCategory(Long tenantId, Long categoryId, int page, int size);

    /**
     * 获取库存不足的商品
     */
    Uni<List<ProductResponse>> getLowStockProducts(Long tenantId);

    /**
     * 批量更新商品状态
     */
    Uni<Boolean> batchUpdateProductStatus(Long tenantId, List<Long> productIds, Integer status);

    /**
     * 复制商品
     */
    Uni<ProductResponse> copyProduct(Long tenantId, Long productId, String newProductCode);

    /**
     * 导出商品数据
     */
    Uni<List<ProductResponse>> exportProducts(Long tenantId, ProductQueryRequest request);

    /**
     * 批量导入商品
     */
    Uni<Map<String, Object>> importProducts(Long tenantId, List<ProductCreateRequest> products);
}
