package com.visthink.erp.core.common.constants;

/**
 * 业务常量定义
 */
public class BusinessConstants {

    /**
     * 商品相关常量
     */
    public static class Product {
        /** 商品类型 - 实物商品 */
        public static final int TYPE_PHYSICAL = 1;
        /** 商品类型 - 虚拟商品 */
        public static final int TYPE_VIRTUAL = 2;
        /** 商品类型 - 服务商品 */
        public static final int TYPE_SERVICE = 3;

        /** 商品状态 - 正常 */
        public static final int STATUS_NORMAL = 1;
        /** 商品状态 - 停售 */
        public static final int STATUS_DISABLED = 2;
        /** 商品状态 - 删除 */
        public static final int STATUS_DELETED = 3;
    }

    /**
     * 订单相关常量
     */
    public static class Order {
        /** 订单类型 - 普通订单 */
        public static final int TYPE_NORMAL = 1;
        /** 订单类型 - 预售订单 */
        public static final int TYPE_PRESALE = 2;
        /** 订单类型 - 团购订单 */
        public static final int TYPE_GROUP = 3;

        /** 订单状态 - 待付款 */
        public static final int STATUS_PENDING_PAYMENT = 1;
        /** 订单状态 - 待发货 */
        public static final int STATUS_PENDING_SHIPMENT = 2;
        /** 订单状态 - 已发货 */
        public static final int STATUS_SHIPPED = 3;
        /** 订单状态 - 已完成 */
        public static final int STATUS_COMPLETED = 4;
        /** 订单状态 - 已取消 */
        public static final int STATUS_CANCELLED = 5;
        /** 订单状态 - 退款中 */
        public static final int STATUS_REFUNDING = 6;
        /** 订单状态 - 已退款 */
        public static final int STATUS_REFUNDED = 7;

        /** 支付状态 - 未支付 */
        public static final int PAYMENT_STATUS_UNPAID = 1;
        /** 支付状态 - 已支付 */
        public static final int PAYMENT_STATUS_PAID = 2;
        /** 支付状态 - 部分支付 */
        public static final int PAYMENT_STATUS_PARTIAL = 3;
        /** 支付状态 - 已退款 */
        public static final int PAYMENT_STATUS_REFUNDED = 4;

        /** 发货状态 - 未发货 */
        public static final int SHIPPING_STATUS_PENDING = 1;
        /** 发货状态 - 部分发货 */
        public static final int SHIPPING_STATUS_PARTIAL = 2;
        /** 发货状态 - 已发货 */
        public static final int SHIPPING_STATUS_SHIPPED = 3;
        /** 发货状态 - 已收货 */
        public static final int SHIPPING_STATUS_DELIVERED = 4;
    }

    /**
     * 库存相关常量
     */
    public static class Inventory {
        /** 库存状态 - 正常 */
        public static final int STATUS_NORMAL = 1;
        /** 库存状态 - 预警 */
        public static final int STATUS_WARNING = 2;
        /** 库存状态 - 缺货 */
        public static final int STATUS_OUT_OF_STOCK = 3;

        /** 库存调整类型 - 入库 */
        public static final int ADJUST_TYPE_IN = 1;
        /** 库存调整类型 - 出库 */
        public static final int ADJUST_TYPE_OUT = 2;
        /** 库存调整类型 - 盘点调整 */
        public static final int ADJUST_TYPE_CHECK = 3;
    }

    /**
     * 仓库相关常量
     */
    public static class Warehouse {
        /** 仓库类型 - 总仓 */
        public static final int TYPE_MAIN = 1;
        /** 仓库类型 - 分仓 */
        public static final int TYPE_BRANCH = 2;
        /** 仓库类型 - 门店仓 */
        public static final int TYPE_STORE = 3;

        /** 仓库状态 - 启用 */
        public static final int STATUS_ENABLED = 1;
        /** 仓库状态 - 禁用 */
        public static final int STATUS_DISABLED = 0;
    }

    /**
     * 平台相关常量
     */
    public static class Platform {
        /** 平台代码 - 淘宝 */
        public static final String CODE_TAOBAO = "taobao";
        /** 平台代码 - 天猫 */
        public static final String CODE_TMALL = "tmall";
        /** 平台代码 - 京东 */
        public static final String CODE_JD = "jd";
        /** 平台代码 - 拼多多 */
        public static final String CODE_PDD = "pdd";
        /** 平台代码 - 抖音 */
        public static final String CODE_DOUYIN = "douyin";
        /** 平台代码 - 快手 */
        public static final String CODE_KUAISHOU = "kuaishou";

        /** 平台状态 - 启用 */
        public static final int STATUS_ENABLED = 1;
        /** 平台状态 - 禁用 */
        public static final int STATUS_DISABLED = 0;

        /** 同步状态 - 成功 */
        public static final int SYNC_STATUS_SUCCESS = 1;
        /** 同步状态 - 失败 */
        public static final int SYNC_STATUS_FAILED = 0;
    }

    /**
     * 通用状态常量
     */
    public static class Common {
        /** 启用状态 */
        public static final int STATUS_ENABLED = 1;
        /** 禁用状态 */
        public static final int STATUS_DISABLED = 0;
        /** 删除状态 */
        public static final int STATUS_DELETED = -1;

        /** 是 */
        public static final int YES = 1;
        /** 否 */
        public static final int NO = 0;

        /** 成功 */
        public static final int SUCCESS = 1;
        /** 失败 */
        public static final int FAILED = 0;
    }

    /**
     * 默认值常量
     */
    public static class Default {
        /** 默认页码 */
        public static final int PAGE = 1;
        /** 默认页大小 */
        public static final int PAGE_SIZE = 20;
        /** 最大页大小 */
        public static final int MAX_PAGE_SIZE = 1000;

        /** 默认排序字段 */
        public static final String SORT_FIELD = "id";
        /** 默认排序方向 */
        public static final String SORT_DIRECTION = "desc";
    }
}
