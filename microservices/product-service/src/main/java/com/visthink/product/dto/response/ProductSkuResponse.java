package com.visthink.product.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品SKU响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class ProductSkuResponse {

    /**
     * SKU ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 规格值（JSON格式）
     */
    private String specValues;

    /**
     * SKU图片URL
     */
    private String imageUrl;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 重量（克）
     */
    private Integer weight;

    /**
     * 库存数量
     */
    private Integer stockQuantity;

    /**
     * 预警库存
     */
    private Integer warningStock;

    /**
     * 销量
     */
    private Integer salesCount;

    /**
     * SKU状态：1-正常，2-停售，3-删除
     */
    private Integer status;

    /**
     * SKU状态名称
     */
    private String statusName;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 是否库存不足
     */
    private Boolean isLowStock;

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "停售";
            case 3:
                return "删除";
            default:
                return "未知";
        }
    }

    /**
     * 是否库存不足
     */
    public Boolean getIsLowStock() {
        if (stockQuantity == null || warningStock == null) {
            return false;
        }
        return stockQuantity <= warningStock;
    }
}
