package com.visthink.product.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 商品实体
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "product")
@EqualsAndHashCode(callSuper = true)
public class Product extends BaseEntity {

    /**
     * 商品编码（系统内唯一）
     */
    @Column(name = "product_code", unique = true, nullable = false, length = 100)
    private String productCode;

    /**
     * 商品名称
     */
    @Column(name = "product_name", nullable = false, length = 500)
    private String productName;

    /**
     * 商品简称
     */
    @Column(name = "short_name", length = 200)
    private String shortName;

    /**
     * 商品分类ID
     */
    @Column(name = "category_id")
    private Long categoryId;

    /**
     * 品牌ID
     */
    @Column(name = "brand_id")
    private Long brandId;

    /**
     * 商品类型：1-实物商品，2-虚拟商品，3-服务商品
     */
    @Column(name = "product_type", nullable = false)
    private Integer productType = 1;

    /**
     * 商品状态：1-正常，2-停售，3-删除
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;

    /**
     * 商品描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 商品详情
     */
    @Column(name = "detail_content", columnDefinition = "TEXT")
    private String detailContent;

    /**
     * 主图URL
     */
    @Column(name = "main_image_url", length = 500)
    private String mainImageUrl;

    /**
     * 商品图片URLs（逗号分隔）
     */
    @Column(name = "image_urls", columnDefinition = "TEXT")
    private String imageUrls;

    /**
     * 商品视频URL
     */
    @Column(name = "video_url", length = 500)
    private String videoUrl;

    /**
     * 市场价
     */
    @Column(name = "market_price", precision = 10, scale = 2)
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    @Column(name = "sale_price", precision = 10, scale = 2)
    private BigDecimal salePrice;

    /**
     * 成本价
     */
    @Column(name = "cost_price", precision = 10, scale = 2)
    private BigDecimal costPrice;

    /**
     * 重量（克）
     */
    @Column(name = "weight")
    private Integer weight;

    /**
     * 长度（毫米）
     */
    @Column(name = "length")
    private Integer length;

    /**
     * 宽度（毫米）
     */
    @Column(name = "width")
    private Integer width;

    /**
     * 高度（毫米）
     */
    @Column(name = "height")
    private Integer height;

    /**
     * 是否支持多规格
     */
    @Column(name = "multi_spec", nullable = false)
    private Boolean multiSpec = false;

    /**
     * 规格参数（JSON格式）
     */
    @Column(name = "spec_params", columnDefinition = "TEXT")
    private String specParams;

    /**
     * 商品属性（JSON格式）
     */
    @Column(name = "attributes", columnDefinition = "TEXT")
    private String attributes;

    /**
     * 关键词
     */
    @Column(name = "keywords", length = 500)
    private String keywords;

    /**
     * 排序权重
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 是否推荐
     */
    @Column(name = "is_recommend", nullable = false)
    private Boolean isRecommend = false;

    /**
     * 是否热销
     */
    @Column(name = "is_hot", nullable = false)
    private Boolean isHot = false;

    /**
     * 是否新品
     */
    @Column(name = "is_new", nullable = false)
    private Boolean isNew = false;

    /**
     * 销量
     */
    @Column(name = "sales_count")
    private Integer salesCount = 0;

    /**
     * 浏览量
     */
    @Column(name = "view_count")
    private Integer viewCount = 0;

    /**
     * 商品状态常量
     */
    public static class Status {
        public static final int NORMAL = 1;      // 正常
        public static final int DISABLED = 2;    // 停售
        public static final int DELETED = 3;     // 删除
    }

    /**
     * 商品类型常量
     */
    public static class Type {
        public static final int PHYSICAL = 1;    // 实物商品
        public static final int VIRTUAL = 2;     // 虚拟商品
        public static final int SERVICE = 3;     // 服务商品
    }
}
