package com.visthink.product.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class ProductResponse {

    /**
     * 商品ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品简称
     */
    private String shortName;

    /**
     * 商品分类ID
     */
    private Long categoryId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 商品类型：1-实物商品，2-虚拟商品，3-服务商品
     */
    private Integer productType;

    /**
     * 商品状态：1-正常，2-停售，3-删除
     */
    private Integer status;

    /**
     * 商品状态名称
     */
    private String statusName;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品详情
     */
    private String detailContent;

    /**
     * 主图URL
     */
    private String mainImageUrl;

    /**
     * 商品图片URLs（逗号分隔）
     */
    private String imageUrls;

    /**
     * 商品图片URL列表
     */
    private List<String> imageUrlList;

    /**
     * 商品视频URL
     */
    private String videoUrl;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 重量（克）
     */
    private Integer weight;

    /**
     * 长度（毫米）
     */
    private Integer length;

    /**
     * 宽度（毫米）
     */
    private Integer width;

    /**
     * 高度（毫米）
     */
    private Integer height;

    /**
     * 是否支持多规格
     */
    private Boolean multiSpec;

    /**
     * 规格参数（JSON格式）
     */
    private String specParams;

    /**
     * 商品属性（JSON格式）
     */
    private String attributes;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 是否推荐
     */
    private Boolean isRecommend;

    /**
     * 是否热销
     */
    private Boolean isHot;

    /**
     * 是否新品
     */
    private Boolean isNew;

    /**
     * 销量
     */
    private Integer salesCount;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * SKU列表
     */
    private List<ProductSkuResponse> skuList;

    /**
     * 总库存数量
     */
    private Integer totalStock;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "停售";
            case 3:
                return "删除";
            default:
                return "未知";
        }
    }

    /**
     * 获取商品类型名称
     */
    public String getProductTypeName() {
        if (productType == null) {
            return "未知";
        }
        switch (productType) {
            case 1:
                return "实物商品";
            case 2:
                return "虚拟商品";
            case 3:
                return "服务商品";
            default:
                return "未知";
        }
    }
}
