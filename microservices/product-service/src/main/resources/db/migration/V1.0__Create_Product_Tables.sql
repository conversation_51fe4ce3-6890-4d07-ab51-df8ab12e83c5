-- 商品服务数据库表结构
-- 版本: V1.0
-- 描述: 创建商品相关表

-- 1. 商品主表
CREATE TABLE IF NOT EXISTS product (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    product_code VARCHAR(100) UNIQUE NOT NULL,
    product_name VARCHAR(500) NOT NULL,
    short_name VA<PERSON><PERSON><PERSON>(200),
    category_id BIGINT,
    brand_id BIGINT,
    product_type INTEGER NOT NULL DEFAULT 1,
    status INTEGER NOT NULL DEFAULT 1,
    description TEXT,
    detail_content TEXT,
    main_image_url VARCHAR(500),
    image_urls TEXT,
    video_url VARCHAR(500),
    market_price DECIMAL(10,2),
    sale_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    weight INTEGER,
    length INTEGER,
    width INTEGER,
    height INTEGER,
    multi_spec BOOLEAN NOT NULL DEFAULT FALSE,
    spec_params TEXT,
    attributes TEXT,
    keywords VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_recommend BOOLEAN NOT NULL DEFAULT FALSE,
    is_hot BOOLEAN NOT NULL DEFAULT FALSE,
    is_new BOOLEAN NOT NULL DEFAULT FALSE,
    sales_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 2. 商品SKU表
CREATE TABLE IF NOT EXISTS product_sku (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    product_code VARCHAR(100),
    sku_code VARCHAR(100) UNIQUE NOT NULL,
    sku_name VARCHAR(500) NOT NULL,
    spec_values TEXT,
    image_url VARCHAR(500),
    market_price DECIMAL(10,2),
    sale_price DECIMAL(10,2) NOT NULL,
    cost_price DECIMAL(10,2),
    weight INTEGER,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    warning_stock INTEGER DEFAULT 0,
    sales_count INTEGER DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    barcode VARCHAR(100),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 3. 商品分类表
CREATE TABLE IF NOT EXISTS product_category (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    parent_id BIGINT DEFAULT 0,
    category_name VARCHAR(200) NOT NULL,
    category_code VARCHAR(100),
    description TEXT,
    image_url VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    level INTEGER NOT NULL DEFAULT 1,
    path VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 4. 商品品牌表
CREATE TABLE IF NOT EXISTS product_brand (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    brand_name VARCHAR(200) NOT NULL,
    brand_code VARCHAR(100),
    description TEXT,
    logo_url VARCHAR(500),
    website VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 5. 商品属性表
CREATE TABLE IF NOT EXISTS product_attribute (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    category_id BIGINT,
    attr_name VARCHAR(200) NOT NULL,
    attr_code VARCHAR(100),
    attr_type INTEGER NOT NULL DEFAULT 1, -- 1:文本 2:数字 3:日期 4:选择
    attr_values TEXT, -- JSON格式存储可选值
    is_required BOOLEAN NOT NULL DEFAULT FALSE,
    is_searchable BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 6. 商品规格表
CREATE TABLE IF NOT EXISTS product_spec (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    category_id BIGINT,
    spec_name VARCHAR(200) NOT NULL,
    spec_code VARCHAR(100),
    spec_values TEXT, -- JSON格式存储规格值
    sort_order INTEGER DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 创建索引
-- 商品表索引
CREATE INDEX IF NOT EXISTS idx_product_tenant_id ON product(tenant_id);
CREATE INDEX IF NOT EXISTS idx_product_code ON product(product_code);
CREATE INDEX IF NOT EXISTS idx_product_tenant_code ON product(tenant_id, product_code);
CREATE INDEX IF NOT EXISTS idx_product_tenant_status ON product(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_product_tenant_category ON product(tenant_id, category_id);
CREATE INDEX IF NOT EXISTS idx_product_tenant_brand ON product(tenant_id, brand_id);
CREATE INDEX IF NOT EXISTS idx_product_name ON product(product_name);
CREATE INDEX IF NOT EXISTS idx_product_keywords ON product(keywords);
CREATE INDEX IF NOT EXISTS idx_product_created_at ON product(created_at);
CREATE INDEX IF NOT EXISTS idx_product_updated_at ON product(updated_at);
CREATE INDEX IF NOT EXISTS idx_product_sales_count ON product(sales_count);
CREATE INDEX IF NOT EXISTS idx_product_view_count ON product(view_count);
CREATE INDEX IF NOT EXISTS idx_product_recommend ON product(tenant_id, is_recommend, status);
CREATE INDEX IF NOT EXISTS idx_product_hot ON product(tenant_id, is_hot, status);
CREATE INDEX IF NOT EXISTS idx_product_new ON product(tenant_id, is_new, status);

-- SKU表索引
CREATE INDEX IF NOT EXISTS idx_sku_tenant_id ON product_sku(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sku_product_id ON product_sku(product_id);
CREATE INDEX IF NOT EXISTS idx_sku_code ON product_sku(sku_code);
CREATE INDEX IF NOT EXISTS idx_sku_tenant_code ON product_sku(tenant_id, sku_code);
CREATE INDEX IF NOT EXISTS idx_sku_tenant_status ON product_sku(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_sku_barcode ON product_sku(barcode);
CREATE INDEX IF NOT EXISTS idx_sku_stock ON product_sku(stock_quantity);
CREATE INDEX IF NOT EXISTS idx_sku_warning_stock ON product_sku(tenant_id, stock_quantity, warning_stock);

-- 分类表索引
CREATE INDEX IF NOT EXISTS idx_category_tenant_id ON product_category(tenant_id);
CREATE INDEX IF NOT EXISTS idx_category_parent_id ON product_category(parent_id);
CREATE INDEX IF NOT EXISTS idx_category_tenant_parent ON product_category(tenant_id, parent_id);
CREATE INDEX IF NOT EXISTS idx_category_code ON product_category(category_code);
CREATE INDEX IF NOT EXISTS idx_category_path ON product_category(path);

-- 品牌表索引
CREATE INDEX IF NOT EXISTS idx_brand_tenant_id ON product_brand(tenant_id);
CREATE INDEX IF NOT EXISTS idx_brand_code ON product_brand(brand_code);
CREATE INDEX IF NOT EXISTS idx_brand_name ON product_brand(brand_name);

-- 属性表索引
CREATE INDEX IF NOT EXISTS idx_attr_tenant_id ON product_attribute(tenant_id);
CREATE INDEX IF NOT EXISTS idx_attr_category_id ON product_attribute(category_id);
CREATE INDEX IF NOT EXISTS idx_attr_code ON product_attribute(attr_code);

-- 规格表索引
CREATE INDEX IF NOT EXISTS idx_spec_tenant_id ON product_spec(tenant_id);
CREATE INDEX IF NOT EXISTS idx_spec_category_id ON product_spec(category_id);
CREATE INDEX IF NOT EXISTS idx_spec_code ON product_spec(spec_code);

-- 外键约束
ALTER TABLE product_sku ADD CONSTRAINT fk_sku_product FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE;
ALTER TABLE product_category ADD CONSTRAINT fk_category_parent FOREIGN KEY (parent_id) REFERENCES product_category(id) ON DELETE SET NULL;

-- 添加注释
COMMENT ON TABLE product IS '商品主表';
COMMENT ON TABLE product_sku IS '商品SKU表';
COMMENT ON TABLE product_category IS '商品分类表';
COMMENT ON TABLE product_brand IS '商品品牌表';
COMMENT ON TABLE product_attribute IS '商品属性表';
COMMENT ON TABLE product_spec IS '商品规格表';

-- 插入默认数据
-- 默认分类
INSERT INTO product_category (tenant_id, category_name, category_code, description, parent_id, level, path, created_by) 
VALUES (1, '默认分类', 'DEFAULT', '系统默认分类', 0, 1, '/1', 'system') 
ON CONFLICT DO NOTHING;

-- 默认品牌
INSERT INTO product_brand (tenant_id, brand_name, brand_code, description, created_by) 
VALUES (1, '默认品牌', 'DEFAULT', '系统默认品牌', 'system') 
ON CONFLICT DO NOTHING;
