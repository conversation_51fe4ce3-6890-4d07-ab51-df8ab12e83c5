package com.visthink.platform.service;

import com.visthink.platform.entity.PlatformConfig;
import com.visthink.platform.dto.PlatformConfigDto;
import com.visthink.common.dto.PageResult;
import com.visthink.common.dto.PageRequest;
import io.smallrye.mutiny.Uni;
import java.util.List;

/**
 * 平台配置服务接口
 * 
 * 提供平台配置的业务逻辑处理
 * 
 * <AUTHOR>
 */
public interface PlatformConfigService {

    /**
     * 分页查询平台配置
     */
    Uni<PageResult<PlatformConfig>> findByPage(Long tenantId, PageRequest pageRequest, 
                                               String platformCode, Boolean enabled);

    /**
     * 根据ID查询平台配置
     */
    Uni<PlatformConfig> findById(Long tenantId, Long id);

    /**
     * 根据平台编码查询配置
     */
    Uni<PlatformConfig> findByPlatformCode(Long tenantId, String platformCode);

    /**
     * 创建平台配置
     */
    Uni<PlatformConfig> create(Long tenantId, PlatformConfigDto configDto);

    /**
     * 更新平台配置
     */
    Uni<PlatformConfig> update(Long tenantId, Long id, PlatformConfigDto configDto);

    /**
     * 删除平台配置
     */
    Uni<Boolean> delete(Long tenantId, Long id);

    /**
     * 更新配置状态
     */
    Uni<Boolean> updateStatus(Long tenantId, Long id, Boolean enabled);

    /**
     * 测试平台连接
     */
    Uni<Boolean> testConnection(Long tenantId, Long id);

    /**
     * 获取支持的平台列表
     */
    Uni<List<String>> getSupportedPlatforms();
}
