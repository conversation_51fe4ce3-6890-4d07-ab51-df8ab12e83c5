package com.visthink.platform.service;

import com.visthink.platform.dto.SyncResult;
import com.visthink.platform.dto.SyncStatus;
import io.smallrye.mutiny.Uni;

/**
 * 商品同步服务接口
 * 
 * 提供商品数据同步的核心业务逻辑
 * 支持增量同步、全量同步、任务监控等功能
 * 
 * <AUTHOR>
 */
public interface ProductSyncService {

    /**
     * 执行增量商品同步
     * 
     * 同步最近更新的商品数据
     * 
     * @return 同步结果
     */
    Uni<SyncResult> executeIncrementalSync();

    /**
     * 执行全量商品同步
     * 
     * 同步所有商品数据
     * 
     * @return 同步结果
     */
    Uni<SyncResult> executeFullSync();

    /**
     * 执行指定租户的商品同步
     * 
     * @param tenantId 租户ID
     * @param platformCode 平台编码
     * @param isFullSync 是否全量同步
     * @return 同步结果
     */
    Uni<SyncResult> executeTenantSync(Long tenantId, String platformCode, boolean isFullSync);

    /**
     * 监控并重试失败的同步任务
     * 
     * @return 重试的任务数量
     */
    Uni<Integer> monitorAndRetryFailedTasks();

    /**
     * 清理过期的同步日志
     * 
     * @param daysToKeep 保留天数
     * @return 删除的日志数量
     */
    Uni<Long> cleanupExpiredLogs(int daysToKeep);

    /**
     * 获取同步任务状态
     * 
     * @param tenantId 租户ID
     * @param platformCode 平台编码
     * @return 同步状态信息
     */
    Uni<SyncStatus> getSyncStatus(Long tenantId, String platformCode);

    /**
     * 取消正在执行的同步任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    Uni<Boolean> cancelSyncTask(Long taskId);
}




