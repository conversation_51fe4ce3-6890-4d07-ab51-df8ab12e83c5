package com.visthink.platform.dto;

import java.time.LocalDateTime;

/**
 * 同步结果DTO
 * 
 * 用于记录平台数据同步的结果信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SyncResult {
    
    /**
     * 同步是否成功
     */
    private Boolean success;
    
    /**
     * 同步的数据类型
     */
    private String dataType;
    
    /**
     * 成功同步的数量
     */
    private Integer successCount;
    
    /**
     * 失败同步的数量
     */
    private Integer failureCount;
    
    /**
     * 总数量
     */
    private Integer totalCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 同步开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 同步结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 耗时（毫秒）
     */
    private Long duration;

    // ==================== Constructors ====================

    public SyncResult() {
    }

    public SyncResult(Boolean success, String dataType) {
        this.success = success;
        this.dataType = dataType;
    }

    public static SyncResult success(String dataType, Integer count) {
        SyncResult result = new SyncResult(true, dataType);
        result.setSuccessCount(count);
        result.setTotalCount(count);
        result.setFailureCount(0);
        return result;
    }

    public static SyncResult failure(String dataType, String errorMessage) {
        SyncResult result = new SyncResult(false, dataType);
        result.setErrorMessage(errorMessage);
        result.setSuccessCount(0);
        result.setFailureCount(1);
        result.setTotalCount(1);
        return result;
    }

    // ==================== Getters and Setters ====================

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(Integer failureCount) {
        this.failureCount = failureCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    // ==================== 业务方法 ====================

    /**
     * 计算成功率
     */
    public Double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) (successCount != null ? successCount : 0) / totalCount * 100;
    }

    /**
     * 是否有错误
     */
    public boolean hasError() {
        return !Boolean.TRUE.equals(success) || 
               (failureCount != null && failureCount > 0);
    }

    @Override
    public String toString() {
        return "SyncResult{" +
                "success=" + success +
                ", dataType='" + dataType + '\'' +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                ", totalCount=" + totalCount +
                ", duration=" + duration +
                '}';
    }
}
