package com.visthink.platform.scheduler;

import com.visthink.platform.service.ProductSyncService;
import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;
import org.eclipse.microprofile.config.inject.ConfigProperty;

/**
 * 商品同步任务调度器
 * 
 * 负责定时执行商品数据同步任务
 * 支持增量同步和全量同步
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class ProductSyncScheduler {

    private static final Logger LOG = Logger.getLogger(ProductSyncScheduler.class);

    @Inject
    ProductSyncService productSyncService;

    @ConfigProperty(name = "sync.product.enabled", defaultValue = "true")
    boolean syncEnabled;

    @ConfigProperty(name = "sync.product.batch-size", defaultValue = "100")
    int batchSize;

    /**
     * 增量商品同步任务
     * 
     * 每5分钟执行一次，同步最近更新的商品数据
     */
    @Scheduled(every = "${sync.product.interval:5m}", identity = "product-incremental-sync")
    public void incrementalSync() {
        if (!syncEnabled) {
            LOG.debug("商品同步已禁用，跳过增量同步任务");
            return;
        }

        LOG.info("开始执行商品增量同步任务");
        
        try {
            productSyncService.executeIncrementalSync()
                .subscribe().with(
                    result -> {
                        LOG.infof("商品增量同步任务完成，同步结果: %s", result);
                    },
                    failure -> {
                        LOG.errorf(failure, "商品增量同步任务执行失败");
                    }
                );
        } catch (Exception e) {
            LOG.errorf(e, "启动商品增量同步任务失败");
        }
    }

    /**
     * 全量商品同步任务
     * 
     * 每天凌晨2点执行一次，进行全量商品数据同步
     */
    @Scheduled(cron = "0 0 2 * * ?", identity = "product-full-sync")
    public void fullSync() {
        if (!syncEnabled) {
            LOG.debug("商品同步已禁用，跳过全量同步任务");
            return;
        }

        LOG.info("开始执行商品全量同步任务");
        
        try {
            productSyncService.executeFullSync()
                .subscribe().with(
                    result -> {
                        LOG.infof("商品全量同步任务完成，同步结果: %s", result);
                    },
                    failure -> {
                        LOG.errorf(failure, "商品全量同步任务执行失败");
                    }
                );
        } catch (Exception e) {
            LOG.errorf(e, "启动商品全量同步任务失败");
        }
    }

    /**
     * 同步任务状态检查
     * 
     * 每10分钟检查一次同步任务状态，处理失败的任务
     */
    @Scheduled(every = "10m", identity = "sync-task-monitor")
    public void monitorSyncTasks() {
        if (!syncEnabled) {
            return;
        }

        LOG.debug("检查同步任务状态");
        
        try {
            productSyncService.monitorAndRetryFailedTasks()
                .subscribe().with(
                    result -> {
                        if (result > 0) {
                            LOG.infof("重试了 %d 个失败的同步任务", result);
                        }
                    },
                    failure -> {
                        LOG.errorf(failure, "监控同步任务状态失败");
                    }
                );
        } catch (Exception e) {
            LOG.errorf(e, "启动同步任务监控失败");
        }
    }

    /**
     * 清理过期的同步日志
     * 
     * 每天凌晨3点执行一次，清理30天前的同步日志
     */
    @Scheduled(cron = "0 0 3 * * ?", identity = "sync-log-cleanup")
    public void cleanupSyncLogs() {
        LOG.info("开始清理过期的同步日志");
        
        try {
            productSyncService.cleanupExpiredLogs(30)
                .subscribe().with(
                    deletedCount -> {
                        LOG.infof("清理了 %d 条过期的同步日志", deletedCount);
                    },
                    failure -> {
                        LOG.errorf(failure, "清理同步日志失败");
                    }
                );
        } catch (Exception e) {
            LOG.errorf(e, "启动同步日志清理任务失败");
        }
    }

    /**
     * 手动触发增量同步
     * 
     * 提供给管理接口调用的手动同步方法
     */
    public void triggerIncrementalSync() {
        LOG.info("手动触发商品增量同步");
        incrementalSync();
    }

    /**
     * 手动触发全量同步
     * 
     * 提供给管理接口调用的手动同步方法
     */
    public void triggerFullSync() {
        LOG.info("手动触发商品全量同步");
        fullSync();
    }

    /**
     * 获取调度器状态
     * 
     * @return 调度器是否启用
     */
    public boolean isEnabled() {
        return syncEnabled;
    }

    /**
     * 获取批处理大小
     * 
     * @return 批处理大小
     */
    public int getBatchSize() {
        return batchSize;
    }
}
