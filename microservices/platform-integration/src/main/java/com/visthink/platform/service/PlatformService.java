package com.visthink.platform.service;

import com.visthink.platform.dto.*;
import com.visthink.platform.dto.PlatformDtos.*;
import io.smallrye.mutiny.Uni;
import java.util.List;

/**
 * 平台服务抽象接口
 * 
 * 定义各个电商平台需要实现的通用接口
 * 支持商品、订单、库存等数据的同步操作
 * 
 * <AUTHOR>
 */
public interface PlatformService {

    /**
     * 获取平台编码
     */
    String getPlatformCode();

    /**
     * 获取平台名称
     */
    String getPlatformName();

    /**
     * 验证平台配置
     * 
     * @param config 平台配置
     * @return 验证结果
     */
    Uni<PlatformValidationResult> validateConfig(PlatformConfigDto config);

    /**
     * 获取授权URL
     * 
     * @param config 平台配置
     * @param redirectUri 回调地址
     * @return 授权URL
     */
    Uni<String> getAuthUrl(PlatformConfigDto config, String redirectUri);

    /**
     * 通过授权码获取访问令牌
     * 
     * @param config 平台配置
     * @param authCode 授权码
     * @return 令牌信息
     */
    Uni<PlatformTokenInfo> getAccessToken(PlatformConfigDto config, String authCode);

    /**
     * 刷新访问令牌
     * 
     * @param config 平台配置
     * @return 新的令牌信息
     */
    Uni<PlatformTokenInfo> refreshAccessToken(PlatformConfigDto config);

    // ==================== 商品相关接口 ====================

    /**
     * 获取商品列表
     * 
     * @param config 平台配置
     * @param request 查询请求
     * @return 商品列表
     */
    Uni<PlatformProductListResult> getProducts(PlatformConfigDto config, ProductQueryRequest request);

    /**
     * 获取商品详情
     * 
     * @param config 平台配置
     * @param platformProductId 平台商品ID
     * @return 商品详情
     */
    Uni<PlatformProductInfo> getProductDetail(PlatformConfigDto config, String platformProductId);

    /**
     * 创建商品
     * 
     * @param config 平台配置
     * @param product 商品信息
     * @return 创建结果
     */
    Uni<PlatformProductCreateResult> createProduct(PlatformConfigDto config, PlatformProductInfo product);

    /**
     * 更新商品
     * 
     * @param config 平台配置
     * @param product 商品信息
     * @return 更新结果
     */
    Uni<PlatformOperationResult> updateProduct(PlatformConfigDto config, PlatformProductInfo product);

    /**
     * 删除商品
     * 
     * @param config 平台配置
     * @param platformProductId 平台商品ID
     * @return 删除结果
     */
    Uni<PlatformOperationResult> deleteProduct(PlatformConfigDto config, String platformProductId);

    // ==================== 订单相关接口 ====================

    /**
     * 获取订单列表
     * 
     * @param config 平台配置
     * @param request 查询请求
     * @return 订单列表
     */
    Uni<PlatformOrderListResult> getOrders(PlatformConfigDto config, OrderQueryRequest request);

    /**
     * 获取订单详情
     * 
     * @param config 平台配置
     * @param platformOrderId 平台订单ID
     * @return 订单详情
     */
    Uni<PlatformOrderInfo> getOrderDetail(PlatformConfigDto config, String platformOrderId);

    /**
     * 更新订单状态
     * 
     * @param config 平台配置
     * @param platformOrderId 平台订单ID
     * @param status 订单状态
     * @return 更新结果
     */
    Uni<PlatformOperationResult> updateOrderStatus(PlatformConfigDto config, String platformOrderId, String status);

    /**
     * 发货
     * 
     * @param config 平台配置
     * @param request 发货请求
     * @return 发货结果
     */
    Uni<PlatformOperationResult> shipOrder(PlatformConfigDto config, OrderShipRequest request);

    // ==================== 库存相关接口 ====================

    /**
     * 获取库存信息
     * 
     * @param config 平台配置
     * @param platformProductId 平台商品ID
     * @return 库存信息
     */
    Uni<PlatformInventoryInfo> getInventory(PlatformConfigDto config, String platformProductId);

    /**
     * 更新库存
     * 
     * @param config 平台配置
     * @param request 库存更新请求
     * @return 更新结果
     */
    Uni<PlatformOperationResult> updateInventory(PlatformConfigDto config, InventoryUpdateRequest request);

    // ==================== 店铺相关接口 ====================

    /**
     * 获取店铺信息
     * 
     * @param config 平台配置
     * @return 店铺信息
     */
    Uni<PlatformShopInfo> getShopInfo(PlatformConfigDto config);

    /**
     * 获取店铺统计信息
     * 
     * @param config 平台配置
     * @param request 统计请求
     * @return 统计信息
     */
    Uni<PlatformShopStatistics> getShopStatistics(PlatformConfigDto config, StatisticsRequest request);

    // ==================== 类目相关接口 ====================

    /**
     * 获取平台类目列表
     * 
     * @param config 平台配置
     * @param parentId 父类目ID（可选）
     * @return 类目列表
     */
    Uni<List<PlatformCategoryInfo>> getCategories(PlatformConfigDto config, String parentId);

    /**
     * 获取类目属性
     * 
     * @param config 平台配置
     * @param categoryId 类目ID
     * @return 类目属性列表
     */
    Uni<List<PlatformCategoryAttribute>> getCategoryAttributes(PlatformConfigDto config, String categoryId);

    // ==================== 物流相关接口 ====================

    /**
     * 获取物流公司列表
     * 
     * @param config 平台配置
     * @return 物流公司列表
     */
    Uni<List<PlatformLogisticsCompany>> getLogisticsCompanies(PlatformConfigDto config);

    /**
     * 查询物流轨迹
     * 
     * @param config 平台配置
     * @param request 物流查询请求
     * @return 物流轨迹
     */
    Uni<PlatformLogisticsTrace> getLogisticsTrace(PlatformConfigDto config, LogisticsQueryRequest request);

    // ==================== 通用接口 ====================

    /**
     * 测试连接
     * 
     * @param config 平台配置
     * @return 测试结果
     */
    Uni<PlatformTestResult> testConnection(PlatformConfigDto config);

    /**
     * 获取平台支持的功能列表
     * 
     * @return 功能列表
     */
    List<String> getSupportedFeatures();

    /**
     * 获取API限制信息
     * 
     * @param config 平台配置
     * @return API限制信息
     */
    Uni<PlatformApiLimits> getApiLimits(PlatformConfigDto config);
}
