package com.visthink.platform.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 平台集成相关的DTO类集合
 * 
 * 为了简化开发，将多个简单的DTO类放在一个文件中
 * 
 * <AUTHOR>
 */
public class PlatformDtos {

    // 商品查询请求
    public static class ProductQueryRequest {
        private Integer page = 1;
        private Integer pageSize = 20;
        private String keyword;
        private String status;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        
        // Getters and Setters
        public Integer getPage() { return page; }
        public void setPage(Integer page) { this.page = page; }
        public Integer getPageSize() { return pageSize; }
        public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
        public String getKeyword() { return keyword; }
        public void setKeyword(String keyword) { this.keyword = keyword; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
    }

    // 订单查询请求
    public static class OrderQueryRequest {
        private Integer page = 1;
        private Integer pageSize = 20;
        private String status;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        
        // Getters and Setters
        public Integer getPage() { return page; }
        public void setPage(Integer page) { this.page = page; }
        public Integer getPageSize() { return pageSize; }
        public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
    }

    // 平台商品信息
    public static class PlatformProductInfo {
        private String platformProductId;
        private String title;
        private String description;
        private BigDecimal price;
        private String status;
        private String categoryId;
        private List<String> images;
        
        // Getters and Setters
        public String getPlatformProductId() { return platformProductId; }
        public void setPlatformProductId(String platformProductId) { this.platformProductId = platformProductId; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getCategoryId() { return categoryId; }
        public void setCategoryId(String categoryId) { this.categoryId = categoryId; }
        public List<String> getImages() { return images; }
        public void setImages(List<String> images) { this.images = images; }
    }

    // 平台订单信息
    public static class PlatformOrderInfo {
        private String platformOrderId;
        private String status;
        private BigDecimal totalAmount;
        private String buyerName;
        private LocalDateTime createTime;
        
        // Getters and Setters
        public String getPlatformOrderId() { return platformOrderId; }
        public void setPlatformOrderId(String platformOrderId) { this.platformOrderId = platformOrderId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        public String getBuyerName() { return buyerName; }
        public void setBuyerName(String buyerName) { this.buyerName = buyerName; }
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    }

    // 平台库存信息
    public static class PlatformInventoryInfo {
        private String platformProductId;
        private Integer quantity;
        
        // Getters and Setters
        public String getPlatformProductId() { return platformProductId; }
        public void setPlatformProductId(String platformProductId) { this.platformProductId = platformProductId; }
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
    }

    // 平台操作结果
    public static class PlatformOperationResult {
        private boolean success;
        private String message;
        private String errorCode;
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
    }

    // 平台测试结果
    public static class PlatformTestResult {
        private boolean success;
        private String message;
        private Long responseTime;
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public Long getResponseTime() { return responseTime; }
        public void setResponseTime(Long responseTime) { this.responseTime = responseTime; }
    }

    // 其他简单的DTO类...
    public static class PlatformProductListResult {
        private Integer totalCount;
        private List<PlatformProductInfo> products;
        private boolean hasMore;
        
        // Getters and Setters
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        public List<PlatformProductInfo> getProducts() { return products; }
        public void setProducts(List<PlatformProductInfo> products) { this.products = products; }
        public boolean isHasMore() { return hasMore; }
        public void setHasMore(boolean hasMore) { this.hasMore = hasMore; }
    }

    public static class PlatformOrderListResult {
        private Integer totalCount;
        private List<PlatformOrderInfo> orders;
        private boolean hasMore;
        
        // Getters and Setters
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        public List<PlatformOrderInfo> getOrders() { return orders; }
        public void setOrders(List<PlatformOrderInfo> orders) { this.orders = orders; }
        public boolean isHasMore() { return hasMore; }
        public void setHasMore(boolean hasMore) { this.hasMore = hasMore; }
    }

    public static class PlatformProductCreateResult {
        private boolean success;
        private String platformProductId;
        private String message;
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getPlatformProductId() { return platformProductId; }
        public void setPlatformProductId(String platformProductId) { this.platformProductId = platformProductId; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    public static class OrderShipRequest {
        private String platformOrderId;
        private String logisticsCompany;
        private String trackingNumber;
        
        // Getters and Setters
        public String getPlatformOrderId() { return platformOrderId; }
        public void setPlatformOrderId(String platformOrderId) { this.platformOrderId = platformOrderId; }
        public String getLogisticsCompany() { return logisticsCompany; }
        public void setLogisticsCompany(String logisticsCompany) { this.logisticsCompany = logisticsCompany; }
        public String getTrackingNumber() { return trackingNumber; }
        public void setTrackingNumber(String trackingNumber) { this.trackingNumber = trackingNumber; }
    }

    public static class InventoryUpdateRequest {
        private String platformProductId;
        private Integer quantity;
        
        // Getters and Setters
        public String getPlatformProductId() { return platformProductId; }
        public void setPlatformProductId(String platformProductId) { this.platformProductId = platformProductId; }
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
    }

    public static class PlatformShopInfo {
        private String shopId;
        private String shopName;
        private String shopType;
        
        // Getters and Setters
        public String getShopId() { return shopId; }
        public void setShopId(String shopId) { this.shopId = shopId; }
        public String getShopName() { return shopName; }
        public void setShopName(String shopName) { this.shopName = shopName; }
        public String getShopType() { return shopType; }
        public void setShopType(String shopType) { this.shopType = shopType; }
    }

    public static class PlatformShopStatistics {
        private Integer orderCount;
        private BigDecimal salesAmount;
        
        // Getters and Setters
        public Integer getOrderCount() { return orderCount; }
        public void setOrderCount(Integer orderCount) { this.orderCount = orderCount; }
        public BigDecimal getSalesAmount() { return salesAmount; }
        public void setSalesAmount(BigDecimal salesAmount) { this.salesAmount = salesAmount; }
    }

    public static class StatisticsRequest {
        private String startDate;
        private String endDate;
        
        // Getters and Setters
        public String getStartDate() { return startDate; }
        public void setStartDate(String startDate) { this.startDate = startDate; }
        public String getEndDate() { return endDate; }
        public void setEndDate(String endDate) { this.endDate = endDate; }
    }

    public static class PlatformCategoryInfo {
        private String categoryId;
        private String categoryName;
        private String parentId;
        
        // Getters and Setters
        public String getCategoryId() { return categoryId; }
        public void setCategoryId(String categoryId) { this.categoryId = categoryId; }
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        public String getParentId() { return parentId; }
        public void setParentId(String parentId) { this.parentId = parentId; }
    }

    public static class PlatformCategoryAttribute {
        private String attributeId;
        private String attributeName;
        private String attributeType;
        
        // Getters and Setters
        public String getAttributeId() { return attributeId; }
        public void setAttributeId(String attributeId) { this.attributeId = attributeId; }
        public String getAttributeName() { return attributeName; }
        public void setAttributeName(String attributeName) { this.attributeName = attributeName; }
        public String getAttributeType() { return attributeType; }
        public void setAttributeType(String attributeType) { this.attributeType = attributeType; }
    }

    public static class PlatformLogisticsCompany {
        private String companyCode;
        private String companyName;
        
        // Getters and Setters
        public String getCompanyCode() { return companyCode; }
        public void setCompanyCode(String companyCode) { this.companyCode = companyCode; }
        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }
    }

    public static class PlatformLogisticsTrace {
        private String trackingNumber;
        private List<String> traces;
        
        // Getters and Setters
        public String getTrackingNumber() { return trackingNumber; }
        public void setTrackingNumber(String trackingNumber) { this.trackingNumber = trackingNumber; }
        public List<String> getTraces() { return traces; }
        public void setTraces(List<String> traces) { this.traces = traces; }
    }

    public static class LogisticsQueryRequest {
        private String trackingNumber;
        private String companyCode;
        
        // Getters and Setters
        public String getTrackingNumber() { return trackingNumber; }
        public void setTrackingNumber(String trackingNumber) { this.trackingNumber = trackingNumber; }
        public String getCompanyCode() { return companyCode; }
        public void setCompanyCode(String companyCode) { this.companyCode = companyCode; }
    }

    public static class PlatformApiLimits {
        private Integer dailyLimit;
        private Integer hourlyLimit;
        private Integer minuteLimit;
        private Integer remainingDaily;
        private Integer remainingHourly;
        private Integer remainingMinute;
        
        // Getters and Setters
        public Integer getDailyLimit() { return dailyLimit; }
        public void setDailyLimit(Integer dailyLimit) { this.dailyLimit = dailyLimit; }
        public Integer getHourlyLimit() { return hourlyLimit; }
        public void setHourlyLimit(Integer hourlyLimit) { this.hourlyLimit = hourlyLimit; }
        public Integer getMinuteLimit() { return minuteLimit; }
        public void setMinuteLimit(Integer minuteLimit) { this.minuteLimit = minuteLimit; }
        public Integer getRemainingDaily() { return remainingDaily; }
        public void setRemainingDaily(Integer remainingDaily) { this.remainingDaily = remainingDaily; }
        public Integer getRemainingHourly() { return remainingHourly; }
        public void setRemainingHourly(Integer remainingHourly) { this.remainingHourly = remainingHourly; }
        public Integer getRemainingMinute() { return remainingMinute; }
        public void setRemainingMinute(Integer remainingMinute) { this.remainingMinute = remainingMinute; }
    }
}
