package com.visthink.platform.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 同步任务实体
 *
 * 记录平台数据同步任务的执行情况
 * 支持商品同步、订单同步、库存同步等不同类型的任务
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "sync_task")
public class SyncTask extends BaseEntity {

    /**
     * 任务编号
     */
    @Column(name = "task_no", nullable = false, unique = true, length = 100)
    public String taskNo;

    /**
     * 平台编码
     */
    @Column(name = "platform_code", nullable = false, length = 50)
    public String platformCode;

    /**
     * 同步类型：1-商品同步 2-订单同步 3-库存同步 4-用户同步
     */
    @Column(name = "sync_type", nullable = false)
    public Integer syncType;

    /**
     * 同步方向：1-从平台拉取 2-推送到平台 3-双向同步
     */
    @Column(name = "sync_direction", nullable = false)
    public Integer syncDirection;

    /**
     * 任务状态：1-待执行 2-执行中 3-成功 4-失败 5-部分成功
     */
    @Column(name = "status", nullable = false)
    public Integer status = Status.PENDING;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    public LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    public LocalDateTime endTime;

    /**
     * 总记录数
     */
    @Column(name = "total_count")
    public Integer totalCount = 0;

    /**
     * 成功记录数
     */
    @Column(name = "success_count")
    public Integer successCount = 0;

    /**
     * 失败记录数
     */
    @Column(name = "failed_count")
    public Integer failedCount = 0;

    /**
     * 跳过记录数
     */
    @Column(name = "skipped_count")
    public Integer skippedCount = 0;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    public String errorMessage;

    /**
     * 同步参数（JSON格式）
     */
    @Column(name = "sync_params", columnDefinition = "TEXT")
    public String syncParams;

    /**
     * 同步结果（JSON格式）
     */
    @Column(name = "sync_result", columnDefinition = "TEXT")
    public String syncResult;

    /**
     * 是否自动任务
     */
    @Column(name = "auto_task", nullable = false)
    public Boolean autoTask = false;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    public Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Column(name = "max_retry_count")
    public Integer maxRetryCount = 3;

    /**
     * 下次重试时间
     */
    @Column(name = "next_retry_time")
    public LocalDateTime nextRetryTime;

    // Getters and Setters
    public String getTaskNo() {
        return taskNo;
    }

    public void setTaskNo(String taskNo) {
        this.taskNo = taskNo;
    }

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public Integer getSyncType() {
        return syncType;
    }

    public void setSyncType(Integer syncType) {
        this.syncType = syncType;
    }

    public Integer getSyncDirection() {
        return syncDirection;
    }

    public void setSyncDirection(Integer syncDirection) {
        this.syncDirection = syncDirection;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(Integer failedCount) {
        this.failedCount = failedCount;
    }

    public Integer getSkippedCount() {
        return skippedCount;
    }

    public void setSkippedCount(Integer skippedCount) {
        this.skippedCount = skippedCount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getSyncParams() {
        return syncParams;
    }

    public void setSyncParams(String syncParams) {
        this.syncParams = syncParams;
    }

    public String getSyncResult() {
        return syncResult;
    }

    public void setSyncResult(String syncResult) {
        this.syncResult = syncResult;
    }

    public Boolean getAutoTask() {
        return autoTask;
    }

    public void setAutoTask(Boolean autoTask) {
        this.autoTask = autoTask;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getMaxRetryCount() {
        return maxRetryCount;
    }

    public void setMaxRetryCount(Integer maxRetryCount) {
        this.maxRetryCount = maxRetryCount;
    }

    public LocalDateTime getNextRetryTime() {
        return nextRetryTime;
    }

    public void setNextRetryTime(LocalDateTime nextRetryTime) {
        this.nextRetryTime = nextRetryTime;
    }

    /**
     * 任务状态常量
     */
    public static class Status {
        public static final int PENDING = 1;        // 待执行
        public static final int RUNNING = 2;        // 执行中
        public static final int SUCCESS = 3;        // 成功
        public static final int FAILED = 4;         // 失败
        public static final int PARTIAL_SUCCESS = 5; // 部分成功
    }

    /**
     * 同步类型常量
     */
    public static class SyncType {
        public static final int PRODUCT = 1;  // 商品同步
        public static final int ORDER = 2;    // 订单同步
        public static final int INVENTORY = 3; // 库存同步
        public static final int USER = 4;     // 用户同步
    }

    /**
     * 同步方向常量
     */
    public static class SyncDirection {
        public static final int PULL = 1;      // 从平台拉取
        public static final int PUSH = 2;      // 推送到平台
        public static final int BIDIRECTIONAL = 3; // 双向同步
    }

    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (double) (successCount != null ? successCount : 0) / totalCount * 100;
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return status == Status.FAILED &&
               retryCount < maxRetryCount &&
               (nextRetryTime == null || nextRetryTime.isBefore(LocalDateTime.now()));
    }

    /**
     * 检查任务是否完成
     */
    public boolean isCompleted() {
        return status == Status.SUCCESS || status == Status.FAILED || status == Status.PARTIAL_SUCCESS;
    }
}
