package com.visthink.platform.client;

import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.Map;

/**
 * 淘宝HTTP客户端接口
 * 
 * 使用Quarkus REST Client进行HTTP调用
 * 支持响应式编程和自动重试
 * 
 * <AUTHOR>
 */
@RegisterRestClient(configKey = "taobao-api")
public interface TaobaoHttpClient {

    /**
     * 调用淘宝API（POST方式）
     * 
     * @param params 请求参数
     * @return API响应结果
     */
    @POST
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Map<String, Object>> callApi(Map<String, String> params);

    /**
     * 调用淘宝API（GET方式）
     * 
     * @param method API方法名
     * @param appKey 应用Key
     * @param timestamp 时间戳
     * @param format 响应格式
     * @param v API版本
     * @param signMethod 签名方法
     * @param sign 签名
     * @param session 会话令牌
     * @param otherParams 其他参数
     * @return API响应结果
     */
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Map<String, Object>> callApiGet(
            @QueryParam("method") String method,
            @QueryParam("app_key") String appKey,
            @QueryParam("timestamp") String timestamp,
            @QueryParam("format") String format,
            @QueryParam("v") String v,
            @QueryParam("sign_method") String signMethod,
            @QueryParam("sign") String sign,
            @QueryParam("session") String session,
            @QueryParam("") Map<String, String> otherParams
    );

    /**
     * 获取访问令牌
     * 
     * @param grantType 授权类型
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @param code 授权码
     * @param redirectUri 回调地址
     * @return 令牌响应
     */
    @POST
    @Path("/token")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Map<String, Object>> getAccessToken(
            @FormParam("grant_type") String grantType,
            @FormParam("client_id") String clientId,
            @FormParam("client_secret") String clientSecret,
            @FormParam("code") String code,
            @FormParam("redirect_uri") String redirectUri
    );

    /**
     * 刷新访问令牌
     * 
     * @param grantType 授权类型
     * @param refreshToken 刷新令牌
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @return 令牌响应
     */
    @POST
    @Path("/token")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    @Produces(MediaType.APPLICATION_JSON)
    Uni<Map<String, Object>> refreshAccessToken(
            @FormParam("grant_type") String grantType,
            @FormParam("refresh_token") String refreshToken,
            @FormParam("client_id") String clientId,
            @FormParam("client_secret") String clientSecret
    );
}
