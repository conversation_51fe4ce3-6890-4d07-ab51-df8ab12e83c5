package com.visthink.platform.client;

import com.visthink.platform.dto.PlatformConfigDto;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.logging.Logger;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 淘宝API客户端工具类
 * 
 * 负责处理淘宝开放平台的API调用，包括：
 * - 签名生成和验证
 * - 请求参数构建
 * - 响应数据解析
 * - 错误处理和重试机制
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class TaobaoApiClient {

    private static final Logger LOG = Logger.getLogger(TaobaoApiClient.class);

    @ConfigProperty(name = "platform.taobao.gateway-url")
    String gatewayUrl;

    @ConfigProperty(name = "platform.taobao.sandbox-url")
    String sandboxUrl;

    @ConfigProperty(name = "platform.taobao.timeout")
    String timeout;

    @ConfigProperty(name = "platform.taobao.retry-count")
    int retryCount;

    /**
     * 构建淘宝API请求参数
     * 
     * @param config 平台配置
     * @param method API方法名
     * @param bizParams 业务参数
     * @return 完整的请求参数Map
     */
    public Map<String, String> buildRequestParams(PlatformConfigDto config, String method, Map<String, Object> bizParams) {
        Map<String, String> params = new HashMap<>();
        
        // 公共参数
        params.put("app_key", config.getAppId());
        params.put("method", method);
        params.put("timestamp", getCurrentTimestamp());
        params.put("format", "json");
        params.put("v", "2.0");
        params.put("sign_method", "md5");
        
        // 添加访问令牌（如果存在）
        if (config.getAccessToken() != null && !config.getAccessToken().isEmpty()) {
            params.put("session", config.getAccessToken());
        }
        
        // 添加业务参数
        if (bizParams != null && !bizParams.isEmpty()) {
            for (Map.Entry<String, Object> entry : bizParams.entrySet()) {
                if (entry.getValue() != null) {
                    params.put(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        
        // 生成签名
        String sign = generateSign(params, config.getAppSecret());
        params.put("sign", sign);
        
        return params;
    }

    /**
     * 生成淘宝API签名
     * 
     * @param params 请求参数
     * @param appSecret 应用密钥
     * @return MD5签名
     */
    public String generateSign(Map<String, String> params, String appSecret) {
        try {
            // 1. 参数排序
            TreeMap<String, String> sortedParams = new TreeMap<>(params);
            
            // 2. 拼接参数字符串
            StringBuilder sb = new StringBuilder(appSecret);
            for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
                if (!"sign".equals(entry.getKey()) && entry.getValue() != null) {
                    sb.append(entry.getKey()).append(entry.getValue());
                }
            }
            sb.append(appSecret);
            
            // 3. MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(sb.toString().getBytes("UTF-8"));
            
            // 4. 转换为大写十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString().toUpperCase();
            
        } catch (Exception e) {
            LOG.errorf(e, "生成淘宝API签名失败");
            throw new RuntimeException("签名生成失败", e);
        }
    }

    /**
     * 获取当前时间戳（淘宝API格式）
     * 
     * @return 格式化的时间戳字符串
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取API网关URL
     * 
     * @param config 平台配置
     * @return 网关URL
     */
    public String getGatewayUrl(PlatformConfigDto config) {
        return config.getSandboxMode() != null && config.getSandboxMode() ? sandboxUrl : gatewayUrl;
    }

    /**
     * 验证API响应
     * 
     * @param response API响应数据
     * @return 是否成功
     */
    public boolean isResponseSuccess(Map<String, Object> response) {
        if (response == null) {
            return false;
        }
        
        // 检查是否有错误响应
        if (response.containsKey("error_response")) {
            Map<String, Object> errorResponse = (Map<String, Object>) response.get("error_response");
            LOG.warnf("淘宝API调用失败: %s - %s", 
                errorResponse.get("code"), errorResponse.get("msg"));
            return false;
        }
        
        return true;
    }

    /**
     * 提取API响应中的错误信息
     * 
     * @param response API响应数据
     * @return 错误信息
     */
    public String extractErrorMessage(Map<String, Object> response) {
        if (response != null && response.containsKey("error_response")) {
            Map<String, Object> errorResponse = (Map<String, Object>) response.get("error_response");
            String code = (String) errorResponse.get("code");
            String msg = (String) errorResponse.get("msg");
            String subCode = (String) errorResponse.get("sub_code");
            String subMsg = (String) errorResponse.get("sub_msg");
            
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("错误码: ").append(code);
            if (msg != null) {
                errorMsg.append(", 错误信息: ").append(msg);
            }
            if (subCode != null) {
                errorMsg.append(", 子错误码: ").append(subCode);
            }
            if (subMsg != null) {
                errorMsg.append(", 子错误信息: ").append(subMsg);
            }
            
            return errorMsg.toString();
        }
        
        return "未知错误";
    }

    /**
     * 构建OAuth2.0授权URL
     * 
     * @param config 平台配置
     * @param redirectUri 回调地址
     * @param state 状态参数
     * @return 授权URL
     */
    public String buildAuthUrl(PlatformConfigDto config, String redirectUri, String state) {
        StringBuilder authUrl = new StringBuilder();
        
        if (config.getSandboxMode() != null && config.getSandboxMode()) {
            authUrl.append("https://oauth.tbsandbox.com/authorize");
        } else {
            authUrl.append("https://oauth.taobao.com/authorize");
        }
        
        authUrl.append("?response_type=code");
        authUrl.append("&client_id=").append(config.getAppId());
        authUrl.append("&redirect_uri=").append(redirectUri);
        authUrl.append("&state=").append(state != null ? state : System.currentTimeMillis());
        
        return authUrl.toString();
    }

    /**
     * 检查访问令牌是否过期
     * 
     * @param config 平台配置
     * @return 是否过期
     */
    public boolean isTokenExpired(PlatformConfigDto config) {
        if (config.getTokenExpiresAt() == null) {
            return true;
        }
        
        return config.getTokenExpiresAt().isBefore(LocalDateTime.now());
    }

    /**
     * 构建商品查询的业务参数
     * 
     * @param page 页码
     * @param pageSize 页大小
     * @param keyword 关键词
     * @return 业务参数Map
     */
    public Map<String, Object> buildProductQueryParams(Integer page, Integer pageSize, String keyword) {
        Map<String, Object> params = new HashMap<>();
        
        if (page != null && page > 0) {
            params.put("page_no", page);
        }
        
        if (pageSize != null && pageSize > 0) {
            params.put("page_size", Math.min(pageSize, 200)); // 淘宝API限制最大200
        }
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            params.put("q", keyword.trim());
        }
        
        // 默认查询在售商品
        params.put("approve_status", "onsale");
        
        return params;
    }

    /**
     * 解析商品列表响应
     * 
     * @param response API响应
     * @return 商品列表数据
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> parseProductListResponse(Map<String, Object> response) {
        try {
            // 根据不同的API方法解析响应结构
            if (response.containsKey("items_onsale_get_response")) {
                Map<String, Object> data = (Map<String, Object>) response.get("items_onsale_get_response");
                if (data.containsKey("items")) {
                    Map<String, Object> items = (Map<String, Object>) data.get("items");
                    if (items.containsKey("item")) {
                        Object itemData = items.get("item");
                        if (itemData instanceof List) {
                            return (List<Map<String, Object>>) itemData;
                        } else if (itemData instanceof Map) {
                            return Arrays.asList((Map<String, Object>) itemData);
                        }
                    }
                }
            }
            
            return new ArrayList<>();
            
        } catch (Exception e) {
            LOG.errorf(e, "解析商品列表响应失败");
            return new ArrayList<>();
        }
    }
}
