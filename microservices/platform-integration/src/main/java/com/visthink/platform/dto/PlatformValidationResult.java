package com.visthink.platform.dto;

/**
 * 平台配置验证结果
 * 
 * <AUTHOR>
 */
public class PlatformValidationResult {
    
    private boolean valid;
    private String message;
    private String errorCode;
    
    public boolean isValid() {
        return valid;
    }
    
    public void setValid(boolean valid) {
        this.valid = valid;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}
