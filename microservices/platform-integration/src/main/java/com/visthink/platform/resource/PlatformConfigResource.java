package com.visthink.platform.resource;

import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.platform.entity.PlatformConfig;
import com.visthink.platform.service.PlatformConfigService;
import com.visthink.platform.dto.PlatformConfigDto;
import com.visthink.platform.dto.RestResult;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import org.jboss.logging.Logger;

import java.util.List;

/**
 * 平台配置管理API
 *
 * 提供平台配置的增删改查功能
 * 支持多租户数据隔离
 *
 * <AUTHOR>
 */
@Path("/api/platform-config")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "平台配置管理", description = "电商平台配置管理相关API")
public class PlatformConfigResource {

    private static final Logger LOG = Logger.getLogger(PlatformConfigResource.class);

    @Inject
    PlatformConfigService platformConfigService;

    /**
     * 获取平台配置列表
     */
    @GET
    @Operation(summary = "获取平台配置列表", description = "分页查询租户的平台配置列表")
    public Uni<RestResult<PageResult<PlatformConfig>>> list(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("20") int size,
            @QueryParam("platformCode") String platformCode,
            @QueryParam("enabled") Boolean enabled) {

        LOG.infof("查询平台配置列表，租户ID: %d, 页码: %d, 页大小: %d", tenantId, page, size);

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        PageRequest pageRequest = new PageRequest(page, size);

        return platformConfigService.findByPage(tenantId, pageRequest, platformCode, enabled)
            .map(RestResult::success)
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "查询平台配置列表失败");
                return RestResult.error("QUERY_FAILED", "查询失败: " + throwable.getMessage());
            });
    }

    /**
     * 根据ID获取平台配置
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "获取平台配置详情", description = "根据ID获取平台配置的详细信息")
    public Uni<RestResult<PlatformConfig>> getById(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            @PathParam("id") Long id) {

        LOG.infof("获取平台配置详情，租户ID: %d, 配置ID: %d", tenantId, id);

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        return platformConfigService.findById(tenantId, id)
            .map(config -> {
                if (config == null) {
                    return RestResult.<PlatformConfig>error("CONFIG_NOT_FOUND", "平台配置不存在");
                }
                return RestResult.success(config);
            })
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "获取平台配置详情失败");
                return RestResult.<PlatformConfig>error("QUERY_FAILED", "查询失败: " + throwable.getMessage());
            });
    }

    /**
     * 根据平台编码获取配置
     */
    @GET
    @Path("/platform/{platformCode}")
    @Operation(summary = "根据平台编码获取配置", description = "根据平台编码获取租户的平台配置")
    public Uni<RestResult<PlatformConfig>> getByPlatformCode(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            @PathParam("platformCode") String platformCode) {

        LOG.infof("根据平台编码获取配置，租户ID: %d, 平台编码: %s", tenantId, platformCode);

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        return platformConfigService.findByPlatformCode(tenantId, platformCode)
            .map(config -> {
                if (config == null) {
                    return RestResult.<PlatformConfig>error("CONFIG_NOT_FOUND", "平台配置不存在");
                }
                return RestResult.success(config);
            })
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "根据平台编码获取配置失败");
                return RestResult.<PlatformConfig>error("QUERY_FAILED", "查询失败: " + throwable.getMessage());
            });
    }

    /**
     * 创建平台配置
     */
    @POST
    @Operation(summary = "创建平台配置", description = "创建新的平台配置")
    public Uni<RestResult<PlatformConfig>> create(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            PlatformConfigDto configDto) {

        LOG.infof("创建平台配置，租户ID: %d, 平台编码: %s", tenantId, configDto.getPlatformCode());

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        configDto.setTenantId(tenantId);

        return platformConfigService.create(tenantId, configDto)
            .map(RestResult::success)
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "创建平台配置失败");
                return RestResult.error("CREATE_FAILED", "创建失败: " + throwable.getMessage());
            });
    }

    /**
     * 更新平台配置
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新平台配置", description = "更新指定的平台配置")
    public Uni<RestResult<PlatformConfig>> update(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            @PathParam("id") Long id,
            PlatformConfigDto configDto) {

        LOG.infof("更新平台配置，租户ID: %d, 配置ID: %d", tenantId, id);

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        configDto.setId(id);
        configDto.setTenantId(tenantId);

        return platformConfigService.update(tenantId, id, configDto)
            .map(RestResult::success)
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "更新平台配置失败");
                return RestResult.error("UPDATE_FAILED", "更新失败: " + throwable.getMessage());
            });
    }

    /**
     * 删除平台配置
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除平台配置", description = "删除指定的平台配置")
    public Uni<RestResult<Boolean>> delete(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            @PathParam("id") Long id) {

        LOG.infof("删除平台配置，租户ID: %d, 配置ID: %d", tenantId, id);

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        return platformConfigService.delete(tenantId, id)
            .map(RestResult::success)
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "删除平台配置失败");
                return RestResult.error("DELETE_FAILED", "删除失败: " + throwable.getMessage());
            });
    }

    /**
     * 启用/禁用平台配置
     */
    @PUT
    @Path("/{id}/status")
    @Operation(summary = "更新平台配置状态", description = "启用或禁用平台配置")
    public Uni<RestResult<Boolean>> updateStatus(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            @PathParam("id") Long id,
            @QueryParam("enabled") Boolean enabled) {

        LOG.infof("更新平台配置状态，租户ID: %d, 配置ID: %d, 启用状态: %s", tenantId, id, enabled);

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        if (enabled == null) {
            return Uni.createFrom().item(RestResult.error("ENABLED_REQUIRED", "启用状态不能为空"));
        }

        return platformConfigService.updateStatus(tenantId, id, enabled)
            .map(RestResult::success)
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "更新平台配置状态失败");
                return RestResult.error("UPDATE_STATUS_FAILED", "更新状态失败: " + throwable.getMessage());
            });
    }

    /**
     * 测试平台连接
     */
    @POST
    @Path("/{id}/test")
    @Operation(summary = "测试平台连接", description = "测试平台配置的连接是否正常")
    public Uni<RestResult<Boolean>> testConnection(
            @HeaderParam("X-Tenant-Id") Long tenantId,
            @PathParam("id") Long id) {

        LOG.infof("测试平台连接，租户ID: %d, 配置ID: %d", tenantId, id);

        if (tenantId == null) {
            return Uni.createFrom().item(RestResult.error("TENANT_ID_REQUIRED", "租户ID不能为空"));
        }

        return platformConfigService.testConnection(tenantId, id)
            .map(RestResult::success)
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "测试平台连接失败");
                return RestResult.error("TEST_CONNECTION_FAILED", "连接测试失败: " + throwable.getMessage());
            });
    }

    /**
     * 获取支持的平台列表
     */
    @GET
    @Path("/supported-platforms")
    @Operation(summary = "获取支持的平台列表", description = "获取系统支持的所有电商平台列表")
    public Uni<RestResult<List<String>>> getSupportedPlatforms() {
        LOG.info("获取支持的平台列表");

        return platformConfigService.getSupportedPlatforms()
            .map(RestResult::success)
            .onFailure().recoverWithItem(throwable -> {
                LOG.errorf(throwable, "获取支持的平台列表失败");
                return RestResult.error("QUERY_FAILED", "查询失败: " + throwable.getMessage());
            });
    }
}
