package com.visthink.platform.dto;

/**
 * REST API 统一返回结果
 * 
 * 临时创建，避免依赖shared-common模块
 * 
 * <AUTHOR>
 */
public class RestResult<T> {
    
    private boolean success;
    private String code;
    private String message;
    private T data;
    private Long timestamp;
    
    public RestResult() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public RestResult(boolean success, String code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = System.currentTimeMillis();
    }
    
    public static <T> RestResult<T> success(T data) {
        return new RestResult<>(true, "SUCCESS", "操作成功", data);
    }
    
    public static <T> RestResult<T> success(String message, T data) {
        return new RestResult<>(true, "SUCCESS", message, data);
    }
    
    @SuppressWarnings("unchecked")
    public static <T> RestResult<T> error(String code, String message) {
        return (RestResult<T>) new RestResult<>(false, code, message, null);
    }

    @SuppressWarnings("unchecked")
    public static <T> RestResult<T> error(String message) {
        return (RestResult<T>) new RestResult<>(false, "ERROR", message, null);
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
}
