package com.visthink.platform.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 平台配置实体
 *
 * 存储各个电商平台的配置信息，包括API密钥、授权信息等
 * 支持多租户，每个租户可以配置不同的平台账号
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "platform_config",
       uniqueConstraints = @UniqueConstraint(columnNames = {"tenant_id", "platform_code"}))
public class PlatformConfig extends BaseEntity {

    /**
     * 平台编码（taobao, pdd, douyin, jd等）
     */
    @Column(name = "platform_code", nullable = false, length = 50)
    public String platformCode;

    /**
     * 平台名称
     */
    @Column(name = "platform_name", nullable = false, length = 100)
    public String platformName;

    /**
     * 应用ID/App Key
     */
    @Column(name = "app_id", nullable = false, length = 200)
    public String appId;

    /**
     * 应用密钥/App Secret
     */
    @Column(name = "app_secret", nullable = false, length = 500)
    public String appSecret;

    /**
     * 访问令牌
     */
    @Column(name = "access_token", length = 1000)
    public String accessToken;

    /**
     * 刷新令牌
     */
    @Column(name = "refresh_token", length = 1000)
    public String refreshToken;

    /**
     * 令牌过期时间
     */
    @Column(name = "token_expires_at")
    public LocalDateTime tokenExpiresAt;

    /**
     * 店铺ID
     */
    @Column(name = "shop_id", length = 100)
    public String shopId;

    /**
     * 店铺名称
     */
    @Column(name = "shop_name", length = 200)
    public String shopName;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    public Boolean enabled = true;

    /**
     * 是否沙箱模式
     */
    @Column(name = "sandbox_mode", nullable = false)
    public Boolean sandboxMode = false;

    /**
     * 配置状态：1-正常 2-授权过期 3-配置错误 4-禁用
     */
    @Column(name = "status", nullable = false)
    public Integer status = 1;

    /**
     * 扩展配置（JSON格式）
     */
    @Column(name = "extra_config", columnDefinition = "TEXT")
    public String extraConfig;

    /**
     * 最后同步时间
     */
    @Column(name = "last_sync_at")
    public LocalDateTime lastSyncAt;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    public String remark;

    // Getters and Setters
    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public LocalDateTime getTokenExpiresAt() {
        return tokenExpiresAt;
    }

    public void setTokenExpiresAt(LocalDateTime tokenExpiresAt) {
        this.tokenExpiresAt = tokenExpiresAt;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getSandboxMode() {
        return sandboxMode;
    }

    public void setSandboxMode(Boolean sandboxMode) {
        this.sandboxMode = sandboxMode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getExtraConfig() {
        return extraConfig;
    }

    public void setExtraConfig(String extraConfig) {
        this.extraConfig = extraConfig;
    }

    public LocalDateTime getLastSyncAt() {
        return lastSyncAt;
    }

    public void setLastSyncAt(LocalDateTime lastSyncAt) {
        this.lastSyncAt = lastSyncAt;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 配置状态常量
     */
    public static class Status {
        public static final int NORMAL = 1;        // 正常
        public static final int TOKEN_EXPIRED = 2; // 授权过期
        public static final int CONFIG_ERROR = 3;  // 配置错误
        public static final int DISABLED = 4;      // 禁用
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired() {
        return tokenExpiresAt != null && tokenExpiresAt.isBefore(LocalDateTime.now());
    }

    /**
     * 检查配置是否可用
     */
    public boolean isAvailable() {
        return enabled && status == Status.NORMAL && !isTokenExpired();
    }
}
