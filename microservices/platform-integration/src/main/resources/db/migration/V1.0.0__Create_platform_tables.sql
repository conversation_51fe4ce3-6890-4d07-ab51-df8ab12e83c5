-- Platform Integration Service 数据库初始化脚本
-- 创建平台集成相关的数据表

-- 平台配置表
CREATE TABLE platform_config (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    platform_code VARCHAR(50) NOT NULL,
    platform_name VARCHAR(100) NOT NULL,
    app_id VARCHAR(200) NOT NULL,
    app_secret VARCHAR(500) NOT NULL,
    access_token VARCHAR(1000),
    refresh_token VARCHAR(1000),
    token_expires_at TIMESTAMP,
    shop_id VARCHAR(100),
    shop_name VARCHAR(200),
    enabled BOOLEAN NOT NULL DEFAULT true,
    sandbox_mode BOOLEAN NOT NULL DEFAULT false,
    status INTEGER NOT NULL DEFAULT 1,
    extra_config TEXT,
    last_sync_at TIMESTAMP,
    remark VARCHAR(500),
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, platform_code)
);

-- 创建索引
CREATE INDEX idx_platform_config_tenant_id ON platform_config(tenant_id);
CREATE INDEX idx_platform_config_platform_code ON platform_config(platform_code);
CREATE INDEX idx_platform_config_status ON platform_config(status);
CREATE INDEX idx_platform_config_enabled ON platform_config(enabled);

-- 同步任务表
CREATE TABLE sync_task (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    task_no VARCHAR(100) NOT NULL UNIQUE,
    platform_code VARCHAR(50) NOT NULL,
    sync_type INTEGER NOT NULL,
    sync_direction INTEGER NOT NULL,
    status INTEGER NOT NULL DEFAULT 1,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    total_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    skipped_count INTEGER DEFAULT 0,
    error_message TEXT,
    sync_params TEXT,
    sync_result TEXT,
    auto_task BOOLEAN NOT NULL DEFAULT false,
    retry_count INTEGER DEFAULT 0,
    max_retry_count INTEGER DEFAULT 3,
    next_retry_time TIMESTAMP,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_sync_task_tenant_id ON sync_task(tenant_id);
CREATE INDEX idx_sync_task_platform_code ON sync_task(platform_code);
CREATE INDEX idx_sync_task_sync_type ON sync_task(sync_type);
CREATE INDEX idx_sync_task_status ON sync_task(status);
CREATE INDEX idx_sync_task_auto_task ON sync_task(auto_task);
CREATE INDEX idx_sync_task_create_time ON sync_task(create_time);

-- 平台商品映射表
CREATE TABLE platform_product_mapping (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    local_product_id BIGINT NOT NULL,
    local_sku_id BIGINT,
    platform_code VARCHAR(50) NOT NULL,
    platform_product_id VARCHAR(200) NOT NULL,
    platform_sku_id VARCHAR(200),
    mapping_type INTEGER NOT NULL DEFAULT 1, -- 1-商品映射 2-SKU映射
    sync_status INTEGER NOT NULL DEFAULT 1,  -- 1-正常 2-待同步 3-同步失败
    last_sync_time TIMESTAMP,
    sync_error_message TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, local_product_id, platform_code),
    UNIQUE(tenant_id, local_sku_id, platform_code)
);

-- 创建索引
CREATE INDEX idx_platform_product_mapping_tenant_id ON platform_product_mapping(tenant_id);
CREATE INDEX idx_platform_product_mapping_local_product_id ON platform_product_mapping(local_product_id);
CREATE INDEX idx_platform_product_mapping_local_sku_id ON platform_product_mapping(local_sku_id);
CREATE INDEX idx_platform_product_mapping_platform_code ON platform_product_mapping(platform_code);
CREATE INDEX idx_platform_product_mapping_platform_product_id ON platform_product_mapping(platform_product_id);
CREATE INDEX idx_platform_product_mapping_sync_status ON platform_product_mapping(sync_status);

-- 平台订单映射表
CREATE TABLE platform_order_mapping (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    local_order_id BIGINT NOT NULL,
    platform_code VARCHAR(50) NOT NULL,
    platform_order_id VARCHAR(200) NOT NULL,
    platform_order_status VARCHAR(100),
    sync_status INTEGER NOT NULL DEFAULT 1,  -- 1-正常 2-待同步 3-同步失败
    last_sync_time TIMESTAMP,
    sync_error_message TEXT,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, local_order_id, platform_code),
    UNIQUE(tenant_id, platform_order_id, platform_code)
);

-- 创建索引
CREATE INDEX idx_platform_order_mapping_tenant_id ON platform_order_mapping(tenant_id);
CREATE INDEX idx_platform_order_mapping_local_order_id ON platform_order_mapping(local_order_id);
CREATE INDEX idx_platform_order_mapping_platform_code ON platform_order_mapping(platform_code);
CREATE INDEX idx_platform_order_mapping_platform_order_id ON platform_order_mapping(platform_order_id);
CREATE INDEX idx_platform_order_mapping_sync_status ON platform_order_mapping(sync_status);

-- 同步日志表
CREATE TABLE sync_log (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    task_id BIGINT,
    platform_code VARCHAR(50) NOT NULL,
    sync_type INTEGER NOT NULL,
    business_type VARCHAR(50), -- product, order, inventory等
    business_id VARCHAR(200),
    operation_type VARCHAR(50), -- create, update, delete, sync等
    status INTEGER NOT NULL, -- 1-成功 2-失败 3-跳过
    request_data TEXT,
    response_data TEXT,
    error_message TEXT,
    execution_time INTEGER, -- 执行时间，毫秒
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_sync_log_tenant_id ON sync_log(tenant_id);
CREATE INDEX idx_sync_log_task_id ON sync_log(task_id);
CREATE INDEX idx_sync_log_platform_code ON sync_log(platform_code);
CREATE INDEX idx_sync_log_sync_type ON sync_log(sync_type);
CREATE INDEX idx_sync_log_business_type ON sync_log(business_type);
CREATE INDEX idx_sync_log_status ON sync_log(status);
CREATE INDEX idx_sync_log_create_time ON sync_log(create_time);

-- 添加表注释
COMMENT ON TABLE platform_config IS '平台配置表';
COMMENT ON TABLE sync_task IS '同步任务表';
COMMENT ON TABLE platform_product_mapping IS '平台商品映射表';
COMMENT ON TABLE platform_order_mapping IS '平台订单映射表';
COMMENT ON TABLE sync_log IS '同步日志表';

-- 添加列注释
COMMENT ON COLUMN platform_config.platform_code IS '平台编码：taobao, pdd, douyin, jd等';
COMMENT ON COLUMN platform_config.status IS '配置状态：1-正常 2-授权过期 3-配置错误 4-禁用';
COMMENT ON COLUMN sync_task.sync_type IS '同步类型：1-商品同步 2-订单同步 3-库存同步 4-用户同步';
COMMENT ON COLUMN sync_task.sync_direction IS '同步方向：1-从平台拉取 2-推送到平台 3-双向同步';
COMMENT ON COLUMN sync_task.status IS '任务状态：1-待执行 2-执行中 3-成功 4-失败 5-部分成功';
