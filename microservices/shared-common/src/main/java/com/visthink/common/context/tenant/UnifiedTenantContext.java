package com.visthink.common.context.tenant;

import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

/**
 * 统一租户上下文管理
 */
@ApplicationScoped
public class UnifiedTenantContext {

    private static final Logger LOG = Logger.getLogger(UnifiedTenantContext.class);

    private static final ThreadLocal<Long> TENANT_ID_HOLDER = new ThreadLocal<>();

    /**
     * 设置当前租户ID
     */
    public void setCurrentTenantId(Long tenantId) {
        TENANT_ID_HOLDER.set(tenantId);
        LOG.debugf("Set current tenant ID: %d", tenantId);
    }

    /**
     * 获取当前租户ID
     */
    public Uni<Long> getCurrentTenantId() {
        Long tenantId = TENANT_ID_HOLDER.get();
        if (tenantId == null) {
            LOG.warn("Current tenant ID is null, using default tenant ID: 1");
            tenantId = 1L; // 默认租户ID
        }
        return Uni.createFrom().item(tenantId);
    }

    /**
     * 清除当前租户ID
     */
    public void clearCurrentTenantId() {
        TENANT_ID_HOLDER.remove();
        LOG.debug("Clear current tenant ID");
    }

    /**
     * 在指定租户上下文中执行操作
     */
    public <T> Uni<T> executeInTenantContext(Long tenantId, java.util.function.Supplier<Uni<T>> operation) {
        return Uni.createFrom().item(() -> {
            Long originalTenantId = TENANT_ID_HOLDER.get();
            try {
                setCurrentTenantId(tenantId);
                return operation.get();
            } finally {
                if (originalTenantId != null) {
                    setCurrentTenantId(originalTenantId);
                } else {
                    clearCurrentTenantId();
                }
            }
        }).flatMap(uni -> uni);
    }
}
