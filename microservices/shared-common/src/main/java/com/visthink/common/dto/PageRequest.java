package com.visthink.common.dto;

import com.visthink.common.constant.CommonConstants;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 统一分页请求DTO
 * 
 * 提供标准化的分页查询参数，包括页码、页面大小、排序和查询条件
 * 支持动态查询条件和多字段排序
 * 
 * <AUTHOR>
 */
@Data
public class PageRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = CommonConstants.DEFAULT_PAGE_NUM;
    
    /**
     * 页面大小
     */
    @Min(value = 1, message = "页面大小必须大于0")
    @Max(value = 1000, message = "页面大小不能超过1000")
    private Integer pageSize = CommonConstants.DEFAULT_PAGE_SIZE;
    
    /**
     * 排序字段
     * 格式：字段名 ASC/DESC，多个字段用逗号分隔
     * 例如：createTime DESC, id ASC
     */
    private String orderBy;
    
    /**
     * 是否统计总数
     * 默认为true，如果只需要分页数据不需要总数可设为false提高性能
     */
    private Boolean count = true;
    
    /**
     * 查询条件
     * 动态查询条件，key为字段名，value为查询值
     */
    private Map<String, Object> conditions = new HashMap<>();
    
    /**
     * 模糊查询字段
     * 指定哪些字段使用模糊查询（LIKE）
     */
    private String[] likeFields;
    
    /**
     * 精确查询字段
     * 指定哪些字段使用精确查询（=）
     */
    private String[] exactFields;
    
    /**
     * 范围查询字段
     * 指定哪些字段使用范围查询（BETWEEN）
     * 值格式：startValue,endValue
     */
    private String[] rangeFields;
    
    /**
     * IN查询字段
     * 指定哪些字段使用IN查询
     * 值格式：value1,value2,value3
     */
    private String[] inFields;
    
    /**
     * 日期范围查询
     * 开始日期
     */
    private String startDate;
    
    /**
     * 日期范围查询
     * 结束日期
     */
    private String endDate;
    
    /**
     * 日期字段名
     * 用于日期范围查询的字段名，默认为createTime
     */
    private String dateField = "createTime";
    
    /**
     * 是否包含已删除数据
     * 默认为false，只查询未删除的数据
     */
    private Boolean includeDeleted = false;
    
    /**
     * 租户ID
     * 用于多租户数据隔离，通常由系统自动设置
     */
    private Long tenantId;
    
    /**
     * 扩展参数
     * 用于传递额外的查询参数
     */
    private Map<String, Object> extras = new HashMap<>();
    
    // ==================== 构造函数 ====================
    
    /**
     * 默认构造函数
     */
    public PageRequest() {
    }
    
    /**
     * 构造函数
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     */
    public PageRequest(Integer pageNum, Integer pageSize) {
        this.pageNum = pageNum != null && pageNum > 0 ? pageNum : CommonConstants.DEFAULT_PAGE_NUM;
        this.pageSize = pageSize != null && pageSize > 0 ? 
                Math.min(pageSize, CommonConstants.MAX_PAGE_SIZE) : CommonConstants.DEFAULT_PAGE_SIZE;
    }
    
    /**
     * 构造函数
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @param orderBy  排序字段
     */
    public PageRequest(Integer pageNum, Integer pageSize, String orderBy) {
        this(pageNum, pageSize);
        this.orderBy = orderBy;
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 添加查询条件
     *
     * @param key   字段名
     * @param value 查询值
     * @return 当前对象
     */
    public PageRequest addCondition(String key, Object value) {
        if (key != null && value != null) {
            this.conditions.put(key, value);
        }
        return this;
    }
    
    /**
     * 添加多个查询条件
     *
     * @param conditions 查询条件Map
     * @return 当前对象
     */
    public PageRequest addConditions(Map<String, Object> conditions) {
        if (conditions != null && !conditions.isEmpty()) {
            this.conditions.putAll(conditions);
        }
        return this;
    }
    
    /**
     * 移除查询条件
     *
     * @param key 字段名
     * @return 当前对象
     */
    public PageRequest removeCondition(String key) {
        if (key != null) {
            this.conditions.remove(key);
        }
        return this;
    }
    
    /**
     * 清空查询条件
     *
     * @return 当前对象
     */
    public PageRequest clearConditions() {
        this.conditions.clear();
        return this;
    }
    
    /**
     * 添加扩展参数
     *
     * @param key   参数名
     * @param value 参数值
     * @return 当前对象
     */
    public PageRequest addExtra(String key, Object value) {
        if (key != null && value != null) {
            this.extras.put(key, value);
        }
        return this;
    }
    
    /**
     * 获取扩展参数
     *
     * @param key 参数名
     * @return 参数值
     */
    public Object getExtra(String key) {
        return this.extras.get(key);
    }
    
    /**
     * 获取扩展参数（指定类型）
     *
     * @param key   参数名
     * @param clazz 参数类型
     * @param <T>   泛型类型
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getExtra(String key, Class<T> clazz) {
        Object value = this.extras.get(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 设置日期范围查询
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 当前对象
     */
    public PageRequest setDateRange(String startDate, String endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
        return this;
    }
    
    /**
     * 设置日期范围查询
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param dateField 日期字段名
     * @return 当前对象
     */
    public PageRequest setDateRange(String startDate, String endDate, String dateField) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.dateField = dateField;
        return this;
    }
    
    /**
     * 计算偏移量（用于数据库查询）
     *
     * @return 偏移量
     */
    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }
    
    /**
     * 获取限制数量（用于数据库查询）
     *
     * @return 限制数量
     */
    public int getLimit() {
        return pageSize;
    }
    
    /**
     * 验证分页参数
     *
     * @return 验证结果
     */
    public boolean isValid() {
        return pageNum != null && pageNum > 0 && 
               pageSize != null && pageSize > 0 && pageSize <= CommonConstants.MAX_PAGE_SIZE;
    }
    
    /**
     * 修正分页参数
     * 确保参数在有效范围内
     */
    public void normalize() {
        if (pageNum == null || pageNum < 1) {
            pageNum = CommonConstants.DEFAULT_PAGE_NUM;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = CommonConstants.DEFAULT_PAGE_SIZE;
        }
        if (pageSize > CommonConstants.MAX_PAGE_SIZE) {
            pageSize = CommonConstants.MAX_PAGE_SIZE;
        }
        if (count == null) {
            count = true;
        }
        if (includeDeleted == null) {
            includeDeleted = false;
        }
    }
    
    /**
     * 创建简单分页请求
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @return 分页请求对象
     */
    public static PageRequest of(Integer pageNum, Integer pageSize) {
        return new PageRequest(pageNum, pageSize);
    }
    
    /**
     * 创建带排序的分页请求
     *
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @param orderBy  排序字段
     * @return 分页请求对象
     */
    public static PageRequest of(Integer pageNum, Integer pageSize, String orderBy) {
        return new PageRequest(pageNum, pageSize, orderBy);
    }

    // ==================== 兼容性方法 ====================

    /**
     * 获取页码（兼容方法）
     * @return 页码
     */
    public Integer getPage() {
        return pageNum;
    }

    /**
     * 设置页码（兼容方法）
     * @param page 页码
     */
    public void setPage(Integer page) {
        this.pageNum = page;
    }

    /**
     * 获取页面大小（兼容方法）
     * @return 页面大小
     */
    public Integer getSize() {
        return pageSize;
    }

    /**
     * 设置页面大小（兼容方法）
     * @param size 页面大小
     */
    public void setSize(Integer size) {
        this.pageSize = size;
    }
    
    @Override
    public String toString() {
        return String.format("PageRequest{pageNum=%d, pageSize=%d, orderBy='%s', count=%s, tenantId=%d}", 
                pageNum, pageSize, orderBy, count, tenantId);
    }
}
