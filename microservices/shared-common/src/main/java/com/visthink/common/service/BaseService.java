package com.visthink.common.service;


import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.entity.BaseEntity;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;

import java.util.List;
import java.util.Map;

/**
 * 通用Service基础接口
 * 定义标准的业务操作方法
 *
 * @param <T> 实体类型，必须继承BaseEntity
 * @param <CreateRequest> 创建请求DTO类型
 * @param <UpdateRequest> 更新请求DTO类型
 * @param <QueryRequest> 查询请求DTO类型
 * <AUTHOR>
 */
public interface BaseService<T extends BaseEntity, CreateRequest, UpdateRequest, QueryRequest> {

    /**
     * 创建实体
     *
     * @param tenantId 租户ID
     * @param request 创建请求
     * @return 创建的实体
     */
    @WithTransaction
    Uni<T> create(Long tenantId, CreateRequest request);

    /**
     * 根据ID更新实体
     *
     * @param tenantId 租户ID
     * @param id 实体ID
     * @param request 更新请求
     * @return 更新后的实体
     */
    @WithTransaction
    Uni<T> update(Long tenantId, Long id, UpdateRequest request);

    /**
     * 根据ID删除实体（软删除）
     *
     * @param tenantId 租户ID
     * @param id 实体ID
     * @return 是否删除成功
     */
    @WithTransaction
    Uni<Boolean> delete(Long tenantId, Long id);

    /**
     * 根据ID查询实体
     *
     * @param tenantId 租户ID
     * @param id 实体ID
     * @return 实体对象
     */
    Uni<T> findById(Long tenantId, Long id);

    /**
     * 查询所有实体
     *
     * @param tenantId 租户ID
     * @return 实体列表
     */
    Uni<List<T>> findAll(Long tenantId);

    /**
     * 分页查询实体
     *
     * @param tenantId 租户ID
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    Uni<PageResult<T>> findByPage(Long tenantId, PageRequest pageRequest);

    /**
     * 根据查询条件分页查询实体
     *
     * @param tenantId 租户ID
     * @param queryRequest 查询请求
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    Uni<PageResult<T>> findByQuery(Long tenantId, QueryRequest queryRequest, PageRequest pageRequest);

    /**
     * 批量创建实体
     *
     * @param tenantId 租户ID
     * @param requests 创建请求列表
     * @return 创建的实体列表
     */
    @WithTransaction
    Uni<List<T>> batchCreate(Long tenantId, List<CreateRequest> requests);

    /**
     * 批量更新实体状态
     *
     * @param tenantId 租户ID
     * @param ids 实体ID列表
     * @param status 新状态
     * @return 更新成功的数量
     */
    @WithTransaction
    Uni<Integer> batchUpdateStatus(Long tenantId, List<Long> ids, Integer status);

    /**
     * 批量删除实体（软删除）
     *
     * @param tenantId 租户ID
     * @param ids 实体ID列表
     * @return 删除成功的数量
     */
    @WithTransaction
    Uni<Integer> batchDelete(Long tenantId, List<Long> ids);

    /**
     * 统计实体数量
     *
     * @param tenantId 租户ID
     * @return 实体数量
     */
    Uni<Long> count(Long tenantId);

    /**
     * 根据查询条件统计实体数量
     *
     * @param tenantId 租户ID
     * @param queryRequest 查询请求
     * @return 实体数量
     */
    Uni<Long> countByQuery(Long tenantId, QueryRequest queryRequest);

    /**
     * 检查实体是否存在
     *
     * @param tenantId 租户ID
     * @param id 实体ID
     * @return 是否存在
     */
    Uni<Boolean> exists(Long tenantId, Long id);

    /**
     * 验证创建请求
     *
     * @param tenantId 租户ID
     * @param request 创建请求
     * @return 验证结果，如果验证通过返回null，否则返回错误信息
     */
    default Uni<String> validateCreateRequest(Long tenantId, CreateRequest request) {
        return Uni.createFrom().nullItem();
    }

    /**
     * 验证更新请求
     *
     * @param tenantId 租户ID
     * @param id 实体ID
     * @param request 更新请求
     * @return 验证结果，如果验证通过返回null，否则返回错误信息
     */
    default Uni<String> validateUpdateRequest(Long tenantId, Long id, UpdateRequest request) {
        return Uni.createFrom().nullItem();
    }

    /**
     * 创建前的业务处理
     *
     * @param tenantId 租户ID
     * @param entity 实体对象
     * @return 处理后的实体
     */
    default Uni<T> beforeCreate(Long tenantId, T entity) {
        return Uni.createFrom().item(entity);
    }

    /**
     * 创建后的业务处理
     *
     * @param tenantId 租户ID
     * @param entity 实体对象
     * @return 处理后的实体
     */
    default Uni<T> afterCreate(Long tenantId, T entity) {
        return Uni.createFrom().item(entity);
    }

    /**
     * 更新前的业务处理
     *
     * @param tenantId 租户ID
     * @param entity 实体对象
     * @return 处理后的实体
     */
    default Uni<T> beforeUpdate(Long tenantId, T entity) {
        return Uni.createFrom().item(entity);
    }

    /**
     * 更新后的业务处理
     *
     * @param tenantId 租户ID
     * @param entity 实体对象
     * @return 处理后的实体
     */
    default Uni<T> afterUpdate(Long tenantId, T entity) {
        return Uni.createFrom().item(entity);
    }

    /**
     * 删除前的业务处理
     *
     * @param tenantId 租户ID
     * @param entity 实体对象
     * @return 是否允许删除
     */
    default Uni<Boolean> beforeDelete(Long tenantId, T entity) {
        return Uni.createFrom().item(true);
    }

    /**
     * 删除后的业务处理
     *
     * @param tenantId 租户ID
     * @param entity 实体对象
     * @return 处理结果
     */
    default Uni<Void> afterDelete(Long tenantId, T entity) {
        return Uni.createFrom().voidItem();
    }

    /**
     * 动态条件查询
     *
     * @param tenantId 租户ID
     * @param conditions 查询条件
     * @return 实体列表
     */
    default Uni<List<T>> findByConditions(Long tenantId, Map<String, Object> conditions) {
        return Uni.createFrom().item(List.of());
    }

    /**
     * 获取实体的业务标识符（如编码、名称等）
     *
     * @param entity 实体对象
     * @return 业务标识符
     */
    default String getEntityIdentifier(T entity) {
        return entity.id != null ? entity.id.toString() : "unknown";
    }

    /**
     * 检查字段值是否唯一
     *
     * @param tenantId 租户ID
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    default Uni<Boolean> isFieldValueUnique(Long tenantId, String fieldName, Object fieldValue, Long excludeId) {
        return Uni.createFrom().item(true);
    }
}
