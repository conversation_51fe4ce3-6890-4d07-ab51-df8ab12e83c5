package com.visthink.erp.core.annotation;

/**
 * 字段转换器接口
 * 用于处理复杂的字段类型转换
 * 
 * @param <S> 源类型
 * @param <T> 目标类型
 * <AUTHOR>
 */
public interface FieldConverter<S, T> {
    
    /**
     * 将源类型转换为目标类型
     * 
     * @param source 源值
     * @return 转换后的目标值
     */
    T convert(S source);
    
    /**
     * 反向转换（可选实现）
     * 
     * @param target 目标值
     * @return 转换后的源值
     */
    default S reverse(T target) {
        throw new UnsupportedOperationException("Reverse conversion not supported");
    }
}
