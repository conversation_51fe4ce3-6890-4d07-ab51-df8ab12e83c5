package com.visthink.common.config;

// import com.visthink.common.interceptor.HibernateTenantInterceptor;
import jakarta.enterprise.context.ApplicationScoped;
// import jakarta.inject.Inject;
import org.jboss.logging.Logger;

/**
 * Hibernate配置类
 * 注册多租户拦截器
 *
 * 注意：在Quarkus中，Hibernate拦截器的注册方式与传统Spring不同
 * 可以通过application.properties配置或者使用CDI事件来注册
 */
@ApplicationScoped
public class HibernateConfig {

    private static final Logger LOG = Logger.getLogger(HibernateConfig.class);

    // @Inject
    // HibernateTenantInterceptor tenantInterceptor;

    /**
     * 初始化配置
     * 在Quarkus中，可以通过@Startup注解或者观察CDI事件来初始化配置
     */
    public void init() {
        LOG.info("初始化Hibernate多租户配置");
        // 在Quarkus中，拦截器通常通过配置文件或CDI事件注册
        // 具体实现可能需要根据Quarkus版本调整
    }
}
