package com.visthink.common.security;

import jakarta.enterprise.util.Nonbinding;
import jakarta.interceptor.InterceptorBinding;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限验证注解
 *
 * 用于方法级权限控制，支持单个或多个权限验证
 * 可配置权限验证逻辑（AND/OR）
 *
 * 使用示例：
 * @RequiresPermission("user:read")
 * @RequiresPermission(value = {"user:read", "user:update"}, logical = Logical.OR)
 *
 * <AUTHOR>
 */
@InterceptorBinding
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiresPermission {

    /**
     * 权限代码数组
     * 格式：resource:action（如 user:read, order:create）
     */
    @Nonbinding
    String[] value() default {};

    /**
     * 多权限验证逻辑
     */
    @Nonbinding
    Logical logical() default Logical.AND;

    /**
     * 是否验证租户权限
     * 如果为true，会额外验证当前用户是否有访问目标租户的权限
     */
    @Nonbinding
    boolean checkTenant() default true;

    /**
     * 权限验证失败时的错误消息
     */
    @Nonbinding
    String message() default "权限不足，无法执行此操作";

    /**
     * 逻辑操作符枚举
     */
    enum Logical {
        /**
         * 逻辑与：需要拥有所有指定权限
         */
        AND,

        /**
         * 逻辑或：拥有任一指定权限即可
         */
        OR
    }
}
