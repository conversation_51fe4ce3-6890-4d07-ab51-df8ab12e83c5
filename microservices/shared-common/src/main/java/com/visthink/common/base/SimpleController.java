package com.visthink.common.base;

import com.visthink.common.context.TenantContext;
import com.visthink.common.dto.ApiResponse;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.jboss.logging.Logger;

/**
 * 简化的Controller基类
 * 只负责HTTP请求处理，不包含业务逻辑
 * 
 * 职责：
 * 1. 租户上下文获取
 * 2. 统一异常处理
 * 3. 响应格式标准化
 * 4. 基础日志记录
 * 
 * <AUTHOR>
 */
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public abstract class SimpleController {

    protected final Logger log = Logger.getLogger(getClass());

    @Inject
    protected TenantContext tenantContext;

    /**
     * 获取当前租户ID
     * 
     * @return 租户ID的Uni包装
     */
    protected Uni<Long> getCurrentTenantId() {
        return tenantContext.getCurrentTenantId();
    }

    /**
     * 同步获取当前租户ID
     * 
     * @return 租户ID
     */
    protected Long getCurrentTenantIdSync() {
        return tenantContext.getCurrentTenantIdSync();
    }

    /**
     * 处理成功响应
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    protected <T> ApiResponse<T> success(T data) {
        return ApiResponse.success(data);
    }

    /**
     * 处理成功响应（带消息）
     * 
     * @param message 成功消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    protected <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.success(message, data);
    }

    /**
     * 处理错误响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 错误响应
     */
    protected <T> ApiResponse<T> error(String message) {
        return ApiResponse.error(message);
    }

    /**
     * 处理异常并返回错误响应
     * 
     * @param throwable 异常
     * @param operation 操作名称
     * @param <T> 数据类型
     * @return 错误响应
     */
    protected <T> ApiResponse<T> handleError(Throwable throwable, String operation) {
        log.errorf(throwable, "%s操作失败", operation);
        return ApiResponse.error(operation + "失败: " + throwable.getMessage());
    }

    /**
     * 处理异常并返回错误响应（简化版）
     * 
     * @param throwable 异常
     * @param <T> 数据类型
     * @return 错误响应
     */
    protected <T> ApiResponse<T> handleError(Throwable throwable) {
        log.error("操作失败", throwable);
        return ApiResponse.error("操作失败: " + throwable.getMessage());
    }

    /**
     * 验证租户ID
     * 
     * @param tenantId 租户ID
     * @return 验证结果
     */
    protected boolean isValidTenantId(Long tenantId) {
        return tenantId != null && tenantId > 0;
    }

    /**
     * 记录操作日志
     * 
     * @param operation 操作名称
     * @param message 日志消息
     */
    protected void logOperation(String operation, String message) {
        log.infof("[%s] %s", operation, message);
    }

    /**
     * 记录操作日志（带租户ID）
     * 
     * @param operation 操作名称
     * @param tenantId 租户ID
     * @param message 日志消息
     */
    protected void logOperation(String operation, Long tenantId, String message) {
        log.infof("[%s] 租户ID: %d, %s", operation, tenantId, message);
    }
}
