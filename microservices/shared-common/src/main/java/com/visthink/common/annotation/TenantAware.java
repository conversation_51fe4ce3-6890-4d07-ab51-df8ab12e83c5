package com.visthink.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 租户感知注解
 * 
 * 标记需要进行租户数据隔离的类或方法
 * 用于自动注入租户上下文和数据过滤
 * 
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface TenantAware {
    
    /**
     * 是否强制租户验证
     * 如果为true，在没有租户上下文时会抛出异常
     * 如果为false，会使用默认租户
     */
    boolean required() default true;
    
    /**
     * 是否允许跨租户访问
     * 只有平台管理员才能进行跨租户访问
     */
    boolean allowCrossTenant() default false;
    
    /**
     * 租户验证级别
     */
    ValidationLevel level() default ValidationLevel.STRICT;
    
    /**
     * 自定义错误消息
     */
    String message() default "租户验证失败";
    
    /**
     * 租户验证级别枚举
     */
    enum ValidationLevel {
        /**
         * 严格模式：必须有有效的租户上下文
         */
        STRICT,
        
        /**
         * 宽松模式：允许使用默认租户
         */
        LENIENT,
        
        /**
         * 跳过模式：不进行租户验证（仅用于系统级操作）
         */
        SKIP
    }
}
