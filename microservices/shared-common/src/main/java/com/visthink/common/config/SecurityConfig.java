package com.visthink.erp.config;

import jakarta.enterprise.context.ApplicationScoped;
import lombok.Getter;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@Getter
@ApplicationScoped
public class SecurityConfig {

    @ConfigProperty(name = "security.jwt-secret", defaultValue = "default-secret-key-change-in-production")
    String jwtSecret;

    @ConfigProperty(name = "security.jwt-expiration", defaultValue = "86400000")
    Long jwtExpiration;

    @ConfigProperty(name = "security.jwt-issuer", defaultValue = "http://localhost:36060/oauth2")
    String jwtIssuer;
}
