package com.visthink.common.config;

// import io.quarkus.oidc.TokenStateManager;
// import io.quarkus.oidc.runtime.DefaultTenantConfigResolver;
// import io.quarkus.oidc.runtime.OidcAuthenticationMechanism;
// import io.quarkus.oidc.runtime.OidcConfig;
import io.quarkus.security.identity.SecurityIdentity;
import io.quarkus.security.spi.runtime.BlockingSecurityExecutor;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Inject;
import org.eclipse.microprofile.jwt.JsonWebToken;

@ApplicationScoped
public class OAuth2Config {

    // @Inject
    // SecurityIdentity securityIdentity;

    // @Inject
    // JsonWebToken jwt;

    // @Inject
    // DefaultTenantConfigResolver tenantConfigResolver;

    // @Inject
    // BlockingSecurityExecutor blockingSecurityExecutor;

    // @Produces
    // @ApplicationScoped
    // public OidcAuthenticationMechanism oidcAuthMechanism() {
    //     return new OidcAuthenticationMechanism(tenantConfigResolver, blockingSecurityExecutor);
    // }
}
