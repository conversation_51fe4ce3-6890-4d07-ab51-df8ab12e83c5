package com.visthink.erp.utils;

import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

public class JwtKeyGenerator {
    public static void main(String[] args) throws Exception {
        // 生成密钥对
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        // 获取公钥和私钥
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        // 将密钥转换为 Base64 编码的字符串
        String publicKeyPEM = "-----BEGIN PUBLIC KEY-----\n" +
                Base64.getEncoder().encodeToString(publicKey.getEncoded()) +
                "\n-----END PUBLIC KEY-----";

        String privateKeyPEM = "-----BEGIN PRIVATE KEY-----\n" +
                Base64.getEncoder().encodeToString(privateKey.getEncoded()) +
                "\n-----END PRIVATE KEY-----";

        // 确保目录存在
        Files.createDirectories(Paths.get("src/main/resources/META-INF/resources"));

        // 保存公钥
        try (FileWriter writer = new FileWriter("src/main/resources/META-INF/resources/publicKey.pem")) {
            writer.write(publicKeyPEM);
        }

        // 保存私钥
        try (FileWriter writer = new FileWriter("src/main/resources/META-INF/resources/privateKey.pem")) {
            writer.write(privateKeyPEM);
        }

        System.out.println("密钥对已生成并保存到 resources 目录");
    }
}
