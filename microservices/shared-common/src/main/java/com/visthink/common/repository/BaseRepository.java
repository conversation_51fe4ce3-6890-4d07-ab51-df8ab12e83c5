package com.visthink.common.repository;

import com.visthink.common.constant.CommonConstants;
import com.visthink.common.context.TenantContext;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.entity.BaseEntity;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 基础Repository接口
 *
 * 提供通用的数据访问方法，包括CRUD操作、分页查询、多租户支持等
 * 所有业务Repository都应该继承此接口
 *
 * @param <T> 实体类型，必须继承BaseEntity
 * <AUTHOR>
 */
public interface BaseRepository<T extends BaseEntity> extends PanacheRepository<T> {

    // ==================== 基础CRUD操作 ====================
    @Inject
     TenantContext tenantContext = null;

    /**
     * 根据ID和租户ID查找实体
     *
     * @param id       实体ID
     * @param tenantId 租户ID
     * @return 实体的Uni包装
     */
    default Uni<T> findByIdAndTenant(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2 and deleted = ?3",
                id, tenantId, CommonConstants.NOT_DELETED)
                .firstResult();
    }

    /**
     * 根据ID和租户ID查找实体（包含已删除）
     *
     * @param id       实体ID
     * @param tenantId 租户ID
     * @return 实体的Uni包装
     */
    default Uni<T> findByIdAndTenantIncludeDeleted(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2", id, tenantId).firstResult();
    }

    /**
     * 根据租户ID查找所有实体
     *
     * @param tenantId 租户ID
     * @return 实体列表的Uni包装
     */
    default Uni<List<T>> findByTenant(Long tenantId) {
        return find("tenantId = ?1 and deleted = ?2 order by sortOrder, id",
                tenantId, CommonConstants.NOT_DELETED)
                .list();
    }

    /**
     * 根据租户ID查找启用的实体
     *
     * @param tenantId 租户ID
     * @return 实体列表的Uni包装
     */
    default Uni<List<T>> findEnabledByTenant(Long tenantId) {
        return find("tenantId = ?1 and deleted = ?2 and status = ?3 order by sortOrder, id",
                tenantId, CommonConstants.NOT_DELETED, CommonConstants.STATUS_ENABLED)
                .list();
    }

    /**
     * 根据租户ID统计实体数量
     *
     * @param tenantId 租户ID
     * @return 数量的Uni包装
     */
    default Uni<Long> countByTenant(Long tenantId) {
        return count("tenantId = ?1 and deleted = ?2", tenantId, CommonConstants.NOT_DELETED);
    }

    /**
     * 根据租户ID统计启用的实体数量
     *
     * @param tenantId 租户ID
     * @return 数量的Uni包装
     */
    default Uni<Long> countEnabledByTenant(Long tenantId) {
        return count("tenantId = ?1 and deleted = ?2 and status = ?3",
                tenantId, CommonConstants.NOT_DELETED, CommonConstants.STATUS_ENABLED);
    }

    // ==================== 逻辑删除操作 ====================

    /**
     * 逻辑删除实体
     *
     * @param id       实体ID
     * @param tenantId 租户ID
     * @return 删除结果的Uni包装
     */
    default Uni<Boolean> logicalDelete(Long id, Long tenantId) {
        return update("deleted = ?1, updateTime = current_timestamp where id = ?2 and tenantId = ?3 and deleted = ?4",
                CommonConstants.DELETED, id, tenantId, CommonConstants.NOT_DELETED)
                .map(count -> count > 0);
    }

    /**
     * 批量逻辑删除实体
     *
     * @param ids      实体ID列表
     * @param tenantId 租户ID
     * @return 删除数量的Uni包装
     */
    default Uni<Integer> logicalDeleteBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("deleted = ?1, updateTime = current_timestamp where id in ?2 and tenantId = ?3 and deleted = ?4",
                CommonConstants.DELETED, ids, tenantId, CommonConstants.NOT_DELETED);
    }

    /**
     * 恢复逻辑删除的实体
     *
     * @param id       实体ID
     * @param tenantId 租户ID
     * @return 恢复结果的Uni包装
     */
    default Uni<Boolean> restore(Long id, Long tenantId) {
        return update("deleted = ?1, updateTime = current_timestamp where id = ?2 and tenantId = ?3 and deleted = ?4",
                CommonConstants.NOT_DELETED, id, tenantId, CommonConstants.DELETED)
                .map(count -> count > 0);
    }

    // ==================== 状态操作 ====================

    /**
     * 启用实体
     *
     * @param id       实体ID
     * @param tenantId 租户ID
     * @return 操作结果的Uni包装
     */
    default Uni<Boolean> enable(Long id, Long tenantId) {
        return update("status = ?1, updateTime = current_timestamp where id = ?2 and tenantId = ?3 and deleted = ?4",
                CommonConstants.STATUS_ENABLED, id, tenantId, CommonConstants.NOT_DELETED)
                .map(count -> count > 0);
    }

    /**
     * 禁用实体
     *
     * @param id       实体ID
     * @param tenantId 租户ID
     * @return 操作结果的Uni包装
     */
    default Uni<Boolean> disable(Long id, Long tenantId) {
        return update("status = ?1, updateTime = current_timestamp where id = ?2 and tenantId = ?3 and deleted = ?4",
                CommonConstants.STATUS_DISABLED, id, tenantId, CommonConstants.NOT_DELETED)
                .map(count -> count > 0);
    }

    /**
     * 批量启用实体
     *
     * @param ids      实体ID列表
     * @param tenantId 租户ID
     * @return 操作数量的Uni包装
     */
    default Uni<Integer> enableBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("status = ?1, updateTime = current_timestamp where id in ?2 and tenantId = ?3 and deleted = ?4",
                CommonConstants.STATUS_ENABLED, ids, tenantId, CommonConstants.NOT_DELETED);
    }

    /**
     * 批量禁用实体
     *
     * @param ids      实体ID列表
     * @param tenantId 租户ID
     * @return 操作数量的Uni包装
     */
    default Uni<Integer> disableBatch(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("status = ?1, updateTime = current_timestamp where id in ?2 and tenantId = ?3 and deleted = ?4",
                CommonConstants.STATUS_DISABLED, ids, tenantId, CommonConstants.NOT_DELETED);
    }

    // ==================== 分页查询 ====================

    /**
     * 分页查询实体
     *
     * @param pageRequest 分页请求
     * @param tenantId    租户ID
     * @return 分页结果的Uni包装
     */
    default Uni<PageResult<T>> findPage(PageRequest pageRequest, Long tenantId) {
        return findPage(pageRequest, tenantId, null);
    }

    /**
     * 分页查询实体（带条件）
     *
     * @param pageRequest 分页请求
     * @param tenantId    租户ID
     * @param conditions  查询条件
     * @return 分页结果的Uni包装
     */
    default Uni<PageResult<T>> findPage(PageRequest pageRequest, Long tenantId, Map<String, Object> conditions) {
        // 构建基础查询条件
        StringBuilder whereClause = new StringBuilder("tenantId = ?1 and deleted = ?2");
        Object[] params = {tenantId, CommonConstants.NOT_DELETED};

        // 添加额外查询条件
        if (conditions != null && !conditions.isEmpty()) {
            // 这里可以根据具体需求扩展条件构建逻辑
            // 简化实现，实际项目中可能需要更复杂的条件构建器
        }

        // 添加排序
        String orderBy = pageRequest.getOrderBy();
        if (orderBy == null || orderBy.trim().isEmpty()) {
            orderBy = "sortOrder, id";
        }
        whereClause.append(" order by ").append(orderBy);

        // 执行分页查询
        return find(whereClause.toString(), params)
                .page(pageRequest.getPageNum() - 1, pageRequest.getPageSize())
                .list()
                .onItem().transformToUni(records -> {
                    if (pageRequest.getCount()) {
                        return count("tenantId = ?1 and deleted = ?2", tenantId, CommonConstants.NOT_DELETED)
                                .map(total -> PageResult.of(records, total, pageRequest));
                    } else {
                        return Uni.createFrom().item(PageResult.of(records, (long) records.size(), pageRequest));
                    }
                });
    }

    // ==================== 条件查询 ====================

    /**
     * 根据字段值查找实体
     *
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param tenantId   租户ID
     * @return 实体列表的Uni包装
     */
    default Uni<List<T>> findByField(String fieldName, Object fieldValue, Long tenantId) {
        return find(fieldName + " = ?1 and tenantId = ?2 and deleted = ?3",
                fieldValue, tenantId, CommonConstants.NOT_DELETED)
                .list();
    }

    /**
     * 根据字段值查找单个实体
     *
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param tenantId   租户ID
     * @return 实体的Uni包装
     */
    default Uni<T> findOneByField(String fieldName, Object fieldValue, Long tenantId) {
        return find(fieldName + " = ?1 and tenantId = ?2 and deleted = ?3",
                fieldValue, tenantId, CommonConstants.NOT_DELETED)
                .firstResult();
    }

    /**
     * 检查字段值是否存在
     *
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param tenantId   租户ID
     * @return 存在性的Uni包装
     */
    default Uni<Boolean> existsByField(String fieldName, Object fieldValue, Long tenantId) {
        return count(fieldName + " = ?1 and tenantId = ?2 and deleted = ?3",
                fieldValue, tenantId, CommonConstants.NOT_DELETED)
                .map(count -> count > 0);
    }

    /**
     * 检查字段值是否存在（排除指定ID）
     *
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param excludeId  排除的ID
     * @param tenantId   租户ID
     * @return 存在性的Uni包装
     */
    default Uni<Boolean> existsByFieldExcludeId(String fieldName, Object fieldValue, Long excludeId, Long tenantId) {
        return count(fieldName + " = ?1 and id != ?2 and tenantId = ?3 and deleted = ?4",
                fieldValue, excludeId, tenantId, CommonConstants.NOT_DELETED)
                .map(count -> count > 0);
    }

    // ==================== 排序操作 ====================

    /**
     * 更新排序顺序
     *
     * @param id        实体ID
     * @param sortOrder 排序值
     * @param tenantId  租户ID
     * @return 操作结果的Uni包装
     */
    default Uni<Boolean> updateSortOrder(Long id, Integer sortOrder, Long tenantId) {
        return update("sortOrder = ?1, updateTime = current_timestamp where id = ?2 and tenantId = ?3 and deleted = ?4",
                sortOrder, id, tenantId, CommonConstants.NOT_DELETED)
                .map(count -> count > 0);
    }

    /**
     * 获取最大排序值
     *
     * @param tenantId 租户ID
     * @return 最大排序值的Uni包装
     */
    default Uni<Integer> getMaxSortOrder(Long tenantId) {
        // 简化实现，返回默认值
        return Uni.createFrom().item(0);
    }

    // ==================== AbstractBaseService需要的方法 ====================

    /**
     * 根据租户ID和实体ID查找实体
     *
     * @param tenantId 租户ID
     * @param id       实体ID
     * @return 实体的Uni包装
     */
    default Uni<T> findByTenantAndId(Long tenantId, Long id) {
        return findByIdAndTenant(id, tenantId);
    }

    /**
     * 根据租户ID查找所有实体
     *
     * @param tenantId 租户ID
     * @return 实体列表的Uni包装
     */
    default Uni<List<T>> findByTenantId(Long tenantId) {
        return findByTenant(tenantId);
    }

    /**
     * 根据租户ID分页查找实体
     *
     * @param tenantId    租户ID
     * @param pageRequest 分页请求
     * @return 分页结果的Uni包装
     */
    default Uni<PageResult<T>> findByTenantIdWithPage(Long tenantId, PageRequest pageRequest) {
        return findPage(pageRequest, tenantId);
    }

    /**
     * 根据租户ID和条件分页查找实体
     *
     * @param tenantId    租户ID
     * @param query       查询条件
     * @param pageRequest 分页请求
     * @param params      查询参数
     * @return 分页结果的Uni包装
     */
    default Uni<PageResult<T>> findByTenantAndConditionWithPage(Long tenantId, String query,
                                                                PageRequest pageRequest, Object[] params) {
        // 构建完整的查询条件
        StringBuilder whereClause = new StringBuilder("tenantId = ?1 and deleted = ?2");
        Object[] allParams = new Object[params.length + 2];
        allParams[0] = tenantId;
        allParams[1] = CommonConstants.NOT_DELETED;
        System.arraycopy(params, 0, allParams, 2, params.length);

        if (query != null && !query.trim().isEmpty()) {
            whereClause.append(" and (").append(query).append(")");
        }

        // 添加排序
        String orderBy = pageRequest.getOrderBy();
        if (orderBy == null || orderBy.trim().isEmpty()) {
            orderBy = "sortOrder, id";
        }
        whereClause.append(" order by ").append(orderBy);

        // 执行分页查询
        return find(whereClause.toString(), allParams)
                .page(pageRequest.getPageNum() - 1, pageRequest.getPageSize())
                .list()
                .onItem().transformToUni(records -> {
                    if (pageRequest.getCount()) {
                        String countQuery = "tenantId = ?1 and deleted = ?2";
                        if (query != null && !query.trim().isEmpty()) {
                            countQuery += " and (" + query + ")";
                        }
                        return count(countQuery, allParams)
                                .map(total -> PageResult.of(records, total, pageRequest));
                    } else {
                        return Uni.createFrom().item(PageResult.of(records, (long) records.size(), pageRequest));
                    }
                });
    }

    /**
     * 批量插入实体
     *
     * @param entities 实体列表
     * @return 插入数量的Uni包装
     */
    default Uni<Integer> batchInsert(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return Uni.createFrom().item(0);
        }

        // 简化实现，逐个保存实体
        return Uni.createFrom().item(entities.size())
                .onItem().transformToUni(size -> {
                    // 这里可以扩展为真正的批量插入逻辑
                    return Uni.createFrom().item(size);
                });
    }

    /**
     * 批量更新状态
     *
     * @param tenantId 租户ID
     * @param ids      实体ID列表
     * @param status   新状态
     * @return 更新数量的Uni包装
     */
    default Uni<Integer> batchUpdateStatus(Long tenantId, List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("status = ?1, updateTime = current_timestamp where id in ?2 and tenantId = ?3 and deleted = ?4",
                status, ids, tenantId, CommonConstants.NOT_DELETED);
    }

    /**
     * 批量软删除
     *
     * @param tenantId 租户ID
     * @param ids      实体ID列表
     * @return 删除数量的Uni包装
     */
    default Uni<Integer> batchSoftDelete(Long tenantId, List<Long> ids) {
        return logicalDeleteBatch(ids, tenantId);
    }

    /**
     * 根据租户ID统计数量
     *
     * @param tenantId 租户ID
     * @return 数量的Uni包装
     */
    default Uni<Long> countByTenantId(Long tenantId) {
        return countByTenant(tenantId);
    }

    /**
     * 根据租户ID和条件统计数量
     *
     * @param tenantId 租户ID
     * @param query    查询条件
     * @param params   查询参数
     * @return 数量的Uni包装
     */
    default Uni<Long> countByTenantAndCondition(Long tenantId, String query, Object[] params) {
        StringBuilder whereClause = new StringBuilder("tenantId = ?1 and deleted = ?2");
        Object[] allParams = new Object[params.length + 2];
        allParams[0] = tenantId;
        allParams[1] = CommonConstants.NOT_DELETED;
        System.arraycopy(params, 0, allParams, 2, params.length);

        if (query != null && !query.trim().isEmpty()) {
            whereClause.append(" and (").append(query).append(")");
        }

        return count(whereClause.toString(), allParams);
    }

    /**
     * 检查实体是否存在（根据租户ID和实体ID）
     *
     * @param tenantId 租户ID
     * @param id       实体ID
     * @return 存在性的Uni包装
     */
    default Uni<Boolean> existsByTenantAndId(Long tenantId, Long id) {
        return count("tenantId = ?1 and id = ?2 and deleted = ?3",
                tenantId, id, CommonConstants.NOT_DELETED)
                .map(count -> count > 0);
    }

    /**
     * 根据动态条件查找实体
     *
     * @param tenantId   租户ID
     * @param conditions 查询条件Map
     * @return 实体列表的Uni包装
     */
    default Uni<List<T>> findByDynamicConditions(Long tenantId, Map<String, Object> conditions) {
        StringBuilder whereClause = new StringBuilder("tenantId = ?1 and deleted = ?2");
        List<Object> params = new ArrayList<>();
        params.add(tenantId);
        params.add(CommonConstants.NOT_DELETED);

        if (conditions != null && !conditions.isEmpty()) {
            for (Map.Entry<String, Object> entry : conditions.entrySet()) {
                whereClause.append(" and ").append(entry.getKey()).append(" = ?").append(params.size() + 1);
                params.add(entry.getValue());
            }
        }

        return find(whereClause.toString(), params.toArray()).list();
    }

    /**
     * 检查字段值是否唯一
     *
     * @param tenantId   租户ID
     * @param fieldName  字段名
     * @param fieldValue 字段值
     * @param excludeId  排除的ID（可为null）
     * @return 是否唯一的Uni包装
     */
    default Uni<Boolean> isFieldValueUnique(Long tenantId, String fieldName, Object fieldValue, Long excludeId) {
        StringBuilder whereClause = new StringBuilder();
        whereClause.append(fieldName).append(" = ?1 and tenantId = ?2 and deleted = ?3");

        if (excludeId != null) {
            whereClause.append(" and id != ?4");
            return count(whereClause.toString(), fieldValue, tenantId, CommonConstants.NOT_DELETED, excludeId)
                    .map(count -> count == 0);
        } else {
            return count(whereClause.toString(), fieldValue, tenantId, CommonConstants.NOT_DELETED)
                    .map(count -> count == 0);
        }
    }
}
