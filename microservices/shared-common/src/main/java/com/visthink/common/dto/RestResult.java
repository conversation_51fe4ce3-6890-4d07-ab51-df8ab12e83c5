package com.visthink.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * REST API 统一返回结果
 * 
 * 提供标准化的REST API响应格式，兼容旧版本API
 * 
 * @param <T> 响应数据类型
 * <AUTHOR>
 */
@Data
public class RestResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 响应状态码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 默认构造函数
     */
    public RestResult() {
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     *
     * @param success 是否成功
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     */
    public RestResult(Boolean success, String code, String message, T data) {
        this();
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功响应
     */
    public static <T> RestResult<T> success(T data) {
        return new RestResult<>(true, "SUCCESS", "操作成功", data);
    }
    
    /**
     * 创建成功响应
     *
     * @param message 自定义消息
     * @param data    响应数据
     * @param <T>     数据类型
     * @return 成功响应
     */
    public static <T> RestResult<T> success(String message, T data) {
        return new RestResult<>(true, "SUCCESS", message, data);
    }
    
    /**
     * 创建成功响应（无数据）
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> RestResult<T> success() {
        return new RestResult<>(true, "SUCCESS", "操作成功", null);
    }
    
    /**
     * 创建成功响应（无数据）
     *
     * @param message 自定义消息
     * @param <T>     数据类型
     * @return 成功响应
     */
    public static <T> RestResult<T> success(String message) {
        return new RestResult<>(true, "SUCCESS", message, null);
    }
    
    /**
     * 创建错误响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> RestResult<T> error(String code, String message) {
        return new RestResult<>(false, code, message, null);
    }
    
    /**
     * 创建错误响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> RestResult<T> error(String message) {
        return new RestResult<>(false, "ERROR", message, null);
    }
    
    /**
     * 创建错误响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @param data    错误数据
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> RestResult<T> error(String code, String message, T data) {
        return new RestResult<>(false, code, message, data);
    }
    
    /**
     * 创建失败响应
     *
     * @param message 失败消息
     * @param <T>     数据类型
     * @return 失败响应
     */
    public static <T> RestResult<T> fail(String message) {
        return new RestResult<>(false, "FAIL", message, null);
    }
    
    /**
     * 创建失败响应
     *
     * @param code    失败码
     * @param message 失败消息
     * @param <T>     数据类型
     * @return 失败响应
     */
    public static <T> RestResult<T> fail(String code, String message) {
        return new RestResult<>(false, code, message, null);
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(this.success);
    }
    
    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isFail() {
        return !isSuccess();
    }
    
    /**
     * 设置时间戳
     *
     * @param startTime 开始时间（毫秒）
     * @return 当前对象
     */
    public RestResult<T> setTimestamp(long startTime) {
        this.timestamp = LocalDateTime.now();
        return this;
    }
    
    @Override
    public String toString() {
        return String.format("RestResult{success=%s, code='%s', message='%s', data=%s, timestamp=%s}", 
                success, code, message, data, timestamp);
    }
}
