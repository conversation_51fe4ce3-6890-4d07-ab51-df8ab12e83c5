package com.visthink.common.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 统一分页响应DTO
 * 
 * 提供标准化的分页查询结果，包括数据列表、总数、页码等信息
 * 支持泛型，可以包装任意类型的数据列表
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 */
@Data
public class PageResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 页面大小
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer totalPages;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 是否为第一页
     */
    private Boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private Boolean isLast;
    
    /**
     * 当前页的记录数
     */
    private Integer size;
    
    /**
     * 是否为空页
     */
    private Boolean isEmpty;
    
    /**
     * 查询耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 扩展信息
     */
    private Object extra;
    
    // ==================== 构造函数 ====================
    
    /**
     * 默认构造函数
     */
    public PageResult() {
        this.records = Collections.emptyList();
        this.total = 0L;
        this.pageNum = 1;
        this.pageSize = 20;
        this.calculateDerivedFields();
    }
    
    /**
     * 构造函数
     *
     * @param records  数据列表
     * @param total    总记录数
     * @param pageNum  页码
     * @param pageSize 页面大小
     */
    public PageResult(List<T> records, Long total, Integer pageNum, Integer pageSize) {
        this.records = records != null ? records : Collections.emptyList();
        this.total = total != null ? total : 0L;
        this.pageNum = pageNum != null ? pageNum : 1;
        this.pageSize = pageSize != null ? pageSize : 20;
        this.calculateDerivedFields();
    }
    
    /**
     * 构造函数
     *
     * @param records     数据列表
     * @param total       总记录数
     * @param pageRequest 分页请求
     */
    public PageResult(List<T> records, Long total, PageRequest pageRequest) {
        this.records = records != null ? records : Collections.emptyList();
        this.total = total != null ? total : 0L;
        this.pageNum = pageRequest != null ? pageRequest.getPageNum() : 1;
        this.pageSize = pageRequest != null ? pageRequest.getPageSize() : 20;
        this.calculateDerivedFields();
    }
    
    // ==================== 计算衍生字段 ====================
    
    /**
     * 计算衍生字段
     */
    private void calculateDerivedFields() {
        // 计算总页数
        this.totalPages = (int) Math.ceil((double) total / pageSize);
        if (this.totalPages < 1) {
            this.totalPages = 1;
        }
        
        // 计算当前页记录数
        this.size = records.size();
        
        // 判断是否为空页
        this.isEmpty = records.isEmpty();
        
        // 判断是否有上一页
        this.hasPrevious = pageNum > 1;
        
        // 判断是否有下一页
        this.hasNext = pageNum < totalPages;
        
        // 判断是否为第一页
        this.isFirst = pageNum == 1;
        
        // 判断是否为最后一页
        this.isLast = pageNum.equals(totalPages);
    }
    
    // ==================== 设置方法重写 ====================
    
    /**
     * 设置数据列表并重新计算衍生字段
     *
     * @param records 数据列表
     */
    public void setRecords(List<T> records) {
        this.records = records != null ? records : Collections.emptyList();
        this.calculateDerivedFields();
    }
    
    /**
     * 设置总记录数并重新计算衍生字段
     *
     * @param total 总记录数
     */
    public void setTotal(Long total) {
        this.total = total != null ? total : 0L;
        this.calculateDerivedFields();
    }
    
    /**
     * 设置页码并重新计算衍生字段
     *
     * @param pageNum 页码
     */
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum != null ? pageNum : 1;
        this.calculateDerivedFields();
    }
    
    /**
     * 设置页面大小并重新计算衍生字段
     *
     * @param pageSize 页面大小
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize != null ? pageSize : 20;
        this.calculateDerivedFields();
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 转换数据类型
     *
     * @param mapper 转换函数
     * @param <R>    目标类型
     * @return 转换后的分页结果
     */
    public <R> PageResult<R> map(Function<T, R> mapper) {
        List<R> mappedRecords = records.stream()
                .map(mapper)
                .collect(Collectors.toList());
        
        PageResult<R> result = new PageResult<>();
        result.setRecords(mappedRecords);
        result.setTotal(this.total);
        result.setPageNum(this.pageNum);
        result.setPageSize(this.pageSize);
        result.setDuration(this.duration);
        result.setExtra(this.extra);
        
        return result;
    }
    
    /**
     * 设置查询耗时
     *
     * @param startTime 开始时间（毫秒）
     * @return 当前对象
     */
    public PageResult<T> setDuration(long startTime) {
        this.duration = System.currentTimeMillis() - startTime;
        return this;
    }
    
    /**
     * 设置扩展信息
     *
     * @param extra 扩展信息
     * @return 当前对象
     */
    public PageResult<T> setExtra(Object extra) {
        this.extra = extra;
        return this;
    }
    
    /**
     * 获取开始记录号（从1开始）
     *
     * @return 开始记录号
     */
    public int getStartRow() {
        return (pageNum - 1) * pageSize + 1;
    }
    
    /**
     * 获取结束记录号
     *
     * @return 结束记录号
     */
    public int getEndRow() {
        return Math.min(getStartRow() + size - 1, total.intValue());
    }
    
    /**
     * 获取分页信息摘要
     *
     * @return 分页信息字符串
     */
    public String getSummary() {
        if (total == 0) {
            return "暂无数据";
        }
        return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页", 
                getStartRow(), getEndRow(), total, pageNum, totalPages);
    }
    
    // ==================== 静态工厂方法 ====================
    
    /**
     * 创建空的分页结果
     *
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>();
    }
    
    /**
     * 创建空的分页结果
     *
     * @param pageRequest 分页请求
     * @param <T>         数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty(PageRequest pageRequest) {
        return new PageResult<>(Collections.emptyList(), 0L, pageRequest);
    }
    
    /**
     * 创建分页结果
     *
     * @param records  数据列表
     * @param total    总记录数
     * @param pageNum  页码
     * @param pageSize 页面大小
     * @param <T>      数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, Integer pageNum, Integer pageSize) {
        return new PageResult<>(records, total, pageNum, pageSize);
    }
    
    /**
     * 创建分页结果
     *
     * @param records     数据列表
     * @param total       总记录数
     * @param pageRequest 分页请求
     * @param <T>         数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Long total, PageRequest pageRequest) {
        return new PageResult<>(records, total, pageRequest);
    }
    
    /**
     * 创建单页结果（不分页）
     *
     * @param records 数据列表
     * @param <T>     数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> single(List<T> records) {
        List<T> data = records != null ? records : Collections.emptyList();
        return new PageResult<>(data, (long) data.size(), 1, data.size());
    }

    // ==================== 兼容性方法 ====================

    /**
     * 获取页码（兼容方法）
     * @return 页码
     */
    public Integer getPage() {
        return pageNum;
    }

    /**
     * 设置页码（兼容方法）
     * @param page 页码
     */
    public void setPage(Integer page) {
        setPageNum(page);
    }

    /**
     * 获取页面大小（兼容方法）
     * @return 页面大小
     */
    public Integer getSize() {
        return pageSize;
    }

    /**
     * 设置页面大小（兼容方法）
     * @param size 页面大小
     */
    public void setSize(Integer size) {
        setPageSize(size);
    }

    /**
     * 获取总页数（兼容方法）
     * @return 总页数
     */
    public Integer getPages() {
        return totalPages;
    }

    /**
     * 设置总页数（兼容方法）
     * @param pages 总页数
     */
    public void setPages(Integer pages) {
        this.totalPages = pages;
    }
    
    @Override
    public String toString() {
        return String.format("PageResult{total=%d, pageNum=%d, pageSize=%d, totalPages=%d, size=%d, isEmpty=%s}", 
                total, pageNum, pageSize, totalPages, size, isEmpty);
    }
}
