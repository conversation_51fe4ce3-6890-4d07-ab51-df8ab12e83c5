package com.visthink.common.entity;

import com.visthink.common.constant.CommonConstants;
import io.quarkus.hibernate.reactive.panache.PanacheEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;


import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体类
 *
 * 提供所有业务实体的通用字段和多租户支持
 * 包含审计字段、租户隔离、乐观锁、逻辑删除等基础功能
 * 整合了原有的VBaseEntity功能，提供统一的实体基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@MappedSuperclass
public abstract class BaseEntity extends PanacheEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 创建人ID
     */
    @Column(name = "create_by", length = 50)
    public String createBy;

    /**
     * 记录创建时间（不可更新）
     */
    @Column(name = "create_time", updatable = false)
    public LocalDateTime createTime;

    /**
     * 创建人姓名
     */
    @Column(name = "create_name", length = 100, updatable = false)
    public String createName;

    /**
     * 最后更新人姓名
     */
    @Column(name = "update_name", length = 100)
    public String updateName;

    /**
     * 最后更新人ID
     */
    @Column(name = "update_by", length = 50)
    public String updateBy;

    /**
     * 最后更新时间
     */
    @Column(name = "update_time")
    public LocalDateTime updateTime;

    /**
     * 关联租户ID，用于多租户数据隔离
     * 平台管理员数据可以为null，表示跨租户数据
     * 注意：移除了@TenantId注解，使用应用层租户隔离而非Hibernate原生多租户
     */
    @Column(name = "tenant_id")
    public Long tenantId;

    /**
     * 业务备注信息
     */
    @Column(name = "remark", length = 1000)
    public String remark;

    /**
     * 逻辑删除标记
     * 0-未删除，1-已删除
     */
    @Column(name = "deleted", nullable = false)
    public Integer deleted = CommonConstants.NOT_DELETED;

    /**
     * 版本号，用于乐观锁控制
     */
    @Version
    @Column(name = "version")
    public Integer version;

    /**
     * 数据状态
     * 1-正常，0-禁用
     */
    @Column(name = "status", nullable = false)
    public Integer status = CommonConstants.STATUS_ENABLED;

    /**
     * 排序字段
     */
    @Column(name = "sort_order")
    public Integer sortOrder = 0;

    /**
     * 扩展字段1（预留）
     */
    @Column(name = "ext_field1", length = 255)
    public String extField1;

    /**
     * 扩展字段2（预留）
     */
    @Column(name = "ext_field2", length = 255)
    public String extField2;

    /**
     * 扩展字段3（预留）
     */
    @Column(name = "ext_field3", length = 255)
    public String extField3;

    /**
     * JSON扩展字段（用于存储复杂的扩展数据）
     */
    @Column(name = "ext_json", columnDefinition = "TEXT")
    public String extJson;

    // ==================== 生命周期回调方法 ====================

    /**
     * 持久化前的回调
     * 自动设置创建时间、创建人等信息
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (deleted == null) {
            deleted = CommonConstants.NOT_DELETED;
        }
        if (status == null) {
            status = CommonConstants.STATUS_ENABLED;
        }
        if (version == null) {
            version = 0L;
        }
        if (sortOrder == null) {
            sortOrder = 0;
        }
    }

    /**
     * 更新前的回调
     * 自动设置更新时间
     */
    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }

    // ==================== 便捷方法 ====================

    /**
     * 判断是否为新实体（未持久化）
     *
     * @return true表示新实体，false表示已持久化
     */
    public boolean isNew() {
        return id == null;
    }

    /**
     * 判断是否已删除
     *
     * @return true表示已删除，false表示未删除
     */
    public boolean isDeleted() {
        return CommonConstants.DELETED.equals(deleted);
    }

    /**
     * 判断是否启用
     *
     * @return true表示启用，false表示禁用
     */
    public boolean isEnabled() {
        return CommonConstants.STATUS_ENABLED.equals(status);
    }

    /**
     * 判断是否禁用
     *
     * @return true表示禁用，false表示启用
     */
    public boolean isDisabled() {
        return !isEnabled();
    }

    /**
     * 设置为已删除状态
     */
    public void markDeleted() {
        this.deleted = CommonConstants.DELETED;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置为未删除状态
     */
    public void markUndeleted() {
        this.deleted = CommonConstants.NOT_DELETED;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置为启用状态
     */
    public void enable() {
        this.status = CommonConstants.STATUS_ENABLED;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置为禁用状态
     */
    public void disable() {
        this.status = CommonConstants.STATUS_DISABLED;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置创建人信息
     *
     * @param userId   用户ID
     * @param userName 用户名
     */
    public void setCreateUser(String userId, String userName) {
        this.createBy = userId;
        this.createName = userName;
    }

    /**
     * 设置更新人信息
     *
     * @param userId   用户ID
     * @param userName 用户名
     */
    public void setUpdateUser(String userId, String userName) {
        this.updateBy = userId;
        this.updateName = userName;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 设置租户信息
     *
     * @param tenantId 租户ID
     */
    public void setTenant(Long tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * 复制审计字段到目标实体
     *
     * @param target 目标实体
     */
    public void copyAuditFieldsTo(BaseEntity target) {
        if (target != null) {
            target.createBy = this.createBy;
            target.createTime = this.createTime;
            target.createName = this.createName;
            target.tenantId = this.tenantId;
        }
    }

    /**
     * 从源实体复制审计字段
     *
     * @param source 源实体
     */
    public void copyAuditFieldsFrom(BaseEntity source) {
        if (source != null) {
            this.createBy = source.createBy;
            this.createTime = source.createTime;
            this.createName = source.createName;
            this.tenantId = source.tenantId;
        }
    }

    /**
     * 获取实体的简要描述
     *
     * @return 实体描述
     */
    public String getEntityDescription() {
        return String.format("%s[id=%d, tenantId=%d, status=%d, deleted=%d]",
                getClass().getSimpleName(), id, tenantId, status, deleted);
    }

    /**
     * 验证实体的基本字段
     *
     * @return 验证结果，true表示通过，false表示失败
     */
    public boolean validateBasicFields() {
        return tenantId != null &&
               status != null &&
               deleted != null &&
               version != null;
    }

    @Override
    public String toString() {
        return String.format("%s{id=%d, tenantId=%d, createTime=%s, updateTime=%s, status=%d, deleted=%d, version=%d}",
                getClass().getSimpleName(), id, tenantId, createTime, updateTime, status, deleted, version);
    }
}
