package com.visthink.erp.utils;

import cn.hutool.crypto.digest.BCrypt;

public class PasswordUtils {
    
    private PasswordUtils() {
        throw new IllegalStateException("Utility class");
    }
    
    public static String encrypt(String password) {
        return BCrypt.hashpw(password);
    }
    
    public static boolean verify(String password, String hashedPassword) {
        return BCrypt.checkpw(password, hashedPassword);
    }
} 