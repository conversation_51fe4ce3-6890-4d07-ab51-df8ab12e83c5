package com.visthink.common.base;

import com.visthink.common.context.TenantContext;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import java.util.List;

/**
 * 通用Service基础抽象类
 * 提供基础的CRUD操作和租户验证功能
 *
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 */
public abstract class BaseService<T, ID> {

    protected final Logger log = Logger.getLogger(getClass());

    @Inject
    protected TenantContext tenantContext;

    /**
     * 验证租户ID
     * 
     * @param tenantId 租户ID
     * @return 验证结果
     */
    protected Uni<Void> validateTenant(Long tenantId) {
        if (tenantId == null || tenantId <= 0) {
            return Uni.createFrom().failure(new IllegalArgumentException("租户ID不能为空"));
        }
        return Uni.createFrom().voidItem();
    }

    /**
     * 根据ID和租户ID查询实体
     * 
     * @param id 实体ID
     * @param tenantId 租户ID
     * @return 实体对象
     */
    public abstract Uni<T> findByIdAndTenant(ID id, Long tenantId);

    /**
     * 根据租户ID查询所有实体
     * 
     * @param tenantId 租户ID
     * @return 实体列表
     */
    public abstract Uni<List<T>> findAllByTenant(Long tenantId);

    /**
     * 创建实体
     * 
     * @param entity 实体对象
     * @return 创建的实体
     */
    public abstract Uni<T> create(T entity);

    /**
     * 更新实体
     * 
     * @param entity 实体对象
     * @return 更新的实体
     */
    public abstract Uni<T> update(T entity);

    /**
     * 删除实体
     * 
     * @param id 实体ID
     * @param tenantId 租户ID
     * @return 是否删除成功
     */
    public abstract Uni<Boolean> delete(ID id, Long tenantId);

    /**
     * 统计实体数量
     * 
     * @param tenantId 租户ID
     * @return 实体数量
     */
    public abstract Uni<Long> count(Long tenantId);

    /**
     * 检查实体是否存在
     * 
     * @param id 实体ID
     * @param tenantId 租户ID
     * @return 是否存在
     */
    public abstract Uni<Boolean> exists(ID id, Long tenantId);

    /**
     * 批量删除实体
     * 
     * @param ids 实体ID列表
     * @param tenantId 租户ID
     * @return 删除成功的数量
     */
    public abstract Uni<Integer> batchDelete(List<ID> ids, Long tenantId);

    /**
     * 获取实体名称（用于日志记录）
     * 
     * @return 实体名称
     */
    protected abstract String getEntityName();

    /**
     * 记录操作日志
     * 
     * @param operation 操作类型
     * @param entityId 实体ID
     * @param tenantId 租户ID
     * @param message 日志消息
     */
    protected void logOperation(String operation, ID entityId, Long tenantId, String message) {
        log.infof("[%s] %s - 租户ID: %d, 实体ID: %s, 消息: %s", 
            getEntityName(), operation, tenantId, entityId, message);
    }

    /**
     * 记录错误日志
     * 
     * @param operation 操作类型
     * @param entityId 实体ID
     * @param tenantId 租户ID
     * @param throwable 异常信息
     */
    protected void logError(String operation, ID entityId, Long tenantId, Throwable throwable) {
        log.errorf(throwable, "[%s] %s失败 - 租户ID: %d, 实体ID: %s", 
            getEntityName(), operation, tenantId, entityId);
    }
}
