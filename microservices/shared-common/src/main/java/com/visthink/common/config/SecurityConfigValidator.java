package com.visthink.common.config;

import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

/**
 * 安全配置验证器
 *
 * 在应用启动时验证安全配置的完整性和安全性
 * 确保生产环境不使用默认的不安全配置
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class SecurityConfigValidator {

    private static final Logger LOG = Logger.getLogger(SecurityConfigValidator.class);

    @Inject
    SecureConfigManager secureConfigManager;

    /**
     * 应用启动时验证安全配置
     */
    void onStart(@Observes StartupEvent ev) {
        LOG.info("开始验证应用安全配置...");

        try {
            // 验证必需的配置
            validateRequiredConfigs();

            // 检查不安全的默认值
            checkInsecureDefaults();

            // 验证配置格式和有效性
            validateConfigFormats();

            // 输出配置摘要
            logConfigSummary();

            LOG.info("安全配置验证完成 ✓");

        } catch (Exception e) {
            LOG.error("安全配置验证失败，应用可能无法正常运行", e);
            // 在生产环境中，可以考虑抛出异常阻止应用启动
            // throw new RuntimeException("安全配置验证失败", e);
        }
    }

    /**
     * 验证必需的配置
     */
    private void validateRequiredConfigs() {
        LOG.info("验证必需的安全配置...");

        try {
            secureConfigManager.validateRequiredConfigs();
            LOG.info("必需配置验证通过 ✓");
        } catch (Exception e) {
            LOG.error("必需配置验证失败", e);
            throw e;
        }
    }

    /**
     * 检查不安全的默认值
     */
    private void checkInsecureDefaults() {
        LOG.info("检查不安全的默认配置...");

        try {
            secureConfigManager.checkDefaultValues();

            // 额外的安全检查
            checkJwtSecurityLevel();
            checkDatabaseSecurity();

            LOG.info("默认配置安全检查完成 ✓");
        } catch (Exception e) {
            LOG.warn("默认配置安全检查出现问题", e);
        }
    }

    /**
     * 验证配置格式和有效性
     */
    private void validateConfigFormats() {
        LOG.info("验证配置格式...");

        try {
            // 验证JWT配置
            validateJwtConfig();

            // 验证数据库配置
            validateDatabaseConfig();

            LOG.info("配置格式验证通过 ✓");
        } catch (Exception e) {
            LOG.error("配置格式验证失败", e);
            throw e;
        }
    }

    /**
     * 检查JWT安全级别
     */
    private void checkJwtSecurityLevel() {
        try {
            String jwtSecret = secureConfigManager.getJwtSecret();

            // 检查密钥强度
            if (jwtSecret.length() < 32) {
                LOG.warn("JWT密钥长度不足32位，建议使用更长的密钥");
            }

            // 检查密钥复杂度
            if (!isStrongPassword(jwtSecret)) {
                LOG.warn("JWT密钥复杂度不足，建议包含大小写字母、数字和特殊字符");
            }

            // 检查是否包含常见的不安全字符串
            String lowerSecret = jwtSecret.toLowerCase();
            String[] insecurePatterns = {"secret", "key", "password", "123456", "admin", "test"};
            for (String pattern : insecurePatterns) {
                if (lowerSecret.contains(pattern)) {
                    LOG.warn("JWT密钥包含常见的不安全字符串: " + pattern);
                    break;
                }
            }

        } catch (Exception e) {
            LOG.warn("JWT安全级别检查失败", e);
        }
    }

    /**
     * 检查数据库安全性
     */
    private void checkDatabaseSecurity() {
        try {
            String dbPassword = secureConfigManager.getDatabasePassword();

            if (dbPassword.length() < 8) {
                LOG.warn("数据库密码长度不足8位，存在安全风险");
            }

            if (!isStrongPassword(dbPassword)) {
                LOG.warn("数据库密码复杂度不足，建议使用更强的密码");
            }

        } catch (Exception e) {
            LOG.warn("数据库安全检查失败", e);
        }
    }

    /**
     * 验证JWT配置
     */
    private void validateJwtConfig() {
        // 验证JWT密钥
        String jwtSecret = secureConfigManager.getJwtSecret();
        if (jwtSecret == null || jwtSecret.trim().isEmpty()) {
            throw new IllegalStateException("JWT密钥不能为空");
        }

        // 验证JWT过期时间
        Long jwtExpiration = secureConfigManager.getJwtExpiration();
        if (jwtExpiration == null || jwtExpiration <= 0) {
            throw new IllegalStateException("JWT过期时间配置无效");
        }

        // 验证JWT签发者
        String jwtIssuer = secureConfigManager.getJwtIssuer();
        if (jwtIssuer == null || jwtIssuer.trim().isEmpty()) {
            throw new IllegalStateException("JWT签发者不能为空");
        }
    }

    /**
     * 验证数据库配置
     */
    private void validateDatabaseConfig() {
        // 验证数据库URL
        String dbUrl = secureConfigManager.getDatabaseUrl();
        if (dbUrl == null || dbUrl.trim().isEmpty()) {
            throw new IllegalStateException("数据库URL不能为空");
        }

        // 验证数据库用户名
        String dbUsername = secureConfigManager.getDatabaseUsername();
        if (dbUsername == null || dbUsername.trim().isEmpty()) {
            throw new IllegalStateException("数据库用户名不能为空");
        }

        // 验证数据库密码
        String dbPassword = secureConfigManager.getDatabasePassword();
        if (dbPassword == null || dbPassword.trim().isEmpty()) {
            throw new IllegalStateException("数据库密码不能为空");
        }
    }

    /**
     * 输出配置摘要
     */
    private void logConfigSummary() {
        LOG.info("=== 安全配置摘要 ===");
        LOG.info(secureConfigManager.getConfigSummary());
        LOG.info("==================");
    }

    /**
     * 检查密码强度
     */
    private boolean isStrongPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }

        boolean hasUpper = password.chars().anyMatch(Character::isUpperCase);
        boolean hasLower = password.chars().anyMatch(Character::isLowerCase);
        boolean hasDigit = password.chars().anyMatch(Character::isDigit);
        boolean hasSpecial = password.chars().anyMatch(ch -> !Character.isLetterOrDigit(ch));

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }
}
