package com.visthink.erp.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自动生成Service实现的注解
 * 在编译时自动生成Service实现类，减少样板代码
 *
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.SOURCE)
public @interface AutoService {

    /**
     * 实体类
     * 必须继承VisthinkBaseEntity
     */
    Class<?> entity();

    /**
     * Repository类
     * 必须继承BaseRepository
     */
    Class<?> repository();

    /**
     * 创建请求DTO类
     */
    Class<?> createRequest();

    /**
     * 更新请求DTO类
     * 默认为Void.class表示不支持更新操作
     */
    Class<?> updateRequest() default Void.class;

    /**
     * 查询请求DTO类
     * 默认为Void.class表示不支持复杂查询
     */
    Class<?> queryRequest() default Void.class;

    /**
     * 字段映射规则
     * 格式: "dtoField:entityField,dtoField2:entityField2"
     * 例如: "userName:username,userEmail:email"
     */
    String fieldMapping() default "";

    /**
     * 是否生成查询条件构建器
     * 如果为true，会根据queryRequest的字段自动生成查询条件
     */
    boolean generateQueryBuilder() default true;

    /**
     * 生成的实现类名称
     * 默认为接口名 + "Impl"
     */
    String implClassName() default "";

    /**
     * 生成的实现类包名
     * 默认为接口包名 + ".impl"
     */
    String implPackage() default "";

    /**
     * 是否生成业务方法的默认实现
     * 如果为true，会为接口中除BaseService定义外的方法生成默认实现
     */
    boolean generateBusinessMethods() default false;

    /**
     * 自定义转换器类
     * 用于复杂的字段转换逻辑
     */
    Class<?> converter() default Void.class;

    /**
     * 是否启用缓存
     * 如果为true，会在生成的代码中添加缓存相关逻辑
     */
    boolean enableCache() default false;

    /**
     * 是否启用审计日志
     * 如果为true，会在CRUD操作中自动记录审计日志
     */
    boolean enableAudit() default true;

    /**
     * 排除的字段列表
     * 这些字段不会参与自动映射
     */
    String[] excludeFields() default {};

    /**
     * 只读字段列表
     * 这些字段只在创建时设置，更新时忽略
     */
    String[] readOnlyFields() default {};
}
