package com.visthink.common.config;

import com.visthink.common.context.tenant.UnifiedTenantContext;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerRequestFilter;
import jakarta.ws.rs.container.PreMatching;
import jakarta.ws.rs.ext.Provider;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

@Provider
@PreMatching
public class TenantInterceptor implements ContainerRequestFilter {

    private static final Logger LOG = Logger.getLogger(TenantInterceptor.class);

    @Inject
    UnifiedTenantContext tenantContext;

    @Inject
    JsonWebToken jwt;

    @Override
    public void filter(ContainerRequestContext requestContext) {
        Long tenantId = extractTenantId(requestContext);

        if (tenantId != null) {
            tenantContext.setCurrentTenantId(tenantId);
            LOG.debugf("Tenant ID set to: %s", tenantId);
        } else {
            LOG.warn("No tenant ID provided in request, using default");
            tenantContext.setCurrentTenantId(1L); // 默认租户ID设为1
        }
    }

    /**
     * 从请求中提取租户ID
     * 优先级：请求头 > JWT声明
     */
    private Long extractTenantId(ContainerRequestContext requestContext) {
        // 1. 从请求头获取
        String headerTenantId = requestContext.getHeaderString("X-Tenant-ID");
        if (headerTenantId != null && !headerTenantId.trim().isEmpty()) {
            try {
                return Long.parseLong(headerTenantId.trim());
            } catch (NumberFormatException e) {
                LOG.warnf("Invalid tenant ID in header: %s", headerTenantId);
            }
        }

        // 2. 从JWT获取
        try {
            if (jwt != null && jwt.containsClaim("tenantId")) {
                Object tenantClaim = jwt.getClaim("tenantId");
                if (tenantClaim instanceof Number) {
                    return ((Number) tenantClaim).longValue();
                } else if (tenantClaim instanceof String) {
                    return Long.parseLong((String) tenantClaim);
                }
            }
        } catch (Exception e) {
            LOG.debug("Failed to extract tenant ID from JWT", e);
        }

        return null;
    }
}
