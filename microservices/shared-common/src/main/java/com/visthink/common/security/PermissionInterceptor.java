package com.visthink.common.security;

import com.visthink.common.context.TenantContext;
import io.smallrye.mutiny.Uni;
import jakarta.annotation.Priority;
import jakarta.inject.Inject;
import jakarta.interceptor.AroundInvoke;
import jakarta.interceptor.Interceptor;
import jakarta.interceptor.InvocationContext;
import jakarta.ws.rs.ForbiddenException;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

import java.lang.reflect.Method;
import java.util.concurrent.CompletionStage;

/**
 * 权限验证拦截器
 *
 * 拦截带有@RequiresPermission注解的方法调用
 * 在方法执行前进行权限验证，确保用户有足够权限
 * 支持响应式编程模式
 *
 * <AUTHOR>
 */
@Interceptor
@RequiresPermission
@Priority(Interceptor.Priority.PLATFORM_BEFORE + 10)
public class PermissionInterceptor {

    private static final Logger LOG = Logger.getLogger(PermissionInterceptor.class);

    @Inject
    PermissionService permissionService;

    @Inject
    TenantContext tenantContext;

    @Inject
    JsonWebToken jwt;

    /**
     * 拦截方法调用，进行权限验证
     */
    @AroundInvoke
    public Object intercept(InvocationContext context) throws Exception {
        Method method = context.getMethod();
        RequiresPermission annotation = getPermissionAnnotation(method);

        if (annotation == null) {
            // 没有权限注解，直接执行
            return context.proceed();
        }

        // 获取权限配置
        String[] permissions = annotation.value();
        RequiresPermission.Logical logical = annotation.logical();
        boolean checkTenant = annotation.checkTenant();
        String errorMessage = annotation.message();

        if (permissions.length == 0) {
            LOG.warn("权限注解配置为空，跳过权限验证");
            return context.proceed();
        }

        // 执行权限验证
        Uni<Boolean> permissionCheck = performPermissionCheck(permissions, logical, checkTenant);

        // 处理响应式返回类型
        Object result = context.proceed();
        if (result instanceof Uni) {
            return permissionCheck
                    .onItem().transformToUni(hasPermission -> {
                        if (!hasPermission) {
                            return Uni.createFrom().failure(
                                new ForbiddenException(errorMessage)
                            );
                        }
                        return (Uni<?>) result;
                    });
        } else if (result instanceof CompletionStage) {
            return permissionCheck
                    .onItem().transformToUni(hasPermission -> {
                        if (!hasPermission) {
                            return Uni.createFrom().failure(
                                new ForbiddenException(errorMessage)
                            );
                        }
                        return Uni.createFrom().completionStage((CompletionStage<?>) result);
                    })
                    .convert().toCompletionStage();
        } else {
            // 同步方法，需要阻塞等待权限验证结果
            Boolean hasPermission = permissionCheck.await().indefinitely();
            if (!hasPermission) {
                throw new ForbiddenException(errorMessage);
            }
            return result;
        }
    }

    /**
     * 获取权限注解
     */
    private RequiresPermission getPermissionAnnotation(Method method) {
        // 先检查方法级注解
        RequiresPermission methodAnnotation = method.getAnnotation(RequiresPermission.class);
        if (methodAnnotation != null) {
            return methodAnnotation;
        }

        // 再检查类级注解
        return method.getDeclaringClass().getAnnotation(RequiresPermission.class);
    }

    /**
     * 执行权限验证
     */
    private Uni<Boolean> performPermissionCheck(String[] permissions,
                                              RequiresPermission.Logical logical,
                                              boolean checkTenant) {

        String userId = getCurrentUserId();
        LOG.debugf("开始权限验证 - 用户：%s，权限：%s，逻辑：%s",
                  userId, String.join(",", permissions), logical);

        Uni<Boolean> permissionResult;

        if (logical == RequiresPermission.Logical.AND) {
            // 需要拥有所有权限
            permissionResult = permissionService.hasAllPermissions(permissions);
        } else {
            // 拥有任一权限即可
            permissionResult = permissionService.hasAnyPermission(permissions);
        }

        // 如果需要检查租户权限
        if (checkTenant) {
            return tenantContext.getCurrentTenantId()
                    .onItem().transformToUni(tenantId -> {
                        LOG.debugf("验证租户权限 - 租户：%d，用户：%s", tenantId, userId);
                        return permissionResult;
                    });
        }

        return permissionResult
                .onItem().invoke(hasPermission -> {
                    if (hasPermission) {
                        LOG.debugf("权限验证通过 - 用户：%s", userId);
                    } else {
                        LOG.warnf("权限验证失败 - 用户：%s，权限：%s",
                                userId, String.join(",", permissions));
                    }
                });
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            if (jwt != null && jwt.getSubject() != null) {
                return jwt.getSubject();
            }
            return "anonymous";
        } catch (Exception e) {
            LOG.debug("获取用户ID失败", e);
            return "unknown";
        }
    }
}
