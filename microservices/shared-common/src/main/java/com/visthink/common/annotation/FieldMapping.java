package com.visthink.erp.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段映射注解
 * 用于指定DTO字段到实体字段的映射关系
 * 
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldMapping {
    
    /**
     * 目标实体字段名
     * 如果不指定，则使用相同的字段名
     */
    String target() default "";
    
    /**
     * 是否忽略此字段
     */
    boolean ignore() default false;
    
    /**
     * 转换器类
     * 用于复杂类型转换
     */
    Class<?> converter() default Void.class;
    
    /**
     * 转换表达式
     * 支持简单的表达式，如 "field1 + field2"
     */
    String expression() default "";
}
