package com.visthink.common.constant;

/**
 * 系统通用常量定义
 * 
 * 定义系统级别的通用常量，包括状态码、默认值、配置键等
 * 所有微服务共享的常量都应该定义在这里
 * 
 * <AUTHOR>
 */
public final class CommonConstants {
    
    /**
     * 私有构造函数，防止实例化
     */
    private CommonConstants() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
    
    // ==================== 租户相关常量 ====================
    
    /**
     * 默认租户ID
     */
    public static final Long DEFAULT_TENANT_ID = 1L;
    
    /**
     * 平台管理员租户ID（跨租户访问）
     */
    public static final Long PLATFORM_ADMIN_TENANT_ID = 0L;
    
    /**
     * 租户ID请求头名称
     */
    public static final String TENANT_ID_HEADER = "X-Tenant-ID";
    
    /**
     * 租户代码请求头名称
     */
    public static final String TENANT_CODE_HEADER = "X-Tenant-Code";
    
    // ==================== 状态相关常量 ====================
    
    /**
     * 启用状态
     */
    public static final Integer STATUS_ENABLED = 1;
    
    /**
     * 禁用状态
     */
    public static final Integer STATUS_DISABLED = 0;
    
    /**
     * 未删除状态
     */
    public static final Integer NOT_DELETED = 0;
    
    /**
     * 已删除状态
     */
    public static final Integer DELETED = 1;
    
    // ==================== 分页相关常量 ====================
    
    /**
     * 默认页码
     */
    public static final Integer DEFAULT_PAGE_NUM = 1;
    
    /**
     * 默认页面大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 20;
    
    /**
     * 最大页面大小
     */
    public static final Integer MAX_PAGE_SIZE = 1000;
    
    // ==================== 响应相关常量 ====================
    
    /**
     * 成功响应码
     */
    public static final String SUCCESS_CODE = "200";
    
    /**
     * 成功响应消息
     */
    public static final String SUCCESS_MESSAGE = "操作成功";
    
    /**
     * 失败响应码
     */
    public static final String ERROR_CODE = "500";
    
    /**
     * 失败响应消息
     */
    public static final String ERROR_MESSAGE = "操作失败";
    
    /**
     * 参数错误响应码
     */
    public static final String PARAM_ERROR_CODE = "400";
    
    /**
     * 参数错误响应消息
     */
    public static final String PARAM_ERROR_MESSAGE = "参数错误";
    
    /**
     * 权限不足响应码
     */
    public static final String PERMISSION_DENIED_CODE = "403";
    
    /**
     * 权限不足响应消息
     */
    public static final String PERMISSION_DENIED_MESSAGE = "权限不足";
    
    // ==================== 缓存相关常量 ====================
    
    /**
     * 默认缓存过期时间（秒）
     */
    public static final Long DEFAULT_CACHE_EXPIRE = 3600L;
    
    /**
     * 短期缓存过期时间（秒）
     */
    public static final Long SHORT_CACHE_EXPIRE = 300L;
    
    /**
     * 长期缓存过期时间（秒）
     */
    public static final Long LONG_CACHE_EXPIRE = 86400L;
    
    // ==================== 字符串相关常量 ====================
    
    /**
     * 空字符串
     */
    public static final String EMPTY_STRING = "";
    
    /**
     * 逗号分隔符
     */
    public static final String COMMA_SEPARATOR = ",";
    
    /**
     * 分号分隔符
     */
    public static final String SEMICOLON_SEPARATOR = ";";
    
    /**
     * 冒号分隔符
     */
    public static final String COLON_SEPARATOR = ":";
    
    /**
     * 下划线分隔符
     */
    public static final String UNDERSCORE_SEPARATOR = "_";
    
    /**
     * 中划线分隔符
     */
    public static final String DASH_SEPARATOR = "-";
    
    // ==================== 日期时间相关常量 ====================
    
    /**
     * 标准日期时间格式
     */
    public static final String STANDARD_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 标准日期格式
     */
    public static final String STANDARD_DATE_FORMAT = "yyyy-MM-dd";
    
    /**
     * 标准时间格式
     */
    public static final String STANDARD_TIME_FORMAT = "HH:mm:ss";
    
    /**
     * ISO日期时间格式
     */
    public static final String ISO_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    
    // ==================== 编码相关常量 ====================
    
    /**
     * UTF-8编码
     */
    public static final String UTF8_ENCODING = "UTF-8";
    
    /**
     * GBK编码
     */
    public static final String GBK_ENCODING = "GBK";
    
    // ==================== 文件相关常量 ====================
    
    /**
     * 默认文件上传大小限制（字节）
     */
    public static final Long DEFAULT_FILE_SIZE_LIMIT = 10 * 1024 * 1024L; // 10MB
    
    /**
     * 允许的图片文件扩展名
     */
    public static final String[] IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"};
    
    /**
     * 允许的文档文件扩展名
     */
    public static final String[] DOCUMENT_EXTENSIONS = {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt"};
    
    // ==================== 正则表达式常量 ====================
    
    /**
     * 手机号正则表达式
     */
    public static final String MOBILE_REGEX = "^1[3-9]\\d{9}$";
    
    /**
     * 邮箱正则表达式
     */
    public static final String EMAIL_REGEX = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    
    /**
     * 身份证号正则表达式
     */
    public static final String ID_CARD_REGEX = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    
    /**
     * 中文字符正则表达式
     */
    public static final String CHINESE_REGEX = "^[\\u4e00-\\u9fa5]+$";
    
    /**
     * 数字正则表达式
     */
    public static final String NUMBER_REGEX = "^\\d+$";
    
    /**
     * 字母数字正则表达式
     */
    public static final String ALPHANUMERIC_REGEX = "^[a-zA-Z0-9]+$";
}
