package com.visthink.common.enums;

/**
 * 统一响应状态码枚举
 * 
 * 定义系统中所有可能的响应状态码和对应的消息
 * 遵循HTTP状态码规范，同时扩展业务状态码
 * 
 * <AUTHOR>
 */
public enum ResponseCode {
    
    // ==================== 成功状态码 (2xx) ====================
    
    /**
     * 操作成功
     */
    SUCCESS("200", "操作成功"),
    
    /**
     * 创建成功
     */
    CREATED("201", "创建成功"),
    
    /**
     * 更新成功
     */
    UPDATED("202", "更新成功"),
    
    /**
     * 删除成功
     */
    DELETED("204", "删除成功"),
    
    // ==================== 客户端错误状态码 (4xx) ====================
    
    /**
     * 请求参数错误
     */
    BAD_REQUEST("400", "请求参数错误"),
    
    /**
     * 未授权访问
     */
    UNAUTHORIZED("401", "未授权访问"),
    
    /**
     * 权限不足
     */
    FORBIDDEN("403", "权限不足"),
    
    /**
     * 资源不存在
     */
    NOT_FOUND("404", "资源不存在"),
    
    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED("405", "请求方法不支持"),
    
    /**
     * 请求超时
     */
    REQUEST_TIMEOUT("408", "请求超时"),
    
    /**
     * 资源冲突
     */
    CONFLICT("409", "资源冲突"),
    
    /**
     * 请求实体过大
     */
    PAYLOAD_TOO_LARGE("413", "请求实体过大"),
    
    /**
     * 请求频率过高
     */
    TOO_MANY_REQUESTS("429", "请求频率过高"),
    
    // ==================== 服务器错误状态码 (5xx) ====================
    
    /**
     * 服务器内部错误
     */
    INTERNAL_SERVER_ERROR("500", "服务器内部错误"),
    
    /**
     * 功能未实现
     */
    NOT_IMPLEMENTED("501", "功能未实现"),
    
    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE("503", "服务不可用"),
    
    /**
     * 网关超时
     */
    GATEWAY_TIMEOUT("504", "网关超时"),
    
    // ==================== 业务错误状态码 (6xx) ====================
    
    /**
     * 业务逻辑错误
     */
    BUSINESS_ERROR("600", "业务逻辑错误"),
    
    /**
     * 数据验证失败
     */
    VALIDATION_ERROR("601", "数据验证失败"),
    
    /**
     * 数据不存在
     */
    DATA_NOT_EXISTS("602", "数据不存在"),
    
    /**
     * 数据已存在
     */
    DATA_ALREADY_EXISTS("603", "数据已存在"),
    
    /**
     * 数据状态错误
     */
    DATA_STATUS_ERROR("604", "数据状态错误"),
    
    /**
     * 操作不被允许
     */
    OPERATION_NOT_ALLOWED("605", "操作不被允许"),
    
    // ==================== 租户相关错误状态码 (7xx) ====================
    
    /**
     * 租户不存在
     */
    TENANT_NOT_EXISTS("700", "租户不存在"),
    
    /**
     * 租户已禁用
     */
    TENANT_DISABLED("701", "租户已禁用"),
    
    /**
     * 租户权限不足
     */
    TENANT_PERMISSION_DENIED("702", "租户权限不足"),
    
    /**
     * 跨租户访问被拒绝
     */
    CROSS_TENANT_ACCESS_DENIED("703", "跨租户访问被拒绝"),
    
    /**
     * 租户上下文缺失
     */
    TENANT_CONTEXT_MISSING("704", "租户上下文缺失"),
    
    // ==================== 用户相关错误状态码 (8xx) ====================
    
    /**
     * 用户不存在
     */
    USER_NOT_EXISTS("800", "用户不存在"),
    
    /**
     * 用户已禁用
     */
    USER_DISABLED("801", "用户已禁用"),
    
    /**
     * 密码错误
     */
    PASSWORD_ERROR("802", "密码错误"),
    
    /**
     * 用户已锁定
     */
    USER_LOCKED("803", "用户已锁定"),
    
    /**
     * 用户会话过期
     */
    SESSION_EXPIRED("804", "用户会话过期"),
    
    // ==================== 系统相关错误状态码 (9xx) ====================
    
    /**
     * 系统维护中
     */
    SYSTEM_MAINTENANCE("900", "系统维护中"),
    
    /**
     * 系统配置错误
     */
    SYSTEM_CONFIG_ERROR("901", "系统配置错误"),
    
    /**
     * 外部服务调用失败
     */
    EXTERNAL_SERVICE_ERROR("902", "外部服务调用失败"),
    
    /**
     * 数据库连接失败
     */
    DATABASE_CONNECTION_ERROR("903", "数据库连接失败"),
    
    /**
     * 缓存服务异常
     */
    CACHE_SERVICE_ERROR("904", "缓存服务异常");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态消息
     */
    private final String message;
    
    /**
     * 构造函数
     *
     * @param code    状态码
     * @param message 状态消息
     */
    ResponseCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
    
    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取状态消息
     *
     * @return 状态消息
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ResponseCode getByCode(String code) {
        for (ResponseCode responseCode : values()) {
            if (responseCode.getCode().equals(code)) {
                return responseCode;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为成功状态码
     *
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return code.startsWith("2");
    }
    
    /**
     * 判断是否为客户端错误
     *
     * @return true表示客户端错误，false表示其他
     */
    public boolean isClientError() {
        return code.startsWith("4");
    }
    
    /**
     * 判断是否为服务器错误
     *
     * @return true表示服务器错误，false表示其他
     */
    public boolean isServerError() {
        return code.startsWith("5");
    }
    
    /**
     * 判断是否为业务错误
     *
     * @return true表示业务错误，false表示其他
     */
    public boolean isBusinessError() {
        return code.startsWith("6") || code.startsWith("7") || code.startsWith("8") || code.startsWith("9");
    }
    
    @Override
    public String toString() {
        return String.format("ResponseCode{code='%s', message='%s'}", code, message);
    }
}
