package com.visthink.common.template;

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.entity.BaseEntity;
import com.visthink.common.service.BaseService;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

/**
 * 标准Controller模板
 * 展示如何使用新的架构模式
 * 
 * 使用说明：
 * 1. 继承SimpleController获得基础功能
 * 2. 注入对应的Service
 * 3. Controller只处理HTTP相关逻辑
 * 4. 所有业务逻辑委托给Service处理
 * 
 * @param <T> 实体类型
 * @param <S> Service类型
 * @param <CreateRequest> 创建请求DTO
 * @param <UpdateRequest> 更新请求DTO
 * @param <QueryRequest> 查询请求DTO
 * <AUTHOR>
 */
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "标准Controller模板", description = "展示标准的Controller实现模式")
public abstract class StandardControllerTemplate<T extends BaseEntity, 
                                                S extends BaseService<T, CreateRequest, UpdateRequest, QueryRequest>,
                                                CreateRequest, 
                                                UpdateRequest, 
                                                QueryRequest> 
        extends SimpleController {

    /**
     * 获取Service实例
     * 子类需要实现此方法返回具体的Service
     * 
     * @return Service实例
     */
    protected abstract S getService();

    /**
     * 获取实体名称（用于日志和响应消息）
     * 
     * @return 实体名称
     */
    protected abstract String getEntityName();

    /**
     * 创建实体
     * 
     * @param request 创建请求
     * @return 创建结果
     */
    @POST
    @Operation(summary = "创建实体", description = "创建新的实体")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<T>> create(@Valid CreateRequest request) {
        logOperation("创建" + getEntityName(), "开始处理创建请求");
        
        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("创建" + getEntityName(), tenantId, "租户验证通过");
                    return getService().create(tenantId, request);
                })
                .map(entity -> {
                    logOperation("创建" + getEntityName(), "创建成功");
                    return success("创建" + getEntityName() + "成功", entity);
                })
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "创建" + getEntityName()));
    }

    /**
     * 根据ID查询实体
     * 
     * @param id 实体ID
     * @return 查询结果
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询实体", description = "根据ID查询实体详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<T>> findById(@PathParam("id") Long id) {
        logOperation("查询" + getEntityName(), "ID: " + id);
        
        return getCurrentTenantId()
                .flatMap(tenantId -> getService().findById(tenantId, id))
                .map(entity -> {
                    if (entity != null) {
                        logOperation("查询" + getEntityName(), "查询成功");
                        return success(entity);
                    } else {
                        return ApiResponse.<T>notFound(getEntityName() + "不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "查询" + getEntityName()));
    }

    /**
     * 查询所有实体
     * 
     * @return 查询结果
     */
    @GET
    @Operation(summary = "查询所有实体", description = "查询所有实体列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<T>>> findAll() {
        logOperation("查询所有" + getEntityName(), "开始处理查询请求");
        
        return getCurrentTenantId()
                .flatMap(tenantId -> getService().findAll(tenantId))
                .map(entities -> {
                    logOperation("查询所有" + getEntityName(), "查询成功，共" + entities.size() + "条记录");
                    return success(entities);
                })
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "查询" + getEntityName() + "列表"));
    }

    /**
     * 分页查询实体
     * 
     * @param page 页码
     * @param size 页大小
     * @return 分页结果
     */
    @GET
    @Path("/page")
    @Operation(summary = "分页查询实体", description = "分页查询实体列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<PageResult<T>>> findByPage(
            @QueryParam("page") @DefaultValue("1") Integer page,
            @QueryParam("size") @DefaultValue("10") Integer size) {
        
        logOperation("分页查询" + getEntityName(), String.format("页码: %d, 页大小: %d", page, size));
        
        PageRequest pageRequest = new PageRequest(page, size);
        
        return getCurrentTenantId()
                .flatMap(tenantId -> getService().findByPage(tenantId, pageRequest))
                .map(pageResult -> {
                    logOperation("分页查询" + getEntityName(), 
                        String.format("查询成功，共%d条记录", pageResult.getTotal()));
                    return success(pageResult);
                })
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "分页查询" + getEntityName()));
    }

    /**
     * 更新实体
     * 
     * @param id 实体ID
     * @param request 更新请求
     * @return 更新结果
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新实体", description = "根据ID更新实体")
    @APIResponse(responseCode = "200", description = "更新成功")
    public Uni<ApiResponse<T>> update(@PathParam("id") Long id, @Valid UpdateRequest request) {
        logOperation("更新" + getEntityName(), "ID: " + id);
        
        return getCurrentTenantId()
                .flatMap(tenantId -> getService().update(tenantId, id, request))
                .map(entity -> {
                    logOperation("更新" + getEntityName(), "更新成功");
                    return success("更新" + getEntityName() + "成功", entity);
                })
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "更新" + getEntityName()));
    }

    /**
     * 删除实体
     * 
     * @param id 实体ID
     * @return 删除结果
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除实体", description = "根据ID删除实体")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Boolean>> delete(@PathParam("id") Long id) {
        logOperation("删除" + getEntityName(), "ID: " + id);
        
        return getCurrentTenantId()
                .flatMap(tenantId -> getService().delete(tenantId, id))
                .map(result -> {
                    if (result) {
                        logOperation("删除" + getEntityName(), "删除成功");
                        return success("删除" + getEntityName() + "成功", true);
                    } else {
                        return ApiResponse.<Boolean>notFound(getEntityName() + "不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "删除" + getEntityName()));
    }

    /**
     * 统计实体数量
     * 
     * @return 统计结果
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计数量", description = "统计实体总数量")
    @APIResponse(responseCode = "200", description = "统计成功")
    public Uni<ApiResponse<Long>> count() {
        logOperation("统计" + getEntityName() + "数量", "开始处理统计请求");
        
        return getCurrentTenantId()
                .flatMap(tenantId -> getService().count(tenantId))
                .map(count -> {
                    logOperation("统计" + getEntityName() + "数量", "统计成功: " + count);
                    return success(count);
                })
                .onFailure().recoverWithItem(throwable -> 
                    handleError(throwable, "统计" + getEntityName() + "数量"));
    }
}
