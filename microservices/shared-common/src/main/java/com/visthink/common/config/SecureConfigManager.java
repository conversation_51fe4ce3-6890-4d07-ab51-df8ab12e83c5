package com.visthink.common.config;

import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.util.Optional;

/**
 * 安全配置管理器
 *
 * 统一管理敏感配置信息，支持环境变量和配置文件
 * 提供配置验证和默认值处理
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class SecureConfigManager {

    private static final Logger LOG = Logger.getLogger(SecureConfigManager.class);

    // 数据库配置
    @ConfigProperty(name = "DB_USERNAME")
    Optional<String> dbUsername;

    @ConfigProperty(name = "DB_PASSWORD")
    Optional<String> dbPassword;

    @ConfigProperty(name = "DB_URL")
    Optional<String> dbUrl;

    // JWT配置
    @ConfigProperty(name = "JWT_SECRET")
    Optional<String> jwtSecret;

    @ConfigProperty(name = "JWT_EXPIRATION", defaultValue = "86400000")
    Long jwtExpiration;

    @ConfigProperty(name = "JWT_ISSUER", defaultValue = "https://api.visthink.com")
    String jwtIssuer;

    // Redis配置
    @ConfigProperty(name = "REDIS_URL")
    Optional<String> redisUrl;

    @ConfigProperty(name = "REDIS_PASSWORD")
    Optional<String> redisPassword;

    // 邮件配置
    @ConfigProperty(name = "MAIL_USERNAME")
    Optional<String> mailUsername;

    @ConfigProperty(name = "MAIL_PASSWORD")
    Optional<String> mailPassword;

    @ConfigProperty(name = "MAIL_SMTP_HOST")
    Optional<String> mailSmtpHost;

    // 短信配置
    @ConfigProperty(name = "SMS_ACCESS_KEY")
    Optional<String> smsAccessKey;

    @ConfigProperty(name = "SMS_SECRET_KEY")
    Optional<String> smsSecretKey;

    /**
     * 获取数据库用户名
     */
    public String getDatabaseUsername() {
        return dbUsername.orElseThrow(() -> {
            LOG.error("数据库用户名未配置，请设置环境变量 DB_USERNAME");
            return new IllegalStateException("数据库用户名未配置");
        });
    }

    /**
     * 获取数据库密码
     */
    public String getDatabasePassword() {
        return dbPassword.orElseThrow(() -> {
            LOG.error("数据库密码未配置，请设置环境变量 DB_PASSWORD");
            return new IllegalStateException("数据库密码未配置");
        });
    }

    /**
     * 获取数据库URL
     */
    public String getDatabaseUrl() {
        return dbUrl.orElseThrow(() -> {
            LOG.error("数据库URL未配置，请设置环境变量 DB_URL");
            return new IllegalStateException("数据库URL未配置");
        });
    }

    /**
     * 获取JWT密钥
     */
    public String getJwtSecret() {
        String secret = jwtSecret.orElse(null);
        if (secret == null || secret.trim().isEmpty()) {
            LOG.error("JWT密钥未配置，请设置环境变量 JWT_SECRET");
            throw new IllegalStateException("JWT密钥未配置");
        }

        // 验证密钥长度（至少256位）
        if (secret.length() < 32) {
            LOG.error("JWT密钥长度不足，至少需要32个字符（256位）");
            throw new IllegalStateException("JWT密钥长度不足");
        }

        return secret;
    }

    /**
     * 获取JWT过期时间
     */
    public Long getJwtExpiration() {
        return jwtExpiration;
    }

    /**
     * 获取JWT签发者
     */
    public String getJwtIssuer() {
        return jwtIssuer;
    }

    /**
     * 获取Redis URL
     */
    public String getRedisUrl() {
        return redisUrl.orElse("redis://localhost:6379");
    }

    /**
     * 获取Redis密码
     */
    public Optional<String> getRedisPassword() {
        return redisPassword;
    }

    /**
     * 获取邮件用户名
     */
    public Optional<String> getMailUsername() {
        return mailUsername;
    }

    /**
     * 获取邮件密码
     */
    public Optional<String> getMailPassword() {
        return mailPassword;
    }

    /**
     * 获取邮件SMTP主机
     */
    public Optional<String> getMailSmtpHost() {
        return mailSmtpHost;
    }

    /**
     * 获取短信访问密钥
     */
    public Optional<String> getSmsAccessKey() {
        return smsAccessKey;
    }

    /**
     * 获取短信秘密密钥
     */
    public Optional<String> getSmsSecretKey() {
        return smsSecretKey;
    }

    /**
     * 验证所有必需的配置是否已设置
     */
    public void validateRequiredConfigs() {
        LOG.info("开始验证安全配置...");

        try {
            getDatabaseUsername();
            getDatabasePassword();
            getDatabaseUrl();
            getJwtSecret();

            LOG.info("安全配置验证通过");
        } catch (Exception e) {
            LOG.error("安全配置验证失败", e);
            throw e;
        }
    }

    /**
     * 检查配置是否使用了默认值（不安全）
     */
    public void checkDefaultValues() {
        LOG.info("检查默认配置值...");

        // 检查JWT密钥是否使用默认值
        String secret = jwtSecret.orElse("");
        if (secret.contains("default") || secret.contains("secret") || secret.contains("key")) {
            LOG.warn("JWT密钥可能使用了不安全的默认值，建议更换");
        }

        // 检查数据库密码是否过于简单
        String password = dbPassword.orElse("");
        if (password.length() < 8) {
            LOG.warn("数据库密码长度不足8位，建议使用更强的密码");
        }

        LOG.info("默认配置值检查完成");
    }

    /**
     * 获取配置摘要（用于日志，不包含敏感信息）
     */
    public String getConfigSummary() {
        return String.format(
            "配置摘要: DB_URL=%s, JWT_ISSUER=%s, REDIS_URL=%s, 邮件配置=%s, 短信配置=%s",
            dbUrl.map(url -> maskSensitiveInfo(url)).orElse("未配置"),
            jwtIssuer,
            redisUrl.map(url -> maskSensitiveInfo(url)).orElse("未配置"),
            mailUsername.isPresent() ? "已配置" : "未配置",
            smsAccessKey.isPresent() ? "已配置" : "未配置"
        );
    }

    /**
     * 掩码敏感信息
     */
    private String maskSensitiveInfo(String value) {
        if (value == null || value.length() <= 8) {
            return "****";
        }
        return value.substring(0, 4) + "****" + value.substring(value.length() - 4);
    }
}
