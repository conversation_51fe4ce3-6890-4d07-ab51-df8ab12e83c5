package com.visthink.common.dto;

import com.visthink.common.enums.ResponseCode;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一API响应DTO
 * 
 * 提供标准化的API响应格式，包括状态码、消息、数据和时间戳
 * 支持泛型，可以包装任意类型的响应数据
 * 
 * @param <T> 响应数据类型
 * <AUTHOR>
 */
@Data
public class ApiResponse<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 响应状态码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 服务器处理耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 扩展信息
     */
    private Object extra;
    
    // ==================== 构造函数 ====================
    
    /**
     * 默认构造函数
     */
    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     *
     * @param code    状态码
     * @param message 消息
     */
    public ApiResponse(String code, String message) {
        this();
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     *
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     */
    public ApiResponse(String code, String message, T data) {
        this(code, message);
        this.data = data;
    }
    
    /**
     * 构造函数
     *
     * @param responseCode 响应码枚举
     */
    public ApiResponse(ResponseCode responseCode) {
        this();
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数
     *
     * @param responseCode 响应码枚举
     * @param data         数据
     */
    public ApiResponse(ResponseCode responseCode, T data) {
        this(responseCode);
        this.data = data;
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 设置请求处理耗时
     *
     * @param startTime 开始时间（毫秒）
     * @return 当前对象
     */
    public ApiResponse<T> setDuration(long startTime) {
        this.duration = System.currentTimeMillis() - startTime;
        return this;
    }
    
    /**
     * 设置请求ID
     *
     * @param requestId 请求ID
     * @return 当前对象
     */
    public ApiResponse<T> setRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }
    
    /**
     * 设置扩展信息
     *
     * @param extra 扩展信息
     * @return 当前对象
     */
    public ApiResponse<T> setExtra(Object extra) {
        this.extra = extra;
        return this;
    }
    
    /**
     * 判断是否成功
     *
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return ResponseCode.SUCCESS.getCode().equals(code) || 
               (code != null && code.startsWith("2"));
    }
    
    /**
     * 判断是否失败
     *
     * @return true表示失败，false表示成功
     */
    public boolean isError() {
        return !isSuccess();
    }
    
    // ==================== 静态工厂方法 - 成功响应 ====================
    
    /**
     * 创建成功响应
     *
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(ResponseCode.SUCCESS);
    }
    
    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(ResponseCode.SUCCESS, data);
    }
    
    /**
     * 创建成功响应
     *
     * @param message 自定义消息
     * @param <T>     数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message) {
        return new ApiResponse<>(ResponseCode.SUCCESS.getCode(), message);
    }
    
    /**
     * 创建成功响应
     *
     * @param message 自定义消息
     * @param data    响应数据
     * @param <T>     数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(ResponseCode.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 创建创建成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 创建成功响应
     */
    public static <T> ApiResponse<T> created(T data) {
        return new ApiResponse<>(ResponseCode.CREATED, data);
    }
    
    /**
     * 创建更新成功响应
     *
     * @param data 响应数据
     * @param <T>  数据类型
     * @return 更新成功响应
     */
    public static <T> ApiResponse<T> updated(T data) {
        return new ApiResponse<>(ResponseCode.UPDATED, data);
    }
    
    /**
     * 创建删除成功响应
     *
     * @param <T> 数据类型
     * @return 删除成功响应
     */
    public static <T> ApiResponse<T> deleted() {
        return new ApiResponse<>(ResponseCode.DELETED);
    }
    
    // ==================== 静态工厂方法 - 错误响应 ====================
    
    /**
     * 创建错误响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(ResponseCode.INTERNAL_SERVER_ERROR.getCode(), message);
    }
    
    /**
     * 创建错误响应
     *
     * @param code    错误码
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String code, String message) {
        return new ApiResponse<>(code, message);
    }
    
    /**
     * 创建错误响应
     *
     * @param responseCode 响应码枚举
     * @param <T>          数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(ResponseCode responseCode) {
        return new ApiResponse<>(responseCode);
    }
    
    /**
     * 创建错误响应
     *
     * @param responseCode 响应码枚举
     * @param data         错误数据
     * @param <T>          数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(ResponseCode responseCode, T data) {
        return new ApiResponse<>(responseCode, data);
    }
    
    /**
     * 创建参数错误响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 参数错误响应
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return new ApiResponse<>(ResponseCode.BAD_REQUEST.getCode(), message);
    }
    
    /**
     * 创建未授权响应
     *
     * @param <T> 数据类型
     * @return 未授权响应
     */
    public static <T> ApiResponse<T> unauthorized() {
        return new ApiResponse<>(ResponseCode.UNAUTHORIZED);
    }
    
    /**
     * 创建权限不足响应
     *
     * @param <T> 数据类型
     * @return 权限不足响应
     */
    public static <T> ApiResponse<T> forbidden() {
        return new ApiResponse<>(ResponseCode.FORBIDDEN);
    }
    
    /**
     * 创建权限不足响应
     *
     * @param message 自定义消息
     * @param <T>     数据类型
     * @return 权限不足响应
     */
    public static <T> ApiResponse<T> forbidden(String message) {
        return new ApiResponse<>(ResponseCode.FORBIDDEN.getCode(), message);
    }
    
    /**
     * 创建资源不存在响应
     *
     * @param <T> 数据类型
     * @return 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound() {
        return new ApiResponse<>(ResponseCode.NOT_FOUND);
    }
    
    /**
     * 创建资源不存在响应
     *
     * @param message 自定义消息
     * @param <T>     数据类型
     * @return 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(ResponseCode.NOT_FOUND.getCode(), message);
    }
    
    /**
     * 创建业务错误响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 业务错误响应
     */
    public static <T> ApiResponse<T> business(String message) {
        return new ApiResponse<>(ResponseCode.BUSINESS_ERROR.getCode(), message);
    }
    
    /**
     * 创建验证错误响应
     *
     * @param message 错误消息
     * @param <T>     数据类型
     * @return 验证错误响应
     */
    public static <T> ApiResponse<T> validation(String message) {
        return new ApiResponse<>(ResponseCode.VALIDATION_ERROR.getCode(), message);
    }
    
    /**
     * 创建租户相关错误响应
     *
     * @param responseCode 租户相关响应码
     * @param <T>          数据类型
     * @return 租户错误响应
     */
    public static <T> ApiResponse<T> tenant(ResponseCode responseCode) {
        return new ApiResponse<>(responseCode);
    }
    
    @Override
    public String toString() {
        return String.format("ApiResponse{code='%s', message='%s', timestamp=%s, duration=%d}", 
                code, message, timestamp, duration);
    }
}
