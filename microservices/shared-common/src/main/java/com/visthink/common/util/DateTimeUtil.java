package com.visthink.common.util;

import com.visthink.common.constant.CommonConstants;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 日期时间工具类
 * 
 * 提供日期时间的格式化、解析、计算等常用功能
 * 基于Java 8的时间API，线程安全
 * 
 * <AUTHOR>
 */
public final class DateTimeUtil {
    
    /**
     * 私有构造函数，防止实例化
     */
    private DateTimeUtil() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
    
    // ==================== 常用格式化器 ====================
    
    /**
     * 标准日期时间格式化器
     */
    public static final DateTimeFormatter STANDARD_DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.STANDARD_DATETIME_FORMAT);
    
    /**
     * 标准日期格式化器
     */
    public static final DateTimeFormatter STANDARD_DATE_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.STANDARD_DATE_FORMAT);
    
    /**
     * 标准时间格式化器
     */
    public static final DateTimeFormatter STANDARD_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.STANDARD_TIME_FORMAT);
    
    /**
     * ISO日期时间格式化器
     */
    public static final DateTimeFormatter ISO_DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern(CommonConstants.ISO_DATETIME_FORMAT);
    
    // ==================== 当前时间获取 ====================
    
    /**
     * 获取当前LocalDateTime
     *
     * @return 当前日期时间
     */
    public static LocalDateTime now() {
        return LocalDateTime.now();
    }
    
    /**
     * 获取当前LocalDate
     *
     * @return 当前日期
     */
    public static LocalDate today() {
        return LocalDate.now();
    }
    
    /**
     * 获取当前LocalTime
     *
     * @return 当前时间
     */
    public static LocalTime currentTime() {
        return LocalTime.now();
    }
    
    /**
     * 获取当前时间戳（毫秒）
     *
     * @return 当前时间戳
     */
    public static long currentTimeMillis() {
        return System.currentTimeMillis();
    }
    
    /**
     * 获取当前时间戳（秒）
     *
     * @return 当前时间戳（秒）
     */
    public static long currentTimeSeconds() {
        return Instant.now().getEpochSecond();
    }
    
    // ==================== 格式化方法 ====================
    
    /**
     * 格式化LocalDateTime为标准格式字符串
     *
     * @param dateTime 日期时间
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(STANDARD_DATETIME_FORMATTER) : null;
    }
    
    /**
     * 格式化LocalDate为标准格式字符串
     *
     * @param date 日期
     * @return 格式化后的字符串
     */
    public static String format(LocalDate date) {
        return date != null ? date.format(STANDARD_DATE_FORMATTER) : null;
    }
    
    /**
     * 格式化LocalTime为标准格式字符串
     *
     * @param time 时间
     * @return 格式化后的字符串
     */
    public static String format(LocalTime time) {
        return time != null ? time.format(STANDARD_TIME_FORMATTER) : null;
    }
    
    /**
     * 使用指定格式格式化LocalDateTime
     *
     * @param dateTime 日期时间
     * @param pattern  格式模式
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }
    
    /**
     * 使用指定格式格式化LocalDate
     *
     * @param date    日期
     * @param pattern 格式模式
     * @return 格式化后的字符串
     */
    public static String format(LocalDate date, String pattern) {
        if (date == null || pattern == null) {
            return null;
        }
        return date.format(DateTimeFormatter.ofPattern(pattern));
    }
    
    // ==================== 解析方法 ====================
    
    /**
     * 解析标准格式的日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr, STANDARD_DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期时间格式错误: " + dateTimeStr, e);
        }
    }
    
    /**
     * 解析标准格式的日期字符串
     *
     * @param dateStr 日期字符串
     * @return LocalDate对象
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr, STANDARD_DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期格式错误: " + dateStr, e);
        }
    }
    
    /**
     * 解析标准格式的时间字符串
     *
     * @param timeStr 时间字符串
     * @return LocalTime对象
     */
    public static LocalTime parseTime(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalTime.parse(timeStr, STANDARD_TIME_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("时间格式错误: " + timeStr, e);
        }
    }
    
    /**
     * 使用指定格式解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern     格式模式
     * @return LocalDateTime对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr, String pattern) {
        if (dateTimeStr == null || pattern == null) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("日期时间格式错误: " + dateTimeStr + ", 期望格式: " + pattern, e);
        }
    }
    
    // ==================== 转换方法 ====================
    
    /**
     * LocalDateTime转换为Date
     *
     * @param localDateTime LocalDateTime对象
     * @return Date对象
     */
    public static Date toDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * LocalDate转换为Date
     *
     * @param localDate LocalDate对象
     * @return Date对象
     */
    public static Date toDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * Date转换为LocalDateTime
     *
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
    
    /**
     * Date转换为LocalDate
     *
     * @param date Date对象
     * @return LocalDate对象
     */
    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
    
    /**
     * 时间戳（毫秒）转换为LocalDateTime
     *
     * @param timestamp 时间戳（毫秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime toLocalDateTime(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
    }
    
    /**
     * LocalDateTime转换为时间戳（毫秒）
     *
     * @param localDateTime LocalDateTime对象
     * @return 时间戳（毫秒）
     */
    public static long toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return 0L;
        }
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
    
    // ==================== 计算方法 ====================
    
    /**
     * 计算两个日期之间的天数差
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 天数差
     */
    public static long daysBetween(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return 0L;
        }
        return ChronoUnit.DAYS.between(startDate, endDate);
    }
    
    /**
     * 计算两个时间之间的小时差
     *
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return 小时差
     */
    public static long hoursBetween(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (startDateTime == null || endDateTime == null) {
            return 0L;
        }
        return ChronoUnit.HOURS.between(startDateTime, endDateTime);
    }
    
    /**
     * 计算两个时间之间的分钟差
     *
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @return 分钟差
     */
    public static long minutesBetween(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (startDateTime == null || endDateTime == null) {
            return 0L;
        }
        return ChronoUnit.MINUTES.between(startDateTime, endDateTime);
    }
    
    // ==================== 判断方法 ====================
    
    /**
     * 判断是否为今天
     *
     * @param date 日期
     * @return true表示是今天，false表示不是
     */
    public static boolean isToday(LocalDate date) {
        return date != null && date.equals(LocalDate.now());
    }
    
    /**
     * 判断是否为今天
     *
     * @param dateTime 日期时间
     * @return true表示是今天，false表示不是
     */
    public static boolean isToday(LocalDateTime dateTime) {
        return dateTime != null && dateTime.toLocalDate().equals(LocalDate.now());
    }
    
    /**
     * 判断是否为工作日（周一到周五）
     *
     * @param date 日期
     * @return true表示是工作日，false表示不是
     */
    public static boolean isWorkday(LocalDate date) {
        if (date == null) {
            return false;
        }
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        return dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY;
    }
    
    /**
     * 判断是否为周末
     *
     * @param date 日期
     * @return true表示是周末，false表示不是
     */
    public static boolean isWeekend(LocalDate date) {
        return !isWorkday(date);
    }
}
