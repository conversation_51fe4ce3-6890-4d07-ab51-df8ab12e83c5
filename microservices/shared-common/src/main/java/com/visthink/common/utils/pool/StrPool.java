package com.visthink.common.utils.pool;

/**
 * 常用字符串常量
 *
 * <AUTHOR>
 */
public interface StrPool {

    String UNKNOWN = "unknown";
    String TRUE = "true";
    String FALSE = "false";
    String SINGLE_QUOTES = "'";
    String DOUBLE_QUOTES = "\"";
    String PARENTHESES = "()";
    String PARENTHESES_START = "(";
    String PARENTHESES_END = ")";
    String DELIM = "{}";
    String BRACKET = "[]";
    String SEMICOLON = ";";
    String EQUAL = "=";
    String AMPERSAND = "&";
    String ASTERISK = "*";
}
