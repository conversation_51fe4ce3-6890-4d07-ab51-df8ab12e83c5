package com.visthink.common.dto;

import jakarta.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * 导出请求DTO
 *
 * 用于封装导出操作的请求参数
 * 支持多种导出格式和条件筛选
 *
 * <AUTHOR>
 */
public class ExportRequest {

    /**
     * 导出格式（excel/csv/pdf）
     */
    @NotBlank(message = "导出格式不能为空")
    private String format;

    /**
     * 导出文件名（不包含扩展名）
     */
    private String fileName;

    /**
     * 导出的字段列表
     */
    private List<String> fields;

    /**
     * 导出条件
     */
    private Map<String, Object> conditions;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection = "ASC";

    /**
     * 是否包含表头
     */
    private Boolean includeHeader = true;

    /**
     * 最大导出行数
     */
    private Integer maxRows = 10000;

    /**
     * 导出模板ID
     */
    private String templateId;

    /**
     * 额外参数
     */
    private Map<String, Object> extraParams;

    // 构造函数
    public ExportRequest() {
    }

    public ExportRequest(String format) {
        this.format = format;
    }

    public ExportRequest(String format, String fileName) {
        this.format = format;
        this.fileName = fileName;
    }

    // Getter和Setter方法
    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public List<String> getFields() {
        return fields;
    }

    public void setFields(List<String> fields) {
        this.fields = fields;
    }

    public Map<String, Object> getConditions() {
        return conditions;
    }

    public void setConditions(Map<String, Object> conditions) {
        this.conditions = conditions;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    public Boolean getIncludeHeader() {
        return includeHeader;
    }

    public void setIncludeHeader(Boolean includeHeader) {
        this.includeHeader = includeHeader;
    }

    public Integer getMaxRows() {
        return maxRows;
    }

    public void setMaxRows(Integer maxRows) {
        this.maxRows = maxRows;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Map<String, Object> getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(Map<String, Object> extraParams) {
        this.extraParams = extraParams;
    }

    @Override
    public String toString() {
        return "ExportRequest{" +
                "format='" + format + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fields=" + fields +
                ", conditions=" + conditions +
                ", sortField='" + sortField + '\'' +
                ", sortDirection='" + sortDirection + '\'' +
                ", includeHeader=" + includeHeader +
                ", maxRows=" + maxRows +
                ", templateId='" + templateId + '\'' +
                ", extraParams=" + extraParams +
                '}';
    }
}
