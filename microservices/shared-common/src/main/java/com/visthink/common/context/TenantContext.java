package com.visthink.common.context;

import com.visthink.common.constant.CommonConstants;
import com.visthink.common.exception.BaseException;
import com.visthink.common.enums.ResponseCode;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import org.jboss.logging.Logger;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 统一租户上下文管理
 * 
 * 提供多租户环境下的租户信息管理，包括租户ID的设置、获取和清理
 * 支持线程安全的租户上下文操作、权限验证和默认租户处理
 * 整合了原有的TenantContext和UnifiedTenantContext功能
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class TenantContext {

    private static final Logger LOG = Logger.getLogger(TenantContext.class);

    /**
     * 线程本地存储租户信息
     * 使用InheritableThreadLocal支持子线程继承，并添加内存泄漏防护
     */
    private static final ThreadLocal<TenantInfo> TENANT_HOLDER = new InheritableThreadLocal<TenantInfo>() {
        @Override
        protected TenantInfo initialValue() {
            return null;
        }

        @Override
        protected TenantInfo childValue(TenantInfo parentValue) {
            // 子线程继承父线程的租户信息
            return parentValue != null ? new TenantInfo(parentValue) : null;
        }
    };

    /**
     * 租户信息缓存，用于提高性能
     */
    private static final ConcurrentMap<Long, TenantInfo> TENANT_CACHE = new ConcurrentHashMap<>();

    /**
     * 线程清理任务，防止内存泄漏
     */
    private static final ThreadLocal<Boolean> CLEANUP_MARKER = new ThreadLocal<>();

    // ==================== 租户ID管理 ====================

    /**
     * 设置当前租户ID
     *
     * @param tenantId 租户ID
     */
    public void setCurrentTenantId(Long tenantId) {
        if (tenantId != null) {
            TenantInfo tenantInfo = getTenantInfo();
            tenantInfo.setTenantId(tenantId);
            TENANT_HOLDER.set(tenantInfo);
            LOG.debugf("设置当前租户ID: %d", tenantId);
        } else {
            LOG.warn("尝试设置空的租户ID，将使用默认租户ID");
            setCurrentTenantId(CommonConstants.DEFAULT_TENANT_ID);
        }
    }

    /**
     * 获取当前租户ID（响应式）
     *
     * @return 当前租户ID的Uni包装
     */
    public Uni<Long> getCurrentTenantId() {
        TenantInfo tenantInfo = TENANT_HOLDER.get();
        Long tenantId = tenantInfo != null ? tenantInfo.getTenantId() : null;
        
        if (tenantId == null) {
            LOG.debug("当前租户ID为空，使用默认租户ID: " + CommonConstants.DEFAULT_TENANT_ID);
            tenantId = CommonConstants.DEFAULT_TENANT_ID;
            setCurrentTenantId(tenantId);
        }
        
        return Uni.createFrom().item(tenantId);
    }

    /**
     * 获取当前租户ID（同步方式）
     *
     * @return 当前租户ID
     */
    public Long getCurrentTenantIdSync() {
        TenantInfo tenantInfo = TENANT_HOLDER.get();
        Long tenantId = tenantInfo != null ? tenantInfo.getTenantId() : null;
        
        if (tenantId == null) {
            LOG.debug("当前租户ID为空，使用默认租户ID: " + CommonConstants.DEFAULT_TENANT_ID);
            tenantId = CommonConstants.DEFAULT_TENANT_ID;
            setCurrentTenantId(tenantId);
        }
        
        return tenantId;
    }

    /**
     * 获取当前租户ID（Optional包装）
     *
     * @return 当前租户ID的Optional包装
     */
    public Optional<Long> getCurrentTenantIdOptional() {
        TenantInfo tenantInfo = TENANT_HOLDER.get();
        return Optional.ofNullable(tenantInfo != null ? tenantInfo.getTenantId() : null);
    }

    // ==================== 租户信息管理 ====================

    /**
     * 设置租户信息
     *
     * @param tenantId   租户ID
     * @param tenantCode 租户代码
     * @param tenantName 租户名称
     */
    public void setTenantInfo(Long tenantId, String tenantCode, String tenantName) {
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId(tenantId);
        tenantInfo.setTenantCode(tenantCode);
        tenantInfo.setTenantName(tenantName);
        tenantInfo.setActive(true);
        
        TENANT_HOLDER.set(tenantInfo);
        // 缓存租户信息
        TENANT_CACHE.put(tenantId, new TenantInfo(tenantInfo));
        
        LOG.debugf("设置租户信息: ID=%d, Code=%s, Name=%s", tenantId, tenantCode, tenantName);
    }

    /**
     * 获取当前租户信息
     *
     * @return 当前租户信息
     */
    public TenantInfo getCurrentTenantInfo() {
        TenantInfo tenantInfo = TENANT_HOLDER.get();
        if (tenantInfo == null) {
            // 创建默认租户信息
            tenantInfo = createDefaultTenantInfo();
            TENANT_HOLDER.set(tenantInfo);
        }
        return tenantInfo;
    }

    /**
     * 根据租户ID获取租户信息
     *
     * @param tenantId 租户ID
     * @return 租户信息的Uni包装
     */
    public Uni<TenantInfo> getTenantInfo(Long tenantId) {
        if (tenantId == null) {
            return Uni.createFrom().failure(
                BaseException.validation("租户ID不能为空")
            );
        }

        // 先从缓存中查找
        TenantInfo cachedInfo = TENANT_CACHE.get(tenantId);
        if (cachedInfo != null) {
            return Uni.createFrom().item(new TenantInfo(cachedInfo));
        }

        // 如果是默认租户，直接返回
        if (CommonConstants.DEFAULT_TENANT_ID.equals(tenantId)) {
            TenantInfo defaultInfo = createDefaultTenantInfo();
            TENANT_CACHE.put(tenantId, defaultInfo);
            return Uni.createFrom().item(defaultInfo);
        }

        // 这里可以扩展为从数据库或其他服务获取租户信息
        return Uni.createFrom().failure(
            new BaseException(ResponseCode.TENANT_NOT_EXISTS)
        );
    }

    // ==================== 权限验证 ====================

    /**
     * 验证租户访问权限
     *
     * @param targetTenantId 目标租户ID
     * @param operation      操作类型
     * @return 验证结果的Uni包装
     */
    public Uni<Boolean> validateTenantAccess(Long targetTenantId, String operation) {
        return getCurrentTenantId()
                .map(currentTenantId -> {
                    // 平台管理员可以访问所有租户
                    if (CommonConstants.PLATFORM_ADMIN_TENANT_ID.equals(currentTenantId)) {
                        LOG.debugf("平台管理员访问租户 %d，操作：%s", targetTenantId, operation);
                        return true;
                    }

                    // 普通租户只能访问自己的数据
                    boolean hasAccess = currentTenantId.equals(targetTenantId);
                    if (!hasAccess) {
                        LOG.warnf("租户 %d 尝试访问租户 %d 的数据，操作：%s - 拒绝访问",
                                currentTenantId, targetTenantId, operation);
                    }
                    return hasAccess;
                });
    }

    /**
     * 验证跨租户操作权限
     *
     * @param operation 操作类型
     * @return 验证结果的Uni包装
     */
    public Uni<Boolean> validateCrossTenantAccess(String operation) {
        return getCurrentTenantId()
                .map(currentTenantId -> {
                    boolean isAllowed = CommonConstants.PLATFORM_ADMIN_TENANT_ID.equals(currentTenantId);
                    if (!isAllowed) {
                        LOG.warnf("租户 %d 尝试执行跨租户操作：%s - 拒绝访问", currentTenantId, operation);
                    }
                    return isAllowed;
                });
    }

    /**
     * 强制验证租户权限（失败时抛出异常）
     *
     * @param targetTenantId 目标租户ID
     * @param operation      操作类型
     * @return 验证成功的Uni
     */
    public Uni<Void> requireTenantAccess(Long targetTenantId, String operation) {
        return validateTenantAccess(targetTenantId, operation)
                .onItem().transformToUni(hasAccess -> {
                    if (!hasAccess) {
                        return Uni.createFrom().failure(
                            new BaseException(ResponseCode.CROSS_TENANT_ACCESS_DENIED)
                                .setData(String.format("操作：%s，目标租户：%d", operation, targetTenantId))
                        );
                    }
                    return Uni.createFrom().voidItem();
                });
    }

    // ==================== 状态判断 ====================

    /**
     * 判断是否为平台管理员
     *
     * @return true表示是平台管理员，false表示不是
     */
    public boolean isPlatformAdmin() {
        Long tenantId = getCurrentTenantIdSync();
        return CommonConstants.PLATFORM_ADMIN_TENANT_ID.equals(tenantId);
    }

    /**
     * 判断是否为默认租户
     *
     * @return true表示是默认租户，false表示不是
     */
    public boolean isDefaultTenant() {
        Long tenantId = getCurrentTenantIdSync();
        return CommonConstants.DEFAULT_TENANT_ID.equals(tenantId);
    }

    // ==================== 上下文切换 ====================

    /**
     * 设置为平台管理员上下文
     */
    public void setPlatformAdminContext() {
        setCurrentTenantId(CommonConstants.PLATFORM_ADMIN_TENANT_ID);
        LOG.debug("设置为平台管理员上下文");
    }

    /**
     * 在指定租户上下文中执行操作
     *
     * @param tenantId  租户ID
     * @param operation 操作函数
     * @param <T>       返回类型
     * @return 操作结果的Uni包装
     */
    public <T> Uni<T> runInTenantContext(Long tenantId, java.util.function.Supplier<Uni<T>> operation) {
        Long originalTenantId = getCurrentTenantIdSync();
        
        return Uni.createFrom().item(() -> {
            setCurrentTenantId(tenantId);
            return tenantId;
        })
        .onItem().transformToUni(id -> operation.get())
        .onTermination().invoke(() -> {
            // 恢复原始租户上下文
            setCurrentTenantId(originalTenantId);
        });
    }

    /**
     * 在平台管理员上下文中执行操作
     *
     * @param operation 操作函数
     * @param <T>       返回类型
     * @return 操作结果的Uni包装
     */
    public <T> Uni<T> runAsPlatformAdmin(java.util.function.Supplier<Uni<T>> operation) {
        return runInTenantContext(CommonConstants.PLATFORM_ADMIN_TENANT_ID, operation);
    }

    // ==================== 清理方法 ====================

    /**
     * 清理当前租户上下文
     * 增强线程安全性，防止内存泄漏
     */
    public void clear() {
        try {
            TENANT_HOLDER.remove();
            CLEANUP_MARKER.remove();
            LOG.debug("清理租户上下文");
        } catch (Exception e) {
            LOG.warn("清理租户上下文时发生异常", e);
        }
    }

    /**
     * 清理租户缓存
     */
    public void clearCache() {
        TENANT_CACHE.clear();
        LOG.info("清理租户信息缓存");
    }

    /**
     * 强制清理所有线程的租户上下文（谨慎使用）
     * 主要用于应用关闭时的清理
     */
    public void clearAll() {
        try {
            clearCache();
            LOG.info("执行全局租户上下文清理");
        } catch (Exception e) {
            LOG.error("全局清理租户上下文时发生异常", e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取租户信息对象
     *
     * @return 租户信息对象
     */
    private TenantInfo getTenantInfo() {
        TenantInfo tenantInfo = TENANT_HOLDER.get();
        if (tenantInfo == null) {
            tenantInfo = new TenantInfo();
        }
        return tenantInfo;
    }

    /**
     * 创建默认租户信息
     *
     * @return 默认租户信息
     */
    private TenantInfo createDefaultTenantInfo() {
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId(CommonConstants.DEFAULT_TENANT_ID);
        tenantInfo.setTenantCode("default");
        tenantInfo.setTenantName("默认租户");
        tenantInfo.setTenantType("STANDARD");
        tenantInfo.setActive(true);
        return tenantInfo;
    }

    // ==================== 静态方法 ====================

    /**
     * 获取默认租户ID
     *
     * @return 默认租户ID
     */
    public static Long getDefaultTenantId() {
        return CommonConstants.DEFAULT_TENANT_ID;
    }

    /**
     * 获取平台管理员租户ID
     *
     * @return 平台管理员租户ID
     */
    public static Long getPlatformAdminTenantId() {
        return CommonConstants.PLATFORM_ADMIN_TENANT_ID;
    }

    /**
     * 租户信息内部类
     *
     * 封装租户的基本信息，包括ID、代码、名称、类型和状态
     * 支持复制构造函数，用于线程继承和缓存
     */
    public static class TenantInfo {

        /**
         * 租户ID
         */
        private Long tenantId;

        /**
         * 租户代码（唯一标识）
         */
        private String tenantCode;

        /**
         * 租户名称
         */
        private String tenantName;

        /**
         * 租户类型（如：STANDARD、PREMIUM、ENTERPRISE等）
         */
        private String tenantType;

        /**
         * 租户状态（是否激活）
         */
        private Boolean active;

        /**
         * 租户配置信息（JSON格式）
         */
        private String config;

        /**
         * 租户创建时间
         */
        private java.time.LocalDateTime createTime;

        /**
         * 租户过期时间
         */
        private java.time.LocalDateTime expireTime;

        /**
         * 默认构造函数
         */
        public TenantInfo() {
            this.active = true;
            this.createTime = java.time.LocalDateTime.now();
        }

        /**
         * 复制构造函数，用于线程继承和缓存
         *
         * @param other 源租户信息
         */
        public TenantInfo(TenantInfo other) {
            if (other != null) {
                this.tenantId = other.tenantId;
                this.tenantCode = other.tenantCode;
                this.tenantName = other.tenantName;
                this.tenantType = other.tenantType;
                this.active = other.active;
                this.config = other.config;
                this.createTime = other.createTime;
                this.expireTime = other.expireTime;
            }
        }

        // ==================== Getter和Setter方法 ====================

        public Long getTenantId() {
            return tenantId;
        }

        public void setTenantId(Long tenantId) {
            this.tenantId = tenantId;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getTenantName() {
            return tenantName;
        }

        public void setTenantName(String tenantName) {
            this.tenantName = tenantName;
        }

        public String getTenantType() {
            return tenantType;
        }

        public void setTenantType(String tenantType) {
            this.tenantType = tenantType;
        }

        public Boolean getActive() {
            return active;
        }

        public void setActive(Boolean active) {
            this.active = active;
        }

        public String getConfig() {
            return config;
        }

        public void setConfig(String config) {
            this.config = config;
        }

        public java.time.LocalDateTime getCreateTime() {
            return createTime;
        }

        public void setCreateTime(java.time.LocalDateTime createTime) {
            this.createTime = createTime;
        }

        public java.time.LocalDateTime getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(java.time.LocalDateTime expireTime) {
            this.expireTime = expireTime;
        }

        // ==================== 便捷方法 ====================

        /**
         * 判断租户是否有效
         *
         * @return true表示有效，false表示无效
         */
        public boolean isValid() {
            if (!Boolean.TRUE.equals(active)) {
                return false;
            }

            if (expireTime != null) {
                return expireTime.isAfter(java.time.LocalDateTime.now());
            }

            return true;
        }

        /**
         * 判断是否为平台管理员租户
         *
         * @return true表示是平台管理员，false表示不是
         */
        public boolean isPlatformAdmin() {
            return CommonConstants.PLATFORM_ADMIN_TENANT_ID.equals(tenantId);
        }

        /**
         * 判断是否为默认租户
         *
         * @return true表示是默认租户，false表示不是
         */
        public boolean isDefault() {
            return CommonConstants.DEFAULT_TENANT_ID.equals(tenantId);
        }

        @Override
        public String toString() {
            return String.format("TenantInfo{tenantId=%d, tenantCode='%s', tenantName='%s', tenantType='%s', active=%s}",
                tenantId, tenantCode, tenantName, tenantType, active);
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TenantInfo that = (TenantInfo) obj;
            return java.util.Objects.equals(tenantId, that.tenantId);
        }

        @Override
        public int hashCode() {
            return java.util.Objects.hash(tenantId);
        }
    }
}
