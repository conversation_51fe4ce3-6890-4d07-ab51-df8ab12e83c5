package com.visthink.erp.utils.validate;

import cn.hutool.core.util.StrUtil;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.regex.Pattern;

public class XssValidator implements ConstraintValidator<Xss, String> {

    // 更全面的XSS检测模式
    private static final Pattern[] XSS_PATTERNS = {
        // 脚本标签
        Pattern.compile("<script[^>]*>.*?</script>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL),
        Pattern.compile("<script[^>]*>", Pattern.CASE_INSENSITIVE),
        // JavaScript事件
        Pattern.compile("on\\w+\\s*=", Pattern.CASE_INSENSITIVE),
        // JavaScript协议
        Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
        // 数据协议
        Pattern.compile("data:", Pattern.CASE_INSENSITIVE),
        // 样式表达式
        Pattern.compile("expression\\s*\\(", Pattern.CASE_INSENSITIVE),
        // iframe标签
        Pattern.compile("<iframe[^>]*>", Pattern.CASE_INSENSITIVE),
        // object和embed标签
        Pattern.compile("<(object|embed)[^>]*>", Pattern.CASE_INSENSITIVE),
        // 危险的HTML标签
        Pattern.compile("<(applet|meta|xml|blink|link|style|script|embed|object|iframe|frame|frameset|ilayer|layer|bgsound|title|base)[^>]*>", Pattern.CASE_INSENSITIVE)
    };

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        return StrUtil.isBlank(value) || !containsXss(value);
    }

    /**
     * 检测是否包含XSS攻击代码
     */
    public static boolean containsXss(String value) {
        if (StrUtil.isBlank(value)) {
            return false;
        }

        // 检查所有XSS模式
        for (Pattern pattern : XSS_PATTERNS) {
            if (pattern.matcher(value).find()) {
                return true;
            }
        }

        return false;
    }

    /**
     * 清理XSS攻击代码（可选功能）
     */
    public static String cleanXss(String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }

        String cleaned = value;
        for (Pattern pattern : XSS_PATTERNS) {
            cleaned = pattern.matcher(cleaned).replaceAll("");
        }

        return cleaned;
    }
}
