package com.visthink.common.base;

import com.visthink.common.dto.ApiResponse;
import com.visthink.common.context.TenantContext;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.jboss.logging.Logger;

import java.util.List;

/**
 * 通用Resource基础抽象类
 * 提供标准的RESTful API接口
 *
 * @param <T> 实体类型
 * @param <S> Service类型，必须继承BaseService
 * @param <ID> 主键类型
 * <AUTHOR>
 *
 * @deprecated 此类已废弃，请使用 com.visthink.common.base.SimpleController
 *
 * 废弃原因：
 * 1. Controller层包含过多业务逻辑，违反单一职责原则
 * 2. 与Service层功能重复，导致代码冗余
 * 3. 职责不清，难以维护和测试
 *
 * 迁移指南：
 * 1. 继承SimpleController替代BaseResource
 * 2. 将业务逻辑移至Service层
 * 3. Controller只处理HTTP相关逻辑
 *
 * 将在下一个版本中移除
 */
@Deprecated
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public abstract class BaseResource<T, S extends BaseService<T, ID>, ID> {

    protected final Logger log = Logger.getLogger(getClass());

    @Inject
    protected TenantContext tenantContext;

    /**
     * 获取Service实例
     * 子类需要实现此方法返回具体的Service
     *
     * @return Service实例
     */
    protected abstract S getService();

    /**
     * 获取实体名称（用于日志和响应消息）
     *
     * @return 实体名称
     */
    protected abstract String getEntityName();

    /**
     * 根据ID查询实体
     *
     * @param id 实体ID
     * @return 查询结果
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "根据ID查询", description = "根据ID查询单个实体")
    @APIResponse(responseCode = "200", description = "查询成功")
    @APIResponse(responseCode = "404", description = "实体不存在")
    public Uni<ApiResponse<T>> findById(@Parameter(description = "实体ID") @PathParam("id") ID id) {
        log.infof("查询%s: id=%s", getEntityName(), id);

        return tenantContext.getCurrentTenantId()
                .onItem().transformToUni(tenantId ->
                    getService().findByIdAndTenant(id, tenantId))
                .map(entity -> {
                    if (entity != null) {
                        log.infof("查询%s成功: id=%s", getEntityName(), id);
                        return ApiResponse.<T>success(entity);
                    } else {
                        log.warnf("查询%s失败，实体不存在: id=%s", getEntityName(), id);
                        return ApiResponse.<T>notFound(getEntityName() + "不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.errorf(throwable, "查询%s失败: id=%s", getEntityName(), id);
                    return ApiResponse.<T>error("查询" + getEntityName() + "失败: " + throwable.getMessage());
                });
    }

    /**
     * 查询所有实体
     *
     * @return 查询结果
     */
    @GET
    @Operation(summary = "查询所有", description = "查询所有实体列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<T>>> findAll() {
        log.infof("查询所有%s", getEntityName());

        return tenantContext.getCurrentTenantId()
                .onItem().transformToUni(tenantId ->
                    getService().findAllByTenant(tenantId))
                .map(entities -> {
                    log.infof("查询所有%s成功，共%d条记录", getEntityName(), entities.size());
                    return ApiResponse.<List<T>>success(entities);
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.errorf(throwable, "查询所有%s失败", getEntityName());
                    return ApiResponse.<List<T>>error("查询" + getEntityName() + "列表失败: " + throwable.getMessage());
                });
    }

    /**
     * 删除实体
     *
     * @param id 实体ID
     * @return 删除结果
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除", description = "根据ID删除实体")
    @APIResponse(responseCode = "200", description = "删除成功")
    @APIResponse(responseCode = "404", description = "实体不存在")
    public Uni<ApiResponse<Boolean>> delete(@Parameter(description = "实体ID") @PathParam("id") ID id) {
        log.infof("删除%s: id=%s", getEntityName(), id);

        return tenantContext.getCurrentTenantId()
                .onItem().transformToUni(tenantId ->
                    getService().delete(id, tenantId))
                .map(result -> {
                    if (result) {
                        log.infof("删除%s成功: id=%s", getEntityName(), id);
                        return ApiResponse.<Boolean>success("删除" + getEntityName() + "成功", true);
                    } else {
                        log.warnf("删除%s失败，实体不存在: id=%s", getEntityName(), id);
                        return ApiResponse.<Boolean>notFound(getEntityName() + "不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.errorf(throwable, "删除%s失败: id=%s", getEntityName(), id);
                    return ApiResponse.<Boolean>error("删除" + getEntityName() + "失败: " + throwable.getMessage());
                });
    }

    /**
     * 统计实体数量
     *
     * @return 统计结果
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计数量", description = "统计实体总数量")
    @APIResponse(responseCode = "200", description = "统计成功")
    public Uni<ApiResponse<Long>> count() {
        log.infof("统计%s数量", getEntityName());

        return tenantContext.getCurrentTenantId()
                .onItem().transformToUni(tenantId ->
                    getService().count(tenantId))
                .map(count -> {
                    log.infof("统计%s数量成功: %d", getEntityName(), count);
                    return ApiResponse.<Long>success(count);
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.errorf(throwable, "统计%s数量失败", getEntityName());
                    return ApiResponse.<Long>error("统计" + getEntityName() + "数量失败: " + throwable.getMessage());
                });
    }

    /**
     * 检查实体是否存在
     *
     * @param id 实体ID
     * @return 检查结果
     */
    @GET
    @Path("/{id}/exists")
    @Operation(summary = "检查存在", description = "检查实体是否存在")
    @APIResponse(responseCode = "200", description = "检查成功")
    public Uni<ApiResponse<Boolean>> exists(@Parameter(description = "实体ID") @PathParam("id") ID id) {
        log.infof("检查%s是否存在: id=%s", getEntityName(), id);

        return tenantContext.getCurrentTenantId()
                .onItem().transformToUni(tenantId ->
                    getService().exists(id, tenantId))
                .map(exists -> {
                    log.infof("检查%s存在性成功: id=%s, exists=%s", getEntityName(), id, exists);
                    return ApiResponse.<Boolean>success(exists);
                })
                .onFailure().recoverWithItem(throwable -> {
                    log.errorf(throwable, "检查%s存在性失败: id=%s", getEntityName(), id);
                    return ApiResponse.<Boolean>error("检查" + getEntityName() + "存在性失败: " + throwable.getMessage());
                });
    }
}
