package com.visthink.common.service;

import com.visthink.common.context.TenantContext;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import com.visthink.common.entity.BaseEntity;
import com.visthink.common.exception.BaseException;
import com.visthink.common.repository.BaseRepository;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 通用Service抽象实现类
 * 提供基础CRUD操作的默认实现
 *
 * @param <T> 实体类型
 * @param <R> Repository类型
 * @param <CreateRequest> 创建请求DTO类型
 * @param <UpdateRequest> 更新请求DTO类型
 * @param <QueryRequest> 查询请求DTO类型
 * <AUTHOR>
 */
public abstract class AbstractBaseService<T extends BaseEntity,
                                         R extends BaseRepository<T>,
                                         CreateRequest,
                                         UpdateRequest,
                                         QueryRequest>
        implements BaseService<T, CreateRequest, UpdateRequest, QueryRequest> {

    protected final Logger log = Logger.getLogger(getClass());
    @Inject
    protected TenantContext tenantContext;
    private Long tenantId;
    /**
     * 获取Repository实例
     * 子类需要实现此方法返回具体的Repository
     *
     * @return Repository实例
     */
    protected abstract R getRepository();

    /**
     * 将创建请求转换为实体对象
     *
     * @param request 创建请求
     * @return 实体对象
     */
    protected abstract T convertCreateRequestToEntity( CreateRequest request);

    /**
     * 将更新请求应用到实体对象
     *
     * @param entity 实体对象
     * @param request 更新请求
     * @return 更新后的实体对象
     */
    protected abstract T applyUpdateRequestToEntity(T entity, UpdateRequest request);

    /**
     * 构建查询条件
     *
     * @param queryRequest 查询请求
     * @return 查询条件和参数
     */
    protected abstract QueryCondition buildQueryCondition(QueryRequest queryRequest);

    /**
     * 获当前租户ID
     * @return 租户ID
     */
    protected Uni<Long> getCurrentTenantId() {
        return tenantContext.getCurrentTenantId();
    }

    @Override
    @WithTransaction
    public Uni<T> create(Long tenantId, CreateRequest request) {
        log.debugf("创建实体: tenantId=%d, request=%s", tenantId, request);

        return validateCreateRequest(tenantId, request)
                .onItem().transformToUni(validationError -> {
                    if (validationError != null) {
                        return Uni.createFrom().failure(new BaseException("VALIDATION_ERROR", validationError));
                    }

                    T entity = convertCreateRequestToEntity(request);
                    entity.tenantId = tenantId;
                    entity.createTime = LocalDateTime.now();
                    entity.updateTime = LocalDateTime.now();

                    return beforeCreate(tenantId, entity)
                            .onItem().transformToUni(processedEntity ->
                                getRepository().persist(processedEntity))
                            .onItem().transformToUni(savedEntity ->
                                afterCreate(tenantId, savedEntity));
                });
    }

    @Override
    @WithTransaction
    public Uni<T> update(Long tenantId, Long id, UpdateRequest request) {
        log.debugf("更新实体: tenantId=%d, id=%d, request=%s", tenantId, id, request);

        return validateUpdateRequest(tenantId, id, request)
                .onItem().transformToUni(validationError -> {
                    if (validationError != null) {
                        return Uni.createFrom().failure(new BaseException("VALIDATION_ERROR", validationError));
                    }

                    return getRepository().findByTenantAndId(tenantId, id)
                            .onItem().ifNull().failWith(() ->
                                new BaseException("ENTITY_NOT_FOUND", "实体不存在: " + id))
                            .onItem().transform(entity -> {
                                T updatedEntity = applyUpdateRequestToEntity(entity, request);
                                updatedEntity.updateTime = LocalDateTime.now();
                                return updatedEntity;
                            })
                            .onItem().transformToUni(entity -> beforeUpdate(tenantId, entity))
                            .onItem().transformToUni(entity -> getRepository().persist(entity))
                            .onItem().transformToUni(entity -> afterUpdate(tenantId, entity));
                });
    }

    @Override
    @WithTransaction
    public Uni<Boolean> delete(Long tenantId, Long id) {
        log.debugf("删除实体: tenantId=%d, id=%d", tenantId, id);

        return getRepository().findByTenantAndId(tenantId, id)
                .onItem().ifNull().failWith(() ->
                    new BaseException("ENTITY_NOT_FOUND", "实体不存在: " + id))
                .onItem().transformToUni(entity ->
                    beforeDelete(tenantId, entity)
                            .onItem().transformToUni(canDelete -> {
                                if (!canDelete) {
                                    return Uni.createFrom().failure(
                                        new BaseException("DELETE_NOT_ALLOWED", "不允许删除此实体"));
                                }

                                // 软删除：设置状态为0
                                entity.updateTime = LocalDateTime.now();
                                return getRepository().update("status = 0, updateTime = ?1 where tenantId = ?2 and id = ?3",
                                        LocalDateTime.now(), tenantId, id)
                                        .onItem().transformToUni(count ->
                                            afterDelete(tenantId, entity).map(v -> count > 0));
                            }));
    }

    @Override
    @WithSession
    public Uni<T> findById(Long tenantId, Long id) {
        log.debugf("查询实体: tenantId=%d, id=%d", tenantId, id);

        return getRepository().findByTenantAndId(tenantId, id)
                .onItem().ifNull().failWith(() ->
                    new BaseException("ENTITY_NOT_FOUND", "实体不存在: " + id));
    }

    @Override
    public Uni<List<T>> findAll(Long tenantId) {
        log.debugf("查询所有实体: tenantId=%d", tenantId);

        return getRepository().findByTenantId(tenantId);
    }

    @Override
    public Uni<PageResult<T>> findByPage(Long tenantId, PageRequest pageRequest) {
        log.debugf("分页查询实体: tenantId=%d, pageRequest=%s", tenantId, pageRequest);

        return getRepository().findByTenantIdWithPage(tenantId, pageRequest);
    }

    @Override
    public Uni<PageResult<T>> findByQuery(Long tenantId, QueryRequest queryRequest, PageRequest pageRequest) {
        log.debugf("条件分页查询实体: tenantId=%s, queryRequest=%s, pageRequest=%s",
                  tenantId, queryRequest, pageRequest);

        QueryCondition condition = buildQueryCondition(queryRequest);
        if (condition == null || condition.getQuery() == null) {
            return findByPage(tenantId, pageRequest);
        }

        return getRepository().findByTenantAndConditionWithPage(
            tenantId, condition.getQuery(), pageRequest, condition.getParams());
    }

    @Override
    @WithTransaction
    public Uni<List<T>> batchCreate(Long tenantId, List<CreateRequest> requests) {
        log.debug("批量创建实体: tenantId=" + tenantId + ", count=" + requests.size());

        if (requests == null || requests.isEmpty()) {
            return Uni.createFrom().item(List.of());
        }

        List<T> entities = requests.stream()
                .map(request -> {
                    T entity = convertCreateRequestToEntity(request);
                    entity.tenantId = tenantId;
                    entity.createTime = LocalDateTime.now();
                    entity.updateTime = LocalDateTime.now();
                    return entity;
                })
                .toList();

        return getRepository().batchInsert(entities)
                .map(count -> entities);
    }

    @Override
    @WithTransaction
    public Uni<Integer> batchUpdateStatus(Long tenantId, List<Long> ids, Integer status) {
        log.debugf("批量更新状态: tenantId=%s, ids=%s, status=%s", tenantId, ids, status);

        return getRepository().batchUpdateStatus(tenantId, ids, status);
    }

    @Override
    @WithTransaction
    public Uni<Integer> batchDelete(Long tenantId, List<Long> ids) {
        log.debugf("批量删除实体: tenantId=%s, ids=%s", tenantId, ids);

        return getRepository().batchSoftDelete(tenantId, ids);
    }

    @Override
    public Uni<Long> count(Long tenantId) {
        log.debugf("统计实体数量: tenantId=%s", tenantId);

        return getRepository().countByTenantId(tenantId);
    }

    @Override
    public Uni<Long> countByQuery(Long tenantId, QueryRequest queryRequest) {
        log.debugf("条件统计实体数量: tenantId=%s, queryRequest=%s", tenantId, queryRequest);

        QueryCondition condition = buildQueryCondition(queryRequest);
        if (condition == null || condition.getQuery() == null) {
            return count(tenantId);
        }

        return getRepository().countByTenantAndCondition(
            tenantId, condition.getQuery(), condition.getParams());
    }

    @Override
    public Uni<Boolean> exists(Long tenantId, Long id) {
        log.debugf("检查实体是否存在: tenantId=%s, id=%s", tenantId, id);

        return getRepository().existsByTenantAndId(tenantId, id);
    }

    @Override
    public Uni<List<T>> findByConditions(Long tenantId, Map<String, Object> conditions) {
        log.debugf("动态条件查询: tenantId=%s, conditions=%s", tenantId, conditions);

        return getRepository().findByDynamicConditions(tenantId, conditions);
    }

    @Override
    public Uni<Boolean> isFieldValueUnique(Long tenantId, String fieldName, Object fieldValue, Long excludeId) {
        log.debugf("检查字段值唯一性: tenantId=%s, field=%s, value=%s, excludeId=%s",
                  tenantId, fieldName, fieldValue, excludeId);

        return getRepository().isFieldValueUnique(tenantId, fieldName, fieldValue, excludeId);
    }

    /**
     * 查询条件封装类
     */
    protected static class QueryCondition {
        private  String query;
        private Object[] params;
   ;
        public QueryCondition(String query, Object... params) {
            this.query = query;
            this.params = params;
        }

        public QueryCondition() {
        }

        public String getQuery() {
            return query;
        }

        public Object[] getParams() {
            return params;
        }
    }
}
