package com.visthink.erp.config;

import io.quarkus.runtime.StartupEvent;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import org.jboss.logging.Logger;

@ApplicationScoped
public class LoggingConfig {
    
    private static final Logger LOG = Logger.getLogger(LoggingConfig.class);
    
    void onStart(@Observes StartupEvent ev) {
        LOG.info("应用程序启动");
    }
} 