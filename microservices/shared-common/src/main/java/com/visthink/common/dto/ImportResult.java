package com.visthink.common.dto;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 导入结果DTO
 *
 * 用于封装数据导入操作的结果信息
 * 包含成功、失败统计和详细错误信息
 *
 * @param <T> 导入的实体类型
 * <AUTHOR>
 */
public class ImportResult<T> {

    /**
     * 导入是否成功
     */
    private Boolean success;

    /**
     * 总行数
     */
    private Integer totalRows;

    /**
     * 成功导入行数
     */
    private Integer successRows;

    /**
     * 失败行数
     */
    private Integer failedRows;

    /**
     * 跳过行数
     */
    private Integer skippedRows;

    /**
     * 导入开始时间
     */
    private LocalDateTime startTime;

    /**
     * 导入结束时间
     */
    private LocalDateTime endTime;

    /**
     * 导入耗时（毫秒）
     */
    private Long duration;

    /**
     * 成功导入的数据
     */
    private List<T> successData;

    /**
     * 失败的行信息
     */
    private List<ImportError> errors;

    /**
     * 导入消息
     */
    private String message;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 导入任务ID
     */
    private String taskId;

    // 构造函数
    public ImportResult() {
        this.successData = new ArrayList<>();
        this.errors = new ArrayList<>();
        this.startTime = LocalDateTime.now();
    }

    public ImportResult(String fileName) {
        this();
        this.fileName = fileName;
    }

    // 便捷方法
    public void addSuccessData(T data) {
        if (this.successData == null) {
            this.successData = new ArrayList<>();
        }
        this.successData.add(data);
    }

    public void addError(int rowNumber, String error) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(new ImportError(rowNumber, error));
    }

    public void addError(int rowNumber, String field, String error) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(new ImportError(rowNumber, field, error));
    }

    public void finish() {
        this.endTime = LocalDateTime.now();
        if (this.startTime != null && this.endTime != null) {
            this.duration = java.time.Duration.between(this.startTime, this.endTime).toMillis();
        }

        // 计算统计信息
        this.successRows = this.successData != null ? this.successData.size() : 0;
        this.failedRows = this.errors != null ? this.errors.size() : 0;
        this.success = this.failedRows == 0;
    }

    // Getter和Setter方法
    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(Integer totalRows) {
        this.totalRows = totalRows;
    }

    public Integer getSuccessRows() {
        return successRows;
    }

    public void setSuccessRows(Integer successRows) {
        this.successRows = successRows;
    }

    public Integer getFailedRows() {
        return failedRows;
    }

    public void setFailedRows(Integer failedRows) {
        this.failedRows = failedRows;
    }

    public Integer getSkippedRows() {
        return skippedRows;
    }

    public void setSkippedRows(Integer skippedRows) {
        this.skippedRows = skippedRows;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public List<T> getSuccessData() {
        return successData;
    }

    public void setSuccessData(List<T> successData) {
        this.successData = successData;
    }

    public List<ImportError> getErrors() {
        return errors;
    }

    public void setErrors(List<ImportError> errors) {
        this.errors = errors;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 导入错误信息
     */
    public static class ImportError {
        private Integer rowNumber;
        private String field;
        private String error;

        public ImportError() {
        }

        public ImportError(Integer rowNumber, String error) {
            this.rowNumber = rowNumber;
            this.error = error;
        }

        public ImportError(Integer rowNumber, String field, String error) {
            this.rowNumber = rowNumber;
            this.field = field;
            this.error = error;
        }

        public Integer getRowNumber() {
            return rowNumber;
        }

        public void setRowNumber(Integer rowNumber) {
            this.rowNumber = rowNumber;
        }

        public String getField() {
            return field;
        }

        public void setField(String field) {
            this.field = field;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        @Override
        public String toString() {
            return "ImportError{" +
                    "rowNumber=" + rowNumber +
                    ", field='" + field + '\'' +
                    ", error='" + error + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "ImportResult{" +
                "success=" + success +
                ", totalRows=" + totalRows +
                ", successRows=" + successRows +
                ", failedRows=" + failedRows +
                ", skippedRows=" + skippedRows +
                ", duration=" + duration +
                ", fileName='" + fileName + '\'' +
                ", taskId='" + taskId + '\'' +
                '}';
    }
}
