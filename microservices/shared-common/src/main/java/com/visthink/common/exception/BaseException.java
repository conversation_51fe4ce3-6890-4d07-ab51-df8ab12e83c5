package com.visthink.common.exception;

import com.visthink.common.enums.ResponseCode;

/**
 * 基础异常类
 * 
 * 所有业务异常的基类，提供统一的异常处理机制
 * 包含错误码、错误消息、异常数据等信息
 * 
 * <AUTHOR>
 */
public class BaseException extends RuntimeException {
    
    /**
     * 序列化版本号
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private String code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 异常数据（可选）
     */
    private Object data;
    
    /**
     * 租户ID（用于多租户环境下的异常追踪）
     */
    private Long tenantId;
    
    /**
     * 用户ID（用于异常追踪）
     */
    private String userId;
    
    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 默认构造函数
     */
    public BaseException() {
        super();
    }
    
    /**
     * 构造函数
     *
     * @param message 错误消息
     */
    public BaseException(String message) {
        super(message);
        this.message = message;
        this.code = ResponseCode.BUSINESS_ERROR.getCode();
    }
    
    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public BaseException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     *
     * @param responseCode 响应码枚举
     */
    public BaseException(ResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数
     *
     * @param responseCode 响应码枚举
     * @param data         异常数据
     */
    public BaseException(ResponseCode responseCode, Object data) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = data;
    }
    
    /**
     * 构造函数
     *
     * @param message 错误消息
     * @param cause   原始异常
     */
    public BaseException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
        this.code = ResponseCode.BUSINESS_ERROR.getCode();
    }
    
    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     * @param cause   原始异常
     */
    public BaseException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     *
     * @param responseCode 响应码枚举
     * @param cause        原始异常
     */
    public BaseException(ResponseCode responseCode, Throwable cause) {
        super(responseCode.getMessage(), cause);
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
    }
    
    /**
     * 构造函数
     *
     * @param responseCode 响应码枚举
     * @param data         异常数据
     * @param cause        原始异常
     */
    public BaseException(ResponseCode responseCode, Object data, Throwable cause) {
        super(responseCode.getMessage(), cause);
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = data;
    }
    
    // ==================== Getter和Setter方法 ====================
    
    public String getCode() {
        return code;
    }
    
    public BaseException setCode(String code) {
        this.code = code;
        return this;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public BaseException setMessage(String message) {
        this.message = message;
        return this;
    }
    
    public Object getData() {
        return data;
    }
    
    public BaseException setData(Object data) {
        this.data = data;
        return this;
    }
    
    public Long getTenantId() {
        return tenantId;
    }
    
    public BaseException setTenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public BaseException setUserId(String userId) {
        this.userId = userId;
        return this;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public BaseException setRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 设置上下文信息
     *
     * @param tenantId  租户ID
     * @param userId    用户ID
     * @param requestId 请求ID
     * @return 当前异常实例
     */
    public BaseException setContext(Long tenantId, String userId, String requestId) {
        this.tenantId = tenantId;
        this.userId = userId;
        this.requestId = requestId;
        return this;
    }
    
    /**
     * 创建业务异常
     *
     * @param message 错误消息
     * @return 异常实例
     */
    public static BaseException business(String message) {
        return new BaseException(ResponseCode.BUSINESS_ERROR.getCode(), message);
    }
    
    /**
     * 创建参数验证异常
     *
     * @param message 错误消息
     * @return 异常实例
     */
    public static BaseException validation(String message) {
        return new BaseException(ResponseCode.VALIDATION_ERROR.getCode(), message);
    }
    
    /**
     * 创建权限不足异常
     *
     * @param message 错误消息
     * @return 异常实例
     */
    public static BaseException forbidden(String message) {
        return new BaseException(ResponseCode.FORBIDDEN.getCode(), message);
    }
    
    /**
     * 创建资源不存在异常
     *
     * @param message 错误消息
     * @return 异常实例
     */
    public static BaseException notFound(String message) {
        return new BaseException(ResponseCode.NOT_FOUND.getCode(), message);
    }
    
    /**
     * 创建租户相关异常
     *
     * @param responseCode 租户相关的响应码
     * @return 异常实例
     */
    public static BaseException tenant(ResponseCode responseCode) {
        return new BaseException(responseCode);
    }
    
    @Override
    public String toString() {
        return String.format("BaseException{code='%s', message='%s', tenantId=%d, userId='%s', requestId='%s'}", 
                code, message, tenantId, userId, requestId);
    }
}
