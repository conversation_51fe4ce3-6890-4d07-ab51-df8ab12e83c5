# 📦 库存管理服务 (Inventory Service)

## 📋 服务概述

库存管理服务是Visthink ERP微服务架构中的核心业务服务，负责管理商品库存、库存变动日志、库存预警等功能，与商品服务紧密集成，为订单服务提供库存支持。

## ✨ 主要功能

### 🎯 核心功能
- **库存管理**: 商品SKU级别的库存数量管理
- **库存变动**: 入库、出库、调拨、盘点等操作
- **预占管理**: 订单预占库存和释放机制
- **仓库管理**: 多仓库库存分布管理
- **库存预警**: 智能库存预警和补货提醒

### 🔍 高级功能
- **变动日志**: 完整的库存变动审计追踪
- **预警系统**: 多级别库存预警机制
- **库存盘点**: 定期库存盘点和差异处理
- **安全库存**: 安全库存和补货点管理
- **统计分析**: 库存周转率和库存价值分析

### 🚨 预警类型
- **库存不足**: 可用库存低于预警阈值
- **零库存**: 商品库存为零的预警
- **负库存**: 异常负库存的紧急预警
- **库存过多**: 库存超过最大库存限制

## 🏗️ 技术架构

### 技术栈
- **框架**: Quarkus 3.22.2
- **编程语言**: Java 17
- **数据库**: PostgreSQL (响应式)
- **ORM**: Hibernate Reactive Panache
- **API**: RESTful + OpenAPI 3.0
- **缓存**: Redis
- **监控**: Prometheus + Micrometer

### 架构模式
- **响应式编程**: 基于Uni<T>的非阻塞编程
- **分层架构**: Entity -> Repository -> Service -> Resource
- **多租户**: 基于租户ID的数据隔离
- **事件驱动**: 库存变动事件通知机制

## 📊 数据模型

### 核心实体

#### Inventory (库存)
```java
- id: 主键ID
- tenantId: 租户ID
- productId: 商品ID
- skuId: SKU ID
- skuCode: SKU编码
- warehouseId: 仓库ID
- quantity: 当前库存数量
- availableQuantity: 可用库存数量
- reservedQuantity: 预占库存数量
- safetyStock: 安全库存
- warningStock: 预警库存
- reorderPoint: 补货点
```

#### InventoryLog (库存变动日志)
```java
- id: 主键ID
- tenantId: 租户ID
- inventoryId: 库存ID
- changeType: 变动类型 (入库/出库/调拨/盘点等)
- changeQuantity: 变动数量
- beforeQuantity: 变动前数量
- afterQuantity: 变动后数量
- businessType: 业务类型
- businessId: 业务单据ID
- operatorId: 操作人ID
```

#### StockAlert (库存预警)
```java
- id: 主键ID
- tenantId: 租户ID
- inventoryId: 库存ID
- alertType: 预警类型
- alertLevel: 预警级别 (低/中/高/紧急)
- alertStatus: 预警状态 (待处理/处理中/已处理)
- currentQuantity: 当前库存数量
- alertThreshold: 预警阈值
```

## 🚀 快速开始

### 环境要求
- JDK 17+
- Maven 3.8+
- Docker 20.10+
- PostgreSQL 13+

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd microservices/inventory-service
```

2. **编译项目**
```bash
mvn clean compile
```

3. **运行服务**
```bash
mvn quarkus:dev
```

4. **访问服务**
- 服务地址: http://localhost:8083
- API文档: http://localhost:8083/q/swagger-ui
- 健康检查: http://localhost:8083/health

### Docker部署

1. **构建镜像**
```bash
mvn clean package
docker build -t inventory-service .
```

2. **运行容器**
```bash
docker run -p 8083:8083 inventory-service
```

## 📚 API文档

### 主要接口

#### 库存管理
- `POST /api/inventory` - 创建库存记录
- `GET /api/inventory/{id}` - 获取库存详情
- `PUT /api/inventory/{id}` - 更新库存信息
- `GET /api/inventory` - 分页查询库存

#### 库存操作
- `POST /api/inventory/{id}/increase` - 增加库存
- `POST /api/inventory/{id}/decrease` - 减少库存
- `POST /api/inventory/{id}/reserve` - 预占库存
- `POST /api/inventory/{id}/release` - 释放预占

#### 库存查询
- `GET /api/inventory/sku/{skuId}` - 根据SKU查询库存
- `GET /api/inventory/product/{productId}` - 根据商品查询库存
- `GET /api/inventory/low-stock` - 查询库存不足商品
- `GET /api/inventory/statistics` - 库存统计信息

#### 库存日志
- `GET /api/inventory-logs` - 查询库存变动日志
- `GET /api/inventory-logs/inventory/{id}` - 查询指定库存的变动日志

#### 库存预警
- `GET /api/stock-alerts` - 查询库存预警
- `PUT /api/stock-alerts/{id}/handle` - 处理库存预警
- `GET /api/stock-alerts/pending` - 查询待处理预警

### 请求示例

#### 创建库存记录
```bash
curl -X POST http://localhost:8083/api/inventory \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: 1" \
  -d '{
    "productId": 1,
    "skuId": 1,
    "skuCode": "SKU001",
    "quantity": 100,
    "safetyStock": 10,
    "warningStock": 20
  }'
```

#### 增加库存
```bash
curl -X POST http://localhost:8083/api/inventory/1/increase \
  -H "Content-Type: application/json" \
  -H "X-Tenant-Id: 1" \
  -d '{
    "quantity": 50,
    "reason": "采购入库",
    "businessType": 1,
    "businessId": 123
  }'
```

## ⚙️ 配置说明

### 应用配置 (application.yml)
```yaml
quarkus:
  application:
    name: inventory-service
  http:
    port: 8083
  datasource:
    reactive:
      url: postgresql://localhost:35432/visthink_inventory

app:
  inventory:
    warning-threshold: 10
    safety-stock: 5
    reorder-point: 20
```

### 环境变量
- `DB_URL`: 数据库连接地址
- `DB_USERNAME`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `REDIS_HOSTS`: Redis连接地址
- `PRODUCT_SERVICE_URL`: 商品服务地址

## 🔧 开发指南

### 库存变动类型
```java
public static class ChangeType {
    public static final int IN_STOCK = 1;      // 入库
    public static final int OUT_STOCK = 2;     // 出库
    public static final int TRANSFER = 3;      // 调拨
    public static final int STOCKTAKING = 4;   // 盘点
    public static final int RESERVE = 5;       // 预占
    public static final int RELEASE = 6;       // 释放
    public static final int LOSS = 7;          // 损耗
    public static final int RETURN = 8;        // 退货
}
```

### 预警级别
```java
public static class AlertLevel {
    public static final int LOW = 1;       // 低
    public static final int MEDIUM = 2;    // 中
    public static final int HIGH = 3;      // 高
    public static final int URGENT = 4;    // 紧急
}
```

### 业务集成

#### 与商品服务集成
```java
// 获取商品信息
@RestClient
ProductServiceClient productService;

public Uni<ProductInfo> getProductInfo(Long productId) {
    return productService.getProduct(productId);
}
```

#### 与订单服务集成
```java
// 预占库存
public Uni<Boolean> reserveStock(Long skuId, Integer quantity) {
    return inventoryRepository.reserveStock(skuId, quantity);
}

// 确认出库
public Uni<Boolean> confirmOutbound(Long skuId, Integer quantity) {
    return inventoryRepository.confirmOutbound(skuId, quantity);
}
```

## 🧪 测试

### 运行测试
```bash
mvn test
```

### 集成测试
```bash
mvn verify
```

## 📈 监控与运维

### 健康检查
- `/health/live` - 存活检查
- `/health/ready` - 就绪检查

### 指标监控
- `/metrics` - Prometheus指标
- 库存数量监控
- 预警数量监控
- 变动频率监控

### 关键指标
- `inventory_total_count` - 总库存数量
- `inventory_low_stock_count` - 库存不足商品数量
- `inventory_alert_count` - 预警数量
- `inventory_change_rate` - 库存变动频率

## 🐛 故障排除

### 常见问题

1. **库存数据不一致**
   - 检查库存变动日志
   - 执行库存盘点校正

2. **预警不及时**
   - 检查预警阈值设置
   - 验证预警任务调度

3. **性能问题**
   - 检查数据库索引
   - 优化查询语句

## 📞 支持与反馈

如有问题或建议，请联系开发团队或提交Issue。

---

**📦 感谢使用Visthink ERP库存管理服务！**
