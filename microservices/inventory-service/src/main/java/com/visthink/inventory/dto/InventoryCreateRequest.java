package com.visthink.inventory.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 库存创建请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class InventoryCreateRequest {

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * SKU ID
     */
    @NotNull(message = "SKU ID不能为空")
    private Long skuId;

    /**
     * SKU编码
     */
    @NotBlank(message = "SKU编码不能为空")
    private String skuCode;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 初始库存数量
     */
    @NotNull(message = "初始库存数量不能为空")
    @Min(value = 0, message = "初始库存数量不能小于0")
    private Integer quantity;

    /**
     * 安全库存数量
     */
    @Min(value = 0, message = "安全库存数量不能小于0")
    private Integer safetyStock = 0;

    /**
     * 预警库存数量
     */
    @Min(value = 0, message = "预警库存数量不能小于0")
    private Integer warningStock = 0;

    /**
     * 最大库存数量
     */
    @Min(value = 0, message = "最大库存数量不能小于0")
    private Integer maxStock;

    /**
     * 最小库存数量
     */
    @Min(value = 0, message = "最小库存数量不能小于0")
    private Integer minStock;

    /**
     * 补货点
     */
    @Min(value = 0, message = "补货点不能小于0")
    private Integer reorderPoint;

    /**
     * 补货数量
     */
    @Min(value = 0, message = "补货数量不能小于0")
    private Integer reorderQuantity;

    /**
     * 平均成本价
     */
    @Min(value = 0, message = "平均成本价不能小于0")
    private BigDecimal averageCost;

    /**
     * 备注
     */
    private String remark;
}
