package com.visthink.inventory.dto;

import lombok.Data;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 库存更新请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class InventoryUpdateRequest {

    /**
     * 安全库存数量
     */
    @Min(value = 0, message = "安全库存数量不能小于0")
    private Integer safetyStock;

    /**
     * 预警库存数量
     */
    @Min(value = 0, message = "预警库存数量不能小于0")
    private Integer warningStock;

    /**
     * 最大库存数量
     */
    @Min(value = 0, message = "最大库存数量不能小于0")
    private Integer maxStock;

    /**
     * 最小库存数量
     */
    @Min(value = 0, message = "最小库存数量不能小于0")
    private Integer minStock;

    /**
     * 补货点
     */
    @Min(value = 0, message = "补货点不能小于0")
    private Integer reorderPoint;

    /**
     * 补货数量
     */
    @Min(value = 0, message = "补货数量不能小于0")
    private Integer reorderQuantity;

    /**
     * 平均成本价
     */
    @Min(value = 0, message = "平均成本价不能小于0")
    private BigDecimal averageCost;

    /**
     * 库存状态：1-正常，2-冻结，3-盘点中
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
