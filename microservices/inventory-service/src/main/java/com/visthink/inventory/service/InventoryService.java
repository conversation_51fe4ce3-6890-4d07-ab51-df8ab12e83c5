package com.visthink.inventory.service;

import com.visthink.common.base.BaseService;
import com.visthink.inventory.entity.Inventory;
import com.visthink.inventory.entity.InventoryLog;
import com.visthink.inventory.exception.BusinessException;
import com.visthink.inventory.repository.InventoryRepository;
import com.visthink.inventory.dto.InventoryCreateRequest;
import com.visthink.inventory.dto.InventoryUpdateRequest;
import com.visthink.inventory.dto.InventoryOperationRequest;
import com.visthink.inventory.dto.InventoryQueryRequest;
import com.visthink.inventory.dto.InventoryStatistics;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import java.util.List;
import java.math.BigDecimal;

/**
 * 库存服务类
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class InventoryService extends BaseService<Inventory, Long> {

    @Inject
    InventoryRepository inventoryRepository;

    @Inject
    InventoryLogService inventoryLogService;

    @Inject
    StockAlertService stockAlertService;

    /**
     * 创建库存记录
     *
     * @param tenantId 租户ID
     * @param request 创建请求
     * @return 库存记录
     */
    public Uni<Inventory> createInventory(Long tenantId, InventoryCreateRequest request) {
        return validateTenant(tenantId)
            .flatMap(v -> {
                Inventory inventory = new Inventory();
                inventory.setTenantId(tenantId);
                inventory.setProductId(request.getProductId());
                inventory.setProductCode(request.getProductCode());
                inventory.setSkuId(request.getSkuId());
                inventory.setSkuCode(request.getSkuCode());
                inventory.setWarehouseId(request.getWarehouseId());
                inventory.setWarehouseCode(request.getWarehouseCode());
                inventory.setQuantity(request.getQuantity());
                inventory.setSafetyStock(request.getSafetyStock());
                inventory.setWarningStock(request.getWarningStock());
                inventory.setMaxStock(request.getMaxStock());
                inventory.setMinStock(request.getMinStock());
                inventory.setReorderPoint(request.getReorderPoint());
                inventory.setReorderQuantity(request.getReorderQuantity());
                inventory.setAverageCost(request.getAverageCost());

                // 计算可用库存和总价值
                inventory.calculateAvailableQuantity();
                inventory.calculateTotalValue();

                return inventoryRepository.persist(inventory);
            })
            .flatMap(inventory -> {
                // 记录库存变动日志
                return inventoryLogService.logInventoryChange(
                    inventory,
                    inventory.getQuantity(),
                    0,
                    inventory.getQuantity(),
                    1, // 入库
                    "初始化库存",
                    null,
                    null,
                    null
                ).replaceWith(inventory);
            })
            .flatMap(inventory -> {
                // 检查是否需要生成预警
                return checkAndCreateAlert(inventory).replaceWith(inventory);
            });
    }

    /**
     * 更新库存信息
     *
     * @param tenantId 租户ID
     * @param id 库存ID
     * @param request 更新请求
     * @return 更新后的库存记录
     */
    public Uni<Inventory> updateInventory(Long tenantId, Long id, InventoryUpdateRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(inventory -> {
                if (request.getSafetyStock() != null) {
                    inventory.setSafetyStock(request.getSafetyStock());
                }
                if (request.getWarningStock() != null) {
                    inventory.setWarningStock(request.getWarningStock());
                }
                if (request.getMaxStock() != null) {
                    inventory.setMaxStock(request.getMaxStock());
                }
                if (request.getMinStock() != null) {
                    inventory.setMinStock(request.getMinStock());
                }
                if (request.getReorderPoint() != null) {
                    inventory.setReorderPoint(request.getReorderPoint());
                }
                if (request.getReorderQuantity() != null) {
                    inventory.setReorderQuantity(request.getReorderQuantity());
                }
                if (request.getAverageCost() != null) {
                    inventory.setAverageCost(request.getAverageCost());
                    inventory.calculateTotalValue();
                }
                if (request.getStatus() != null) {
                    inventory.setStatus(request.getStatus());
                }
                if (request.getRemark() != null) {
                    inventory.setRemark(request.getRemark());
                }

                return inventoryRepository.persist(inventory);
            });
    }

    /**
     * 增加库存
     *
     * @param tenantId 租户ID
     * @param id 库存ID
     * @param request 操作请求
     * @return 操作结果
     */
    public Uni<Boolean> increaseStock(Long tenantId, Long id, InventoryOperationRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(inventory -> {
                Integer beforeQuantity = inventory.getQuantity();
                Integer beforeAvailable = inventory.getAvailableQuantity();

                return inventoryRepository.increaseQuantity(id, request.getQuantity())
                    .flatMap(updated -> {
                        if (updated > 0) {
                            // 重新查询更新后的库存
                            return inventoryRepository.findById(id)
                                .flatMap(updatedInventory -> {
                                    // 更新平均成本
                                    if (request.getUnitPrice() != null) {
                                        updateAverageCost(updatedInventory, request.getQuantity(), request.getUnitPrice());
                                        return inventoryRepository.persist(updatedInventory);
                                    }
                                    return Uni.createFrom().item(updatedInventory);
                                })
                                .flatMap(updatedInventory -> {
                                    // 记录库存变动日志
                                    return inventoryLogService.logInventoryChange(
                                        updatedInventory,
                                        request.getQuantity(),
                                        beforeQuantity,
                                        updatedInventory.getQuantity(),
                                        1, // 入库
                                        request.getReason(),
                                        request.getBusinessType(),
                                        request.getBusinessId(),
                                        request.getBusinessNo()
                                    );
                                })
                                .replaceWith(true);
                        }
                        return Uni.createFrom().item(false);
                    });
            });
    }

    /**
     * 减少库存
     *
     * @param tenantId 租户ID
     * @param id 库存ID
     * @param request 操作请求
     * @return 操作结果
     */
    public Uni<Boolean> decreaseStock(Long tenantId, Long id, InventoryOperationRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(inventory -> {
                // 检查库存是否充足
                if (inventory.getAvailableQuantity() < request.getQuantity()) {
                    return Uni.createFrom().failure(new RuntimeException("库存不足"));
                }

                Integer beforeQuantity = inventory.getQuantity();

                return inventoryRepository.decreaseQuantity(id, request.getQuantity())
                    .flatMap(updated -> {
                        if (updated > 0) {
                            // 重新查询更新后的库存
                            return inventoryRepository.findById(id)
                                .flatMap(updatedInventory -> {
                                    // 记录库存变动日志
                                    return inventoryLogService.logInventoryChange(
                                        updatedInventory,
                                        -request.getQuantity(),
                                        beforeQuantity,
                                        updatedInventory.getQuantity(),
                                        2, // 出库
                                        request.getReason(),
                                        request.getBusinessType(),
                                        request.getBusinessId(),
                                        request.getBusinessNo()
                                    );
                                })
                                .flatMap(v -> {
                                    // 检查是否需要生成预警
                                    return inventoryRepository.findById(id)
                                        .flatMap(this::checkAndCreateAlert);
                                })
                                .replaceWith(true);
                        }
                        return Uni.createFrom().item(false);
                    });
            });
    }

    /**
     * 预占库存
     *
     * @param tenantId 租户ID
     * @param id 库存ID
     * @param request 操作请求
     * @return 操作结果
     */
    @WithTransaction
    public Uni<Boolean> reserveStock(Long tenantId, Long id, InventoryOperationRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(inventory -> {
                if (inventory == null) {
                    throw new BusinessException("INVENTORY_NOT_FOUND", "库存记录不存在");
                }

                // 检查库存状态
                if (inventory.getStatus() != Inventory.Status.NORMAL) {
                    throw new BusinessException("INVENTORY_STATUS_ERROR", "库存状态异常，无法预占");
                }

                // 检查可用库存是否充足
                if (inventory.getAvailableQuantity() < request.getQuantity()) {
                    throw new BusinessException("INSUFFICIENT_STOCK",
                        String.format("可用库存不足，当前可用：%d，需要：%d",
                            inventory.getAvailableQuantity(), request.getQuantity()));
                }

                Integer beforeReservedQuantity = inventory.getReservedQuantity();

                return inventoryRepository.reserveStock(id, request.getQuantity())
                    .flatMap(updated -> {
                        if (updated > 0) {
                            // 重新查询更新后的库存
                            return inventoryRepository.findById(id)
                                .flatMap(updatedInventory -> {
                                    // 记录库存变动日志
                                    return inventoryLogService.logInventoryChange(
                                        updatedInventory,
                                        request.getQuantity(),
                                        beforeReservedQuantity,
                                        updatedInventory.getReservedQuantity(),
                                        InventoryLog.ChangeType.RESERVE, // 预占
                                        request.getReason() != null ? request.getReason() : "库存预占",
                                        request.getBusinessType(),
                                        request.getBusinessId(),
                                        request.getBusinessNo()
                                    );
                                })
                                .flatMap(v -> {
                                    // 检查是否需要生成预警
                                    return inventoryRepository.findById(id)
                                        .flatMap(this::checkAndCreateAlert);
                                })
                                .replaceWith(true);
                        }
                        throw new BusinessException("RESERVE_STOCK_FAILED", "预占库存失败");
                    });
            });
    }

    /**
     * 释放预占库存
     *
     * @param tenantId 租户ID
     * @param id 库存ID
     * @param request 操作请求
     * @return 操作结果
     */
    @WithTransaction
    public Uni<Boolean> releaseReservedStock(Long tenantId, Long id, InventoryOperationRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(inventory -> {
                if (inventory == null) {
                    throw new BusinessException("INVENTORY_NOT_FOUND", "库存记录不存在");
                }

                // 检查预占库存是否充足
                if (inventory.getReservedQuantity() < request.getQuantity()) {
                    throw new BusinessException("INSUFFICIENT_RESERVED_STOCK",
                        String.format("预占库存不足，当前预占：%d，需要释放：%d",
                            inventory.getReservedQuantity(), request.getQuantity()));
                }

                Integer beforeReservedQuantity = inventory.getReservedQuantity();

                return inventoryRepository.releaseReservedStock(id, request.getQuantity())
                    .flatMap(updated -> {
                        if (updated > 0) {
                            // 重新查询更新后的库存
                            return inventoryRepository.findById(id)
                                .flatMap(updatedInventory -> {
                                    // 记录库存变动日志
                                    return inventoryLogService.logInventoryChange(
                                        updatedInventory,
                                        -request.getQuantity(),
                                        beforeReservedQuantity,
                                        updatedInventory.getReservedQuantity(),
                                        InventoryLog.ChangeType.RELEASE, // 释放
                                        request.getReason() != null ? request.getReason() : "释放预占库存",
                                        request.getBusinessType(),
                                        request.getBusinessId(),
                                        request.getBusinessNo()
                                    );
                                })
                                .replaceWith(true);
                        }
                        throw new BusinessException("RELEASE_STOCK_FAILED", "释放预占库存失败");
                    });
            });
    }

    /**
     * 确认出库（减少预占库存和实际库存）
     *
     * @param tenantId 租户ID
     * @param id 库存ID
     * @param request 操作请求
     * @return 操作结果
     */
    public Uni<Boolean> confirmOutbound(Long tenantId, Long id, InventoryOperationRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(inventory -> {
                // 检查库存和预占库存是否充足
                if (inventory.getQuantity() < request.getQuantity() ||
                    inventory.getReservedQuantity() < request.getQuantity()) {
                    return Uni.createFrom().failure(new RuntimeException("库存或预占库存不足"));
                }

                Integer beforeQuantity = inventory.getQuantity();

                return inventoryRepository.confirmOutbound(id, request.getQuantity())
                    .flatMap(updated -> {
                        if (updated > 0) {
                            // 重新查询更新后的库存
                            return inventoryRepository.findById(id)
                                .flatMap(updatedInventory -> {
                                    // 记录库存变动日志
                                    return inventoryLogService.logInventoryChange(
                                        updatedInventory,
                                        -request.getQuantity(),
                                        beforeQuantity,
                                        updatedInventory.getQuantity(),
                                        2, // 出库
                                        request.getReason(),
                                        request.getBusinessType(),
                                        request.getBusinessId(),
                                        request.getBusinessNo()
                                    );
                                })
                                .flatMap(v -> {
                                    // 检查是否需要生成预警
                                    return inventoryRepository.findById(id)
                                        .flatMap(this::checkAndCreateAlert);
                                })
                                .replaceWith(true);
                        }
                        return Uni.createFrom().item(false);
                    });
            });
    }

    /**
     * 更新平均成本
     */
    private void updateAverageCost(Inventory inventory, Integer newQuantity, BigDecimal newUnitPrice) {
        if (inventory.getAverageCost() == null) {
            inventory.setAverageCost(newUnitPrice);
        } else {
            // 加权平均成本计算
            BigDecimal totalValue = inventory.getAverageCost().multiply(BigDecimal.valueOf(inventory.getQuantity() - newQuantity));
            BigDecimal newValue = newUnitPrice.multiply(BigDecimal.valueOf(newQuantity));
            BigDecimal newAverageCost = totalValue.add(newValue).divide(BigDecimal.valueOf(inventory.getQuantity()), 2, BigDecimal.ROUND_HALF_UP);
            inventory.setAverageCost(newAverageCost);
        }
        inventory.calculateTotalValue();
    }

    /**
     * 检查并创建库存预警
     */
    private Uni<Void> checkAndCreateAlert(Inventory inventory) {
        if (inventory.isLowStock()) {
            return stockAlertService.createLowStockAlert(inventory).replaceWith((Void) null);
        }
        return Uni.createFrom().voidItem();
    }

    /**
     * 根据SKU ID查询库存
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @return 库存列表
     */
    public Uni<List<Inventory>> findBySkuId(Long tenantId, Long skuId) {
        return inventoryRepository.findBySkuId(tenantId, skuId);
    }

    /**
     * 根据商品ID查询库存
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByProductId(Long tenantId, Long productId) {
        return inventoryRepository.findByProductId(tenantId, productId);
    }

    /**
     * 查询库存不足商品
     *
     * @param tenantId 租户ID
     * @param page 页码
     * @param size 页大小
     * @return 库存列表
     */
    public Uni<List<Inventory>> findLowStockInventory(Long tenantId, int page, int size) {
        return inventoryRepository.findLowStockInventory(tenantId, page, size);
    }

    /**
     * 查询需要补货商品
     *
     * @param tenantId 租户ID
     * @param page 页码
     * @param size 页大小
     * @return 库存列表
     */
    public Uni<List<Inventory>> findNeedReorderInventory(Long tenantId, int page, int size) {
        return inventoryRepository.findNeedReorderInventory(tenantId, page, size);
    }

    /**
     * 获取库存统计信息
     *
     * @param tenantId 租户ID
     * @return 统计信息
     */
    public Uni<InventoryStatistics> getInventoryStatistics(Long tenantId) {
        return inventoryRepository.getInventoryStatistics(tenantId);
    }

    /**
     * 批量更新库存状态
     *
     * @param tenantId 租户ID
     * @param ids 库存ID列表
     * @param status 状态
     * @return 更新数量
     */
    public Uni<Integer> batchUpdateStatus(Long tenantId, List<Long> ids, Integer status) {
        return inventoryRepository.batchUpdateStatus(tenantId, ids, status);
    }

    /**
     * 库存盘点
     *
     * @param tenantId 租户ID
     * @param id 库存ID
     * @param request 操作请求
     * @return 操作结果
     */
    public Uni<Boolean> stocktaking(Long tenantId, Long id, InventoryOperationRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(inventory -> {
                Integer beforeQuantity = inventory.getQuantity();
                Integer actualQuantity = request.getQuantity();
                Integer difference = actualQuantity - beforeQuantity;

                // 更新库存数量
                inventory.setQuantity(actualQuantity);
                inventory.calculateAvailableQuantity();
                inventory.setLastStocktakingAt(java.time.LocalDateTime.now());

                return inventoryRepository.persist(inventory)
                    .flatMap(updatedInventory -> {
                        // 记录盘点日志
                        return inventoryLogService.logInventoryChange(
                            updatedInventory,
                            difference,
                            beforeQuantity,
                            actualQuantity,
                            4, // 盘点
                            request.getReason() != null ? request.getReason() : "库存盘点",
                            request.getBusinessType(),
                            request.getBusinessId(),
                            request.getBusinessNo()
                        );
                    })
                    .flatMap(v -> {
                        // 检查是否需要生成预警
                        return checkAndCreateAlert(inventory);
                    })
                    .replaceWith(true);
            });
    }

    /**
     * 根据条件查询库存
     *
     * @param tenantId 租户ID
     * @param request 查询请求
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByConditions(Long tenantId, InventoryQueryRequest request) {
        return inventoryRepository.findByConditions(tenantId, request);
    }

    // ========== BaseService 抽象方法实现 ==========

    @Override
    public Uni<Inventory> findByIdAndTenant(Long id, Long tenantId) {
        return inventoryRepository.findByIdAndTenant(id, tenantId);
    }

    @Override
    public Uni<List<Inventory>> findAllByTenant(Long tenantId) {
        return inventoryRepository.findAllByTenant(tenantId);
    }

    @Override
    public Uni<Inventory> create(Inventory entity) {
        return inventoryRepository.persist(entity);
    }

    @Override
    public Uni<Inventory> update(Inventory entity) {
        return inventoryRepository.persist(entity);
    }

    @Override
    public Uni<Boolean> delete(Long id, Long tenantId) {
        return inventoryRepository.deleteByIdAndTenant(id, tenantId)
            .map(deleted -> deleted > 0);
    }

    @Override
    public Uni<Long> count(Long tenantId) {
        return inventoryRepository.countByTenant(tenantId);
    }

    @Override
    public Uni<Boolean> exists(Long id, Long tenantId) {
        return findByIdAndTenant(id, tenantId)
            .map(inventory -> inventory != null);
    }

    @Override
    public Uni<Integer> batchDelete(List<Long> ids, Long tenantId) {
        return inventoryRepository.batchDeleteByTenant(ids, tenantId);
    }

    @Override
    protected String getEntityName() {
        return "库存";
    }

    protected InventoryRepository getRepository() {
        return inventoryRepository;
    }
}
