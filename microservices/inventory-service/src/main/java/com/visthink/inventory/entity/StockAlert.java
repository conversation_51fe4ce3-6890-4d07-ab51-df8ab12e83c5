package com.visthink.inventory.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;

/**
 * 库存预警实体
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "stock_alert")
@EqualsAndHashCode(callSuper = true)
public class StockAlert extends BaseEntity {

    /**
     * 库存ID
     */
    @Column(name = "inventory_id", nullable = false)
    private Long inventoryId;

    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;

    /**
     * 商品编码
     */
    @Column(name = "product_code", length = 100)
    private String productCode;

    /**
     * 商品名称
     */
    @Column(name = "product_name", length = 500)
    private String productName;

    /**
     * SKU ID
     */
    @Column(name = "sku_id", nullable = false)
    private Long skuId;

    /**
     * SKU编码
     */
    @Column(name = "sku_code", length = 100, nullable = false)
    private String skuCode;

    /**
     * SKU名称
     */
    @Column(name = "sku_name", length = 500)
    private String skuName;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @Column(name = "warehouse_code", length = 100)
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @Column(name = "warehouse_name", length = 200)
    private String warehouseName;

    /**
     * 预警类型：1-库存不足，2-库存过多，3-零库存，4-负库存
     */
    @Column(name = "alert_type", nullable = false)
    private Integer alertType;

    /**
     * 当前库存数量
     */
    @Column(name = "current_quantity", nullable = false)
    private Integer currentQuantity;

    /**
     * 可用库存数量
     */
    @Column(name = "available_quantity", nullable = false)
    private Integer availableQuantity;

    /**
     * 预警阈值
     */
    @Column(name = "alert_threshold")
    private Integer alertThreshold;

    /**
     * 预警级别：1-低，2-中，3-高，4-紧急
     */
    @Column(name = "alert_level", nullable = false)
    private Integer alertLevel;

    /**
     * 预警状态：1-待处理，2-处理中，3-已处理，4-已忽略
     */
    @Column(name = "alert_status", nullable = false)
    private Integer alertStatus = 1;

    /**
     * 预警消息
     */
    @Column(name = "alert_message", length = 1000)
    private String alertMessage;

    /**
     * 处理人ID
     */
    @Column(name = "handler_id")
    private Long handlerId;

    /**
     * 处理人姓名
     */
    @Column(name = "handler_name", length = 100)
    private String handlerName;

    /**
     * 处理时间
     */
    @Column(name = "handled_at")
    private java.time.LocalDateTime handledAt;

    /**
     * 处理备注
     */
    @Column(name = "handle_remark", length = 500)
    private String handleRemark;

    /**
     * 预警类型常量
     */
    public static class AlertType {
        public static final int LOW_STOCK = 1;     // 库存不足
        public static final int OVER_STOCK = 2;    // 库存过多
        public static final int ZERO_STOCK = 3;    // 零库存
        public static final int NEGATIVE_STOCK = 4; // 负库存
    }

    /**
     * 预警级别常量
     */
    public static class AlertLevel {
        public static final int LOW = 1;       // 低
        public static final int MEDIUM = 2;    // 中
        public static final int HIGH = 3;      // 高
        public static final int URGENT = 4;    // 紧急
    }

    /**
     * 预警状态常量
     */
    public static class AlertStatus {
        public static final int PENDING = 1;   // 待处理
        public static final int PROCESSING = 2; // 处理中
        public static final int HANDLED = 3;   // 已处理
        public static final int IGNORED = 4;   // 已忽略
    }

    /**
     * 获取预警类型名称
     */
    public String getAlertTypeName() {
        switch (alertType) {
            case AlertType.LOW_STOCK:
                return "库存不足";
            case AlertType.OVER_STOCK:
                return "库存过多";
            case AlertType.ZERO_STOCK:
                return "零库存";
            case AlertType.NEGATIVE_STOCK:
                return "负库存";
            default:
                return "未知";
        }
    }

    /**
     * 获取预警级别名称
     */
    public String getAlertLevelName() {
        switch (alertLevel) {
            case AlertLevel.LOW:
                return "低";
            case AlertLevel.MEDIUM:
                return "中";
            case AlertLevel.HIGH:
                return "高";
            case AlertLevel.URGENT:
                return "紧急";
            default:
                return "未知";
        }
    }

    /**
     * 获取预警状态名称
     */
    public String getAlertStatusName() {
        switch (alertStatus) {
            case AlertStatus.PENDING:
                return "待处理";
            case AlertStatus.PROCESSING:
                return "处理中";
            case AlertStatus.HANDLED:
                return "已处理";
            case AlertStatus.IGNORED:
                return "已忽略";
            default:
                return "未知";
        }
    }
}
