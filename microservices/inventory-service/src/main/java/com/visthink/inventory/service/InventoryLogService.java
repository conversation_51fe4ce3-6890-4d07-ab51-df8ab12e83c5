package com.visthink.inventory.service;

import com.visthink.common.base.BaseService;
import com.visthink.inventory.entity.Inventory;
import com.visthink.inventory.entity.InventoryLog;
import com.visthink.inventory.repository.InventoryLogRepository;
import com.visthink.inventory.dto.InventoryLogQueryRequest;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存变动日志服务类
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class InventoryLogService extends BaseService<InventoryLog, Long> {

    @Inject
    InventoryLogRepository inventoryLogRepository;

    /**
     * 记录库存变动日志
     *
     * @param inventory 库存记录
     * @param changeQuantity 变动数量
     * @param beforeQuantity 变动前数量
     * @param afterQuantity 变动后数量
     * @param changeType 变动类型
     * @param reason 变动原因
     * @param businessType 业务类型
     * @param businessId 业务单据ID
     * @param businessNo 业务单据编号
     * @return 日志记录
     */
    public Uni<InventoryLog> logInventoryChange(
            Inventory inventory,
            Integer changeQuantity,
            Integer beforeQuantity,
            Integer afterQuantity,
            Integer changeType,
            String reason,
            Integer businessType,
            Long businessId,
            String businessNo) {

        InventoryLog log = new InventoryLog();
        log.setTenantId(inventory.getTenantId());
        log.setInventoryId(inventory.id);
        log.setProductId(inventory.getProductId());
        log.setProductCode(inventory.getProductCode());
        log.setSkuId(inventory.getSkuId());
        log.setSkuCode(inventory.getSkuCode());
        log.setWarehouseId(inventory.getWarehouseId());
        log.setWarehouseCode(inventory.getWarehouseCode());
        log.setChangeType(changeType);
        log.setChangeQuantity(changeQuantity);
        log.setBeforeQuantity(beforeQuantity);
        log.setAfterQuantity(afterQuantity);
        log.setBeforeAvailableQuantity(inventory.getAvailableQuantity() - changeQuantity);
        log.setAfterAvailableQuantity(inventory.getAvailableQuantity());
        log.setBusinessType(businessType);
        log.setBusinessId(businessId);
        log.setBusinessNo(businessNo);
        log.setReason(reason);

        // 计算金额
        if (inventory.getAverageCost() != null) {
            log.setUnitPrice(inventory.getAverageCost());
            log.setTotalAmount(inventory.getAverageCost().multiply(BigDecimal.valueOf(Math.abs(changeQuantity))));
        }

        return inventoryLogRepository.persist(log);
    }

    /**
     * 查询库存变动日志
     *
     * @param tenantId 租户ID
     * @param request 查询请求
     * @return 日志列表
     */
    public Uni<List<InventoryLog>> queryInventoryLogs(Long tenantId, InventoryLogQueryRequest request) {
        return inventoryLogRepository.findByConditions(tenantId, request);
    }

    /**
     * 根据库存ID查询变动日志
     *
     * @param tenantId 租户ID
     * @param inventoryId 库存ID
     * @param page 页码
     * @param size 页大小
     * @return 日志列表
     */
    public Uni<List<InventoryLog>> findByInventoryId(Long tenantId, Long inventoryId, int page, int size) {
        return inventoryLogRepository.findByInventoryId(tenantId, inventoryId, page, size);
    }

    /**
     * 根据SKU ID查询变动日志
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @param page 页码
     * @param size 页大小
     * @return 日志列表
     */
    public Uni<List<InventoryLog>> findBySkuId(Long tenantId, Long skuId, int page, int size) {
        return inventoryLogRepository.findBySkuId(tenantId, skuId, page, size);
    }

    /**
     * 根据商品ID查询变动日志
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @param page 页码
     * @param size 页大小
     * @return 日志列表
     */
    public Uni<List<InventoryLog>> findByProductId(Long tenantId, Long productId, int page, int size) {
        return inventoryLogRepository.findByProductId(tenantId, productId, page, size);
    }

    /**
     * 根据业务单据查询变动日志
     *
     * @param tenantId 租户ID
     * @param businessType 业务类型
     * @param businessId 业务单据ID
     * @return 日志列表
     */
    public Uni<List<InventoryLog>> findByBusiness(Long tenantId, Integer businessType, Long businessId) {
        return inventoryLogRepository.findByBusiness(tenantId, businessType, businessId);
    }

    /**
     * 统计指定时间段内的库存变动
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public Uni<List<Object[]>> statisticsInventoryChanges(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return inventoryLogRepository.statisticsInventoryChanges(tenantId, startTime, endTime);
    }

    /**
     * 统计指定商品的库存变动
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    public Uni<List<Object[]>> statisticsProductInventoryChanges(Long tenantId, Long productId, LocalDateTime startTime, LocalDateTime endTime) {
        return inventoryLogRepository.statisticsProductInventoryChanges(tenantId, productId, startTime, endTime);
    }

    /**
     * 获取库存变动趋势
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @param days 天数
     * @return 趋势数据
     */
    public Uni<List<Object[]>> getInventoryTrend(Long tenantId, Long skuId, int days) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);
        return inventoryLogRepository.getInventoryTrend(tenantId, skuId, startTime, endTime);
    }

    /**
     * 获取热门变动商品
     *
     * @param tenantId 租户ID
     * @param days 天数
     * @param limit 限制数量
     * @return 热门商品列表
     */
    public Uni<List<Object[]>> getTopChangedProducts(Long tenantId, int days, int limit) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        return inventoryLogRepository.getTopChangedProducts(tenantId, startTime, limit);
    }

    /**
     * 获取库存变动汇总
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 汇总数据
     */
    public Uni<Object[]> getInventoryChangeSummary(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return inventoryLogRepository.getInventoryChangeSummary(tenantId, startTime, endTime);
    }

    /**
     * 删除过期的库存变动日志
     *
     * @param tenantId 租户ID
     * @param days 保留天数
     * @return 删除数量
     */
    public Uni<Long> deleteExpiredLogs(Long tenantId, int days) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        return inventoryLogRepository.deleteExpiredLogs(tenantId, expireTime);
    }

    /**
     * 批量记录库存变动日志
     *
     * @param logs 日志列表
     * @return 保存结果
     */
    public Uni<List<InventoryLog>> batchLogInventoryChanges(List<InventoryLog> logs) {
        return inventoryLogRepository.persist(logs)
            .map(ignored -> logs); // persist返回Void，我们返回原始的logs列表
    }

    /**
     * 获取库存变动类型统计
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 类型统计
     */
    public Uni<List<Object[]>> getChangeTypeStatistics(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return inventoryLogRepository.getChangeTypeStatistics(tenantId, startTime, endTime);
    }

    /**
     * 获取仓库库存变动统计
     *
     * @param tenantId 租户ID
     * @param warehouseId 仓库ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 仓库统计
     */
    public Uni<List<Object[]>> getWarehouseChangeStatistics(Long tenantId, Long warehouseId, LocalDateTime startTime, LocalDateTime endTime) {
        return inventoryLogRepository.getWarehouseChangeStatistics(tenantId, warehouseId, startTime, endTime);
    }

    /**
     * 验证库存变动日志的完整性
     *
     * @param tenantId 租户ID
     * @param inventoryId 库存ID
     * @return 验证结果
     */
    public Uni<Boolean> validateInventoryLogIntegrity(Long tenantId, Long inventoryId) {
        return inventoryLogRepository.validateLogIntegrity(tenantId, inventoryId);
    }

    // ========== BaseService 抽象方法实现 ==========

    @Override
    public Uni<InventoryLog> findByIdAndTenant(Long id, Long tenantId) {
        return inventoryLogRepository.findByIdAndTenant(id, tenantId);
    }

    @Override
    public Uni<List<InventoryLog>> findAllByTenant(Long tenantId) {
        return inventoryLogRepository.findAllByTenant(tenantId);
    }

    @Override
    public Uni<InventoryLog> create(InventoryLog entity) {
        return inventoryLogRepository.persist(entity);
    }

    @Override
    public Uni<InventoryLog> update(InventoryLog entity) {
        return inventoryLogRepository.persist(entity);
    }

    @Override
    public Uni<Boolean> delete(Long id, Long tenantId) {
        return inventoryLogRepository.deleteByIdAndTenant(id, tenantId)
            .map(deleted -> deleted > 0);
    }

    @Override
    public Uni<Long> count(Long tenantId) {
        return inventoryLogRepository.countByTenant(tenantId);
    }

    @Override
    public Uni<Boolean> exists(Long id, Long tenantId) {
        return findByIdAndTenant(id, tenantId)
            .map(log -> log != null);
    }

    @Override
    public Uni<Integer> batchDelete(List<Long> ids, Long tenantId) {
        return inventoryLogRepository.batchDeleteByTenant(ids, tenantId);
    }

    @Override
    protected String getEntityName() {
        return "库存变动日志";
    }

    protected InventoryLogRepository getRepository() {
        return inventoryLogRepository;
    }
}
