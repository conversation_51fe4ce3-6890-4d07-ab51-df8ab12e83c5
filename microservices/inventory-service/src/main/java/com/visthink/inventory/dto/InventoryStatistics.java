package com.visthink.inventory.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 库存统计DTO
 * 
 * <AUTHOR>
 */
@Data
public class InventoryStatistics {

    /**
     * 总库存数量
     */
    private Long totalQuantity;

    /**
     * 总可用库存数量
     */
    private Long totalAvailableQuantity;

    /**
     * 总预占库存数量
     */
    private Long totalReservedQuantity;

    /**
     * 总在途库存数量
     */
    private Long totalInTransitQuantity;

    /**
     * 总库存价值
     */
    private BigDecimal totalValue;

    /**
     * 商品种类数量
     */
    private Long productCount;

    /**
     * SKU数量
     */
    private Long skuCount;

    /**
     * 库存不足商品数量
     */
    private Long lowStockCount;

    /**
     * 零库存商品数量
     */
    private Long zeroStockCount;

    /**
     * 需要补货商品数量
     */
    private Long reorderCount;

    /**
     * 冻结库存数量
     */
    private Long frozenStockCount;

    /**
     * 盘点中库存数量
     */
    private Long stocktakingCount;

    /**
     * 平均库存周转天数
     */
    private Double averageTurnoverDays;

    /**
     * 库存周转率
     */
    private Double turnoverRate;

    /**
     * 库存准确率
     */
    private Double accuracyRate;

    /**
     * 最后更新时间
     */
    private java.time.LocalDateTime lastUpdateTime;
}
