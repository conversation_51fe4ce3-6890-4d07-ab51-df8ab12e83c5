package com.visthink.inventory.resource;

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.inventory.entity.StockAlert;
import com.visthink.inventory.service.StockAlertService;
import com.visthink.inventory.dto.StockAlertQueryRequest;
import com.visthink.inventory.dto.StockAlertHandleRequest;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import java.util.List;

/**
 * 库存预警REST接口 - 重构版本
 *
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给StockAlertService处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 */
@Path("/api/stock-alerts")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "库存预警", description = "库存预警相关接口")
public class StockAlertResource extends SimpleController {

    @Inject
    StockAlertService stockAlertService;

    /**
     * 根据ID获取库存预警详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "获取库存预警详情", description = "根据ID获取库存预警详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<StockAlert>> getStockAlert(@PathParam("id") Long id) {
        logOperation("获取库存预警详情", "预警ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> stockAlertService.findByIdAndTenant(id, tenantId))
                .map(alert -> {
                    if (alert != null) {
                        logOperation("获取库存预警详情", "查询成功");
                        return success(alert);
                    } else {
                        return ApiResponse.<StockAlert>notFound("库存预警不存在");
                    }
                })
            .onFailure().recoverWithItem(this::handleError);
    }



    /**
     * 处理库存预警
     */
    @PUT
    @Path("/{id}/handle")
    @Operation(summary = "处理库存预警", description = "处理指定的库存预警")
    @APIResponse(responseCode = "200", description = "处理成功")
    public Uni<ApiResponse<StockAlert>> handleAlert(@PathParam("id") Long id, @Valid StockAlertHandleRequest request) {
        logOperation("处理库存预警", "预警ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> stockAlertService.handleAlert(tenantId, id, request))
                .map(alert -> {
                    logOperation("处理库存预警", "处理成功");
                    return success("处理库存预警成功", alert);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "处理库存预警"));
    }

    /**
     * 删除库存预警
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除库存预警", description = "删除指定的库存预警")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Boolean>> deleteStockAlert(@PathParam("id") Long id) {
        logOperation("删除库存预警", "预警ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> stockAlertService.delete(id, tenantId))
                .map(result -> {
                    if (result) {
                        logOperation("删除库存预警", "删除成功");
                        return success("删除库存预警成功", true);
                    } else {
                        return ApiResponse.<Boolean>notFound("库存预警不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "删除库存预警"));
    }

    /**
     * 查询待处理预警
     */
    @GET
    @Path("/pending")
    @Operation(summary = "查询待处理预警", description = "查询待处理的库存预警")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<StockAlert>>> getPendingAlerts(
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        logOperation("查询待处理预警", String.format("页码: %d, 页大小: %d", page, size));

        return getCurrentTenantId()
                .flatMap(tenantId -> stockAlertService.findPendingAlerts(tenantId, page, size))
                .map(alerts -> {
                    logOperation("查询待处理预警", "查询成功，共" + alerts.size() + "条记录");
                    return success(alerts);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "查询待处理预警"));
    }

    /**
     * 查询高优先级预警
     */
    @GET
    @Path("/high-priority")
    @Operation(summary = "查询高优先级预警", description = "查询高优先级的库存预警")
    public Uni<ApiResponse<List<StockAlert>>> getHighPriorityAlerts(
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        logOperation("查询高优先级预警", String.format("页码: %d, 页大小: %d", page, size));

        return getCurrentTenantId()
                .flatMap(tenantId -> stockAlertService.findHighPriorityAlerts(tenantId, page, size))
                .map(alerts -> {
                    logOperation("查询高优先级预警", "查询成功，共" + alerts.size() + "条记录");
                    return success(alerts);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "查询高优先级预警"));
    }

    /**
     * 统计预警数量按类型分组
     */
    @GET
    @Path("/statistics/by-type")
    @Operation(summary = "按类型统计预警数量", description = "统计预警数量按类型分组")
    public Uni<ApiResponse<List<Object[]>>> countAlertsByType() {
        logOperation("按类型统计预警数量", "开始处理统计请求");

        return getCurrentTenantId()
                .flatMap(stockAlertService::countAlertsByType)
                .map(statistics -> {
                    logOperation("按类型统计预警数量", "统计成功");
                    return success(statistics);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "按类型统计预警数量"));
    }

    /**
     * 统计库存预警数量
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计库存预警数量", description = "统计库存预警总数量")
    @APIResponse(responseCode = "200", description = "统计成功")
    public Uni<ApiResponse<Long>> count() {
        logOperation("统计库存预警数量", "开始处理统计请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> stockAlertService.count(tenantId))
                .map(count -> {
                    logOperation("统计库存预警数量", "统计成功: " + count);
                    return success(count);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "统计库存预警数量"));
    }



    /**
     * 分页查询库存预警
     */
    @GET
    @Operation(summary = "分页查询库存预警", description = "根据条件分页查询库存预警")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<StockAlert>>> queryStockAlerts(@BeanParam @Valid StockAlertQueryRequest request) {
        logOperation("分页查询库存预警", "开始处理查询请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> stockAlertService.queryStockAlerts(tenantId, request))
                .map(alerts -> {
                    logOperation("分页查询库存预警", "查询成功，共" + alerts.size() + "条记录");
                    return success(alerts);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "分页查询库存预警"));
    }
}
