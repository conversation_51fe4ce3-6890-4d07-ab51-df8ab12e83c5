package com.visthink.inventory.dto;

import jakarta.ws.rs.QueryParam;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 库存查询请求DTO
 *
 * <AUTHOR>
 */
@Data
public class InventoryQueryRequest {

    /**
     * 商品ID
     */
    @QueryParam("productId")
    private Long productId;

    /**
     * 商品编码
     */
    @QueryParam("productCode")
    private String productCode;

    /**
     * SKU ID
     */
     @QueryParam("skuId")
    private Long skuId;

    /**
     * SKU编码
     */
    @QueryParam("skuCode")
    private String skuCode;

    /**
     * 仓库ID
     */
    @QueryParam("warehouseId")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @QueryParam("warehouseCode")
    private String warehouseCode;

    /**
     * 库存状态：1-正常，2-冻结，3-盘点中
     */
    @QueryParam("status")
    private Integer status;

    /**
     * 是否库存不足
     */
    @QueryParam("isLowStock")
    private Boolean isLowStock;

    /**
     * 是否需要补货
     */
    @QueryParam("needReorder")
    private Boolean needReorder;

    /**
     * 最小库存数量
     */
    @QueryParam("minQuantity")
    private Integer minQuantity;

    /**
     * 最大库存数量
     */
    @QueryParam("maxQuantity")
    private Integer maxQuantity;

    /**
     * 创建时间开始
     */
    @QueryParam("createTimeStart")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @QueryParam("createTimeEnd")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @QueryParam("updateTimeStart")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @QueryParam("updateTimeEnd")
    private LocalDateTime updateTimeEnd;

    /**
     * 排序字段
     */
    @QueryParam("sortField")
    private String sortField = "id";

    /**
     * 排序方向：asc/desc
     */
    @QueryParam("sortDirection")
    private String sortDirection = "desc";

    /**
     * 页码
     */
    @QueryParam("page")
    private Integer page = 1;

    /**
     * 页大小
     */
    @QueryParam("size")
    private Integer size = 20;
}
