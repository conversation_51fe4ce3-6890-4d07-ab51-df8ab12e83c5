package com.visthink.inventory.repository;

import com.visthink.common.repository.BaseRepository;
import com.visthink.inventory.entity.StockAlert;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存预警数据访问层
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class StockAlertRepository implements BaseRepository<StockAlert> {

    /**
     * 根据租户ID分页查询预警
     */
    public Uni<List<StockAlert>> findByTenantId(Long tenantId, Page page) {
        return find("tenantId = ?1 order by createdAt desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 根据库存ID查询预警
     */
    public Uni<List<StockAlert>> findByInventoryId(Long inventoryId) {
        return find("inventoryId = ?1 order by createdAt desc", inventoryId).list();
    }

    /**
     * 根据租户ID和库存ID查询预警
     */
    public Uni<List<StockAlert>> findByTenantAndInventoryId(Long tenantId, Long inventoryId) {
        return find("tenantId = ?1 and inventoryId = ?2 order by createdAt desc", tenantId, inventoryId).list();
    }

    /**
     * 根据SKU ID查询预警
     */
    public Uni<List<StockAlert>> findBySkuId(Long skuId) {
        return find("skuId = ?1 order by createdAt desc", skuId).list();
    }

    /**
     * 根据租户ID和SKU ID查询预警
     */
    public Uni<List<StockAlert>> findByTenantAndSkuId(Long tenantId, Long skuId) {
        return find("tenantId = ?1 and skuId = ?2 order by createdAt desc", tenantId, skuId).list();
    }

    /**
     * 根据商品ID查询预警
     */
    public Uni<List<StockAlert>> findByProductId(Long productId, Page page) {
        return find("productId = ?1 order by createdAt desc", productId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和商品ID查询预警
     */
    public Uni<List<StockAlert>> findByTenantAndProductId(Long tenantId, Long productId, Page page) {
        return find("tenantId = ?1 and productId = ?2 order by createdAt desc", tenantId, productId)
                .page(page)
                .list();
    }

    /**
     * 根据预警类型查询
     */
    public Uni<List<StockAlert>> findByTenantAndAlertType(Long tenantId, Integer alertType, Page page) {
        return find("tenantId = ?1 and alertType = ?2 order by createdAt desc", tenantId, alertType)
                .page(page)
                .list();
    }

    /**
     * 根据预警级别查询
     */
    public Uni<List<StockAlert>> findByTenantAndAlertLevel(Long tenantId, Integer alertLevel, Page page) {
        return find("tenantId = ?1 and alertLevel = ?2 order by createdAt desc", tenantId, alertLevel)
                .page(page)
                .list();
    }

    /**
     * 根据预警状态查询
     */
    public Uni<List<StockAlert>> findByTenantAndAlertStatus(Long tenantId, Integer alertStatus, Page page) {
        return find("tenantId = ?1 and alertStatus = ?2 order by createdAt desc", tenantId, alertStatus)
                .page(page)
                .list();
    }

    /**
     * 查询待处理的预警
     */
    public Uni<List<StockAlert>> findPendingAlerts(Long tenantId, Page page) {
        return find("tenantId = ?1 and alertStatus = 1 order by alertLevel desc, createdAt desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 查询紧急预警
     */
    public Uni<List<StockAlert>> findUrgentAlerts(Long tenantId) {
        return find("tenantId = ?1 and alertLevel = 4 and alertStatus in (1, 2) order by createdAt desc", tenantId).list();
    }

    /**
     * 根据处理人查询预警
     */
    public Uni<List<StockAlert>> findByHandler(Long handlerId, Page page) {
        return find("handlerId = ?1 order by handledAt desc", handlerId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和处理人查询预警
     */
    public Uni<List<StockAlert>> findByTenantAndHandler(Long tenantId, Long handlerId, Page page) {
        return find("tenantId = ?1 and handlerId = ?2 order by handledAt desc", tenantId, handlerId)
                .page(page)
                .list();
    }

    /**
     * 根据时间范围查询预警
     */
    public Uni<List<StockAlert>> findByDateRange(Long tenantId, LocalDateTime startTime, LocalDateTime endTime, Page page) {
        return find("tenantId = ?1 and createdAt >= ?2 and createdAt <= ?3 order by createdAt desc",
                tenantId, startTime, endTime)
                .page(page)
                .list();
    }

    /**
     * 根据仓库查询预警
     */
    public Uni<List<StockAlert>> findByWarehouse(Long tenantId, Long warehouseId, Page page) {
        return find("tenantId = ?1 and warehouseId = ?2 order by createdAt desc", tenantId, warehouseId)
                .page(page)
                .list();
    }

    /**
     * 统计租户预警总数
     */
    public Uni<Long> countByTenant(Long tenantId) {
        return count("tenantId = ?1", tenantId);
    }

    /**
     * 统计指定状态的预警数量
     */
    public Uni<Long> countByTenantAndStatus(Long tenantId, Integer alertStatus) {
        return count("tenantId = ?1 and alertStatus = ?2", tenantId, alertStatus);
    }

    /**
     * 统计指定类型的预警数量
     */
    public Uni<Long> countByTenantAndType(Long tenantId, Integer alertType) {
        return count("tenantId = ?1 and alertType = ?2", tenantId, alertType);
    }

    /**
     * 统计指定级别的预警数量
     */
    public Uni<Long> countByTenantAndLevel(Long tenantId, Integer alertLevel) {
        return count("tenantId = ?1 and alertLevel = ?2", tenantId, alertLevel);
    }

    /**
     * 统计待处理预警数量
     */
    public Uni<Long> countPendingAlerts(Long tenantId) {
        return count("tenantId = ?1 and alertStatus = 1", tenantId);
    }

    /**
     * 统计紧急预警数量
     */
    public Uni<Long> countUrgentAlerts(Long tenantId) {
        return count("tenantId = ?1 and alertLevel = 4 and alertStatus in (1, 2)", tenantId);
    }

    /**
     * 批量更新预警状态
     */
    public Uni<Integer> updateStatusByIds(List<Long> alertIds, Integer alertStatus, Long handlerId, String handlerName) {
        return update("alertStatus = ?1, handlerId = ?2, handlerName = ?3, handledAt = current_timestamp where id in ?4",
                alertStatus, handlerId, handlerName, alertIds);
    }

    /**
     * 检查是否存在相同的预警
     */
    public Uni<StockAlert> findExistingAlert(Long tenantId, Long inventoryId, Integer alertType, Integer alertStatus) {
        return find("tenantId = ?1 and inventoryId = ?2 and alertType = ?3 and alertStatus = ?4",
                tenantId, inventoryId, alertType, alertStatus).firstResult();
    }

    /**
     * 搜索预警
     */
    public Uni<List<StockAlert>> searchAlerts(Long tenantId, String keyword, Page page) {
        String query = "tenantId = ?1 and (productCode like ?2 or productName like ?2 or skuCode like ?2 or skuName like ?2 or alertMessage like ?2) order by createdAt desc";
        String searchKeyword = "%" + keyword + "%";
        return find(query, tenantId, searchKeyword)
                .page(page)
                .list();
    }

    /**
     * 删除已处理的旧预警
     */
    public Uni<Long> deleteOldHandledAlerts(LocalDateTime expireTime) {
        return delete("alertStatus = 3 and handledAt < ?1", expireTime);
    }

    /**
     * 根据条件查询库存预警
     */
    public Uni<List<StockAlert>> findByConditions(Long tenantId, com.visthink.inventory.dto.StockAlertQueryRequest request) {
        StringBuilder query = new StringBuilder("tenantId = ?1");
        java.util.List<Object> params = new java.util.ArrayList<>();
        params.add(tenantId);

        if (request.getInventoryId() != null) {
            query.append(" and inventoryId = ?").append(params.size() + 1);
            params.add(request.getInventoryId());
        }
        if (request.getProductId() != null) {
            query.append(" and productId = ?").append(params.size() + 1);
            params.add(request.getProductId());
        }
        if (request.getSkuId() != null) {
            query.append(" and skuId = ?").append(params.size() + 1);
            params.add(request.getSkuId());
        }
        if (request.getAlertType() != null) {
            query.append(" and alertType = ?").append(params.size() + 1);
            params.add(request.getAlertType());
        }
        if (request.getAlertLevel() != null) {
            query.append(" and alertLevel = ?").append(params.size() + 1);
            params.add(request.getAlertLevel());
        }
        if (request.getAlertStatus() != null) {
            query.append(" and alertStatus = ?").append(params.size() + 1);
            params.add(request.getAlertStatus());
        }
        if (request.getAlertTimeStart() != null) {
            query.append(" and createdAt >= ?").append(params.size() + 1);
            params.add(request.getAlertTimeStart());
        }
        if (request.getAlertTimeEnd() != null) {
            query.append(" and createdAt <= ?").append(params.size() + 1);
            params.add(request.getAlertTimeEnd());
        }

        query.append(" order by ").append(request.getSortField()).append(" ").append(request.getSortDirection());

        return find(query.toString(), params.toArray())
                .page(request.getPage() - 1, request.getSize())
                .list();
    }

    /**
     * 分页查询待处理预警
     */
    public Uni<List<StockAlert>> findPendingAlerts(Long tenantId, int page, int size) {
        return find("tenantId = ?1 and alertStatus = 1 order by alertLevel desc, createdAt desc", tenantId)
                .page(page - 1, size)
                .list();
    }

    /**
     * 分页查询高优先级预警
     */
    public Uni<List<StockAlert>> findHighPriorityAlerts(Long tenantId, int page, int size) {
        return find("tenantId = ?1 and alertLevel >= 3 and alertStatus in (1, 2) order by alertLevel desc, createdAt desc", tenantId)
                .page(page - 1, size)
                .list();
    }

    /**
     * 查找待处理的预警（用于检查重复）
     */
    public Uni<StockAlert> findPendingAlert(Long tenantId, Long inventoryId, Integer alertType) {
        return find("tenantId = ?1 and inventoryId = ?2 and alertType = ?3 and alertStatus = 1",
                tenantId, inventoryId, alertType).firstResult();
    }

    /**
     * 统计预警数量按类型分组
     */
    public Uni<List<Object[]>> countAlertsByType(Long tenantId) {
        return find("select s.alertType, count(s) from StockAlert s " +
                "where s.tenantId = ?1 group by s.alertType order by s.alertType", tenantId)
            .project(Object[].class)
            .list();
    }

    /**
     * 统计预警数量按级别分组
     */
    public Uni<List<Object[]>> countAlertsByLevel(Long tenantId) {
        return find("select s.alertLevel, count(s) from StockAlert s " +
                "where s.tenantId = ?1 group by s.alertLevel order by s.alertLevel", tenantId)
            .project(Object[].class)
            .list();
    }

    /**
     * 统计预警数量按状态分组
     */
    public Uni<List<Object[]>> countAlertsByStatus(Long tenantId) {
        return find("select s.alertStatus, count(s) from StockAlert s " +
                "where s.tenantId = ?1 group by s.alertStatus order by s.alertStatus", tenantId)
            .project(Object[].class)
            .list();
    }

    /**
     * 批量更新预警状态
     */
    public Uni<Integer> batchUpdateStatus(Long tenantId, List<Long> alertIds, Integer alertStatus,
            Long handlerId, String handlerName, String handleRemark) {
        if (alertIds == null || alertIds.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("alertStatus = ?1, handlerId = ?2, handlerName = ?3, handledAt = current_timestamp, handleRemark = ?4 " +
                "where tenantId = ?5 and id in ?6",
                alertStatus, handlerId, handlerName, handleRemark, tenantId, alertIds);
    }

    /**
     * 删除已处理的预警
     */
    public Uni<Long> deleteHandledAlerts(Long tenantId, java.time.LocalDateTime expireTime) {
        return delete("tenantId = ?1 and alertStatus = 3 and handledAt < ?2", tenantId, expireTime);
    }

    // ========== BaseService 需要的基础方法 ==========

    /**
     * 根据ID和租户ID查询库存预警
     */
    public Uni<StockAlert> findByIdAndTenant(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2", id, tenantId).firstResult();
    }

    /**
     * 查询租户下所有库存预警
     */
    public Uni<List<StockAlert>> findAllByTenant(Long tenantId) {
        return find("tenantId = ?1 order by id desc", tenantId).list();
    }

    /**
     * 根据ID和租户ID删除库存预警
     */
    public Uni<Integer> deleteByIdAndTenant(Long id, Long tenantId) {
        return delete("id = ?1 and tenantId = ?2", id, tenantId)
            .map(Long::intValue);
    }



    /**
     * 批量删除租户下的库存预警
     */
    public Uni<Integer> batchDeleteByTenant(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return delete("id in ?1 and tenantId = ?2", ids, tenantId)
            .map(Long::intValue);
    }
}
