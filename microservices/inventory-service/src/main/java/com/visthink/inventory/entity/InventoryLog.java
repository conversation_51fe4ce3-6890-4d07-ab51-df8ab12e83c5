package com.visthink.inventory.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 库存变动日志实体
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "inventory_log")
@EqualsAndHashCode(callSuper = true)
public class InventoryLog extends BaseEntity {

    /**
     * 库存ID
     */
    @Column(name = "inventory_id", nullable = false)
    private Long inventoryId;

    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;

    /**
     * 商品编码
     */
    @Column(name = "product_code", length = 100)
    private String productCode;

    /**
     * SKU ID
     */
    @Column(name = "sku_id", nullable = false)
    private Long skuId;

    /**
     * SKU编码
     */
    @Column(name = "sku_code", length = 100, nullable = false)
    private String skuCode;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @Column(name = "warehouse_code", length = 100)
    private String warehouseCode;

    /**
     * 变动类型：1-入库，2-出库，3-调拨，4-盘点，5-预占，6-释放，7-损耗，8-退货
     */
    @Column(name = "change_type", nullable = false)
    private Integer changeType;

    /**
     * 变动数量（正数为增加，负数为减少）
     */
    @Column(name = "change_quantity", nullable = false)
    private Integer changeQuantity;

    /**
     * 变动前库存数量
     */
    @Column(name = "before_quantity", nullable = false)
    private Integer beforeQuantity;

    /**
     * 变动后库存数量
     */
    @Column(name = "after_quantity", nullable = false)
    private Integer afterQuantity;

    /**
     * 变动前可用库存数量
     */
    @Column(name = "before_available_quantity", nullable = false)
    private Integer beforeAvailableQuantity;

    /**
     * 变动后可用库存数量
     */
    @Column(name = "after_available_quantity", nullable = false)
    private Integer afterAvailableQuantity;

    /**
     * 单价
     */
    @Column(name = "unit_price", precision = 10, scale = 2)
    private BigDecimal unitPrice;

    /**
     * 总价值
     */
    @Column(name = "total_amount", precision = 12, scale = 2)
    private BigDecimal totalAmount;

    /**
     * 关联业务单据类型：1-采购单，2-销售单，3-调拨单，4-盘点单，5-其他
     */
    @Column(name = "business_type")
    private Integer businessType;

    /**
     * 关联业务单据ID
     */
    @Column(name = "business_id")
    private Long businessId;

    /**
     * 关联业务单据编号
     */
    @Column(name = "business_no", length = 100)
    private String businessNo;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name", length = 100)
    private String operatorName;

    /**
     * 变动原因
     */
    @Column(name = "reason", length = 500)
    private String reason;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 变动类型常量
     */
    public static class ChangeType {
        public static final int IN_STOCK = 1;      // 入库
        public static final int OUT_STOCK = 2;     // 出库
        public static final int TRANSFER = 3;      // 调拨
        public static final int STOCKTAKING = 4;   // 盘点
        public static final int RESERVE = 5;       // 预占
        public static final int RELEASE = 6;       // 释放
        public static final int LOSS = 7;          // 损耗
        public static final int RETURN = 8;        // 退货
    }

    /**
     * 业务类型常量
     */
    public static class BusinessType {
        public static final int PURCHASE = 1;      // 采购单
        public static final int SALE = 2;          // 销售单
        public static final int TRANSFER = 3;      // 调拨单
        public static final int STOCKTAKING = 4;   // 盘点单
        public static final int OTHER = 5;         // 其他
    }

    /**
     * 获取变动类型名称
     */
    public String getChangeTypeName() {
        switch (changeType) {
            case ChangeType.IN_STOCK:
                return "入库";
            case ChangeType.OUT_STOCK:
                return "出库";
            case ChangeType.TRANSFER:
                return "调拨";
            case ChangeType.STOCKTAKING:
                return "盘点";
            case ChangeType.RESERVE:
                return "预占";
            case ChangeType.RELEASE:
                return "释放";
            case ChangeType.LOSS:
                return "损耗";
            case ChangeType.RETURN:
                return "退货";
            default:
                return "未知";
        }
    }

    /**
     * 获取业务类型名称
     */
    public String getBusinessTypeName() {
        if (businessType == null) {
            return "未知";
        }
        switch (businessType) {
            case BusinessType.PURCHASE:
                return "采购单";
            case BusinessType.SALE:
                return "销售单";
            case BusinessType.TRANSFER:
                return "调拨单";
            case BusinessType.STOCKTAKING:
                return "盘点单";
            case BusinessType.OTHER:
                return "其他";
            default:
                return "未知";
        }
    }
}
