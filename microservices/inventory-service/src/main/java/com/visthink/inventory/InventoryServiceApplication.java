package com.visthink.inventory;

import io.quarkus.runtime.Quarkus;
import io.quarkus.runtime.QuarkusApplication;
import io.quarkus.runtime.annotations.QuarkusMain;
import lombok.extern.slf4j.Slf4j;

/**
 * 库存服务应用程序入口
 * 
 * <AUTHOR>
 */
@Slf4j
@QuarkusMain
public class InventoryServiceApplication implements QuarkusApplication {

    @Override
    public int run(String... args) throws Exception {
        log.info("🚀 库存服务启动中...");
        log.info("📦 服务名称: Inventory Service");
        log.info("🌐 服务端口: 8083");
        log.info("📚 API文档: http://localhost:8083/q/swagger-ui");
        log.info("❤️ 健康检查: http://localhost:8083/health");
        log.info("📊 指标监控: http://localhost:8083/metrics");
        
        Quarkus.waitForExit();
        return 0;
    }

    public static void main(String[] args) {
        Quarkus.run(InventoryServiceApplication.class, args);
    }
}
