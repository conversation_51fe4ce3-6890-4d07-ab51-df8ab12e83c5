package com.visthink.inventory.repository;

import com.visthink.inventory.entity.Inventory;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 库存数据访问层
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class InventoryRepository implements PanacheRepository<Inventory> {

    /**
     * 根据租户ID分页查询库存
     */
    public Uni<List<Inventory>> findByTenantId(Long tenantId, Page page) {
        return find("tenantId = ?1 order by id desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 根据SKU ID查询库存
     */
    public Uni<Inventory> findBySkuId(Long skuId) {
        return find("skuId", skuId).firstResult();
    }

    /**
     * 根据租户ID和SKU ID查询库存列表
     */
    public Uni<List<Inventory>> findBySkuId(Long tenantId, Long skuId) {
        return find("tenantId = ?1 and skuId = ?2", tenantId, skuId).list();
    }

    /**
     * 根据租户ID和SKU ID查询库存
     */
    public Uni<Inventory> findByTenantAndSkuId(Long tenantId, Long skuId) {
        return find("tenantId = ?1 and skuId = ?2", tenantId, skuId).firstResult();
    }

    /**
     * 根据SKU编码查询库存
     */
    public Uni<Inventory> findBySkuCode(String skuCode) {
        return find("skuCode", skuCode).firstResult();
    }

    /**
     * 根据租户ID和SKU编码查询库存
     */
    public Uni<Inventory> findByTenantAndSkuCode(Long tenantId, String skuCode) {
        return find("tenantId = ?1 and skuCode = ?2", tenantId, skuCode).firstResult();
    }

    /**
     * 根据商品ID查询库存列表
     */
    public Uni<List<Inventory>> findByProductId(Long productId) {
        return find("productId = ?1 order by skuCode", productId).list();
    }

    /**
     * 根据租户ID和商品ID查询库存列表
     */
    public Uni<List<Inventory>> findByProductId(Long tenantId, Long productId) {
        return find("tenantId = ?1 and productId = ?2 order by skuCode", tenantId, productId).list();
    }

    /**
     * 根据租户ID和商品ID查询库存列表
     */
    public Uni<List<Inventory>> findByTenantAndProductId(Long tenantId, Long productId) {
        return find("tenantId = ?1 and productId = ?2 order by skuCode", tenantId, productId).list();
    }

    /**
     * 根据仓库ID查询库存列表
     */
    public Uni<List<Inventory>> findByWarehouseId(Long warehouseId, Page page) {
        return find("warehouseId = ?1 order by id desc", warehouseId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和仓库ID查询库存列表
     */
    public Uni<List<Inventory>> findByTenantAndWarehouseId(Long tenantId, Long warehouseId, Page page) {
        return find("tenantId = ?1 and warehouseId = ?2 order by id desc", tenantId, warehouseId)
                .page(page)
                .list();
    }

    /**
     * 查询库存不足的商品
     */
    public Uni<List<Inventory>> findLowStockInventories(Long tenantId) {
        return find("tenantId = ?1 and availableQuantity <= warningStock and status = 1", tenantId).list();
    }

    /**
     * 分页查询库存不足的商品
     */
    public Uni<List<Inventory>> findLowStockInventory(Long tenantId, int page, int size) {
        return find("tenantId = ?1 and availableQuantity <= warningStock and status = 1 order by availableQuantity asc", tenantId)
                .page(page - 1, size)
                .list();
    }

    /**
     * 查询零库存的商品
     */
    public Uni<List<Inventory>> findZeroStockInventories(Long tenantId) {
        return find("tenantId = ?1 and quantity = 0 and status = 1", tenantId).list();
    }

    /**
     * 查询负库存的商品
     */
    public Uni<List<Inventory>> findNegativeStockInventories(Long tenantId) {
        return find("tenantId = ?1 and quantity < 0 and status = 1", tenantId).list();
    }

    /**
     * 查询需要补货的商品
     */
    public Uni<List<Inventory>> findReorderInventories(Long tenantId) {
        return find("tenantId = ?1 and reorderPoint is not null and availableQuantity <= reorderPoint and status = 1", tenantId).list();
    }

    /**
     * 分页查询需要补货的商品
     */
    public Uni<List<Inventory>> findNeedReorderInventory(Long tenantId, int page, int size) {
        return find("tenantId = ?1 and reorderPoint is not null and availableQuantity <= reorderPoint and status = 1 order by availableQuantity asc", tenantId)
                .page(page - 1, size)
                .list();
    }

    /**
     * 查询库存过多的商品
     */
    public Uni<List<Inventory>> findOverStockInventories(Long tenantId) {
        return find("tenantId = ?1 and maxStock is not null and quantity > maxStock and status = 1", tenantId).list();
    }

    /**
     * 根据状态查询库存
     */
    public Uni<List<Inventory>> findByTenantAndStatus(Long tenantId, Integer status, Page page) {
        return find("tenantId = ?1 and status = ?2 order by id desc", tenantId, status)
                .page(page)
                .list();
    }

    /**
     * 统计租户库存总数
     */
    public Uni<Long> countByTenant(Long tenantId) {
        return count("tenantId = ?1", tenantId);
    }

    /**
     * 统计指定状态的库存数量
     */
    public Uni<Long> countByTenantAndStatus(Long tenantId, Integer status) {
        return count("tenantId = ?1 and status = ?2", tenantId, status);
    }

    /**
     * 根据商品ID列表查询库存
     */
    public Uni<List<Inventory>> findByProductIds(List<Long> productIds) {
        return find("productId in ?1", productIds).list();
    }

    /**
     * 根据SKU ID列表查询库存
     */
    public Uni<List<Inventory>> findBySkuIds(List<Long> skuIds) {
        return find("skuId in ?1", skuIds).list();
    }

    /**
     * 批量更新库存状态
     */
    public Uni<Integer> updateStatusByIds(List<Long> inventoryIds, Integer status) {
        return update("status = ?1 where id in ?2", status, inventoryIds);
    }

    /**
     * 更新库存数量
     */
    public Uni<Integer> updateQuantity(Long inventoryId, Integer quantity, Integer availableQuantity) {
        return update("quantity = ?1, availableQuantity = ?2, updatedAt = current_timestamp where id = ?3", 
                quantity, availableQuantity, inventoryId);
    }

    /**
     * 增加库存数量
     */
    public Uni<Integer> increaseQuantity(Long inventoryId, Integer quantity) {
        return update("quantity = quantity + ?1, availableQuantity = availableQuantity + ?1, updatedAt = current_timestamp where id = ?2", 
                quantity, inventoryId);
    }

    /**
     * 减少库存数量
     */
    public Uni<Integer> decreaseQuantity(Long inventoryId, Integer quantity) {
        return update("quantity = quantity - ?1, availableQuantity = availableQuantity - ?1, updatedAt = current_timestamp where id = ?2 and quantity >= ?1", 
                quantity, inventoryId);
    }

    /**
     * 预占库存
     */
    public Uni<Integer> reserveStock(Long inventoryId, Integer quantity) {
        return update("reservedQuantity = reservedQuantity + ?1, availableQuantity = availableQuantity - ?1, updatedAt = current_timestamp where id = ?2 and availableQuantity >= ?1", 
                quantity, inventoryId);
    }

    /**
     * 释放预占库存
     */
    public Uni<Integer> releaseReservedStock(Long inventoryId, Integer quantity) {
        return update("reservedQuantity = reservedQuantity - ?1, availableQuantity = availableQuantity + ?1, updatedAt = current_timestamp where id = ?2 and reservedQuantity >= ?1", 
                quantity, inventoryId);
    }

    /**
     * 确认出库（减少预占库存和实际库存）
     */
    public Uni<Integer> confirmOutbound(Long inventoryId, Integer quantity) {
        return update("quantity = quantity - ?1, reservedQuantity = reservedQuantity - ?1, updatedAt = current_timestamp where id = ?2 and quantity >= ?1 and reservedQuantity >= ?1", 
                quantity, inventoryId);
    }

    /**
     * 搜索库存
     */
    public Uni<List<Inventory>> searchInventories(Long tenantId, String keyword, Page page) {
        String query = "tenantId = ?1 and (productCode like ?2 or skuCode like ?2) order by id desc";
        String searchKeyword = "%" + keyword + "%";
        return find(query, tenantId, searchKeyword)
                .page(page)
                .list();
    }

    /**
     * 批量更新库存状态
     */
    public Uni<Integer> batchUpdateStatus(Long tenantId, List<Long> inventoryIds, Integer status) {
        if (inventoryIds == null || inventoryIds.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return update("status = ?1, updatedAt = current_timestamp where tenantId = ?2 and id in ?3",
                     status, tenantId, inventoryIds);
    }

    /**
     * 根据条件查询库存
     */
    public Uni<List<Inventory>> findByConditions(Long tenantId, com.visthink.inventory.dto.InventoryQueryRequest request) {
        StringBuilder query = new StringBuilder("tenantId = ?1");
        java.util.List<Object> params = new java.util.ArrayList<>();
        params.add(tenantId);

        if (request.getProductId() != null) {
            query.append(" and productId = ?").append(params.size() + 1);
            params.add(request.getProductId());
        }
        if (request.getProductCode() != null && !request.getProductCode().trim().isEmpty()) {
            query.append(" and productCode like ?").append(params.size() + 1);
            params.add("%" + request.getProductCode().trim() + "%");
        }
        if (request.getSkuId() != null) {
            query.append(" and skuId = ?").append(params.size() + 1);
            params.add(request.getSkuId());
        }
        if (request.getSkuCode() != null && !request.getSkuCode().trim().isEmpty()) {
            query.append(" and skuCode like ?").append(params.size() + 1);
            params.add("%" + request.getSkuCode().trim() + "%");
        }
        if (request.getWarehouseId() != null) {
            query.append(" and warehouseId = ?").append(params.size() + 1);
            params.add(request.getWarehouseId());
        }
        if (request.getStatus() != null) {
            query.append(" and status = ?").append(params.size() + 1);
            params.add(request.getStatus());
        }
        if (request.getMinQuantity() != null) {
            query.append(" and quantity >= ?").append(params.size() + 1);
            params.add(request.getMinQuantity());
        }
        if (request.getMaxQuantity() != null) {
            query.append(" and quantity <= ?").append(params.size() + 1);
            params.add(request.getMaxQuantity());
        }
        if (Boolean.TRUE.equals(request.getIsLowStock())) {
            query.append(" and availableQuantity <= warningStock");
        }
        if (Boolean.TRUE.equals(request.getNeedReorder())) {
            query.append(" and reorderPoint is not null and availableQuantity <= reorderPoint");
        }

        query.append(" order by ").append(request.getSortField()).append(" ").append(request.getSortDirection());

        return find(query.toString(), params.toArray())
                .page(request.getPage() - 1, request.getSize())
                .list();
    }

    /**
     * 获取库存统计信息
     */
    public Uni<com.visthink.inventory.dto.InventoryStatistics> getInventoryStatistics(Long tenantId) {
        return find("select " +
                "sum(i.quantity), " +
                "sum(i.availableQuantity), " +
                "sum(i.reservedQuantity), " +
                "sum(i.inTransitQuantity), " +
                "sum(i.totalValue), " +
                "count(distinct i.productId), " +
                "count(i.id), " +
                "sum(case when i.availableQuantity <= i.warningStock then 1 else 0 end), " +
                "sum(case when i.quantity = 0 then 1 else 0 end), " +
                "sum(case when i.reorderPoint is not null and i.availableQuantity <= i.reorderPoint then 1 else 0 end), " +
                "sum(case when i.status = 2 then 1 else 0 end), " +
                "sum(case when i.status = 3 then 1 else 0 end) " +
                "from Inventory i where i.tenantId = ?1", tenantId)
            .project(Object[].class)
            .firstResult()
            .map(result -> {
                Object[] row = result;
                com.visthink.inventory.dto.InventoryStatistics stats = new com.visthink.inventory.dto.InventoryStatistics();
                stats.setTotalQuantity(row[0] != null ? ((Number) row[0]).longValue() : 0L);
                stats.setTotalAvailableQuantity(row[1] != null ? ((Number) row[1]).longValue() : 0L);
                stats.setTotalReservedQuantity(row[2] != null ? ((Number) row[2]).longValue() : 0L);
                stats.setTotalInTransitQuantity(row[3] != null ? ((Number) row[3]).longValue() : 0L);
                stats.setTotalValue(row[4] != null ? (java.math.BigDecimal) row[4] : java.math.BigDecimal.ZERO);
                stats.setProductCount(row[5] != null ? ((Number) row[5]).longValue() : 0L);
                stats.setSkuCount(row[6] != null ? ((Number) row[6]).longValue() : 0L);
                stats.setLowStockCount(row[7] != null ? ((Number) row[7]).longValue() : 0L);
                stats.setZeroStockCount(row[8] != null ? ((Number) row[8]).longValue() : 0L);
                stats.setReorderCount(row[9] != null ? ((Number) row[9]).longValue() : 0L);
                stats.setFrozenStockCount(row[10] != null ? ((Number) row[10]).longValue() : 0L);
                stats.setStocktakingCount(row[11] != null ? ((Number) row[11]).longValue() : 0L);
                stats.setLastUpdateTime(java.time.LocalDateTime.now());
                return stats;
            });
    }

    // ========== BaseService 需要的基础方法 ==========

    /**
     * 根据ID和租户ID查询库存
     */
    public Uni<Inventory> findByIdAndTenant(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2", id, tenantId).firstResult();
    }

    /**
     * 查询租户下所有库存
     */
    public Uni<List<Inventory>> findAllByTenant(Long tenantId) {
        return find("tenantId = ?1 order by id desc", tenantId).list();
    }

    /**
     * 根据ID和租户ID删除库存
     */
    public Uni<Integer> deleteByIdAndTenant(Long id, Long tenantId) {
        return delete("id = ?1 and tenantId = ?2", id, tenantId)
            .map(Long::intValue);
    }

    /**
     * 批量删除租户下的库存
     */
    public Uni<Integer> batchDeleteByTenant(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return delete("id in ?1 and tenantId = ?2", ids, tenantId)
            .map(Long::intValue);
    }
}
