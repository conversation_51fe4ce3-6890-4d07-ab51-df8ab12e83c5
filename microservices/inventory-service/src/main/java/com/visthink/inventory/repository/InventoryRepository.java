package com.visthink.inventory.repository;

import com.visthink.inventory.entity.Inventory;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

/**
 * 库存数据访问层
 *
 * 使用ReactiveBaseRepository提供安全的Hibernate Reactive会话管理
 * 解决JdbcValuesSourceProcessingState错误
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class InventoryRepository extends ReactiveBaseRepository {

    /**
     * 根据租户ID分页查询库存
     *
     * @param tenantId 租户ID
     * @param page 分页参数
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByTenantId(Long tenantId, Page page) {
        String query = "FROM Inventory WHERE tenantId = ?1 ORDER BY id DESC";
        return executeQuery(session -> {
            return session.createQuery(query, Inventory.class)
                    .setParameter(1, tenantId)
                    .setFirstResult(page.index * page.size)
                    .setMaxResults(page.size)
                    .getResultList();
        });
    }

    /**
     * 根据SKU ID查询库存
     *
     * @param skuId SKU ID
     * @return 库存对象
     */
    public Uni<Inventory> findBySkuId(Long skuId) {
        return findSingle("FROM Inventory WHERE skuId = ?1", Inventory.class, skuId);
    }

    /**
     * 根据租户ID和SKU ID查询库存列表
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @return 库存列表
     */
    public Uni<List<Inventory>> findBySkuId(Long tenantId, Long skuId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND skuId = ?2", Inventory.class, tenantId, skuId);
    }

    /**
     * 根据租户ID和SKU ID查询库存
     *
     * @param tenantId 租户ID
     * @param skuId SKU ID
     * @return 库存对象
     */
    public Uni<Inventory> findByTenantAndSkuId(Long tenantId, Long skuId) {
        return findSingle("FROM Inventory WHERE tenantId = ?1 AND skuId = ?2", Inventory.class, tenantId, skuId);
    }

    /**
     * 根据SKU编码查询库存
     *
     * @param skuCode SKU编码
     * @return 库存对象
     */
    public Uni<Inventory> findBySkuCode(String skuCode) {
        return findSingle("FROM Inventory WHERE skuCode = ?1", Inventory.class, skuCode);
    }

    /**
     * 根据租户ID和SKU编码查询库存
     *
     * @param tenantId 租户ID
     * @param skuCode SKU编码
     * @return 库存对象
     */
    public Uni<Inventory> findByTenantAndSkuCode(Long tenantId, String skuCode) {
        return findSingle("FROM Inventory WHERE tenantId = ?1 AND skuCode = ?2", Inventory.class, tenantId, skuCode);
    }

    /**
     * 根据商品ID查询库存列表
     *
     * @param productId 商品ID
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByProductId(Long productId) {
        return findList("FROM Inventory WHERE productId = ?1 ORDER BY skuCode", Inventory.class, productId);
    }

    /**
     * 根据租户ID和商品ID查询库存列表
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByProductId(Long tenantId, Long productId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND productId = ?2 ORDER BY skuCode", Inventory.class, tenantId, productId);
    }

    /**
     * 根据租户ID和商品ID查询库存列表
     *
     * @param tenantId 租户ID
     * @param productId 商品ID
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByTenantAndProductId(Long tenantId, Long productId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND productId = ?2 ORDER BY skuCode", Inventory.class, tenantId, productId);
    }

    /**
     * 根据仓库ID查询库存列表
     *
     * @param warehouseId 仓库ID
     * @param page 分页参数
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByWarehouseId(Long warehouseId, Page page) {
        String query = "FROM Inventory WHERE warehouseId = ?1 ORDER BY id DESC";
        return executeQuery(session -> {
            return session.createQuery(query, Inventory.class)
                    .setParameter(1, warehouseId)
                    .setFirstResult(page.index * page.size)
                    .setMaxResults(page.size)
                    .getResultList();
        });
    }

    /**
     * 根据租户ID和仓库ID查询库存列表
     *
     * @param tenantId 租户ID
     * @param warehouseId 仓库ID
     * @param page 分页参数
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByTenantAndWarehouseId(Long tenantId, Long warehouseId, Page page) {
        String query = "FROM Inventory WHERE tenantId = ?1 AND warehouseId = ?2 ORDER BY id DESC";
        return executeQuery(session -> {
            return session.createQuery(query, Inventory.class)
                    .setParameter(1, tenantId)
                    .setParameter(2, warehouseId)
                    .setFirstResult(page.index * page.size)
                    .setMaxResults(page.size)
                    .getResultList();
        });
    }

    /**
     * 查询库存不足的商品
     *
     * @param tenantId 租户ID
     * @return 库存不足的商品列表
     */
    public Uni<List<Inventory>> findLowStockInventories(Long tenantId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND availableQuantity <= warningStock AND status = 1",
                       Inventory.class, tenantId);
    }

    /**
     * 分页查询库存不足的商品
     *
     * @param tenantId 租户ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 库存不足的商品列表
     */
    public Uni<List<Inventory>> findLowStockInventory(Long tenantId, int page, int size) {
        String query = "FROM Inventory WHERE tenantId = ?1 AND availableQuantity <= warningStock AND status = 1 ORDER BY availableQuantity ASC";
        return executeQuery(session -> {
            return session.createQuery(query, Inventory.class)
                    .setParameter(1, tenantId)
                    .setFirstResult((page - 1) * size)
                    .setMaxResults(size)
                    .getResultList();
        });
    }

    /**
     * 查询零库存的商品
     *
     * @param tenantId 租户ID
     * @return 零库存商品列表
     */
    public Uni<List<Inventory>> findZeroStockInventories(Long tenantId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND quantity = 0 AND status = 1",
                       Inventory.class, tenantId);
    }

    /**
     * 查询负库存的商品
     *
     * @param tenantId 租户ID
     * @return 负库存商品列表
     */
    public Uni<List<Inventory>> findNegativeStockInventories(Long tenantId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND quantity < 0 AND status = 1",
                       Inventory.class, tenantId);
    }

    /**
     * 查询需要补货的商品
     *
     * @param tenantId 租户ID
     * @return 需要补货的商品列表
     */
    public Uni<List<Inventory>> findReorderInventories(Long tenantId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND reorderPoint IS NOT NULL AND availableQuantity <= reorderPoint AND status = 1",
                       Inventory.class, tenantId);
    }

    /**
     * 分页查询需要补货的商品
     *
     * @param tenantId 租户ID
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 需要补货的商品列表
     */
    public Uni<List<Inventory>> findNeedReorderInventory(Long tenantId, int page, int size) {
        String query = "FROM Inventory WHERE tenantId = ?1 AND reorderPoint IS NOT NULL AND availableQuantity <= reorderPoint AND status = 1 ORDER BY availableQuantity ASC";
        return executeQuery(session -> {
            return session.createQuery(query, Inventory.class)
                    .setParameter(1, tenantId)
                    .setFirstResult((page - 1) * size)
                    .setMaxResults(size)
                    .getResultList();
        });
    }

    /**
     * 查询库存过多的商品
     *
     * @param tenantId 租户ID
     * @return 库存过多的商品列表
     */
    public Uni<List<Inventory>> findOverStockInventories(Long tenantId) {
        return findList("FROM Inventory WHERE tenantId = ?1 AND maxStock IS NOT NULL AND quantity > maxStock AND status = 1",
                       Inventory.class, tenantId);
    }

    /**
     * 根据状态查询库存
     *
     * @param tenantId 租户ID
     * @param status 状态
     * @param page 分页参数
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByTenantAndStatus(Long tenantId, Integer status, Page page) {
        String query = "FROM Inventory WHERE tenantId = ?1 AND status = ?2 ORDER BY id DESC";
        return executeQuery(session -> {
            return session.createQuery(query, Inventory.class)
                    .setParameter(1, tenantId)
                    .setParameter(2, status)
                    .setFirstResult(page.index * page.size)
                    .setMaxResults(page.size)
                    .getResultList();
        });
    }

    /**
     * 统计租户库存总数
     *
     * @param tenantId 租户ID
     * @return 库存总数
     */
    public Uni<Long> countByTenant(Long tenantId) {
        return count("SELECT COUNT(*) FROM Inventory WHERE tenantId = ?1", tenantId);
    }

    /**
     * 统计指定状态的库存数量
     *
     * @param tenantId 租户ID
     * @param status 状态
     * @return 库存数量
     */
    public Uni<Long> countByTenantAndStatus(Long tenantId, Integer status) {
        return count("SELECT COUNT(*) FROM Inventory WHERE tenantId = ?1 AND status = ?2", tenantId, status);
    }

    /**
     * 根据商品ID列表查询库存
     *
     * @param productIds 商品ID列表
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByProductIds(List<Long> productIds) {
        return executeQuery(session -> {
            return session.createQuery("FROM Inventory WHERE productId IN (:productIds)", Inventory.class)
                    .setParameter("productIds", productIds)
                    .getResultList();
        });
    }

    /**
     * 根据SKU ID列表查询库存
     *
     * @param skuIds SKU ID列表
     * @return 库存列表
     */
    public Uni<List<Inventory>> findBySkuIds(List<Long> skuIds) {
        return executeQuery(session -> {
            return session.createQuery("FROM Inventory WHERE skuId IN (:skuIds)", Inventory.class)
                    .setParameter("skuIds", skuIds)
                    .getResultList();
        });
    }

    /**
     * 批量更新库存状态
     *
     * @param inventoryIds 库存ID列表
     * @param status 新状态
     * @return 更新的记录数
     */
    public Uni<Integer> updateStatusByIds(List<Long> inventoryIds, Integer status) {
        return executeUpdate("UPDATE Inventory SET status = ?1 WHERE id IN (:inventoryIds)", status, inventoryIds);
    }

    /**
     * 更新库存数量
     *
     * @param inventoryId 库存ID
     * @param quantity 总数量
     * @param availableQuantity 可用数量
     * @return 更新的记录数
     */
    public Uni<Integer> updateQuantity(Long inventoryId, Integer quantity, Integer availableQuantity) {
        return executeUpdate("UPDATE Inventory SET quantity = ?1, availableQuantity = ?2, updatedAt = CURRENT_TIMESTAMP WHERE id = ?3",
                quantity, availableQuantity, inventoryId);
    }

    /**
     * 增加库存数量
     *
     * @param inventoryId 库存ID
     * @param quantity 增加的数量
     * @return 更新的记录数
     */
    public Uni<Integer> increaseQuantity(Long inventoryId, Integer quantity) {
        return executeUpdate("UPDATE Inventory SET quantity = quantity + ?1, availableQuantity = availableQuantity + ?1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?2",
                quantity, inventoryId);
    }

    /**
     * 减少库存数量
     *
     * @param inventoryId 库存ID
     * @param quantity 减少的数量
     * @return 更新的记录数
     */
    public Uni<Integer> decreaseQuantity(Long inventoryId, Integer quantity) {
        return executeUpdate("UPDATE Inventory SET quantity = quantity - ?1, availableQuantity = availableQuantity - ?1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?2 AND quantity >= ?1",
                quantity, inventoryId);
    }

    /**
     * 预占库存
     *
     * @param inventoryId 库存ID
     * @param quantity 预占数量
     * @return 更新的记录数
     */
    public Uni<Integer> reserveStock(Long inventoryId, Integer quantity) {
        return executeUpdate("UPDATE Inventory SET reservedQuantity = reservedQuantity + ?1, availableQuantity = availableQuantity - ?1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?2 AND availableQuantity >= ?1",
                quantity, inventoryId);
    }

    /**
     * 释放预占库存
     *
     * @param inventoryId 库存ID
     * @param quantity 释放数量
     * @return 更新的记录数
     */
    public Uni<Integer> releaseReservedStock(Long inventoryId, Integer quantity) {
        return executeUpdate("UPDATE Inventory SET reservedQuantity = reservedQuantity - ?1, availableQuantity = availableQuantity + ?1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?2 AND reservedQuantity >= ?1",
                quantity, inventoryId);
    }

    /**
     * 确认出库（减少预占库存和实际库存）
     *
     * @param inventoryId 库存ID
     * @param quantity 出库数量
     * @return 更新的记录数
     */
    public Uni<Integer> confirmOutbound(Long inventoryId, Integer quantity) {
        return executeUpdate("UPDATE Inventory SET quantity = quantity - ?1, reservedQuantity = reservedQuantity - ?1, updatedAt = CURRENT_TIMESTAMP WHERE id = ?2 AND quantity >= ?1 AND reservedQuantity >= ?1",
                quantity, inventoryId);
    }

    /**
     * 搜索库存
     *
     * @param tenantId 租户ID
     * @param keyword 搜索关键词
     * @param page 分页参数
     * @return 库存列表
     */
    public Uni<List<Inventory>> searchInventories(Long tenantId, String keyword, Page page) {
        String query = "FROM Inventory WHERE tenantId = ?1 AND (productCode LIKE ?2 OR skuCode LIKE ?2) ORDER BY id DESC";
        String searchKeyword = "%" + keyword + "%";
        return executeQuery(session -> {
            return session.createQuery(query, Inventory.class)
                    .setParameter(1, tenantId)
                    .setParameter(2, searchKeyword)
                    .setFirstResult(page.index * page.size)
                    .setMaxResults(page.size)
                    .getResultList();
        });
    }

    /**
     * 批量更新库存状态
     *
     * @param tenantId 租户ID
     * @param inventoryIds 库存ID列表
     * @param status 新状态
     * @return 更新的记录数
     */
    public Uni<Integer> batchUpdateStatus(Long tenantId, List<Long> inventoryIds, Integer status) {
        if (inventoryIds == null || inventoryIds.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return executeUpdate("UPDATE Inventory SET status = ?1, updatedAt = CURRENT_TIMESTAMP WHERE tenantId = ?2 AND id IN (:inventoryIds)",
                     status, tenantId, inventoryIds);
    }

    /**
     * 根据条件查询库存
     *
     * @param tenantId 租户ID
     * @param request 查询请求
     * @return 库存列表
     */
    public Uni<List<Inventory>> findByConditions(Long tenantId, com.visthink.inventory.dto.InventoryQueryRequest request) {
        StringBuilder query = new StringBuilder("FROM Inventory WHERE tenantId = :tenantId");

        return executeQuery(session -> {
            var queryObj = session.createQuery(query.toString(), Inventory.class);
            queryObj.setParameter("tenantId", tenantId);

            // 动态添加查询条件
            if (request.getProductId() != null) {
                query.append(" AND productId = :productId");
                queryObj = session.createQuery(query.toString(), Inventory.class);
                queryObj.setParameter("tenantId", tenantId);
                queryObj.setParameter("productId", request.getProductId());
            }
            if (request.getProductCode() != null && !request.getProductCode().trim().isEmpty()) {
                query.append(" AND productCode LIKE :productCode");
                queryObj = session.createQuery(query.toString(), Inventory.class);
                queryObj.setParameter("tenantId", tenantId);
                if (request.getProductId() != null) {
                    queryObj.setParameter("productId", request.getProductId());
                }
                queryObj.setParameter("productCode", "%" + request.getProductCode().trim() + "%");
            }
            if (request.getSkuId() != null) {
                query.append(" AND skuId = :skuId");
                queryObj = session.createQuery(query.toString(), Inventory.class);
                queryObj.setParameter("tenantId", tenantId);
                if (request.getProductId() != null) {
                    queryObj.setParameter("productId", request.getProductId());
                }
                if (request.getProductCode() != null && !request.getProductCode().trim().isEmpty()) {
                    queryObj.setParameter("productCode", "%" + request.getProductCode().trim() + "%");
                }
                queryObj.setParameter("skuId", request.getSkuId());
            }
            if (request.getSkuCode() != null && !request.getSkuCode().trim().isEmpty()) {
                query.append(" AND skuCode LIKE :skuCode");
                queryObj = session.createQuery(query.toString(), Inventory.class);
                queryObj.setParameter("tenantId", tenantId);
                // 重新设置所有之前的参数
                if (request.getProductId() != null) {
                    queryObj.setParameter("productId", request.getProductId());
                }
                if (request.getProductCode() != null && !request.getProductCode().trim().isEmpty()) {
                    queryObj.setParameter("productCode", "%" + request.getProductCode().trim() + "%");
                }
                if (request.getSkuId() != null) {
                    queryObj.setParameter("skuId", request.getSkuId());
                }
                queryObj.setParameter("skuCode", "%" + request.getSkuCode().trim() + "%");
            }

            // 添加排序
            query.append(" ORDER BY ").append(request.getSortField()).append(" ").append(request.getSortDirection());

            // 重新创建最终查询
            var finalQuery = session.createQuery(query.toString(), Inventory.class);
            finalQuery.setParameter("tenantId", tenantId);

            // 设置所有参数
            if (request.getProductId() != null) {
                finalQuery.setParameter("productId", request.getProductId());
            }
            if (request.getProductCode() != null && !request.getProductCode().trim().isEmpty()) {
                finalQuery.setParameter("productCode", "%" + request.getProductCode().trim() + "%");
            }
            if (request.getSkuId() != null) {
                finalQuery.setParameter("skuId", request.getSkuId());
            }
            if (request.getSkuCode() != null && !request.getSkuCode().trim().isEmpty()) {
                finalQuery.setParameter("skuCode", "%" + request.getSkuCode().trim() + "%");
            }

            return finalQuery
                    .setFirstResult((request.getPage() - 1) * request.getSize())
                    .setMaxResults(request.getSize())
                    .getResultList();
        });
    }

    /**
     * 获取库存统计信息
     *
     * @param tenantId 租户ID
     * @return 库存统计信息
     */
    public Uni<com.visthink.inventory.dto.InventoryStatistics> getInventoryStatistics(Long tenantId) {
        String query = "SELECT " +
                "SUM(i.quantity), " +
                "SUM(i.availableQuantity), " +
                "SUM(i.reservedQuantity), " +
                "SUM(i.inTransitQuantity), " +
                "SUM(i.totalValue), " +
                "COUNT(DISTINCT i.productId), " +
                "COUNT(i.id), " +
                "SUM(CASE WHEN i.availableQuantity <= i.warningStock THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN i.quantity = 0 THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN i.reorderPoint IS NOT NULL AND i.availableQuantity <= i.reorderPoint THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN i.status = 2 THEN 1 ELSE 0 END), " +
                "SUM(CASE WHEN i.status = 3 THEN 1 ELSE 0 END) " +
                "FROM Inventory i WHERE i.tenantId = ?1";

        return executeQuery(session -> {
            return session.createQuery(query, Object[].class)
                    .setParameter(1, tenantId)
                    .getSingleResult()
                    .map(row -> {
                        com.visthink.inventory.dto.InventoryStatistics stats = new com.visthink.inventory.dto.InventoryStatistics();
                        stats.setTotalQuantity(row[0] != null ? ((Number) row[0]).longValue() : 0L);
                        stats.setTotalAvailableQuantity(row[1] != null ? ((Number) row[1]).longValue() : 0L);
                        stats.setTotalReservedQuantity(row[2] != null ? ((Number) row[2]).longValue() : 0L);
                        stats.setTotalInTransitQuantity(row[3] != null ? ((Number) row[3]).longValue() : 0L);
                        stats.setTotalValue(row[4] != null ? (java.math.BigDecimal) row[4] : java.math.BigDecimal.ZERO);
                        stats.setProductCount(row[5] != null ? ((Number) row[5]).longValue() : 0L);
                        stats.setSkuCount(row[6] != null ? ((Number) row[6]).longValue() : 0L);
                        stats.setLowStockCount(row[7] != null ? ((Number) row[7]).longValue() : 0L);
                        stats.setZeroStockCount(row[8] != null ? ((Number) row[8]).longValue() : 0L);
                        stats.setReorderCount(row[9] != null ? ((Number) row[9]).longValue() : 0L);
                        stats.setFrozenStockCount(row[10] != null ? ((Number) row[10]).longValue() : 0L);
                        stats.setStocktakingCount(row[11] != null ? ((Number) row[11]).longValue() : 0L);
                        stats.setLastUpdateTime(java.time.LocalDateTime.now());
                        return stats;
                    });
        });
    }

    // ========== BaseService 需要的基础方法 ==========

    /**
     * 根据ID和租户ID查询库存
     */
    public Uni<Inventory> findByIdAndTenant(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2", id, tenantId).firstResult();
    }

    /**
     * 查询租户下所有库存
     */
    public Uni<List<Inventory>> findAllByTenant(Long tenantId) {
        return find("tenantId = ?1 order by id desc", tenantId).list();
    }

    /**
     * 根据ID和租户ID删除库存
     */
    public Uni<Integer> deleteByIdAndTenant(Long id, Long tenantId) {
        return delete("id = ?1 and tenantId = ?2", id, tenantId)
            .map(Long::intValue);
    }

    /**
     * 批量删除租户下的库存
     */
    public Uni<Integer> batchDeleteByTenant(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return delete("id in ?1 and tenantId = ?2", ids, tenantId)
            .map(Long::intValue);
    }
}
