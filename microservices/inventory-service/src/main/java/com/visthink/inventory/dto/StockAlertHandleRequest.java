package com.visthink.inventory.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;

/**
 * 库存预警处理请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class StockAlertHandleRequest {

    /**
     * 预警状态：2-处理中，3-已处理，4-已忽略
     */
    @NotNull(message = "预警状态不能为空")
    private Integer alertStatus;

    /**
     * 处理人ID
     */
    private Long handlerId;

    /**
     * 处理人姓名
     */
    private String handlerName;

    /**
     * 处理备注
     */
    private String handleRemark;
}
