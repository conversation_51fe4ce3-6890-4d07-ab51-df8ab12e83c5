package com.visthink.inventory.resource;

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.inventory.dto.InventoryCreateRequest;
import com.visthink.inventory.dto.InventoryUpdateRequest;
import com.visthink.inventory.dto.InventoryQueryRequest;
import com.visthink.inventory.entity.Inventory;
import com.visthink.inventory.service.InventoryService;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

/**
 * 库存管理REST接口 - 重构版本
 *
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给InventoryService处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 */
@Path("/api/inventory")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "库存管理", description = "库存管理相关接口")
public class InventoryResourceRefactored extends SimpleController {

    @Inject
    InventoryService inventoryService;

    /**
     * 创建库存记录
     *
     * @param request 创建请求
     * @return 创建结果
     */
    @POST
    @Operation(summary = "创建库存", description = "创建新的库存记录")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<Inventory>> create(@Valid InventoryCreateRequest request) {
        logOperation("创建库存", "开始处理创建请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("创建库存", tenantId, "租户验证通过");
                    return inventoryService.createInventory(tenantId, request);
                })
                .map(inventory -> {
                    logOperation("创建库存", "创建成功，库存ID: " + inventory.id);
                    return success("创建库存成功", inventory);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "创建库存"));
    }

    /**
     * 根据ID查询库存
     *
     * @param id 库存ID
     * @return 查询结果
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "查询库存", description = "根据ID查询库存详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<Inventory>> findById(@PathParam("id") Long id) {
        logOperation("查询库存", "ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findByIdAndTenant(id, tenantId))
                .map(inventory -> {
                    if (inventory != null) {
                        logOperation("查询库存", "查询成功");
                        return success(inventory);
                    } else {
                        return ApiResponse.<Inventory>notFound("库存不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "查询库存"));
    }

    /**
     * 查询所有库存
     *
     * @return 查询结果
     */
    @GET
    @Operation(summary = "查询所有库存", description = "查询所有库存列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<Inventory>>> findAll() {
        logOperation("查询所有库存", "开始处理查询请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findAllByTenant(tenantId))
                .map(inventories -> {
                    logOperation("查询所有库存", "查询成功，共" + inventories.size() + "条记录");
                    return success(inventories);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "查询库存列表"));
    }

    /**
     * 分页查询库存
     *
     * @param request 查询请求（包含分页参数）
     * @return 分页结果
     */
    @GET
    @Path("/page")
    @Operation(summary = "分页查询库存", description = "根据条件分页查询库存列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<Inventory>>> findByPage(@BeanParam InventoryQueryRequest request) {

        logOperation("分页查询库存", String.format("页码: %d, 页大小: %d", request.getPage(), request.getSize()));

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findByConditions(tenantId, request))
                .map(inventories -> {
                    logOperation("分页查询库存",
                        String.format("查询成功，共%d条记录", inventories.size()));
                    return success(inventories);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "分页查询库存"));
    }

    /**
     * 更新库存
     *
     * @param id 库存ID
     * @param request 更新请求
     * @return 更新结果
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新库存", description = "根据ID更新库存")
    @APIResponse(responseCode = "200", description = "更新成功")
    public Uni<ApiResponse<Inventory>> update(@PathParam("id") Long id,
                                             @Valid InventoryUpdateRequest request) {
        logOperation("更新库存", "ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findByIdAndTenant(id, tenantId))
                .flatMap(inventory -> {
                    if (inventory == null) {
                        return Uni.createFrom().item(ApiResponse.<Inventory>notFound("库存不存在"));
                    }

                    // 更新库存字段
                    if (request.getSafetyStock() != null) {
                        inventory.setSafetyStock(request.getSafetyStock());
                    }
                    if (request.getWarningStock() != null) {
                        inventory.setWarningStock(request.getWarningStock());
                    }
                    if (request.getMaxStock() != null) {
                        inventory.setMaxStock(request.getMaxStock());
                    }
                    if (request.getMinStock() != null) {
                        inventory.setMinStock(request.getMinStock());
                    }
                    if (request.getReorderPoint() != null) {
                        inventory.setReorderPoint(request.getReorderPoint());
                    }
                    if (request.getReorderQuantity() != null) {
                        inventory.setReorderQuantity(request.getReorderQuantity());
                    }
                    if (request.getRemark() != null) {
                        inventory.setRemark(request.getRemark());
                    }

                    return inventoryService.update(inventory)
                            .map(updatedInventory -> {
                                logOperation("更新库存", "更新成功");
                                return success("更新库存成功", updatedInventory);
                            });
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "更新库存"));
    }

    /**
     * 删除库存
     *
     * @param id 库存ID
     * @return 删除结果
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除库存", description = "根据ID删除库存")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Boolean>> delete(@PathParam("id") Long id) {
        logOperation("删除库存", "ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.delete(id, tenantId))
                .map(result -> {
                    if (result) {
                        logOperation("删除库存", "删除成功");
                        return success("删除库存成功", true);
                    } else {
                        return ApiResponse.<Boolean>notFound("库存不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "删除库存"));
    }

    /**
     * 根据商品查询库存
     *
     * @param productId 商品ID
     * @return 查询结果
     */
    @GET
    @Path("/product/{productId}")
    @Operation(summary = "根据商品查询库存", description = "根据商品ID查询库存信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<Inventory>>> getInventoryByProductId(@PathParam("productId") Long productId) {
        logOperation("根据商品查询库存", "商品ID: " + productId);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findByProductId(tenantId, productId))
                .map(inventories -> {
                    logOperation("根据商品查询库存", "查询成功，共" + inventories.size() + "条记录");
                    return success(inventories);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据商品查询库存"));
    }

    /**
     * 库存入库
     *
     * @param id 库存ID
     * @param quantity 入库数量
     * @param reason 入库原因
     * @return 操作结果
     */
    @POST
    @Path("/{id}/stock-in")
    @Operation(summary = "库存入库", description = "增加库存数量")
    @APIResponse(responseCode = "200", description = "入库成功")
    public Uni<ApiResponse<Boolean>> stockIn(@PathParam("id") Long id,
                                            @QueryParam("quantity") Integer quantity,
                                            @QueryParam("reason") String reason) {
        logOperation("库存入库", String.format("库存ID: %d, 数量: %d", id, quantity));

        // 创建操作请求
        com.visthink.inventory.dto.InventoryOperationRequest request =
            new com.visthink.inventory.dto.InventoryOperationRequest();
        request.setQuantity(quantity);
        request.setReason(reason != null ? reason : "库存入库");

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.increaseStock(tenantId, id, request))
                .map(result -> {
                    logOperation("库存入库", "入库成功");
                    return success("库存入库成功", result);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "库存入库"));
    }

    /**
     * 库存出库
     *
     * @param id 库存ID
     * @param quantity 出库数量
     * @param reason 出库原因
     * @return 操作结果
     */
    @POST
    @Path("/{id}/stock-out")
    @Operation(summary = "库存出库", description = "减少库存数量")
    @APIResponse(responseCode = "200", description = "出库成功")
    public Uni<ApiResponse<Boolean>> stockOut(@PathParam("id") Long id,
                                             @QueryParam("quantity") Integer quantity,
                                             @QueryParam("reason") String reason) {
        logOperation("库存出库", String.format("库存ID: %d, 数量: %d", id, quantity));

        // 创建操作请求
        com.visthink.inventory.dto.InventoryOperationRequest request =
            new com.visthink.inventory.dto.InventoryOperationRequest();
        request.setQuantity(quantity);
        request.setReason(reason != null ? reason : "库存出库");

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.decreaseStock(tenantId, id, request))
                .map(result -> {
                    logOperation("库存出库", "出库成功");
                    return success("库存出库成功", result);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "库存出库"));
    }

    /**
     * 统计库存数量
     *
     * @return 统计结果
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计库存数量", description = "统计库存总数量")
    @APIResponse(responseCode = "200", description = "统计成功")
    public Uni<ApiResponse<Long>> count() {
        logOperation("统计库存数量", "开始处理统计请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.count(tenantId))
                .map(count -> {
                    logOperation("统计库存数量", "统计成功: " + count);
                    return success(count);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "统计库存数量"));
    }
}
