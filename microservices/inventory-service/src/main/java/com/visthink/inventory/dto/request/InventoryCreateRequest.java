package com.visthink.inventory.dto.request;

import lombok.Data;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 库存创建请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class InventoryCreateRequest {

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID不能为空")
    private Long productId;

    /**
     * 商品编码
     */
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    private String productCode;

    /**
     * SKU ID
     */
    @NotNull(message = "SKU ID不能为空")
    private Long skuId;

    /**
     * SKU编码
     */
    @NotBlank(message = "SKU编码不能为空")
    @Size(max = 100, message = "SKU编码长度不能超过100个字符")
    private String skuCode;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @Size(max = 100, message = "仓库编码长度不能超过100个字符")
    private String warehouseCode;

    /**
     * 初始库存数量
     */
    @NotNull(message = "初始库存数量不能为空")
    @Min(value = 0, message = "初始库存数量不能小于0")
    private Integer quantity = 0;

    /**
     * 安全库存数量
     */
    @Min(value = 0, message = "安全库存数量不能小于0")
    private Integer safetyStock = 0;

    /**
     * 预警库存数量
     */
    @Min(value = 0, message = "预警库存数量不能小于0")
    private Integer warningStock = 0;

    /**
     * 最大库存数量
     */
    @Min(value = 0, message = "最大库存数量不能小于0")
    private Integer maxStock;

    /**
     * 最小库存数量
     */
    @Min(value = 0, message = "最小库存数量不能小于0")
    private Integer minStock;

    /**
     * 补货点
     */
    @Min(value = 0, message = "补货点不能小于0")
    private Integer reorderPoint;

    /**
     * 补货数量
     */
    @Min(value = 0, message = "补货数量不能小于0")
    private Integer reorderQuantity;

    /**
     * 平均成本价
     */
    @DecimalMin(value = "0.00", message = "平均成本价不能小于0")
    @Digits(integer = 8, fraction = 2, message = "平均成本价格式不正确")
    private BigDecimal averageCost;

    /**
     * 库存状态：1-正常，2-冻结，3-盘点中
     */
    @Min(value = 1, message = "库存状态值无效")
    @Max(value = 3, message = "库存状态值无效")
    private Integer status = 1;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
