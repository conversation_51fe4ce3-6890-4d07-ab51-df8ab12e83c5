package com.visthink.inventory.repository;

import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import org.hibernate.reactive.mutiny.Mutiny;

import jakarta.inject.Inject;
import java.util.List;
import java.util.function.Function;

/**
 * 响应式基础Repository
 * 
 * 提供安全的Hibernate Reactive会话管理
 * 解决JdbcValuesSourceProcessingState错误
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public abstract class ReactiveBaseRepository {

    @Inject
    protected Mutiny.SessionFactory sessionFactory;

    /**
     * 执行查询操作（只读会话）
     * 
     * @param operation 查询操作
     * @param <T> 返回类型
     * @return 查询结果
     */
    protected <T> Uni<T> executeQuery(Function<Mutiny.Session, Uni<T>> operation) {
        return sessionFactory.withSession(session -> {
            try {
                return operation.apply(session);
            } catch (Exception e) {
                Log.errorf(e, "执行查询操作失败");
                return Uni.createFrom().failure(e);
            }
        }).onFailure().invoke(throwable -> {
            Log.errorf(throwable, "查询操作执行失败");
        });
    }

    /**
     * 执行事务操作（读写会话）
     * 
     * @param operation 事务操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    protected <T> Uni<T> executeTransaction(Function<Mutiny.Session, Uni<T>> operation) {
        return sessionFactory.withTransaction(session -> {
            try {
                return operation.apply(session);
            } catch (Exception e) {
                Log.errorf(e, "执行事务操作失败");
                return Uni.createFrom().failure(e);
            }
        }).onFailure().invoke(throwable -> {
            Log.errorf(throwable, "事务操作执行失败");
        });
    }

    /**
     * 查找单个实体
     * 
     * @param query HQL查询语句
     * @param parameters 查询参数
     * @param <T> 实体类型
     * @return 单个实体或null
     */
    protected <T> Uni<T> findSingle(String query, Class<T> entityClass, Object... parameters) {
        return executeQuery(session -> {
            var queryObj = session.createQuery(query, entityClass);
            for (int i = 0; i < parameters.length; i++) {
                queryObj.setParameter(i + 1, parameters[i]);
            }
            return queryObj.getSingleResultOrNull();
        });
    }

    /**
     * 查找实体列表
     * 
     * @param query HQL查询语句
     * @param parameters 查询参数
     * @param <T> 实体类型
     * @return 实体列表
     */
    protected <T> Uni<List<T>> findList(String query, Class<T> entityClass, Object... parameters) {
        return executeQuery(session -> {
            var queryObj = session.createQuery(query, entityClass);
            for (int i = 0; i < parameters.length; i++) {
                queryObj.setParameter(i + 1, parameters[i]);
            }
            return queryObj.getResultList();
        });
    }

    /**
     * 执行计数查询
     * 
     * @param query HQL计数查询语句
     * @param parameters 查询参数
     * @return 计数结果
     */
    protected Uni<Long> count(String query, Object... parameters) {
        return executeQuery(session -> {
            var queryObj = session.createQuery(query, Long.class);
            for (int i = 0; i < parameters.length; i++) {
                queryObj.setParameter(i + 1, parameters[i]);
            }
            return queryObj.getSingleResult();
        });
    }

    /**
     * 执行更新操作
     * 
     * @param query HQL更新语句
     * @param parameters 查询参数
     * @return 影响的行数
     */
    protected Uni<Integer> executeUpdate(String query, Object... parameters) {
        return executeTransaction(session -> {
            var queryObj = session.createQuery(query);
            for (int i = 0; i < parameters.length; i++) {
                queryObj.setParameter(i + 1, parameters[i]);
            }
            return queryObj.executeUpdate();
        });
    }

    /**
     * 持久化实体
     * 
     * @param entity 要持久化的实体
     * @param <T> 实体类型
     * @return 持久化后的实体
     */
    protected <T> Uni<T> persist(T entity) {
        return executeTransaction(session -> 
            session.persist(entity)
                .call(() -> session.flush())
                .replaceWith(entity)
        );
    }

    /**
     * 合并实体
     * 
     * @param entity 要合并的实体
     * @param <T> 实体类型
     * @return 合并后的实体
     */
    protected <T> Uni<T> merge(T entity) {
        return executeTransaction(session -> 
            session.merge(entity)
                .call(() -> session.flush())
        );
    }

    /**
     * 删除实体
     * 
     * @param entity 要删除的实体
     * @return 删除操作结果
     */
    protected <T> Uni<Void> remove(T entity) {
        return executeTransaction(session -> 
            session.remove(entity)
                .call(() -> session.flush())
        );
    }

    /**
     * 根据ID查找实体
     * 
     * @param entityClass 实体类
     * @param id 实体ID
     * @param <T> 实体类型
     * @return 实体或null
     */
    protected <T> Uni<T> findById(Class<T> entityClass, Object id) {
        return executeQuery(session -> session.find(entityClass, id));
    }

    /**
     * 刷新会话
     * 
     * @return 刷新操作结果
     */
    protected Uni<Void> flush() {
        return executeTransaction(session -> session.flush());
    }
}
