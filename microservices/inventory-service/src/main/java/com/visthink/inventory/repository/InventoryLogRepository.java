package com.visthink.inventory.repository;

import com.visthink.inventory.dto.InventoryLogQueryRequest;
import com.visthink.inventory.entity.InventoryLog;
import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 库存变动日志数据访问层
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class InventoryLogRepository implements PanacheRepository<InventoryLog> {

    /**
     * 根据租户ID分页查询库存日志
     */
    public Uni<List<InventoryLog>> findByTenantId(Long tenantId, Page page) {
        return find("tenantId = ?1 order by createdAt desc", tenantId)
                .page(page)
                .list();
    }

    /**
     * 根据库存ID查询变动日志
     */
    public Uni<List<InventoryLog>> findByInventoryId(Long inventoryId, Page page) {
        return find("inventoryId = ?1 order by createdAt desc", inventoryId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和库存ID查询变动日志
     */
    public Uni<List<InventoryLog>> findByTenantAndInventoryId(Long tenantId, Long inventoryId, Page page) {
        return find("tenantId = ?1 and inventoryId = ?2 order by createdAt desc", tenantId, inventoryId)
                .page(page)
                .list();
    }

    /**
     * 根据SKU ID查询变动日志
     */
    public Uni<List<InventoryLog>> findBySkuId(Long skuId, Page page) {
        return find("skuId = ?1 order by createdAt desc", skuId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和SKU ID查询变动日志
     */
    public Uni<List<InventoryLog>> findByTenantAndSkuId(Long tenantId, Long skuId, Page page) {
        return find("tenantId = ?1 and skuId = ?2 order by createdAt desc", tenantId, skuId)
                .page(page)
                .list();
    }

    /**
     * 根据商品ID查询变动日志
     */
    public Uni<List<InventoryLog>> findByProductId(Long productId, Page page) {
        return find("productId = ?1 order by createdAt desc", productId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和商品ID查询变动日志
     */
    public Uni<List<InventoryLog>> findByTenantAndProductId(Long tenantId, Long productId, Page page) {
        return find("tenantId = ?1 and productId = ?2 order by createdAt desc", tenantId, productId)
                .page(page)
                .list();
    }

    /**
     * 根据变动类型查询日志
     */
    public Uni<List<InventoryLog>> findByTenantAndChangeType(Long tenantId, Integer changeType, Page page) {
        return find("tenantId = ?1 and changeType = ?2 order by createdAt desc", tenantId, changeType)
                .page(page)
                .list();
    }

    /**
     * 根据业务单据查询日志
     */
    public Uni<List<InventoryLog>> findByBusinessDocument(Integer businessType, Long businessId) {
        return find("businessType = ?1 and businessId = ?2 order by createdAt desc", businessType, businessId).list();
    }

    /**
     * 根据业务单据编号查询日志
     */
    public Uni<List<InventoryLog>> findByBusinessNo(String businessNo) {
        return find("businessNo = ?1 order by createdAt desc", businessNo).list();
    }

    /**
     * 根据操作人查询日志
     */
    public Uni<List<InventoryLog>> findByOperator(Long operatorId, Page page) {
        return find("operatorId = ?1 order by createdAt desc", operatorId)
                .page(page)
                .list();
    }

    /**
     * 根据租户ID和操作人查询日志
     */
    public Uni<List<InventoryLog>> findByTenantAndOperator(Long tenantId, Long operatorId, Page page) {
        return find("tenantId = ?1 and operatorId = ?2 order by createdAt desc", tenantId, operatorId)
                .page(page)
                .list();
    }

    /**
     * 根据时间范围查询日志
     */
    public Uni<List<InventoryLog>> findByDateRange(Long tenantId, LocalDateTime startTime, LocalDateTime endTime, Page page) {
        return find("tenantId = ?1 and createdAt >= ?2 and createdAt <= ?3 order by createdAt desc",
                tenantId, startTime, endTime)
                .page(page)
                .list();
    }

    /**
     * 根据仓库查询日志
     */
    public Uni<List<InventoryLog>> findByWarehouse(Long tenantId, Long warehouseId, Page page) {
        return find("tenantId = ?1 and warehouseId = ?2 order by createdAt desc", tenantId, warehouseId)
                .page(page)
                .list();
    }

    /**
     * 统计租户日志总数
     */
    public Uni<Long> countByTenant(Long tenantId) {
        return count("tenantId = ?1", tenantId);
    }

    /**
     * 统计指定变动类型的日志数量
     */
    public Uni<Long> countByTenantAndChangeType(Long tenantId, Integer changeType) {
        return count("tenantId = ?1 and changeType = ?2", tenantId, changeType);
    }

    /**
     * 统计指定时间范围的日志数量
     */
    public Uni<Long> countByDateRange(Long tenantId, LocalDateTime startTime, LocalDateTime endTime) {
        return count("tenantId = ?1 and createdAt >= ?2 and createdAt <= ?3", tenantId, startTime, endTime);
    }

    /**
     * 删除过期日志
     */
    public Uni<Long> deleteExpiredLogs(LocalDateTime expireTime) {
        return delete("createdAt < ?1", expireTime);
    }

    /**
     * 搜索库存日志
     */
    public Uni<List<InventoryLog>> searchLogs(Long tenantId, String keyword, Page page) {
        String query = "tenantId = ?1 and (productCode like ?2 or skuCode like ?2 or businessNo like ?2 or operatorName like ?2) order by createdAt desc";
        String searchKeyword = "%" + keyword + "%";
        return find(query, tenantId, searchKeyword)
                .page(page)
                .list();
    }

    /**
     * 获取最近的库存变动记录
     */
    public Uni<InventoryLog> findLatestByInventoryId(Long inventoryId) {
        return find("inventoryId = ?1 order by createdAt desc", inventoryId).firstResult();
    }

    /**
     * 获取指定SKU的最近变动记录
     */
    public Uni<InventoryLog> findLatestBySkuId(Long skuId) {
        return find("skuId = ?1 order by createdAt desc", skuId).firstResult();
    }

    /**
     * 根据条件查询库存变动日志
     */
    public Uni<List<InventoryLog>> findByConditions(Long tenantId, InventoryLogQueryRequest request) {
        StringBuilder query = new StringBuilder("tenantId = ?1");
        java.util.List<Object> params = new java.util.ArrayList<>();
        params.add(tenantId);

        if (request.getInventoryId() != null) {
            query.append(" and inventoryId = ?").append(params.size() + 1);
            params.add(request.getInventoryId());
        }
        if (request.getProductId() != null) {
            query.append(" and productId = ?").append(params.size() + 1);
            params.add(request.getProductId());
        }
        if (request.getSkuId() != null) {
            query.append(" and skuId = ?").append(params.size() + 1);
            params.add(request.getSkuId());
        }
        if (request.getChangeType() != null) {
            query.append(" and changeType = ?").append(params.size() + 1);
            params.add(request.getChangeType());
        }
        if (request.getBusinessType() != null) {
            query.append(" and businessType = ?").append(params.size() + 1);
            params.add(request.getBusinessType());
        }
        if (request.getBusinessId() != null) {
            query.append(" and businessId = ?").append(params.size() + 1);
            params.add(request.getBusinessId());
        }
        if (request.getChangeTimeStart() != null) {
            query.append(" and createdAt >= ?").append(params.size() + 1);
            params.add(request.getChangeTimeStart());
        }
        if (request.getChangeTimeEnd() != null) {
            query.append(" and createdAt <= ?").append(params.size() + 1);
            params.add(request.getChangeTimeEnd());
        }

        query.append(" order by ").append(request.getSortField()).append(" ").append(request.getSortDirection());

        return find(query.toString(), params.toArray())
                .page(request.getPage() - 1, request.getSize())
                .list();
    }

    /**
     * 根据租户ID和库存ID查询变动日志
     */
    public Uni<List<InventoryLog>> findByInventoryId(Long tenantId, Long inventoryId, int page, int size) {
        return find("tenantId = ?1 and inventoryId = ?2 order by createdAt desc", tenantId, inventoryId)
                .page(page - 1, size)
                .list();
    }

    /**
     * 根据租户ID和SKU ID查询变动日志
     */
    public Uni<List<InventoryLog>> findBySkuId(Long tenantId, Long skuId, int page, int size) {
        return find("tenantId = ?1 and skuId = ?2 order by createdAt desc", tenantId, skuId)
                .page(page - 1, size)
                .list();
    }

    /**
     * 根据租户ID和商品ID查询变动日志
     */
    public Uni<List<InventoryLog>> findByProductId(Long tenantId, Long productId, int page, int size) {
        return find("tenantId = ?1 and productId = ?2 order by createdAt desc", tenantId, productId)
                .page(page - 1, size)
                .list();
    }

    /**
     * 根据业务单据查询变动日志
     */
    public Uni<List<InventoryLog>> findByBusiness(Long tenantId, Integer businessType, Long businessId) {
        return find("tenantId = ?1 and businessType = ?2 and businessId = ?3 order by createdAt desc",
                tenantId, businessType, businessId).list();
    }

    /**
     * 统计指定时间段内的库存变动
     */
    public Uni<List<Object[]>> statisticsInventoryChanges(Long tenantId, java.time.LocalDateTime startTime, java.time.LocalDateTime endTime) {
        return find("select l.changeType, count(l), sum(abs(l.changeQuantity)) from InventoryLog l " +
                "where l.tenantId = ?1 and l.createdAt >= ?2 and l.createdAt <= ?3 " +
                "group by l.changeType order by l.changeType",
                tenantId, startTime, endTime)
            .project(Object[].class)
            .list();
    }

    /**
     * 统计指定商品的库存变动
     */
    public Uni<List<Object[]>> statisticsProductInventoryChanges(Long tenantId, Long productId, java.time.LocalDateTime startTime, java.time.LocalDateTime endTime) {
        return find("select l.changeType, count(l), sum(abs(l.changeQuantity)) from InventoryLog l " +
                "where l.tenantId = ?1 and l.productId = ?2 and l.createdAt >= ?3 and l.createdAt <= ?4 " +
                "group by l.changeType order by l.changeType",
                tenantId, productId, startTime, endTime)
            .project(Object[].class)
            .list();
    }

    /**
     * 获取库存变动趋势
     */
    public Uni<List<Object[]>> getInventoryTrend(Long tenantId, Long skuId, java.time.LocalDateTime startTime, java.time.LocalDateTime endTime) {
        return find("select date(l.createdAt), sum(l.changeQuantity) from InventoryLog l " +
                "where l.tenantId = ?1 and l.skuId = ?2 and l.createdAt >= ?3 and l.createdAt <= ?4 " +
                "group by date(l.createdAt) order by date(l.createdAt)",
                tenantId, skuId, startTime, endTime)
            .project(Object[].class)
            .list();
    }

    /**
     * 获取热门变动商品
     */
    public Uni<List<Object[]>> getTopChangedProducts(Long tenantId, java.time.LocalDateTime startTime, int limit) {
        return find("select l.productId, l.productCode, count(l), sum(abs(l.changeQuantity)) from InventoryLog l " +
                "where l.tenantId = ?1 and l.createdAt >= ?2 " +
                "group by l.productId, l.productCode " +
                "order by count(l) desc",
                tenantId, startTime)
            .project(Object[].class)
            .page(0, limit)
            .list();
    }

    /**
     * 获取库存变动汇总
     */
    public Uni<Object[]> getInventoryChangeSummary(Long tenantId, java.time.LocalDateTime startTime, java.time.LocalDateTime endTime) {
        return find("select count(l), sum(case when l.changeQuantity > 0 then l.changeQuantity else 0 end), " +
                "sum(case when l.changeQuantity < 0 then abs(l.changeQuantity) else 0 end), " +
                "count(distinct l.productId), count(distinct l.skuId) from InventoryLog l " +
                "where l.tenantId = ?1 and l.createdAt >= ?2 and l.createdAt <= ?3",
                tenantId, startTime, endTime)
            .project(Object[].class)
            .firstResult();
    }

    /**
     * 删除过期的库存变动日志
     */
    public Uni<Long> deleteExpiredLogs(Long tenantId, java.time.LocalDateTime expireTime) {
        return delete("tenantId = ?1 and createdAt < ?2", tenantId, expireTime);
    }

    /**
     * 获取库存变动类型统计
     */
    public Uni<List<Object[]>> getChangeTypeStatistics(Long tenantId, java.time.LocalDateTime startTime, java.time.LocalDateTime endTime) {
        return find("select l.changeType, count(l), sum(abs(l.changeQuantity)) from InventoryLog l " +
                "where l.tenantId = ?1 and l.createdAt >= ?2 and l.createdAt <= ?3 " +
                "group by l.changeType order by count(l) desc",
                tenantId, startTime, endTime)
            .project(Object[].class)
            .list();
    }

    /**
     * 获取仓库库存变动统计
     */
    public Uni<List<Object[]>> getWarehouseChangeStatistics(Long tenantId, Long warehouseId, java.time.LocalDateTime startTime, java.time.LocalDateTime endTime) {
        return find("select l.changeType, count(l), sum(abs(l.changeQuantity)) from InventoryLog l " +
                "where l.tenantId = ?1 and l.warehouseId = ?2 and l.createdAt >= ?3 and l.createdAt <= ?4 " +
                "group by l.changeType order by l.changeType",
                tenantId, warehouseId, startTime, endTime)
            .project(Object[].class)
            .list();
    }

    /**
     * 验证库存变动日志的完整性
     */
    public Uni<Boolean> validateLogIntegrity(Long tenantId, Long inventoryId) {
        return find("select sum(l.changeQuantity) from InventoryLog l " +
                "where l.tenantId = ?1 and l.inventoryId = ?2", tenantId, inventoryId)
            .firstResult()
            .map(result -> {
                // 这里可以添加更复杂的验证逻辑
                return result != null;
            });
    }

    // ========== BaseService 需要的基础方法 ==========

    /**
     * 根据ID和租户ID查询库存日志
     */
    public Uni<InventoryLog> findByIdAndTenant(Long id, Long tenantId) {
        return find("id = ?1 and tenantId = ?2", id, tenantId).firstResult();
    }

    /**
     * 查询租户下所有库存日志
     */
    public Uni<List<InventoryLog>> findAllByTenant(Long tenantId) {
        return find("tenantId = ?1 order by id desc", tenantId).list();
    }

    /**
     * 根据ID和租户ID删除库存日志
     */
    public Uni<Integer> deleteByIdAndTenant(Long id, Long tenantId) {
        return delete("id = ?1 and tenantId = ?2", id, tenantId)
            .map(Long::intValue);
    }



    /**
     * 批量删除租户下的库存日志
     */
    public Uni<Integer> batchDeleteByTenant(List<Long> ids, Long tenantId) {
        if (ids == null || ids.isEmpty()) {
            return Uni.createFrom().item(0);
        }
        return delete("id in ?1 and tenantId = ?2", ids, tenantId)
            .map(Long::intValue);
    }
}
