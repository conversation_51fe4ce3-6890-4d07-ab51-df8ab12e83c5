package com.visthink.inventory.dto;

import lombok.Data;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 库存操作请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class InventoryOperationRequest {

    /**
     * 操作数量
     */
    @NotNull(message = "操作数量不能为空")
    @Min(value = 1, message = "操作数量必须大于0")
    private Integer quantity;

    /**
     * 单价（用于计算成本）
     */
    @Min(value = 0, message = "单价不能小于0")
    private BigDecimal unitPrice;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 业务类型：1-采购单，2-销售单，3-调拨单，4-盘点单，5-其他
     */
    private Integer businessType;

    /**
     * 业务单据ID
     */
    private Long businessId;

    /**
     * 业务单据编号
     */
    private String businessNo;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 备注
     */
    private String remark;
}
