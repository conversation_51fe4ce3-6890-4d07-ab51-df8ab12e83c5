package com.visthink.inventory.entity;

import com.visthink.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 库存实体
 * 
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "inventory")
@EqualsAndHashCode(callSuper = true)
public class Inventory extends BaseEntity {

    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;

    /**
     * 商品编码（冗余字段，便于查询）
     */
    @Column(name = "product_code", length = 100)
    private String productCode;

    /**
     * SKU ID
     */
    @Column(name = "sku_id", nullable = false)
    private Long skuId;

    /**
     * SKU编码（冗余字段，便于查询）
     */
    @Column(name = "sku_code", length = 100, nullable = false)
    private String skuCode;

    /**
     * 仓库ID
     */
    @Column(name = "warehouse_id")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @Column(name = "warehouse_code", length = 100)
    private String warehouseCode;

    /**
     * 当前库存数量
     */
    @Column(name = "quantity", nullable = false)
    private Integer quantity = 0;

    /**
     * 可用库存数量（扣除预占库存）
     */
    @Column(name = "available_quantity", nullable = false)
    private Integer availableQuantity = 0;

    /**
     * 预占库存数量（已下单但未发货）
     */
    @Column(name = "reserved_quantity", nullable = false)
    private Integer reservedQuantity = 0;

    /**
     * 在途库存数量（已采购但未入库）
     */
    @Column(name = "in_transit_quantity", nullable = false)
    private Integer inTransitQuantity = 0;

    /**
     * 安全库存数量
     */
    @Column(name = "safety_stock", nullable = false)
    private Integer safetyStock = 0;

    /**
     * 预警库存数量
     */
    @Column(name = "warning_stock", nullable = false)
    private Integer warningStock = 0;

    /**
     * 最大库存数量
     */
    @Column(name = "max_stock")
    private Integer maxStock;

    /**
     * 最小库存数量
     */
    @Column(name = "min_stock")
    private Integer minStock;

    /**
     * 补货点（自动补货阈值）
     */
    @Column(name = "reorder_point")
    private Integer reorderPoint;

    /**
     * 补货数量
     */
    @Column(name = "reorder_quantity")
    private Integer reorderQuantity;

    /**
     * 平均成本价
     */
    @Column(name = "average_cost", precision = 10, scale = 2)
    private BigDecimal averageCost;

    /**
     * 库存总价值
     */
    @Column(name = "total_value", precision = 12, scale = 2)
    private BigDecimal totalValue;

    /**
     * 库存状态：1-正常，2-冻结，3-盘点中
     */
    @Column(name = "status", nullable = false)
    private Integer status = 1;

    /**
     * 最后盘点时间
     */
    @Column(name = "last_stocktaking_at")
    private java.time.LocalDateTime lastStocktakingAt;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 库存状态常量
     */
    public static class Status {
        public static final int NORMAL = 1;      // 正常
        public static final int FROZEN = 2;      // 冻结
        public static final int STOCKTAKING = 3; // 盘点中
    }

    /**
     * 计算可用库存
     */
    public void calculateAvailableQuantity() {
        this.availableQuantity = Math.max(0, this.quantity - this.reservedQuantity);
    }

    /**
     * 计算库存总价值
     */
    public void calculateTotalValue() {
        if (this.averageCost != null && this.quantity != null) {
            this.totalValue = this.averageCost.multiply(BigDecimal.valueOf(this.quantity));
        }
    }

    /**
     * 是否库存不足
     */
    public boolean isLowStock() {
        return this.availableQuantity <= this.warningStock;
    }

    /**
     * 是否需要补货
     */
    public boolean needReorder() {
        return this.reorderPoint != null && this.availableQuantity <= this.reorderPoint;
    }

    /**
     * 是否库存过多
     */
    public boolean isOverStock() {
        return this.maxStock != null && this.quantity > this.maxStock;
    }
}
