package com.visthink.inventory.resource;

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.inventory.entity.InventoryLog;
import com.visthink.inventory.service.InventoryLogService;
import com.visthink.inventory.dto.InventoryLogQueryRequest;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import java.util.List;
import java.time.LocalDateTime;

/**
 * 库存变动日志REST接口 - 重构版本
 *
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给InventoryLogService处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 */
@Path("/api/inventory-logs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "库存变动日志", description = "库存变动日志相关接口")
public class InventoryLogResource extends SimpleController {

    @Inject
    InventoryLogService inventoryLogService;

    /**
     * 分页查询库存变动日志
     */
    @GET
    @Operation(summary = "分页查询库存变动日志", description = "根据条件分页查询库存变动日志")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<InventoryLog>>> queryInventoryLogs(@BeanParam @Valid InventoryLogQueryRequest request) {
        logOperation("分页查询库存变动日志", "开始处理查询请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryLogService.queryInventoryLogs(tenantId, request))
                .map(logs -> {
                    logOperation("分页查询库存变动日志", "查询成功，共" + logs.size() + "条记录");
                    return success(logs);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "分页查询库存变动日志"));
    }

    /**
     * 根据库存ID查询变动日志
     */
    @GET
    @Path("/inventory/{inventoryId}")
    @Operation(summary = "根据库存ID查询变动日志", description = "根据库存ID查询变动日志")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<InventoryLog>>> getInventoryLogsByInventoryId(
            @PathParam("inventoryId") Long inventoryId,
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        logOperation("根据库存ID查询变动日志", String.format("库存ID: %d, 页码: %d, 页大小: %d", inventoryId, page, size));

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryLogService.findByInventoryId(tenantId, inventoryId, page, size))
                .map(logs -> {
                    logOperation("根据库存ID查询变动日志", "查询成功，共" + logs.size() + "条记录");
                    return success(logs);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据库存ID查询变动日志"));
    }

    /**
     * 根据SKU ID查询变动日志
     */
    @GET
    @Path("/sku/{skuId}")
    @Operation(summary = "根据SKU ID查询变动日志", description = "根据SKU ID查询变动日志")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<InventoryLog>>> getInventoryLogsBySkuId(
            @PathParam("skuId") Long skuId,
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        logOperation("根据SKU ID查询变动日志", String.format("SKU ID: %d, 页码: %d, 页大小: %d", skuId, page, size));

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryLogService.findBySkuId(tenantId, skuId, page, size))
                .map(logs -> {
                    logOperation("根据SKU ID查询变动日志", "查询成功，共" + logs.size() + "条记录");
                    return success(logs);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据SKU ID查询变动日志"));
    }

    /**
     * 根据商品ID查询变动日志
     */
    @GET
    @Path("/product/{productId}")
    @Operation(summary = "根据商品ID查询变动日志", description = "根据商品ID查询变动日志")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<InventoryLog>>> getInventoryLogsByProductId(
            @PathParam("productId") Long productId,
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        logOperation("根据商品ID查询变动日志", String.format("商品ID: %d, 页码: %d, 页大小: %d", productId, page, size));

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryLogService.findByProductId(tenantId, productId, page, size))
                .map(logs -> {
                    logOperation("根据商品ID查询变动日志", "查询成功，共" + logs.size() + "条记录");
                    return success(logs);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据商品ID查询变动日志"));
    }

    /**
     * 根据业务单据查询变动日志
     */
    @GET
    @Path("/business")
    @Operation(summary = "根据业务单据查询变动日志", description = "根据业务单据查询变动日志")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<InventoryLog>>> getInventoryLogsByBusiness(
            @QueryParam("businessType") Integer businessType,
            @QueryParam("businessId") Long businessId) {
        logOperation("根据业务单据查询变动日志", String.format("业务类型: %d, 业务ID: %d", businessType, businessId));

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryLogService.findByBusiness(tenantId, businessType, businessId))
                .map(logs -> {
                    logOperation("根据业务单据查询变动日志", "查询成功，共" + logs.size() + "条记录");
                    return success(logs);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据业务单据查询变动日志"));
    }

    /**
     * 统计指定时间段内的库存变动
     */
    @GET
    @Path("/statistics/changes")
    @Operation(summary = "统计库存变动", description = "统计指定时间段内的库存变动")
    @APIResponse(responseCode = "200", description = "统计成功")
    public Uni<ApiResponse<List<Object[]>>> statisticsInventoryChanges(
            @QueryParam("startTime") String startTime,
            @QueryParam("endTime") String endTime) {
        logOperation("统计库存变动", String.format("时间段: %s - %s", startTime, endTime));

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    LocalDateTime start = LocalDateTime.parse(startTime);
                    LocalDateTime end = LocalDateTime.parse(endTime);
                    return inventoryLogService.statisticsInventoryChanges(tenantId, start, end);
                })
                .map(statistics -> {
                    logOperation("统计库存变动", "统计成功");
                    return success(statistics);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "统计库存变动"));
    }

    /**
     * 统计指定商品的库存变动
     */
    @GET
    @Path("/statistics/product/{productId}")
    @Operation(summary = "统计商品库存变动", description = "统计指定商品的库存变动")
    @APIResponse(responseCode = "200", description = "统计成功")
    public Uni<ApiResponse<List<Object[]>>> statisticsProductInventoryChanges(
            @PathParam("productId") Long productId,
            @QueryParam("startTime") String startTime,
            @QueryParam("endTime") String endTime) {
        logOperation("统计商品库存变动", String.format("商品ID: %d, 时间段: %s - %s", productId, startTime, endTime));

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    LocalDateTime start = LocalDateTime.parse(startTime);
                    LocalDateTime end = LocalDateTime.parse(endTime);
                    return inventoryLogService.statisticsProductInventoryChanges(tenantId, productId, start, end);
                })
                .map(statistics -> {
                    logOperation("统计商品库存变动", "统计成功");
                    return success(statistics);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "统计商品库存变动"));
    }

    /**
     * 获取库存变动趋势
     */
    @GET
    @Path("/trend/sku/{skuId}")
    @Operation(summary = "获取库存变动趋势", description = "获取指定SKU的库存变动趋�?")
    public Uni<ApiResponse<List<Object[]>>> getInventoryTrend(
            @PathParam("skuId") Long skuId,
            @QueryParam("days") @DefaultValue("30") int days) {
        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryLogService.getInventoryTrend(tenantId, skuId, days))
                .map(trend -> {
                    logOperation("获取库存变动趋势", "查询成功");
                    return success(trend);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "获取库存变动趋势"));
    }

    /**
     * 删除过期的库存变动日志
     */
    @DELETE
    @Path("/expired")
    @Operation(summary = "删除过期日志", description = "删除过期的库存变动日志")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Long>> deleteExpiredLogs(@QueryParam("days") @DefaultValue("365") int days) {
        logOperation("删除过期日志", "保留天数: " + days);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryLogService.deleteExpiredLogs(tenantId, days))
                .map(count -> {
                    logOperation("删除过期日志", "删除成功，共删除" + count + "条记录");
                    return success("删除过期日志成功", count);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "删除过期日志"));
    }



}
