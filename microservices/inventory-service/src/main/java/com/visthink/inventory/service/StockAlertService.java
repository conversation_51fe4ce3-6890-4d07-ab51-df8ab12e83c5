package com.visthink.inventory.service;

import com.visthink.common.base.BaseService;
import com.visthink.inventory.entity.Inventory;
import com.visthink.inventory.entity.StockAlert;
import com.visthink.inventory.repository.StockAlertRepository;
import com.visthink.inventory.dto.StockAlertQueryRequest;
import com.visthink.inventory.dto.StockAlertHandleRequest;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.List;
import java.time.LocalDateTime;

/**
 * 库存预警服务类
 *
 * <AUTHOR>
 */
@ApplicationScoped
public class StockAlertService extends BaseService<StockAlert, Long> {

    @Inject
    StockAlertRepository stockAlertRepository;

    /**
     * 创建库存不足预警
     *
     * @param inventory 库存记录
     * @return 预警记录
     */
    public Uni<StockAlert> createLowStockAlert(Inventory inventory) {
        // 检查是否已存在未处理的预警
        return stockAlertRepository.findPendingAlert(inventory.getTenantId(), inventory.id, 1)
            .flatMap(existingAlert -> {
                if (existingAlert != null) {
                    // 更新现有预警
                    existingAlert.setCurrentQuantity(inventory.getQuantity());
                    existingAlert.setAvailableQuantity(inventory.getAvailableQuantity());
                    existingAlert.setAlertMessage(generateAlertMessage(inventory, 1));
                    return stockAlertRepository.persist(existingAlert);
                } else {
                    // 创建新预警
                    StockAlert alert = new StockAlert();
                    alert.setTenantId(inventory.getTenantId());
                    alert.setInventoryId(inventory.id);
                    alert.setProductId(inventory.getProductId());
                    alert.setProductCode(inventory.getProductCode());
                    alert.setSkuId(inventory.getSkuId());
                    alert.setSkuCode(inventory.getSkuCode());
                    alert.setWarehouseId(inventory.getWarehouseId());
                    alert.setWarehouseCode(inventory.getWarehouseCode());
                    alert.setAlertType(1); // 库存不足
                    alert.setCurrentQuantity(inventory.getQuantity());
                    alert.setAvailableQuantity(inventory.getAvailableQuantity());
                    alert.setAlertThreshold(inventory.getWarningStock());
                    alert.setAlertLevel(calculateAlertLevel(inventory));
                    alert.setAlertMessage(generateAlertMessage(inventory, 1));
                    alert.setAlertStatus(1); // 待处理

                    return stockAlertRepository.persist(alert);
                }
            });
    }

    /**
     * 创建零库存预警
     *
     * @param inventory 库存记录
     * @return 预警记录
     */
    public Uni<StockAlert> createZeroStockAlert(Inventory inventory) {
        StockAlert alert = new StockAlert();
        alert.setTenantId(inventory.getTenantId());
        alert.setInventoryId(inventory.id);
        alert.setProductId(inventory.getProductId());
        alert.setProductCode(inventory.getProductCode());
        alert.setSkuId(inventory.getSkuId());
        alert.setSkuCode(inventory.getSkuCode());
        alert.setWarehouseId(inventory.getWarehouseId());
        alert.setWarehouseCode(inventory.getWarehouseCode());
        alert.setAlertType(3); // 零库存
        alert.setCurrentQuantity(inventory.getQuantity());
        alert.setAvailableQuantity(inventory.getAvailableQuantity());
        alert.setAlertThreshold(0);
        alert.setAlertLevel(4); // 紧急
        alert.setAlertMessage(generateAlertMessage(inventory, 3));
        alert.setAlertStatus(1); // 待处理

        return stockAlertRepository.persist(alert);
    }

    /**
     * 创建负库存预警
     *
     * @param inventory 库存记录
     * @return 预警记录
     */
    public Uni<StockAlert> createNegativeStockAlert(Inventory inventory) {
        StockAlert alert = new StockAlert();
        alert.setTenantId(inventory.getTenantId());
        alert.setInventoryId(inventory.id);
        alert.setProductId(inventory.getProductId());
        alert.setProductCode(inventory.getProductCode());
        alert.setSkuId(inventory.getSkuId());
        alert.setSkuCode(inventory.getSkuCode());
        alert.setWarehouseId(inventory.getWarehouseId());
        alert.setWarehouseCode(inventory.getWarehouseCode());
        alert.setAlertType(4); // 负库存
        alert.setCurrentQuantity(inventory.getQuantity());
        alert.setAvailableQuantity(inventory.getAvailableQuantity());
        alert.setAlertThreshold(0);
        alert.setAlertLevel(4); // 紧急
        alert.setAlertMessage(generateAlertMessage(inventory, 4));
        alert.setAlertStatus(1); // 待处理

        return stockAlertRepository.persist(alert);
    }

    /**
     * 创建库存过多预警
     *
     * @param inventory 库存记录
     * @return 预警记录
     */
    public Uni<StockAlert> createOverStockAlert(Inventory inventory) {
        StockAlert alert = new StockAlert();
        alert.setTenantId(inventory.getTenantId());
        alert.setInventoryId(inventory.id);
        alert.setProductId(inventory.getProductId());
        alert.setProductCode(inventory.getProductCode());
        alert.setSkuId(inventory.getSkuId());
        alert.setSkuCode(inventory.getSkuCode());
        alert.setWarehouseId(inventory.getWarehouseId());
        alert.setWarehouseCode(inventory.getWarehouseCode());
        alert.setAlertType(2); // 库存过多
        alert.setCurrentQuantity(inventory.getQuantity());
        alert.setAvailableQuantity(inventory.getAvailableQuantity());
        alert.setAlertThreshold(inventory.getMaxStock());
        alert.setAlertLevel(2); // 中等
        alert.setAlertMessage(generateAlertMessage(inventory, 2));
        alert.setAlertStatus(1); // 待处理

        return stockAlertRepository.persist(alert);
    }

    /**
     * 处理库存预警
     *
     * @param tenantId 租户ID
     * @param id 预警ID
     * @param request 处理请求
     * @return 处理结果
     */
    public Uni<StockAlert> handleAlert(Long tenantId, Long id, StockAlertHandleRequest request) {
        return findByIdAndTenant(id, tenantId)
            .flatMap(alert -> {
                alert.setAlertStatus(request.getAlertStatus());
                alert.setHandlerId(request.getHandlerId());
                alert.setHandlerName(request.getHandlerName());
                alert.setHandledAt(LocalDateTime.now());
                alert.setHandleRemark(request.getHandleRemark());

                return stockAlertRepository.persist(alert);
            });
    }

    /**
     * 查询库存预警
     *
     * @param tenantId 租户ID
     * @param request 查询请求
     * @return 预警列表
     */
    public Uni<List<StockAlert>> queryStockAlerts(Long tenantId, StockAlertQueryRequest request) {
        return stockAlertRepository.findByConditions(tenantId, request);
    }

    /**
     * 查询待处理预警
     *
     * @param tenantId 租户ID
     * @param page 页码
     * @param size 页大小
     * @return 预警列表
     */
    public Uni<List<StockAlert>> findPendingAlerts(Long tenantId, int page, int size) {
        return stockAlertRepository.findPendingAlerts(tenantId, page, size);
    }

    /**
     * 查询高优先级预警
     *
     * @param tenantId 租户ID
     * @param page 页码
     * @param size 页大小
     * @return 预警列表
     */
    public Uni<List<StockAlert>> findHighPriorityAlerts(Long tenantId, int page, int size) {
        return stockAlertRepository.findHighPriorityAlerts(tenantId, page, size);
    }

    /**
     * 统计预警数量按类型分组
     *
     * @param tenantId 租户ID
     * @return 统计结果
     */
    public Uni<List<Object[]>> countAlertsByType(Long tenantId) {
        return stockAlertRepository.countAlertsByType(tenantId);
    }

    /**
     * 统计预警数量按级别分组
     *
     * @param tenantId 租户ID
     * @return 统计结果
     */
    public Uni<List<Object[]>> countAlertsByLevel(Long tenantId) {
        return stockAlertRepository.countAlertsByLevel(tenantId);
    }

    /**
     * 统计预警数量按状态分组
     *
     * @param tenantId 租户ID
     * @return 统计结果
     */
    public Uni<List<Object[]>> countAlertsByStatus(Long tenantId) {
        return stockAlertRepository.countAlertsByStatus(tenantId);
    }

    /**
     * 批量处理预警
     *
     * @param tenantId 租户ID
     * @param alertIds 预警ID列表
     * @param request 处理请求
     * @return 处理数量
     */
    public Uni<Integer> batchHandleAlerts(Long tenantId, List<Long> alertIds, StockAlertHandleRequest request) {
        return stockAlertRepository.batchUpdateStatus(tenantId, alertIds, request.getAlertStatus(),
                request.getHandlerId(), request.getHandlerName(), request.getHandleRemark());
    }

    /**
     * 自动清理已处理的预警
     *
     * @param tenantId 租户ID
     * @param days 保留天数
     * @return 清理数量
     */
    public Uni<Long> cleanupHandledAlerts(Long tenantId, int days) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
        return stockAlertRepository.deleteHandledAlerts(tenantId, expireTime);
    }

    /**
     * 计算预警级别
     */
    private Integer calculateAlertLevel(Inventory inventory) {
        if (inventory.getQuantity() <= 0) {
            return 4; // 紧急
        } else if (inventory.getAvailableQuantity() <= inventory.getSafetyStock()) {
            return 3; // 高
        } else if (inventory.getAvailableQuantity() <= inventory.getWarningStock()) {
            return 2; // 中
        } else {
            return 1; // 低
        }
    }

    /**
     * 生成预警消息
     */
    private String generateAlertMessage(Inventory inventory, Integer alertType) {
        StringBuilder message = new StringBuilder();

        switch (alertType) {
            case 1: // 库存不足
                message.append("商品 ").append(inventory.getSkuCode())
                       .append(" 库存不足，当前库存：").append(inventory.getAvailableQuantity())
                       .append("，预警阈值：").append(inventory.getWarningStock());
                break;
            case 2: // 库存过多
                message.append("商品 ").append(inventory.getSkuCode())
                       .append(" 库存过多，当前库存：").append(inventory.getQuantity())
                       .append("，最大库存：").append(inventory.getMaxStock());
                break;
            case 3: // 零库存
                message.append("商品 ").append(inventory.getSkuCode())
                       .append(" 库存为零，请及时补货");
                break;
            case 4: // 负库存
                message.append("商品 ").append(inventory.getSkuCode())
                       .append(" 出现负库存，当前库存：").append(inventory.getQuantity())
                       .append("，请立即处理");
                break;
            default:
                message.append("商品 ").append(inventory.getSkuCode()).append(" 库存异常");
        }

        if (inventory.getWarehouseCode() != null) {
            message.append("，仓库：").append(inventory.getWarehouseCode());
        }

        return message.toString();
    }

    /**
     * 为指定库存创建库存不足预警
     */
    public Uni<StockAlert> createLowStockAlertForInventory(Long tenantId, Long inventoryId) {
        // 这里应该先查询库存信息，然后创建预警
        // 为了简化，我们创建一个基本的预警记录
        StockAlert alert = new StockAlert();
        alert.setTenantId(tenantId);
        alert.setInventoryId(inventoryId);
        alert.setAlertType(1); // 库存不足
        alert.setAlertLevel(2); // 中等级别
        alert.setAlertStatus(1); // 待处理
        alert.setAlertMessage("手动创建的库存不足预警");
//        alert.setCreatedAt(LocalDateTime.now());
//        alert.setUpdatedAt(LocalDateTime.now());

        return stockAlertRepository.persist(alert);
    }

    // ========== BaseService 抽象方法实现 ==========

    @Override
    public Uni<StockAlert> findByIdAndTenant(Long id, Long tenantId) {
        return stockAlertRepository.findByIdAndTenant(id, tenantId);
    }

    @Override
    public Uni<List<StockAlert>> findAllByTenant(Long tenantId) {
        return stockAlertRepository.findAllByTenant(tenantId);
    }

    @Override
    public Uni<StockAlert> create(StockAlert entity) {
        return stockAlertRepository.persist(entity);
    }

    @Override
    public Uni<StockAlert> update(StockAlert entity) {
        return stockAlertRepository.persist(entity);
    }

    @Override
    public Uni<Boolean> delete(Long id, Long tenantId) {
        return stockAlertRepository.deleteByIdAndTenant(id, tenantId)
            .map(deleted -> deleted > 0);
    }

    @Override
    public Uni<Long> count(Long tenantId) {
        return stockAlertRepository.countByTenant(tenantId);
    }

    @Override
    public Uni<Boolean> exists(Long id, Long tenantId) {
        return findByIdAndTenant(id, tenantId)
            .map(alert -> alert != null);
    }

    @Override
    public Uni<Integer> batchDelete(List<Long> ids, Long tenantId) {
        return stockAlertRepository.batchDeleteByTenant(ids, tenantId);
    }

    @Override
    protected String getEntityName() {
        return "库存预警";
    }

    protected StockAlertRepository getRepository() {
        return stockAlertRepository;
    }
}
