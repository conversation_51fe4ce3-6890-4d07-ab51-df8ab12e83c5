package com.visthink.inventory.config;

import io.quarkus.logging.Log;
import io.quarkus.runtime.StartupEvent;
import org.hibernate.reactive.mutiny.Mutiny;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;

/**
 * Hibernate Reactive 配置验证类
 * 
 * 在应用启动时验证 Hibernate Reactive 配置是否正确
 * 确保 SessionFactory 正常初始化
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class HibernateReactiveConfig {

    @Inject
    Mutiny.SessionFactory sessionFactory;

    /**
     * 应用启动时验证 Hibernate Reactive 配置
     * 
     * @param ev 启动事件
     */
    void onStart(@Observes StartupEvent ev) {
        Log.info("=== 库存服务 Hibernate Reactive 配置初始化 ===");
        
        try {
            // 验证SessionFactory是否正常初始化
            if (sessionFactory != null) {
                Log.info("✅ SessionFactory 验证通过");
                Log.info("✅ 数据库连接池配置已优化");
                Log.info("✅ Hibernate多租户配置已禁用");
                Log.info("✅ ReactiveBaseRepository 模式已启用");
            } else {
                Log.error("❌ SessionFactory 初始化失败");
                throw new RuntimeException("SessionFactory 初始化失败");
            }
            
            Log.info("=== 库存服务 Hibernate Reactive 配置初始化完成 ===");
            
        } catch (Exception e) {
            Log.errorf(e, "❌ Hibernate Reactive 配置初始化失败: %s", e.getMessage());
            throw new RuntimeException("Hibernate Reactive 配置初始化失败", e);
        }
    }
}
