package com.visthink.inventory.resource;

import com.visthink.common.base.SimpleController;
import com.visthink.common.dto.ApiResponse;
import com.visthink.inventory.entity.Inventory;
import com.visthink.inventory.service.InventoryService;
import com.visthink.inventory.dto.*;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import java.util.List;

/**
 * 库存管理REST接口 - 重构版本
 *
 * 重构说明：
 * 1. 继承SimpleController，只处理HTTP相关逻辑
 * 2. 所有业务逻辑委托给InventoryService处理
 * 3. 职责清晰：Controller专注HTTP，Service专注业务
 * 4. 易于测试和维护
 *
 * <AUTHOR>
 */
@Path("/api/inventory")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "库存管理", description = "库存管理相关接口")
public class InventoryResource extends SimpleController {

    @Inject
    InventoryService inventoryService;

    /**
     * 创建库存记录
     */
    @POST
    @Operation(summary = "创建库存记录", description = "创建新的库存记录")
    @APIResponse(responseCode = "200", description = "创建成功")
    public Uni<ApiResponse<Inventory>> createInventory(@Valid InventoryCreateRequest request) {
        logOperation("创建库存记录", "开始处理创建请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> {
                    logOperation("创建库存记录", tenantId, "租户验证通过");
                    return inventoryService.createInventory(tenantId, request);
                })
                .map(inventory -> {
                    logOperation("创建库存记录", "创建成功，库存ID: " + inventory.id);
                    return success("创建库存记录成功", inventory);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "创建库存记录"));
    }

    /**
     * 根据ID获取库存详情
     */
    @GET
    @Path("/{id}")
    @Operation(summary = "获取库存详情", description = "根据ID获取库存详情")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<Inventory>> getInventory(@PathParam("id") Long id) {
        logOperation("获取库存详情", "库存ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findByIdAndTenant(id, tenantId))
                .map(inventory -> {
                    if (inventory != null) {
                        logOperation("获取库存详情", "查询成功");
                        return success(inventory);
                    } else {
                        return ApiResponse.<Inventory>notFound("库存不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "获取库存详情"));
    }

    /**
     * 更新库存信息
     */
    @PUT
    @Path("/{id}")
    @Operation(summary = "更新库存信息", description = "更新库存基本信息")
    @APIResponse(responseCode = "200", description = "更新成功")
    public Uni<ApiResponse<Inventory>> updateInventory(@PathParam("id") Long id, @Valid InventoryUpdateRequest request) {
        logOperation("更新库存信息", "库存ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.updateInventory(tenantId, id, request))
                .map(inventory -> {
                    logOperation("更新库存信息", "更新成功");
                    return success("更新库存信息成功", inventory);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "更新库存信息"));
    }

    /**
     * 删除库存记录
     */
    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除库存记录", description = "删除指定的库存记录")
    @APIResponse(responseCode = "200", description = "删除成功")
    public Uni<ApiResponse<Boolean>> deleteInventory(@PathParam("id") Long id) {
        logOperation("删除库存记录", "库存ID: " + id);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.delete(id, tenantId))
                .map(result -> {
                    if (result) {
                        logOperation("删除库存记录", "删除成功");
                        return success("删除库存记录成功", true);
                    } else {
                        return ApiResponse.<Boolean>notFound("库存不存在");
                    }
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "删除库存记录"));
    }

    /**
     * 分页查询库存
     */
    @GET
    @Operation(summary = "分页查询库存", description = "根据条件分页查询库存列表")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<Inventory>>> queryInventory(@BeanParam InventoryQueryRequest request) {
        logOperation("分页查询库存", "开始处理查询请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findByConditions(tenantId, request))
                .map(inventories -> {
                    logOperation("分页查询库存", "查询成功，共" + inventories.size() + "条记录");
                    return success(inventories);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "分页查询库存"));
    }

    /**
     * 增加库存
     */
    @POST
    @Path("/{id}/increase")
    @Operation(summary = "增加库存", description = "增加指定库存的数量")
    @APIResponse(responseCode = "200", description = "操作成功")
    public Uni<ApiResponse<Boolean>> increaseStock(@PathParam("id") Long id, @Valid InventoryOperationRequest request) {
        logOperation("增加库存", String.format("库存ID: %d, 数量: %d", id, request.getQuantity()));

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.increaseStock(tenantId, id, request))
                .map(result -> {
                    logOperation("增加库存", "操作成功");
                    return success("增加库存成功", result);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "增加库存"));
    }

    /**
     * 减少库存
     */
    @POST
    @Path("/{id}/decrease")
    @Operation(summary = "减少库存", description = "减少指定库存的数量")
    @APIResponse(responseCode = "200", description = "操作成功")
    public Uni<ApiResponse<Boolean>> decreaseStock(@PathParam("id") Long id, @Valid InventoryOperationRequest request) {
        logOperation("减少库存", String.format("库存ID: %d, 数量: %d", id, request.getQuantity()));

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.decreaseStock(tenantId, id, request))
                .map(result -> {
                    logOperation("减少库存", "操作成功");
                    return success("减少库存成功", result);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "减少库存"));
    }

    /**
     * 根据SKU查询库存
     */
    @GET
    @Path("/sku/{skuId}")
    @Operation(summary = "根据SKU查询库存", description = "根据SKU ID查询库存信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<Inventory>>> getInventoryBySkuId(@PathParam("skuId") Long skuId) {
        logOperation("根据SKU查询库存", "SKU ID: " + skuId);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findBySkuId(tenantId, skuId))
                .map(inventories -> {
                    logOperation("根据SKU查询库存", "查询成功，共" + inventories.size() + "条记录");
                    return success(inventories);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据SKU查询库存"));
    }

    /**
     * 根据商品查询库存
     */
    @GET
    @Path("/product/{productId}")
    @Operation(summary = "根据商品查询库存", description = "根据商品ID查询库存信息")
    @APIResponse(responseCode = "200", description = "查询成功")
    public Uni<ApiResponse<List<Inventory>>> getInventoryByProductId(@PathParam("productId") Long productId) {
        logOperation("根据商品查询库存", "商品ID: " + productId);

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.findByProductId(tenantId, productId))
                .map(inventories -> {
                    logOperation("根据商品查询库存", "查询成功，共" + inventories.size() + "条记录");
                    return success(inventories);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "根据商品查询库存"));
    }

    /**
     * 统计库存数量
     */
    @GET
    @Path("/count")
    @Operation(summary = "统计库存数量", description = "统计库存总数量")
    @APIResponse(responseCode = "200", description = "统计成功")
    public Uni<ApiResponse<Long>> count() {
        logOperation("统计库存数量", "开始处理统计请求");

        return getCurrentTenantId()
                .flatMap(tenantId -> inventoryService.count(tenantId))
                .map(count -> {
                    logOperation("统计库存数量", "统计成功: " + count);
                    return success(count);
                })
                .onFailure().recoverWithItem(throwable ->
                    handleError(throwable, "统计库存数量"));
    }
}
