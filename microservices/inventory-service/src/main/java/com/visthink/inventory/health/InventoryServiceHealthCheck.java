package com.visthink.inventory.health;

import com.visthink.inventory.repository.InventoryRepository;
import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.Liveness;
import org.eclipse.microprofile.health.Readiness;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

/**
 * 库存服务健康检查
 * 
 * <AUTHOR>
 */
@ApplicationScoped
public class InventoryServiceHealthCheck {

    @Inject
    InventoryRepository inventoryRepository;

    @Liveness
    public HealthCheck livenessCheck() {
        return () -> HealthCheckResponse.named("inventory-service-liveness")
                .status(true)
                .withData("service", "inventory-service")
                .withData("version", "1.0.0")
                .withData("status", "UP")
                .build();
    }

    @Readiness
    public HealthCheck readinessCheck() {
        return () -> {
            try {
                // 检查数据库连接
                inventoryRepository.count().await().indefinitely();
                
                return HealthCheckResponse.named("inventory-service-readiness")
                        .status(true)
                        .withData("database", "connected")
                        .withData("service", "inventory-service")
                        .withData("status", "READY")
                        .build();
            } catch (Exception e) {
                return HealthCheckResponse.named("inventory-service-readiness")
                        .status(false)
                        .withData("database", "disconnected")
                        .withData("service", "inventory-service")
                        .withData("status", "NOT_READY")
                        .withData("error", e.getMessage())
                        .build();
            }
        };
    }
}
