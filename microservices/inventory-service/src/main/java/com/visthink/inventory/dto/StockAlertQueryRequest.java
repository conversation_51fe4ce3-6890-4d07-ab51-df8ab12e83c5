package com.visthink.inventory.dto;

import lombok.Data;
import jakarta.ws.rs.QueryParam;
import java.time.LocalDateTime;

/**
 * 库存预警查询请求DTO
 *
 * <AUTHOR>
 */
@Data
public class StockAlertQueryRequest {

    /**
     * 库存ID
     */
    @QueryParam("inventoryId")
    private Long inventoryId;

    /**
     * 商品ID
     */
    @QueryParam("productId")
    private Long productId;

    /**
     * 商品编码
     */
    @QueryParam("productCode")
    private String productCode;

    /**
     * 商品名称
     */
    @QueryParam("productName")
    private String productName;

    /**
     * SKU ID
     */
    @QueryParam("skuId")
    private Long skuId;

    /**
     * SKU编码
     */
    @QueryParam("skuCode")
    private String skuCode;

    /**
     * SKU名称
     */
    @QueryParam("skuName")
    private String skuName;

    /**
     * 仓库ID
     */
    @QueryParam("warehouseId")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @QueryParam("warehouseCode")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @QueryParam("warehouseName")
    private String warehouseName;

    /**
     * 预警类型：1-库存不足，2-库存过多，3-零库存，4-负库存
     */
    @QueryParam("alertType")
    private Integer alertType;

    /**
     * 预警级别：1-低，2-中，3-高，4-紧急
     */
    @QueryParam("alertLevel")
    private Integer alertLevel;

    /**
     * 预警状态：1-待处理，2-处理中，3-已处理，4-已忽略
     */
    @QueryParam("alertStatus")
    private Integer alertStatus;

    /**
     * 处理人ID
     */
    @QueryParam("handlerId")
    private Long handlerId;

    /**
     * 处理人姓名
     */
    @QueryParam("handlerName")
    private String handlerName;

    /**
     * 预警时间开始
     */
    @QueryParam("alertTimeStart")
    private LocalDateTime alertTimeStart;

    /**
     * 预警时间结束
     */
    @QueryParam("alertTimeEnd")
    private LocalDateTime alertTimeEnd;

    /**
     * 处理时间开始
     */
    @QueryParam("handleTimeStart")
    private LocalDateTime handleTimeStart;

    /**
     * 处理时间结束
     */
    @QueryParam("handleTimeEnd")
    private LocalDateTime handleTimeEnd;

    /**
     * 排序字段
     */
    @QueryParam("sortField")
    private String sortField = "createTime";

    /**
     * 排序方向：asc/desc
     */
    @QueryParam("sortDirection")
    private String sortDirection = "desc";

    /**
     * 页码
     */
    @QueryParam("page")
    private Integer page = 1;

    /**
     * 页大小
     */
    @QueryParam("size")
    private Integer size = 20;
}
