package com.visthink.inventory.dto;

import jakarta.ws.rs.QueryParam;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 库存变动日志查询请求DTO
 *
 * <AUTHOR>
 */
@Data
public class InventoryLogQueryRequest {

    /**
     * 库存ID
     */
    @QueryParam("inventoryId")
    private Long inventoryId;

    /**
     * 商品ID
     */
    @QueryParam("productId")
    private Long productId;

    /**
     * 商品编码
     */
    @QueryParam("productCode")
    private String productCode;

    /**
     * SKU ID
     */
    @QueryParam("skuId")
    private Long skuId;

    /**
     * SKU编码
     */
    @QueryParam("skuCode")
    private String skuCode;

    /**
     * 仓库ID
     */
    @QueryParam("warehouseId")
    private Long warehouseId;

    /**
     * 仓库编码
     */
    @QueryParam("warehouseCode")
    private String warehouseCode;

    /**
     * 变动类型：1-入库，2-出库，3-调拨，4-盘点，5-预占，6-释放，7-损耗，8-退货
     */
    @QueryParam("changeType")
    private Integer changeType;

    /**
     * 业务类型：1-采购单，2-销售单，3-调拨单，4-盘点单，5-其他
     */
    @QueryParam("businessType")
    private Integer businessType;

    /**
     * 业务单据ID
     */
    @QueryParam("businessId")
    private Long businessId;

    /**
     * 业务单据编号
     */
    @QueryParam("businessNo")
    private String businessNo;

    /**
     * 操作人ID
     */
    @QueryParam("operatorId")
    private Long operatorId;

    /**
     * 操作人姓名
     */
    @QueryParam("operatorName")
    private String operatorName;

    /**
     * 变动时间开始
     */
    @QueryParam("changeTimeStart")
    private LocalDateTime changeTimeStart;

    /**
     * 变动时间结束
     */
    @QueryParam("changeTimeEnd")
    private LocalDateTime changeTimeEnd;

    /**
     * 最小变动数量
     */
    @QueryParam("minChangeQuantity")
    private Integer minChangeQuantity;

    /**
     * 最大变动数量
     */
    @QueryParam("maxChangeQuantity")
    private Integer maxChangeQuantity;

    /**
     * 排序字段
     */
    @QueryParam("sortField")
    private String sortField = "createTime";

    /**
     * 排序方向：asc/desc
     */
    @QueryParam("sortDirection")
    private String sortDirection = "desc";

    /**
     * 页码
     */
    @QueryParam("page")
    private Integer page = 1;

    /**
     * 页大小
     */
    @QueryParam("size")
    private Integer size = 20;
}
