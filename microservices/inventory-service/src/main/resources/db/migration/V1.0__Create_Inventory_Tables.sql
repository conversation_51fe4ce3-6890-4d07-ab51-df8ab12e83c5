-- 库存服务数据库表结构
-- 版本: V1.0
-- 描述: 创建库存相关表

-- 1. 库存主表
CREATE TABLE IF NOT EXISTS inventory (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    product_code VARCHAR(100),
    sku_id BIGINT NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    warehouse_id BIGINT,
    warehouse_code VARCHAR(100),
    quantity INTEGER NOT NULL DEFAULT 0,
    available_quantity INTEGER NOT NULL DEFAULT 0,
    reserved_quantity INTEGER NOT NULL DEFAULT 0,
    in_transit_quantity INTEGER NOT NULL DEFAULT 0,
    safety_stock INTEGER NOT NULL DEFAULT 0,
    warning_stock INTEGER NOT NULL DEFAULT 0,
    max_stock INTEGER,
    min_stock INTEGER,
    reorder_point INTEGER,
    reorder_quantity INTEGER,
    average_cost DECIMAL(10,2),
    total_value DECIMAL(12,2),
    status INTEGER NOT NULL DEFAULT 1,
    last_stocktaking_at TIMESTAMP,
    remark VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 2. 库存变动日志表
CREATE TABLE IF NOT EXISTS inventory_log (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    inventory_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    product_code VARCHAR(100),
    sku_id BIGINT NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    warehouse_id BIGINT,
    warehouse_code VARCHAR(100),
    change_type INTEGER NOT NULL,
    change_quantity INTEGER NOT NULL,
    before_quantity INTEGER NOT NULL,
    after_quantity INTEGER NOT NULL,
    before_available_quantity INTEGER NOT NULL,
    after_available_quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2),
    total_amount DECIMAL(12,2),
    business_type INTEGER,
    business_id BIGINT,
    business_no VARCHAR(100),
    operator_id BIGINT,
    operator_name VARCHAR(100),
    reason VARCHAR(500),
    remark VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 3. 库存预警表
CREATE TABLE IF NOT EXISTS stock_alert (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    inventory_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    product_code VARCHAR(100),
    product_name VARCHAR(500),
    sku_id BIGINT NOT NULL,
    sku_code VARCHAR(100) NOT NULL,
    sku_name VARCHAR(500),
    warehouse_id BIGINT,
    warehouse_code VARCHAR(100),
    warehouse_name VARCHAR(200),
    alert_type INTEGER NOT NULL,
    current_quantity INTEGER NOT NULL,
    available_quantity INTEGER NOT NULL,
    alert_threshold INTEGER,
    alert_level INTEGER NOT NULL,
    alert_status INTEGER NOT NULL DEFAULT 1,
    alert_message VARCHAR(1000),
    handler_id BIGINT,
    handler_name VARCHAR(100),
    handled_at TIMESTAMP,
    handle_remark VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 4. 仓库表
CREATE TABLE IF NOT EXISTS warehouse (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL,
    warehouse_code VARCHAR(100) UNIQUE NOT NULL,
    warehouse_name VARCHAR(200) NOT NULL,
    warehouse_type INTEGER NOT NULL DEFAULT 1,
    address VARCHAR(500),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    status INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    remark VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    version INTEGER NOT NULL DEFAULT 0
);

-- 创建索引
-- 库存表索引
CREATE INDEX IF NOT EXISTS idx_inventory_tenant_id ON inventory(tenant_id);
CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_sku_id ON inventory(sku_id);
CREATE INDEX IF NOT EXISTS idx_inventory_sku_code ON inventory(sku_code);
CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_id ON inventory(warehouse_id);
CREATE INDEX IF NOT EXISTS idx_inventory_tenant_sku ON inventory(tenant_id, sku_id);
CREATE INDEX IF NOT EXISTS idx_inventory_tenant_product ON inventory(tenant_id, product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_tenant_warehouse ON inventory(tenant_id, warehouse_id);
CREATE INDEX IF NOT EXISTS idx_inventory_status ON inventory(status);
CREATE INDEX IF NOT EXISTS idx_inventory_quantity ON inventory(quantity);
CREATE INDEX IF NOT EXISTS idx_inventory_available_quantity ON inventory(available_quantity);
CREATE INDEX IF NOT EXISTS idx_inventory_warning_stock ON inventory(tenant_id, available_quantity, warning_stock);
CREATE INDEX IF NOT EXISTS idx_inventory_reorder_point ON inventory(tenant_id, available_quantity, reorder_point);

-- 库存日志表索引
CREATE INDEX IF NOT EXISTS idx_inventory_log_tenant_id ON inventory_log(tenant_id);
CREATE INDEX IF NOT EXISTS idx_inventory_log_inventory_id ON inventory_log(inventory_id);
CREATE INDEX IF NOT EXISTS idx_inventory_log_product_id ON inventory_log(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_log_sku_id ON inventory_log(sku_id);
CREATE INDEX IF NOT EXISTS idx_inventory_log_warehouse_id ON inventory_log(warehouse_id);
CREATE INDEX IF NOT EXISTS idx_inventory_log_change_type ON inventory_log(change_type);
CREATE INDEX IF NOT EXISTS idx_inventory_log_business ON inventory_log(business_type, business_id);
CREATE INDEX IF NOT EXISTS idx_inventory_log_business_no ON inventory_log(business_no);
CREATE INDEX IF NOT EXISTS idx_inventory_log_operator ON inventory_log(operator_id);
CREATE INDEX IF NOT EXISTS idx_inventory_log_created_at ON inventory_log(created_at);

-- 库存预警表索引
CREATE INDEX IF NOT EXISTS idx_stock_alert_tenant_id ON stock_alert(tenant_id);
CREATE INDEX IF NOT EXISTS idx_stock_alert_inventory_id ON stock_alert(inventory_id);
CREATE INDEX IF NOT EXISTS idx_stock_alert_product_id ON stock_alert(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_alert_sku_id ON stock_alert(sku_id);
CREATE INDEX IF NOT EXISTS idx_stock_alert_warehouse_id ON stock_alert(warehouse_id);
CREATE INDEX IF NOT EXISTS idx_stock_alert_type ON stock_alert(alert_type);
CREATE INDEX IF NOT EXISTS idx_stock_alert_level ON stock_alert(alert_level);
CREATE INDEX IF NOT EXISTS idx_stock_alert_status ON stock_alert(alert_status);
CREATE INDEX IF NOT EXISTS idx_stock_alert_handler ON stock_alert(handler_id);
CREATE INDEX IF NOT EXISTS idx_stock_alert_created_at ON stock_alert(created_at);
CREATE INDEX IF NOT EXISTS idx_stock_alert_handled_at ON stock_alert(handled_at);

-- 仓库表索引
CREATE INDEX IF NOT EXISTS idx_warehouse_tenant_id ON warehouse(tenant_id);
CREATE INDEX IF NOT EXISTS idx_warehouse_code ON warehouse(warehouse_code);
CREATE INDEX IF NOT EXISTS idx_warehouse_tenant_code ON warehouse(tenant_id, warehouse_code);
CREATE INDEX IF NOT EXISTS idx_warehouse_status ON warehouse(status);

-- 外键约束
ALTER TABLE inventory_log ADD CONSTRAINT fk_inventory_log_inventory FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE;
ALTER TABLE stock_alert ADD CONSTRAINT fk_stock_alert_inventory FOREIGN KEY (inventory_id) REFERENCES inventory(id) ON DELETE CASCADE;

-- 唯一约束
CREATE UNIQUE INDEX IF NOT EXISTS uk_inventory_tenant_sku_warehouse ON inventory(tenant_id, sku_id, COALESCE(warehouse_id, 0));

-- 添加注释
COMMENT ON TABLE inventory IS '库存主表';
COMMENT ON TABLE inventory_log IS '库存变动日志表';
COMMENT ON TABLE stock_alert IS '库存预警表';
COMMENT ON TABLE warehouse IS '仓库表';

-- 插入默认数据
-- 默认仓库
INSERT INTO warehouse (tenant_id, warehouse_code, warehouse_name, warehouse_type, address, status, created_by) 
VALUES (1, 'DEFAULT', '默认仓库', 1, '默认地址', 1, 'system') 
ON CONFLICT DO NOTHING;
