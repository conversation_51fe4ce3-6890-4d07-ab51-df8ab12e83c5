-- 初始化微服务数据库脚本
-- 为每个微服务创建独立的数据库

-- 创建用户与租户服务数据库
CREATE DATABASE visthink_member;
GRANT ALL PRIVILEGES ON DATABASE visthink_member TO postgres;

-- 创建商品服务数据库
CREATE DATABASE visthink_product;
GRANT ALL PRIVILEGES ON DATABASE visthink_product TO postgres;

-- 创建库存数据库
CREATE DATABASE visthink_inventory;
GRANT ALL PRIVILEGES ON DATABASE visthink_inventory TO visthink;

-- 创建库存服务数据库
CREATE DATABASE visthink_inventory;
GRANT ALL PRIVILEGES ON DATABASE visthink_inventory TO postgres;

-- 创建订单服务数据库
CREATE DATABASE visthink_order;
GRANT ALL PRIVILEGES ON DATABASE visthink_order TO postgres;

-- 创建工作流服务数据库
CREATE DATABASE visthink_workflow;
GRANT ALL PRIVILEGES ON DATABASE visthink_workflow TO postgres;

-- 创建平台集成服务数据库
CREATE DATABASE visthink_platform;
GRANT ALL PRIVILEGES ON DATABASE visthink_platform TO postgres;

-- 创建文件服务数据库
CREATE DATABASE visthink_file;
GRANT ALL PRIVILEGES ON DATABASE visthink_file TO postgres;

-- 创建API日志服务数据库
CREATE DATABASE visthink_log;
GRANT ALL PRIVILEGES ON DATABASE visthink_log TO postgres;

-- 创建工作流服务数据库
CREATE DATABASE visthink_workflow;
GRANT ALL PRIVILEGES ON DATABASE visthink_workflow TO postgres;

-- 创建配置中心数据库
CREATE DATABASE visthink_config;
GRANT ALL PRIVILEGES ON DATABASE visthink_config TO postgres;

-- 创建系统监控数据库
CREATE DATABASE visthink_monitor;
GRANT ALL PRIVILEGES ON DATABASE visthink_monitor TO postgres;

-- 输出创建结果
\echo '数据库初始化完成！'
\echo '已创建以下数据库：'
\echo '- visthink_member: 用户与租户服务'
\echo '- visthink_product: 商品服务'
\echo '- visthink_inventory: 库存服务'
\echo '- visthink_order: 订单服务'
\echo '- visthink_platform: 平台集成服务'
\echo '- visthink_file: 文件服务'
\echo '- visthink_log: API日志服务'
\echo '- visthink_workflow: 工作流服务'
\echo '- visthink_config: 配置中心'
\echo '- visthink_monitor: 系统监控'
