version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: visthink-postgres
    environment:
      POSTGRES_DB: visthink_erp
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: zylp
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "35432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - visthink-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: visthink-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - visthink-network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Consul服务注册中心
  consul:
    image: consul:1.15
    container_name: visthink-consul
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    volumes:
      - consul_data:/consul/data
    networks:
      - visthink-network
    restart: unless-stopped
    command: >
      consul agent -dev 
      -client=0.0.0.0 
      -ui 
      -log-level=INFO
      -data-dir=/consul/data
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Jaeger链路追踪
  jaeger:
    image: jaegertracing/all-in-one:1.45
    container_name: visthink-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
      - "14250:14250"  # gRPC collector
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - visthink-network
    restart: unless-stopped

  # Prometheus监控
  prometheus:
    image: prom/prometheus:v2.44.0
    container_name: visthink-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - visthink-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana可视化
  grafana:
    image: grafana/grafana:9.5.2
    container_name: visthink-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - visthink-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # MinIO对象存储
  minio:
    image: minio/minio:RELEASE.2023-06-19T19-52-50Z
    container_name: visthink-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    networks:
      - visthink-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Nginx API网关
  nginx:
    image: nginx:1.24-alpine
    container_name: visthink-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - visthink-network
    restart: unless-stopped
    depends_on:
      - member-center
      - product-service
      - inventory-service
      - order-service
      - workflow-service

  # 用户与租户服务
  member-center:
    build:
      context: ./member-center
      dockerfile: Dockerfile
    container_name: visthink-member-center
    ports:
      - "8081:8081"
    environment:
      DB_URL: vertx-reactive:postgresql://postgres:5432/visthink_member
      DB_USERNAME: postgres
      DB_PASSWORD: zylp
      REDIS_URL: redis://redis:6379
      CONSUL_URL: http://consul:8500
      JAEGER_ENDPOINT: http://jaeger:14268/api/traces
    networks:
      - visthink-network
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      consul:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 商品服务
  product-service:
    build:
      context: ./product-service
      dockerfile: Dockerfile
    container_name: visthink-product-service
    ports:
      - "8082:8082"
    environment:
      DB_URL: vertx-reactive:postgresql://postgres:5432/visthink_product
      DB_USERNAME: postgres
      DB_PASSWORD: zylp
      REDIS_URL: redis://redis:6379
      CONSUL_URL: http://consul:8500
      JAEGER_ENDPOINT: http://jaeger:14268/api/traces
    networks:
      - visthink-network
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      consul:
        condition: service_healthy

  # 库存服务
  inventory-service:
    build:
      context: ./inventory-service
      dockerfile: Dockerfile
    container_name: visthink-inventory-service
    ports:
      - "8083:8083"
    environment:
      DB_URL: vertx-reactive:postgresql://postgres:5432/visthink_inventory
      DB_USERNAME: postgres
      DB_PASSWORD: zylp
      REDIS_URL: redis://redis:6379
      CONSUL_URL: http://consul:8500
      JAEGER_ENDPOINT: http://jaeger:14268/api/traces
    networks:
      - visthink-network
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      consul:
        condition: service_healthy

  # 订单服务
  order-service:
    build:
      context: ./order-service
      dockerfile: Dockerfile
    container_name: visthink-order-service
    ports:
      - "8084:8084"
    environment:
      DB_URL: vertx-reactive:postgresql://postgres:5432/visthink_order
      DB_USERNAME: postgres
      DB_PASSWORD: zylp
      REDIS_URL: redis://redis:6379
      CONSUL_URL: http://consul:8500
      JAEGER_ENDPOINT: http://jaeger:14268/api/traces
    networks:
      - visthink-network
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      consul:
        condition: service_healthy

  # 工作流服务
  workflow-service:
    build:
      context: ./workflow-service
      dockerfile: Dockerfile
    container_name: visthink-workflow-service
    ports:
      - "8088:8088"
    environment:
      DB_URL: vertx-reactive:postgresql://postgres:5432/visthink_workflow
      DB_USERNAME: postgres
      DB_PASSWORD: zylp
      REDIS_URL: redis://redis:6379/2
      CONSUL_URL: http://consul:8500
      JAEGER_ENDPOINT: http://jaeger:14268/api/traces
      MEMBER_SERVICE_URL: http://member-center:8081
    networks:
      - visthink-network
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      consul:
        condition: service_healthy
      member-center:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  consul_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  minio_data:
    driver: local

networks:
  visthink-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
