@echo off
REM Visthink ERP 微服务启动脚本 (Windows版本)
REM 用于按顺序启动微服务，确保依赖关系正确

setlocal enabledelayedexpansion

REM 设置颜色
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM 日志函数
:log_info
echo %GREEN%[INFO]%NC% %~1
goto :eof

:log_warn
echo %YELLOW%[WARN]%NC% %~1
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:log_step
echo %BLUE%[STEP]%NC% %~1
goto :eof

REM 检查Docker和Docker Compose
:check_prerequisites
call :log_step "检查前置条件..."

where docker >nul 2>nul
if %errorlevel% neq 0 (
    call :log_error "Docker未安装，请先安装Docker Desktop"
    exit /b 1
)

where docker-compose >nul 2>nul
if %errorlevel% neq 0 (
    call :log_error "Docker Compose未安装，请先安装Docker Compose"
    exit /b 1
)

call :log_info "前置条件检查通过"
goto :eof

REM 清理旧容器
:cleanup_old_containers
call :log_step "清理旧容器..."

docker-compose down --remove-orphans
docker image prune -f

call :log_info "旧容器清理完成"
goto :eof

REM 构建共享模块
:build_shared_module
call :log_step "构建共享模块..."

cd shared-common
mvn clean install -DskipTests
cd ..

call :log_info "共享模块构建完成"
goto :eof

REM 启动基础设施服务
:start_infrastructure
call :log_step "启动基础设施服务..."

docker-compose up -d postgres redis consul jaeger prometheus grafana minio

call :log_info "等待基础设施服务启动..."
timeout /t 30 /nobreak >nul

call :log_info "基础设施服务启动完成"
goto :eof

REM 构建微服务
:build_microservices
call :log_step "构建微服务..."

if exist "member-center" (
    call :log_info "构建 member-center 服务..."
    cd member-center
    mvn clean package -DskipTests
    cd ..
)

REM 构建其他服务（当它们存在时）
for %%s in (product-service inventory-service order-service platform-integration file-service api-log-service workflow-service config-center gateway) do (
    if exist "%%s" (
        call :log_info "构建 %%s 服务..."
        cd %%s
        mvn clean package -DskipTests
        cd ..
    ) else (
        call :log_warn "%%s 服务目录不存在，跳过构建"
    )
)

call :log_info "微服务构建完成"
goto :eof

REM 启动核心服务
:start_core_services
call :log_step "启动核心微服务..."

for %%s in (member-center product-service inventory-service order-service) do (
    if exist "%%s" (
        call :log_info "启动 %%s 服务..."
        docker-compose up -d %%s
        timeout /t 15 /nobreak >nul
        
        docker-compose ps %%s | findstr "Up" >nul
        if !errorlevel! equ 0 (
            call :log_info "%%s 服务启动成功"
        ) else (
            call :log_error "%%s 服务启动失败"
            docker-compose logs %%s
            exit /b 1
        )
    ) else (
        call :log_warn "%%s 服务目录不存在，跳过启动"
    )
)

call :log_info "核心微服务启动完成"
goto :eof

REM 启动支撑服务
:start_support_services
call :log_step "启动支撑服务..."

for %%s in (platform-integration file-service api-log-service workflow-service) do (
    if exist "%%s" (
        call :log_info "启动 %%s 服务..."
        docker-compose up -d %%s
        timeout /t 10 /nobreak >nul
    ) else (
        call :log_warn "%%s 服务目录不存在，跳过启动"
    )
)

call :log_info "支撑服务启动完成"
goto :eof

REM 启动网关服务
:start_gateway
call :log_step "启动API网关..."

docker-compose up -d nginx
timeout /t 10 /nobreak >nul

curl -f http://localhost/health >nul 2>nul
if %errorlevel% equ 0 (
    call :log_info "API网关启动成功"
) else (
    call :log_warn "API网关可能未完全就绪"
)

call :log_info "API网关启动完成"
goto :eof

REM 显示服务状态
:show_service_status
call :log_step "显示服务状态..."

echo.
echo === 服务状态 ===
docker-compose ps

echo.
echo === 服务访问地址 ===
echo API网关: http://localhost
echo 用户服务: http://localhost:8081
echo Consul UI: http://localhost:8500
echo Prometheus: http://localhost:9090
echo Grafana: http://localhost:3000 (admin/admin123)
echo Jaeger UI: http://localhost:16686
echo MinIO Console: http://localhost:9001 (minioadmin/minioadmin123)
echo.
goto :eof

REM 主函数
:main
call :log_info "开始启动 Visthink ERP 微服务架构..."

call :check_prerequisites
call :cleanup_old_containers
call :build_shared_module
call :start_infrastructure
call :build_microservices
call :start_core_services
call :start_support_services
call :start_gateway
call :show_service_status

call :log_info "Visthink ERP 微服务架构启动完成！"
call :log_info "请访问 http://localhost 开始使用系统"
goto :eof

REM 脚本参数处理
if "%1"=="infrastructure" (
    call :check_prerequisites
    call :cleanup_old_containers
    call :start_infrastructure
    goto :eof
)

if "%1"=="build" (
    call :build_shared_module
    call :build_microservices
    goto :eof
)

if "%1"=="core" (
    call :start_core_services
    goto :eof
)

if "%1"=="support" (
    call :start_support_services
    goto :eof
)

if "%1"=="gateway" (
    call :start_gateway
    goto :eof
)

if "%1"=="status" (
    call :show_service_status
    goto :eof
)

if "%1"=="stop" (
    call :log_info "停止所有服务..."
    docker-compose down
    goto :eof
)

if "%1"=="restart" (
    call :log_info "重启所有服务..."
    docker-compose restart
    goto :eof
)

if "%1"=="logs" (
    if "%2"=="" (
        docker-compose logs -f
    ) else (
        docker-compose logs -f %2
    )
    goto :eof
)

REM 默认执行主函数
call :main
